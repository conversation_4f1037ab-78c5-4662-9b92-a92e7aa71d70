<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->
<resources>
    <!--SheetBehavior Address-->
    <string name="side_sheet_behavior" translatable="false" translatablet="false">com.microsoft.fluentui.drawer.SideSheetBehavior</string>
    <string name="top_sheet_behavior" translatable="false" translatablet="false">com.microsoft.fluentui.drawer.TopSheetBehavior</string>

    <!-- Text for bottomsheet handle -->
    <string name="drag_handle">Drag Handle</string>
    <!-- onClick Label for bottomsheet to expand -->
    <string name="expand">expand</string>
    <string name="expanded">expanded</string>
    <!-- onClick Label for bottomsheet to collapse -->
    <string name="collapse">collapse</string>
    <string name="collapsed">collapsed</string>
</resources>