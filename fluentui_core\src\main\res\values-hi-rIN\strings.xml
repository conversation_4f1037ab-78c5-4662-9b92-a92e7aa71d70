<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">प्राथमिक</string>
    <string name="fluentui_secondary">द्वितीयक</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">ख़ारिज करें</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">चयनित</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">चयनित नहीं</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">आइकन</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">आइकन</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">अक्षम किया गया</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">क्रिया बटन</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">सक्षम किया गया</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">पत्रक बंद करें</string>
    <!-- Describes close action -->
    <string name="fluentui_close">बंद करें</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">रद्द करें</string>
    <!--name of the icon -->
    <string name="fluentui_search">खोजें</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">माइक्रोफ़ोन</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">टेक्स्ट साफ़ करें</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">वापस जाएँ</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">सक्रिय किया गया</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">निष्क्रिय किया गया</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">कोई राय नहीं</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">ब्रांड</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">कॉन्ट्रास्ट</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">एक्सेंट</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">चेतावनी</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">खतरा</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">कोई त्रुटि हुई है.</string>
    <string name="fluentui_error">त्रुटि</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">संकेत</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">शेवरॉन</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">बाह्यरेखा</string>

    <string name="fluentui_action_button_icon">क्रिया बटन का आइकन</string>
    <string name="fluentui_center">मध्य का टेक्स्ट</string>
    <string name="fluentui_accessory_button">‘सहायक उपकरण’ बटन</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">रेडियो बटन</string>
    <string name="fluentui_label">लेबल करें</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">विस्तृत किया गया</string>
    <string name="fluentui_collapsed">संक्षिप्त किया गया</string>

    <!--types of control -->
    <string name="fluentui_large">बड़ा</string>
    <string name="fluentui_medium">मध्यम</string>
    <string name="fluentui_small">छोटा</string>
    <string name="fluentui_password_mode">पासवर्ड मोड</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">उपशीर्षक</string>
    <string name="fluentui_assistive_text">सहायक टेक्स्ट</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">शीर्षक</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">छोटा</string>"
    <string name="fluentui_long">लंबा</string>"
    <string name="fluentui_indefinite">अनिश्चित</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">बटन दबाया गया</string>
    <string name="fluentui_dismissed">ख़ारिज किया गया</string>
    <string name="fluentui_timeout">समयबाह्य</string>
    <string name="fluentui_left_swiped">बाईं ओर स्वाइप किया गया</string>
    <string name="fluentui_right_swiped">दाईं ओर स्वाइप किया गया</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">टेक्स्ट</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">संख्या</string>
    <string name="fluentui_keyboard_phone">फ़ोन</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">ईमेल</string>
    <string name="fluentui_keyboard_password">पासवर्ड</string>
    <string name="fluentui_keyboard_number_password">नंबर पासवर्ड</string>
    <string name="fluentui_keyboard_decimal">दशमलव</string>
</resources>