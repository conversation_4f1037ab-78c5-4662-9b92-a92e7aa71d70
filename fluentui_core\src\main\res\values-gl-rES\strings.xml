<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Principal</string>
    <string name="fluentui_secondary">Secundario</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Rexeitar</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Seleccionada</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Non se seleccionou</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Icona</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Icona</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Desactivado</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Botón de acción</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Activado</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Pechar folla</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Pechar</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Cancelar</string>
    <!--name of the icon -->
    <string name="fluentui_search">Buscar</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Micrófono</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Borrar texto</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Atrás</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Activado</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Desactivado</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutro</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marca</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contraste</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Énfase</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Aviso</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Perigo</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Produciuse un erro</string>
    <string name="fluentui_error">Erro</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Pista</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Comiñas angulares</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Contorno</string>

    <string name="fluentui_action_button_icon">Icona do botón de acción</string>
    <string name="fluentui_center">Centrar texto</string>
    <string name="fluentui_accessory_button">Botóns accesorios</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Botón de opción</string>
    <string name="fluentui_label">Etiqueta</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Expandido</string>
    <string name="fluentui_collapsed">Contraído</string>

    <!--types of control -->
    <string name="fluentui_large">Grande</string>
    <string name="fluentui_medium">Medio</string>
    <string name="fluentui_small">Pequeno</string>
    <string name="fluentui_password_mode">Modo de contrasinal</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Subtítulo</string>
    <string name="fluentui_assistive_text">Texto asistencial</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Título</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Curto</string>"
    <string name="fluentui_long">Longo</string>"
    <string name="fluentui_indefinite">Indefinido</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Botón premido</string>
    <string name="fluentui_dismissed">Rexeitado</string>
    <string name="fluentui_timeout">Excedeu o tempo de espera</string>
    <string name="fluentui_left_swiped">Pasouse á esquerda</string>
    <string name="fluentui_right_swiped">Pasouse á dereita</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Texto</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Número</string>
    <string name="fluentui_keyboard_phone">Teléfono</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Correo electrónico</string>
    <string name="fluentui_keyboard_password">Contrasinal</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimal</string>
</resources>