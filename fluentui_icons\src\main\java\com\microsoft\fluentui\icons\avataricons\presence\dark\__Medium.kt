package com.microsoft.fluentui.icons.avataricons.presence.dark

import androidx.compose.ui.graphics.vector.ImageVector
import com.microsoft.fluentui.icons.avataricons.presence.DarkGroup
import kotlin.collections.List as ____KtList

object MediumGroup

val DarkGroup.Medium: MediumGroup
    get() = MediumGroup

private var __AllIcons: ____KtList<ImageVector>? = null

val MediumGroup.AllIcons: ____KtList<ImageVector>
    get() {
        if (__AllIcons != null) {
            return __AllIcons!!
        }
        __AllIcons = listOf()
        return __AllIcons!!
    }
