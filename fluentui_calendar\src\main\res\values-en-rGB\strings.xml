<?xml version="1.0" encoding="utf-8"?>
<resources>
  <string name="monday_initial">M</string>
  <string name="tuesday_initial">T</string>
  <string name="wednesday_initial">W</string>
  <string name="thursday_initial">T</string>
  <string name="friday_initial">F</string>
  <string name="saturday_initial">S</string>
  <string name="sunday_initial">S</string>
  <string name="accessibility_goto_next_week">Go to next week</string>
  <string name="accessibility_goto_previous_week">Go to previous week</string>
  <string name="accessibility_today">today</string>
  <string name="accessibility_selected">Selected</string>
  <string name="done">Done</string>
  <string name="date_time">%1$s, %2$s</string>
  <string name="today">Today</string>
  <string name="tomorrow">Tomorrow</string>
  <string name="yesterday">Yesterday</string>
  <string name="date_time_picker_start_time">Start time</string>
  <string name="date_time_picker_end_time">End time</string>
  <string name="date_time_picker_start_date">Start date</string>
  <string name="date_time_picker_end_date">End date</string>
  <string name="date_time_picker_choose_time">Choose Time</string>
  <string name="date_time_picker_choose_date">Choose Date</string>
  <string name="date_time_picker_accessibility_dialog_title">Date Time Picker</string>
  <string name="date_picker_accessibility_dialog_title">Date Picker</string>
  <string name="date_time_picker_range_accessibility_dialog_title">Date Time Picker Range</string>
  <string name="date_picker_range_accessibility_dialog_title">Date Picker Range</string>
  <string name="date_time_picker_accessiblility_start_time">Start time tab</string>
  <string name="date_time_picker_accessiblility_end_time">End time tab</string>
  <string name="date_picker_accessiblility_start_date">Start date tab</string>
  <string name="date_picker_accessiblility_end_date">End date tab</string>
  <string name="date_time_picker_accessibility_close_dialog_button">Close dialogue</string>
  <string name="date_picker_accessibility_increment_month_button">Increment month</string>
  <string name="date_picker_accessibility_next_month_click_action">select next month</string>
  <string name="date_picker_accessibility_decrement_month_button">Decrement month</string>
  <string name="date_picker_accessibility_previous_month_click_action">select previous month</string>
  <string name="date_picker_accessibility_increment_day_button">Increment day</string>
  <string name="date_picker_accessibility_next_day_click_action">select next day</string>
  <string name="date_picker_accessibility_decrement_day_button">Decrement day</string>
  <string name="date_picker_accessibility_previous_day_click_action">select previous day</string>
  <string name="date_picker_accessibility_increment_year_button">Increment year</string>
  <string name="date_picker_accessibility_next_year_click_action">select next year</string>
  <string name="date_picker_accessibility_decrement_year_button">Decrement year</string>
  <string name="date_picker_accessibility_previous_year_click_action">select previous year</string>
  <string name="date_time_picker_accessibility_increment_date_button">Increment date</string>
  <string name="date_picker_accessibility_next_date_click_action">select next date</string>
  <string name="date_time_picker_accessibility_decrement_date_button">Decrement date</string>
  <string name="date_picker_accessibility_previous_date_click_action">select previous date</string>
  <string name="date_time_picker_accessibility_increment_hour_button">Increment hour</string>
  <string name="date_picker_accessibility_next_hour_click_action">select next hour</string>
  <string name="date_time_picker_accessibility_decrement_hour_button">Decrement hour</string>
  <string name="date_picker_accessibility_previous_hour_click_action">select previous hour</string>
  <string name="date_time_picker_accessibility_increment_minute_button">Increment minute</string>
  <string name="date_picker_accessibility_next_minute_click_action">select next minute</string>
  <string name="date_time_picker_accessibility_decrement_minute_button">Decrement minute</string>
  <string name="date_picker_accessibility_previous_minute_click_action">select previous minute</string>
  <string name="date_time_picker_accessibility_period_toggle_button">Toggle AM PM period</string>
  <string name="date_time_picker_accessibility_period_toggle_click_action">toggle AM PM period</string>
  <string name="date_time_picker_accessibility_selected_date">%s selected</string>
  <string name="calendar_adapter_accessibility_item_selected">selected</string>
</resources>