<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">L</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">M</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">M</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">X</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">V</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">S</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">D</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Ir á semana seguinte</string>
    <string name="accessibility_goto_previous_week">Ir á semana anterior</string>
    <string name="accessibility_today">hoxe</string>
    <string name="accessibility_selected">Seleccionado</string>

    <!-- *** Shared *** -->
    <string name="done">Concluído</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Hoxe</string>
    <string name="tomorrow">Mañá</string>
    <string name="yesterday">Onte</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Hora de inicio</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Hora de finalización</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Data de inicio</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Data de finalización</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Escoller hora</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Escoller data</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Selector de hora de data</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Selector de data</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Intervalo de selector de hora de data</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Intervalo de selector de data</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Separador de hora de inicio</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Separador de hora de fin</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Pestana de data de inicio</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Pestana de data de finalización</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Pechar caixa de diálogo</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Incrementar mes</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">seleccionar mes seguinte</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Reducir mes</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">seleccionar mes anterior</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Incrementar día</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">seleccionar día seguinte</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Reducir día</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">seleccionar día anterior</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Incrementar ano</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">seleccionar ano seguinte</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Reducir ano</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">seleccionar ano anterior</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Incrementar data</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">seleccionar data seguinte</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Reducir data</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">seleccionar data anterior</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Incrementar hora</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">seleccionar hora seguinte</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Reducir hora</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">seleccionar a hora anterior</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Incrementar minuto</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">seleccionar o minuto seguinte</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Reducir minuto</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">seleccionar o minuto anterior</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">Alternar entre a.m. e p.m.</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">Alternar entre o período a.m. e p.m.</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s seleccionada</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">seleccionado</string>
</resources>
