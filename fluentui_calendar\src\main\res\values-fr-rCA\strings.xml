<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">L</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">M</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">M</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">J</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">V</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">S</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">D</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Passer à la semaine suivante</string>
    <string name="accessibility_goto_previous_week">Revenir à la semaine précédente</string>
    <string name="accessibility_today">aujourd’hui</string>
    <string name="accessibility_selected">Sélectionné</string>

    <!-- *** Shared *** -->
    <string name="done">Terminé</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Aujourd\'hui</string>
    <string name="tomorrow">Demain</string>
    <string name="yesterday">Hier</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Heure de début</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Heure de fin</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Date de début</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Date de fin</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Choisir une heure</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Choisir une date</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Sélecteur de date/heure</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Sélecteur de dates</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Plage du sélecteur de date/heure</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Plage du sélecteur de date</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Onglet Heure de début</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Onglet Heure de fin</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Onglet Date de début</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Onglet Date de fin</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Fermer la boîte de dialogue</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Incrémenter d’un mois</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">sélectionner le mois suivant</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Décrémenter d’un mois</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">sélectionner le mois précédent</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Incrémenter d’un jour</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">sélectionner le jour suivant</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Décrémenter d’un jour</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">sélectionner le jour précédent</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Incrémenter d’une année</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">sélectionner l’année suivante</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Décrémenter d’une année</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">sélectionner l’année précédente</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Date d’incrémentation</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">sélectionner la date suivante</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Date de décrémentation</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">sélectionner la date précédente</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Heure d’incrémentation</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">sélectionner l’heure suivante</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Heure de décrémentation</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">sélectionner l’heure précédente</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Minute d’incrémentation</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">sélectionner la minute suivante</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Minute de décrémentation</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">sélectionner la minute précédente</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">Activer/désactiver la période AM/PM</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">activer/désactiver la période AM/PM</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s sélectionnée</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">sélectionné</string>
</resources>
