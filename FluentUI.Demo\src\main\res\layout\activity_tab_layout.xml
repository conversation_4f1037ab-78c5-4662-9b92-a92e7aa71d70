<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.microsoft.fluentui.tablayout.TabLayout
        android:id="@+id/demo_tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:fluentui_tabType="Standard"/>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="@dimen/tab_layout_pager_height"/>

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_tab_standard_two_segment"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_marginStart="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tab_standard_two_segment" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_tab_standard_three_segment"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_marginStart="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tab_standard_three_segment" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_tab_standard_four_segment"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_marginStart="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tab_standard_four_segment" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_tab_standard_with_pager"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_marginStart="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tab_standard_with_pager" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_tab_switch"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_marginStart="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tab_switch" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_tab_pills"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_marginStart="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tab_pills" />

</LinearLayout>