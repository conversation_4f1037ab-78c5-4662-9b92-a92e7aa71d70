<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/default_layout_margin"
    android:layout_marginStart="@dimen/default_layout_margin"
    android:layout_marginBottom="@dimen/default_layout_margin"
    android:layout_marginEnd="@dimen/default_layout_margin">

    <!--Single Date-->
    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/date_time_picker_title" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:layout_marginBottom="@dimen/default_layout_margin"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoTitle"
        android:layout_marginBottom="@dimen/demo_headline_padding_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/date_time_picker_single_date_title"/>

    <TextView
        android:id="@+id/date_text_view"
        style="@style/TextAppearance.DemoDescription"
        android:layout_marginBottom="@dimen/default_layout_margin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/date_time_picker_date_picked_default" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/date_picker_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/date_time_picker_date_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/date_time_picker_date_selected_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_view_margin"
        android:text="@string/date_time_picker_calendar_date_time_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/date_time_picker_time_selected_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_view_margin"
        android:text="@string/date_time_picker_date_time_calendar_button" />

    <!--Date Range-->
    <TextView
        style="@style/TextAppearance.DemoTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/date_time_picker_date_range_title"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            style="@style/TextAppearance.DemoDescription.Label"
            android:layout_width="@dimen/date_picker_range_title_width"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/date_picker_range_title_padding_end"
            android:text="@string/date_time_picker_date_range_start_title" />

        <TextView
            android:id="@+id/start_date_text_view"
            style="@style/TextAppearance.DemoDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_start_picked_default" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/default_layout_margin">

        <TextView
            style="@style/TextAppearance.DemoDescription.Label"
            android:layout_marginEnd="@dimen/date_picker_range_title_padding_end"
            android:layout_width="@dimen/date_picker_range_title_width"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_range_end_title" />

        <TextView
            android:id="@+id/end_date_text_view"
            style="@style/TextAppearance.DemoDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_end_picked_default" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/default_layout_margin">

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/date_range_start_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_start_button" />

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/date_range_end_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/default_view_margin"
            android:text="@string/date_time_picker_date_end_button" />

    </LinearLayout>

    <!--Date Time Range-->
    <TextView
        style="@style/TextAppearance.DemoTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Date Time Range"
        android:paddingBottom="@dimen/demo_headline_padding_bottom" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            style="@style/TextAppearance.DemoDescription.Label"
            android:layout_width="@dimen/date_picker_range_title_width"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/date_picker_range_title_padding_end"
            android:text="@string/date_time_picker_date_range_start_title" />

        <TextView
            android:id="@+id/start_date_time_text_view"
            style="@style/TextAppearance.DemoDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_start_picked_default" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/default_layout_margin">

        <TextView
            style="@style/TextAppearance.DemoDescription.Label"
            android:layout_marginEnd="@dimen/date_picker_range_title_padding_end"
            android:layout_width="@dimen/date_picker_range_title_width"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_range_end_title" />

        <TextView
            android:id="@+id/end_date_time_text_view"
            style="@style/TextAppearance.DemoDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_end_picked_default" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/default_layout_margin">

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/date_time_range_start_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/date_time_picker_date_time_range_button" />

    </LinearLayout>

    <!--DateTimePickerDialog-->
    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/date_time_picker_dialog_title" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:layout_marginBottom="@dimen/default_layout_margin"
        android:background="@drawable/ms_row_divider"/>

    <TextView
        android:id="@+id/date_time_picker_dialog_date_text_view"
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/default_layout_margin"
        android:text="@string/date_time_picker_date_picked_default" />

    <Button
        android:id="@+id/date_time_picker_dialog_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/date_time_picker_dialog_button" />

</LinearLayout>