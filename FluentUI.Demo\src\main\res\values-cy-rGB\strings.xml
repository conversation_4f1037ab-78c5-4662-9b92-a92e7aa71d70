<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demo UI Fluent</string>
    <string name="app_title">UI Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Wedi dewis %s</string>
    <string name="app_modifiable_parameters">Paramedrau y Mae Modd eu Haddasu</string>
    <string name="app_right_accessory_view">Gwedd Ategolion ar y Dde</string>

    <string name="app_style">Arddull</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Eicon wedi\'i Bwyso</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Dechrau\'r Demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carwsél</string>
    <string name="actionbar_icon_radio_label">Eicon</string>
    <string name="actionbar_basic_radio_label">Syml</string>
    <string name="actionbar_position_bottom_radio_label">Gwaelod</string>
    <string name="actionbar_position_top_radio_label">Brig</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Math o FarGweithredu</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Safle\'r BarGweithredu</string>

    <!--AppBar-->
    <string name="app_bar_style">Arddull BarApiau</string>
    <string name="app_bar_subtitle">Is-deitl</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Border Isaf</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Wedi clicio ar eicon llywio.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Fflagio</string>
    <string name="app_bar_layout_menu_settings">Gosodiadau</string>
    <string name="app_bar_layout_menu_search">Chwilio</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Ymddygiad sgrolio: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Toglo ymddygiad sgrolio</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Toglo’r cwarel llywio</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Dangos afatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Dangos eicon yn ôl</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Cuddio\'r eicon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Dangos eicon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Toglo arddull cynllun y bar chwilio</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Dangos fel gwedd ategolion</string>
    <string name="app_bar_layout_searchbar_action_view_button">Dangos fel gwedd gweithredoedd</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Toglo rhwng themâu (yn ail-greu gweithgareddau)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Toglo\'r thema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Eitem</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Cynnwys ychwanegol mae modd ei sgrolio</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Arddull cylch</string>
    <string name="avatar_style_square">Arddull sgwâr</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">Mawr Iawn Iawn</string>
    <string name="avatar_size_xlarge">Mawr Iawn</string>
    <string name="avatar_size_large">Mawr</string>
    <string name="avatar_size_medium">Canolig</string>
    <string name="avatar_size_small">Bach</string>
    <string name="avatar_size_xsmall">Bach Iawn</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Mawr Iawn Dwbl</string>
    <string name="avatar_size_xlarge_accessibility">Mawr Iawn</string>
    <string name="avatar_size_xsmall_accessibility">Bach Iawn</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Y Nifer Mwyaf o Afatarau a Ddangosir</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Cyfrif Avatar Gorlif</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Math o Forder</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Fydd y Grŵp Avatar gyda\'r set OverflowAvatarCount ddim yn cydymffurfio â nifer mwyaf o Afatarau a Ddangosir.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Pentwr Wynebau</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Pentwr Wynebau</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Wedi clicio ar orlif</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Wedi clicio ar WeddAfatar yn y mynegai %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Bathodyn Hysbysiadau</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Dot</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Rhestr</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Nod</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Lluniau</string>
    <string name="bottom_navigation_menu_item_news">Newyddion</string>
    <string name="bottom_navigation_menu_item_alerts">Rhybuddion</string>
    <string name="bottom_navigation_menu_item_calendar">Calendr</string>
    <string name="bottom_navigation_menu_item_team">Tîm</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Toglo labeli</string>
    <string name="bottom_navigation_three_menu_items_button">Dangos tair eitem ar y ddewislen</string>
    <string name="bottom_navigation_four_menu_items_button">Dangos pedair eitem ar y ddewislen</string>
    <string name="bottom_navigation_five_menu_items_button">Dangos pum eitem ar y ddewislen</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Mae labeli %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">DalenGwaelod</string>
    <string name="bottom_sheet_dialog">DeialogDalenGwaelod</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Galluogi Symud Bys i Lawr i Ddiystyru</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Dangos gydag eitemau un llinell</string>
    <string name="bottom_sheet_with_double_line_items">Dangos gydag eitemau llinell ddwbl</string>
    <string name="bottom_sheet_with_single_line_header">Dangos gyda phennyn un llinell</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Dangos gyda phennyn llinell ddwbl a gwahanwyr</string>
    <string name="bottom_sheet_dialog_button">Dangos</string>
    <string name="drawer_content_desc_collapse_state">Ehangu</string>
    <string name="drawer_content_desc_expand_state">Lleihau</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Clicio ar %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Clicio\'n Hir Ar %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Clicio ar waredu</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Mewnosod Eitem</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Diweddaru Eitem</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Gwaredu</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Ychwanegu</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Sôn</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Trwm</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Italig</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Tanlinellu</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Llinell Drwodd</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Dad-wneud</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Ail-wneud</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Bwled</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Rhestr</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Dolen</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Wrthi\'n Diweddaru Eitem</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Bylchau</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Gwaredu\'r Safle</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">DECHRAU</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">DIWEDD</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Bwlch grŵp</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Bwlch eitem</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Fflagio</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Wedi clicio ar eitem fflagio</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Ateb</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Wedi clicio ar eitem ateb</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Anfon Ymlaen</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Wedi clicio ar eitem anfon ymlaen</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Dileu</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Wedi clicio ar eitem dileu</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Afatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Camera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Tynnu llun</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Wedi clicio ar eitem camera</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Oriel</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Gweld eich lluniau</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Wedi clicio ar eitem oriel</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Fideos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Chwarae eich fideos</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Wedi clicio ar eitem fideos</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Rheoli</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Rheoli eich llyfrgell cyfryngau</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Wedi clicio ar eitem rheoli</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Gweithredoedd E-bost</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dogfennau</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Diweddarwyd ddiwethaf 2:14PM</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Rhannu</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Wedi clicio ar eitem rhannu</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Symud</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Wedi clicio ar eitem symud</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Dileu</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Wedi clicio ar eitem dileu</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Gwybodaeth</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Wedi clicio ar eitem gwybodaeth</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Cloc</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Wedi clicio ar eitem cloc</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Larwm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Wedi clicio ar eitem larwm</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Cylchfa amser</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Wedi clicio ar eitem cylchfa amser</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Gwahanol golygfeydd o’r botwm</string>
    <string name="button">Botwm</string>
    <string name="buttonbar">BarBotymau</string>
    <string name="button_disabled">Esiampl Botwm wedi\'i Analluogi</string>
    <string name="button_borderless">Esiampl Botwm Heb Ymylon</string>
    <string name="button_borderless_disabled">Esiampl Botwm Heb Ymyl Wedi\'i Analluogi</string>
    <string name="button_large">Enghraifft Botwm Mawr</string>
    <string name="button_large_disabled">Esiampl Botwm Mawr Wedi\'i Analluogi</string>
    <string name="button_outlined">Esiampl Botwm wedi\'i Amlinellu</string>
    <string name="button_outlined_disabled">Esiampl Botwm Wedi\'i Analluogi wedi\'i Amlinellu</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Dewis dyddiad</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DewiswrDyddiadAmser</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Un Dyddiad</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Heb ddewis dyddiad</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Dangos dewiswr dyddiad</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Dangos dewiswr dyddiad amser gyda\'r tab dyddiad wedi\'i ddewis</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Dangos dewiswr dyddiad amser gyda\'r tab amser wedi\'i ddewis</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Dangos dewiswr dyddiad amser</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Ystod Dyddiadau</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Dechrau:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Diwedd:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Heb ddewis dechrau</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Heb ddewis diwedd</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Dewis y dyddiad dechrau</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Dewis y dyddiad gorffen</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Ystod Amser Dyddiad</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Dewis ystod dyddiad amser</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DeialogDewiswrDyddiadAmser</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Dangos Blwch Deialog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Dangos y drôr</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Dangos blwch deialog y drôr</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Deialog gwaelod dim pylu</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Dangos y drôr uchaf</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Blwch deialog uchaf dim pylu</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Dangos blwch deialog uchaf gwedd angor</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Dangos dim deialog teitl uchaf</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Dangos dan blwch deialog y teitl uchaf</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Dangos y drôr dde</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Dangos y drôr chwith</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Teitl, prif destun</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Is-deitl, testun eilaidd</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Testun is-deitl personol</string>
    <!-- Footer -->
    <string name="list_item_footer">Troedyn, testun trydyddol</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Rhestr un llinell gyda thestun is bennyn llwyd</string>
    <string name="list_item_sub_header_two_line">Rhestr dwy linell</string>
    <string name="list_item_sub_header_two_line_dense">Rhestr dwy linell gyda bylchau trwchus</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Rhestr dwy linell gyda gwedd is-deitlau eilaidd personol</string>
    <string name="list_item_sub_header_three_line">Rhestr tair llinell gyda thestun is bennyn du</string>
    <string name="list_item_sub_header_no_custom_views">Eitemau rhestr heb weddau personol</string>
    <string name="list_item_sub_header_large_header">Eitemau rhestr gyda gweddau personol fawr</string>
    <string name="list_item_sub_header_wrapped_text">Eitemau rhestr gyda thestun wedi\'i amlapio</string>
    <string name="list_item_sub_header_truncated_text">Rhestru eitemau gyda thestun wedi\'i docio</string>
    <string name="list_item_sub_header_custom_accessory_text">Gweithred</string>
    <string name="list_item_truncation_middle">Tocio canol.</string>
    <string name="list_item_truncation_end">Gorffen tocio.</string>
    <string name="list_item_truncation_start">Dechrau tocio.</string>
    <string name="list_item_custom_text_view">Gwerth</string>
    <string name="list_item_click">Rydych chi wedi clicio ar yr eitem rhestr.</string>
    <string name="list_item_click_custom_accessory_view">Rydych chi wedi clicio ar wedd ategolion bersonol.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Rydych chi wedi clicio ar wedd ategolion bersonol yr is bennyn.</string>
    <string name="list_item_more_options">Mwy o ddewisiadau</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Dewis</string>
    <string name="people_picker_select_deselect_example">DewisDadddewis</string>
    <string name="people_picker_none_example">Dim</string>
    <string name="people_picker_delete_example">Dileu</string>
    <string name="people_picker_custom_persona_description">Mae\'r enghraifft hon yn dangos sut i greu gwrthrych IPersona personol.</string>
    <string name="people_picker_dialog_title_removed">Rydych chi wedi tynnu persona:</string>
    <string name="people_picker_dialog_title_added">Rydych chi wedi ychwanegu persona:</string>
    <string name="people_picker_drag_started">Wedi dechrau llusgo</string>
    <string name="people_picker_drag_ended">Llusgo wedi dod i ben</string>
    <string name="people_picker_picked_personas_listener">Gwrandawyr Personas</string>
    <string name="people_picker_suggestions_listener">Gwrandawyr Awgrymiadau</string>
    <string name="people_picker_persona_chip_click">Rydych chi wedi clicio ar %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="zero">%1$s derbynyddion</item>
        <item quantity="one">%1$s derbynnydd</item>
        <item quantity="two">%1$s dderbynnydd</item>
        <item quantity="few">%1$s derbynnydd</item>
        <item quantity="many">%1$s derbynnydd</item>
        <item quantity="other">%1$s derbynnydd</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Ehangu DalenGwaelod Parhaus</string>
    <string name="collapse_persistent_sheet_button"> Cuddio DalenGwaelod Parhaus</string>
    <string name="show_persistent_sheet_button"> Dangos DalenGwaelod Parhaus</string>
    <string name="new_view">Dyma Wedd Newydd</string>
    <string name="toggle_sheet_content">Toglo Cynnwys Dalen Gwaelod</string>
    <string name="switch_to_custom_content">Newid i Gynnwys personol</string>
    <string name="one_line_content">Cynnwys DalenGwaelod Un Llinell</string>
    <string name="toggle_disable_all_items">Toglo Analluogi Pob Eitem</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Ychwanegu/Tynnu Gwedd</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Newid Uchder wedi\'i Grebachu</string>
    <string name="persistent_sheet_item_create_new_folder_title">Ffolder Newydd</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Wedi clicio ar eitem Ffolder Newydd</string>
    <string name="persistent_sheet_item_edit_title">Golygu</string>
    <string name="persistent_sheet_item_edit_toast">Wedi clicio ar eitem golygu</string>
    <string name="persistent_sheet_item_save_title">Cadw</string>
    <string name="persistent_sheet_item_save_toast">Wedi clicio ar eitem cadw</string>
    <string name="persistent_sheet_item_zoom_in_title">Nesáu</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Wedi clicio ar eitem Nesáu</string>
    <string name="persistent_sheet_item_zoom_out_title">Pellhau</string>
    <string name="persistent_sheet_item_zoom_out_toast">Wedi clicio ar eitem Pellhau</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Ar gael</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Dylunydd</string>
    <string name="persona_subtitle_engineer">Peiriannwr</string>
    <string name="persona_subtitle_manager">Rheolwr</string>
    <string name="persona_subtitle_researcher">Ymchwilydd</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (enghraifft testun hir i brofi tocio)</string>
    <string name="persona_view_description_xxlarge">Afatar Mawr Iawn Iawn gyda thair llinell o destun</string>
    <string name="persona_view_description_large">Afatar mawr gyda dwy linell o destun</string>
    <string name="persona_view_description_small">Afatar bach gydag un llinell o destun</string>
    <string name="people_picker_hint">Dim gyda chliw wedi\'i ddangos</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Sglodyn Persona wedi\'i Analluogi</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Sglodyn Persona Gwall</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Sglodyn Persona heb eicon cau</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Sglodyn Persona Sylfaenol</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Rydych chi wedi clicio ar Sglodyn Persona a ddewiswyd.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Rhannu</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Dilyn</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Gwahodd pobl</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Adnewyddu\'r dudalen</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Agor mewn porwr</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Dewislen Naid aml linell yw hon. Mae\'r nifer mwyaf o linellau wedi\'i osod i ddwy linell, caiff gweddill y testun ei docio.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Yr holl newyddion</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Newyddion wedi\'u cadw</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Newyddion o safleoedd</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Rhoi gwybod y tu allan i oriau gwaith</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Rhoi gwybod pan fydd yn segur ar y bwrdd gwaith</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Rydych chi wedi clicio ar yr eitem:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Dewislen seml</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Dewislen seml2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Dewislen gydag un eitem mae modd ei dewis a gwahanydd</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Dewislen gyda\'r holl eitemau, eiconau a thestun hir y mae modd eu dewis</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Dangos</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Cynnydd Cylchol</string>
    <string name="circular_progress_xsmall">Bach Iawn</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Bach</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Canolig</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Mawr</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Cynnydd Llinellol</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Amhenderfynadwy</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Penderfynadwy</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">BarChwilio</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Galw\'n ôl Meicroffon</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">AwtoGywiro</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Meicroffon wedi\'i Bwyso</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Gwedd Dde wedi\'i Phwyso</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Chwiliad Bysellfwrdd wedi\'i Bwyso</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Dangos y Bar Hysbysiadau</string>
    <string name="fluentui_dismiss_snackbar">Gwaredu\'r Bar Hysbysiadau</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Gweithred</string>
    <string name="snackbar_action_long">Gweithred Testun Hir</string>
    <string name="snackbar_single_line">Bar hysbysiadau un llinell</string>
    <string name="snackbar_multiline">Bar hysbysiadau aml linell yw hwn. Mae\'r nifer mwyaf o linellau wedi\'i osod i ddwy linell, caiff gweddill y testun ei docio.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Bar hysbysiadau cyhoeddiadau yw hwn. Mae\'n cael ei ddefnyddio i sôn am nodweddion newydd.</string>
    <string name="snackbar_primary">Prif far hysbysiadau yw hwn.</string>
    <string name="snackbar_light">Bar hysbysiadau ysgafn yw hwn.</string>
    <string name="snackbar_warning">Bar hysbysiadau rhybudd yw hwn.</string>
    <string name="snackbar_danger">Mae hwn yn far hysbysiadau perygl.</string>
    <string name="snackbar_description_single_line">Hyd byr</string>
    <string name="snackbar_description_single_line_custom_view">Hyd hir gyda chynnydd cylchol fel gwedd bersonol fach</string>
    <string name="snackbar_description_single_line_action">Hyd byr gyda gweithred</string>
    <string name="snackbar_description_single_line_action_custom_view">Hyd byr gyda gweithred a gwedd bersonol ganolig</string>
    <string name="snackbar_description_single_line_custom_text_color">Hyd byr gyda lliw testun personol</string>
    <string name="snackbar_description_multiline">Hyd hir</string>
    <string name="snackbar_description_multiline_custom_view">Hyd hir gyda gwedd bersonol fach</string>
    <string name="snackbar_description_multiline_action">Hyd amhenodol gyda gweithred a diweddariadau testun</string>
    <string name="snackbar_description_multiline_action_custom_view">Hyd byr gyda gweithred a gwedd bersonol ganolig</string>
    <string name="snackbar_description_multiline_action_long">Hyd byr gyda thestun gweithredu hir</string>
    <string name="snackbar_description_announcement">Hyd byr</string>
    <string name="snackbar_description_updated">Mae\'r testun hwn wedi\'i ddiweddaru.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Dangos y Bar Hysbysiadau</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Un llinell</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Aml-linell</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Arddull cyhoeddiad</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Prif arddull</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Arddull ysgafn</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Arddull rhybudd</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Arddull beryglus</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Hafan</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Post</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Gosodiadau</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Hysbysiad</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mwy</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Alinio Testun</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Ar i fyny</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Ar draws</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Dim Testun</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Eitemau Tab</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Teitl</string>
    <string name="cell_sample_description">Disgrifiad</string>
    <string name="calculate_cells">Llwytho/cyfrifo 100 cell</string>
    <string name="calculate_layouts">Llwytho/cyfrifo 100 cynllun</string>
    <string name="template_list">Rhestr Templedi</string>
    <string name="regular_list">Rhestr Arferol</string>
    <string name="cell_example_title">Teitl: Cell</string>
    <string name="cell_example_description">Disgrifiad: Tarwch i newid cyfeiriad</string>
    <string name="vertical_layout">Cynllun Fertigol</string>
    <string name="horizontal_layout">Cynllun Llorweddol</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Tab Safonol 2-Segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Tab Safonol 3-Segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Tab Safonol 4-Segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Tab Safonol gyda Galwr</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Newid Tab</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Tab Pils </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tarwch ar gyfer Cyngor</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tarwch ar gyfer Cyngor Calendr Personol</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Taro Cyngor Lliw Personol</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tarwch ar gyfer Gwaredu Cyngor Tu Mewn</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tarwch ar gyfer Cyngor Gwedd Bersonol</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Cyngor Lliw Personol Brig</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Cyngor Gorffen Brig gyda chilosodX 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Cyngor Dechrau Gwaelod</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Cyngor Gorffen Gwaelod gyda chilosodY 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Gwaredu Cyngor tu mewn</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Wedi gwaredu\'r cyngor</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Mae\'r Pennyn yn 28sp Ysgafn</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Mae Teitl 1 yn 20sp Canolig</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Mae Teitl 2 yn 20sp Rheolaidd</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Mae\'r Pennawd yn 18sp Rheolaidd</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Mae is-bennawd 1 yn 16sp Rheolaidd</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Mae\'r is-bennawd 2 yn 16sp Canolig</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Mae Corff 1 yn 14sp Rheolaidd</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Mae Corff 2 yn 14sp Canolig</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Mae\'r Capsiwn yn 12sp Rheolaidd</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Fersiwn SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Eitem %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Ffolder</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Wedi\'i Glicio</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Sgaffald</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB wedi\'i Ehangu</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB wedi\'i Grebachu</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Clicio i adnewyddu\'r rhestr</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Agor y Drôr</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Eitem Dewislen</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Atred X (mewn dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Atred Y (mewn dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Testun y Cynnwys</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Ailadrodd Testun y Cynnwys</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Bydd lled y ddewislen yn newid o ran Testun y Cynnwys. Y terfyn uchaf
        mae\'r lled wedi\'i gyfyngu iddo yw 75% maint y sgrin. Mae ymyl y cynnwys o\'r ochr a\'r gwaelod yn cael ei lywodraethu gan docyn. Bydd yr un testun cynnwys yn ailadrodd er mwyn amrywio
        yr uchder.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Agor y Ddewislen</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Cerdyn Sylfaenol</string>
    <!-- UI Label for Card -->
    <string name="file_card">Cerdyn Ffeil</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Cerdyn Cyhoeddiad</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">UI ar Hap</string>
    <!-- UI Label for Options -->
    <string name="card_options">Dewisiadau</string>
    <!-- UI Label for Title -->
    <string name="card_title">Teitl</string>
    <!-- UI Label for text -->
    <string name="card_text">Testun</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Is Destun</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Gall ail gopi ar gyfer y faner hon amlapio i ddwy linell os bydd angen.</string>
    <!-- UI Label Button -->
    <string name="card_button">Botwm</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Dangos Blwch Deialog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Diystyru\'r blwch deialog wrth glicio y tu allan</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Diystyru\'r blwch deialog ar y wasg cefn</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Wedi diystyru\'r blwch deialog</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Canslo</string>
    <!-- UI Label Ok -->
    <string name="ok">Iawn</string>
    <!-- A sample description -->
    <string name="dialog_description">Mae blwch deialog yn ffenestr fach sy\'n annog y defnyddiwr i wneud penderfyniad neu i roi gwybodaeth ychwanegol.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Agor y Drôr</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Ehangu’r Drôr</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Cau\'r Drôr</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Dewis Math o Luniwr</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Brig</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Bydd y drôr cyfan yn dangos yn y rhanbarth gweladwy.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Gwaelod</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Bydd y drôr cyfan yn dangos yn y rhanbarth gweladwy. Symudwch eich cynnwys sgrolio i fyny. Drôr mae modd ei ehangu drwy lusgo dolen.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Sleid Chwith Dros</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Drôr yn llithro drosodd i ranbarth gweladwy o\'r ochr chwith.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Sleid i\'r Dde Dros</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Llithrwch y drôr drosodd i ranbarth gweladwy o\'r ochr dde.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Sleid Isaf Dros</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Drôr yn llithro drosodd i ranbarth gweladwy o waelod y sgrin. Symud bysiwch eich bys ar y drôr mae modd ei ehangu a daw gweddill y rhan i\'r rhanbarth gweladwy ac wedyn sgrolio.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Sgrechan Gweladwy</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Dewis Cynnwys y Drôr</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Cynnwys sgrolio maint sgrin lawn</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mwy na hanner cynnwys y sgrin</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Llai na hanner cynnwys y sgrin</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Cynnwys maint deinamig</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Cynnwys Drôr wedi\'i Nythu</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Mae modd ei ehangu</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Hepgor Cyflwr Agored</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Atal Disodli ar Glic Sgrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Dangos y Ddolen</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Teitl</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Testun Blwch Offer</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tarwch i gael Cyngor Cynnwys Personol</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Dechrau Gorau </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Pen Uchaf</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Dechrau Gwaelod </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Pen Gwaelod</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Canoli </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Canolfan Bersonol</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">I gael diweddariadau ar Nodiadau Rhyddhau, </string>
    <string name="click_here">glicio yma.</string>
    <string name="open_source_cross_platform">Agor y System Dylunio ar draws llwyfannau ffynhonnell.</string>
    <string name="intuitive_and_powerful">Deallus &amp; Pwerus.</string>
    <string name="design_tokens">Tocynnau Dylunio</string>
    <string name="release_notes">Nodiadau Rhyddhau</string>
    <string name="github_repo">Ail-wneud GitHub</string>
    <string name="github_repo_link">Dolen Ail-wneud GitHub</string>
    <string name="report_issue">Riportio Problem</string>
    <string name="v1_components">Cydrannau V1</string>
    <string name="v2_components">Cydrannau V2</string>
    <string name="all_components">Pob</string>
    <string name="fluent_logo">Logo Fluent</string>
    <string name="new_badge">Newydd</string>
    <string name="modified_badge">Addaswyd</string>
    <string name="api_break_badge">Toriad API</string>
    <string name="app_bar_more">Mwy</string>
    <string name="accent">Pwyslais</string>
    <string name="appearance">Ymddangosiad</string>
    <string name="choose_brand_theme">Dewiswch eich thema brand:</string>
    <string name="fluent_brand_theme">Brand Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Dewis Ymddangosiad</string>
    <string name="appearance_system_default">Diofyn y System</string>
    <string name="appearance_light">Golau</string>
    <string name="appearance_dark">Tywyll</string>
    <string name="demo_activity_github_link">Dolen GitHub Gweithgaredd Demo</string>
    <string name="control_tokens_details">Manylion Tocynnau Rheoli</string>
    <string name="parameters">Paramedrau</string>
    <string name="control_tokens">Tocynnau Rheoli</string>
    <string name="global_tokens">Tocynnau Cyffredinol</string>
    <string name="alias_tokens">Tocynnau Enw Arall</string>
    <string name="sample_text">Testun</string>
    <string name="sample_icon">Eicon Sampl</string>
    <string name="color">Lliw</string>
    <string name="neutral_color_tokens">Tocynnau Lliw Niwtral</string>
    <string name="font_size_tokens">Tocynnau Maint Ffont</string>
    <string name="line_height_tokens">Tocynnau Uchder Llinell</string>
    <string name="font_weight_tokens">Tocynnau Pwysau Ffont</string>
    <string name="icon_size_tokens">Tocynnau Maint Eicon</string>
    <string name="size_tokens">Maint Tocynnau</string>
    <string name="shadow_tokens">Tocynnau Cysgod</string>
    <string name="corner_radius_tokens">Tocynnau Radiws Cornel</string>
    <string name="stroke_width_tokens">Tocynnau Lled Strôc</string>
    <string name="brand_color_tokens">Tocynnau Lliw Brand</string>
    <string name="neutral_background_color_tokens">Tocynnau Lliw Cefndir Niwtral</string>
    <string name="neutral_foreground_color_tokens">Tocynnau Lliw Blaendir Niwtral</string>
    <string name="neutral_stroke_color_tokens">Tocynnau Lliw Strôc Niwtral</string>
    <string name="brand_background_color_tokens">Tocynnau Lliw Cefndir Brand</string>
    <string name="brand_foreground_color_tokens">Tocynnau Lliw Blaendir y Brand</string>
    <string name="brand_stroke_color_tokens">Tocynnau Lliw Strôc Brand</string>
    <string name="error_and_status_color_tokens">Tocynnau Lliw Gwall a Statws</string>
    <string name="presence_tokens">Tocynnau Lliw Presenoldeb</string>
    <string name="typography_tokens">Tocynnau Teipograffeg</string>
    <string name="unspecified">Amhenodol</string>

</resources>