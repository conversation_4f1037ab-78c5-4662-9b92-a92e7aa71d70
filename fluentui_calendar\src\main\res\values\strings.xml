<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">M</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">T</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">W</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">T</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">F</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">S</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">S</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Go to next week</string>
    <string name="accessibility_goto_previous_week">Go to previous week</string>
    <string name="accessibility_today">today</string>
    <string name="accessibility_selected">Selected</string>

    <!-- *** Shared *** -->
    <string name="done">Done</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Today</string>
    <string name="tomorrow">Tomorrow</string>
    <string name="yesterday">Yesterday</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Start time</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">End time</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Start date</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">End date</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Choose Time</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Choose Date</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Date Time Picker</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Date Picker</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Date Time Picker Range</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Date Picker Range</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Start time tab</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">End time tab</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Start date tab</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">End date tab</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Close dialog</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Increment month</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">select next month</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Decrement month</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">select previous month</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Increment day</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">select next day</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Decrement day</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">select previous day</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Increment year</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">select next year</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Decrement year</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">select previous year</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Increment date</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">select next date</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Decrement date</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">select previous date</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Increment hour</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">select next hour</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Decrement hour</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">select previous hour</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Increment minute</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">select next minute</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Decrement minute</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">select previous minute</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">Toggle AM PM period</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">toggle AM PM period</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s selected</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">selected</string>
</resources>
