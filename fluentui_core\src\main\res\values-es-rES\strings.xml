<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Principal</string>
    <string name="fluentui_secondary">Secundario</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Descartar</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Se<PERSON>ccionado</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">No seleccionado</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Icono</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Icono</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Deshabilitado</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Botón de acción</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Habilitado</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Cerrar hoja</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Cerrar</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Cancelar</string>
    <!--name of the icon -->
    <string name="fluentui_search">Búsqueda</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Micrófono</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Borrar texto</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Volver</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Activado</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Desactivado</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutral</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marca</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contraste</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Énfasis</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Advertencia</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Peligro</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Se ha producido un error</string>
    <string name="fluentui_error">Error</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Sugerencia</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Cheurón</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Esquema</string>

    <string name="fluentui_action_button_icon">Icono de botón de acción</string>
    <string name="fluentui_center">Centrar texto</string>
    <string name="fluentui_accessory_button">Botones de accesorio</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Botón de opción</string>
    <string name="fluentui_label">Etiqueta</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Expandido</string>
    <string name="fluentui_collapsed">Contraído</string>

    <!--types of control -->
    <string name="fluentui_large">Grande</string>
    <string name="fluentui_medium">Mediana</string>
    <string name="fluentui_small">Pequeño</string>
    <string name="fluentui_password_mode">Modo de contraseña</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Subtítulo</string>
    <string name="fluentui_assistive_text">Texto de asistencia</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Título</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Corto</string>"
    <string name="fluentui_long">Larga</string>"
    <string name="fluentui_indefinite">Indefinido</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Botón presionado</string>
    <string name="fluentui_dismissed">Descartado</string>
    <string name="fluentui_timeout">Tiempo de espera agotado</string>
    <string name="fluentui_left_swiped">Deslizar el dedo hacia la izquierda</string>
    <string name="fluentui_right_swiped">Deslizar el dedo hacia la derecha</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Texto</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Número</string>
    <string name="fluentui_keyboard_phone">Teléfono</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Correo electrónico</string>
    <string name="fluentui_keyboard_password">Contraseña</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimal</string>
</resources>