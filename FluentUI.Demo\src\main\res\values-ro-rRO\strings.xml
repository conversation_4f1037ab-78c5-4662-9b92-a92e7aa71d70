<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demonstrație interfață utilizator Fluent</string>
    <string name="app_title">Interfață utilizator Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s selectate</string>
    <string name="app_modifiable_parameters">Parametri care se pot modifica</string>
    <string name="app_right_accessory_view">Vizualizare de accesoriu de dreapta</string>

    <string name="app_style">Stil</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Pictogramă apăsată</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Începeți demonstrația</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carusel</string>
    <string name="actionbar_icon_radio_label">Pictogramă</string>
    <string name="actionbar_basic_radio_label">Basic</string>
    <string name="actionbar_position_bottom_radio_label">Jos</string>
    <string name="actionbar_position_top_radio_label">Sus</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tip de bară de acțiuni</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Poziție de bară de acțiune</string>

    <!--AppBar-->
    <string name="app_bar_style">Stil de bară de aplicații</string>
    <string name="app_bar_subtitle">Subtitlu</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Bordura de jos</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">S-a făcut clic pe pictograma de navigare.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Fanion</string>
    <string name="app_bar_layout_menu_settings">Setări</string>
    <string name="app_bar_layout_menu_search">Căutare</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Comportament de defilare: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Comutați comportamentul de defilare</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Comutați pictograma de navigare</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Afișați avatarul</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Afișați pictograma Înapoi</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ascundeți pictograma</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Afișați pictograma</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Comutați stilul de aspect al barei de căutare</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Afișați ca vizualizare de accesoriu</string>
    <string name="app_bar_layout_searchbar_action_view_button">Afișați ca vizualizare de acțiune</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Comutați între teme (recreează activitatea)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Comutați tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Conținut suplimentar în care se poate defila</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Stil cerc</string>
    <string name="avatar_style_square">Stil pătrat</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Mare</string>
    <string name="avatar_size_medium">Mediu</string>
    <string name="avatar_size_small">Mic</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dublu foarte mare</string>
    <string name="avatar_size_xlarge_accessibility">Foarte mare</string>
    <string name="avatar_size_xsmall_accessibility">Foarte mic</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Număr maxim de avataruri afișate</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Număr de avataruri de depășire</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tipul bordurii</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Grupul de avataruri cu setul OverflowAvatarCount nu va respecta avatarul afișat maxim.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Stivă de față</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Teanc de fețe</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">S-a făcut clic pe depășire</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">S-a făcut clic pe AvatarView la indexul %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Ecuson de notificare</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punct</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Listă</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Caracter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotografii</string>
    <string name="bottom_navigation_menu_item_news">Știri</string>
    <string name="bottom_navigation_menu_item_alerts">Avertizări</string>
    <string name="bottom_navigation_menu_item_calendar">Calendar</string>
    <string name="bottom_navigation_menu_item_team">Echipă</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Comutați etichetele</string>
    <string name="bottom_navigation_three_menu_items_button">Afișați trei elemente de meniu</string>
    <string name="bottom_navigation_four_menu_items_button">Afișați patru elemente de meniu</string>
    <string name="bottom_navigation_five_menu_items_button">Afișați cinci elemente de meniu</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Etichetele sunt %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Foaie inferioară</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Activați tragerea cu degetul în jos pentru a respinge</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Afișați cu elemente cu o singură linie</string>
    <string name="bottom_sheet_with_double_line_items">Afișați cu elemente cu linie dublă</string>
    <string name="bottom_sheet_with_single_line_header">Afișare cu antet cu o singură linie</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Afișați cu antet cu linie dublă și separatori</string>
    <string name="bottom_sheet_dialog_button">Afișați</string>
    <string name="drawer_content_desc_collapse_state">Extindeți</string>
    <string name="drawer_content_desc_expand_state">Minimizați</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Faceți clic pe %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Faceți clic lung pe %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Faceți clic pe Îndepărtați</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Inserați elementul</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Actualizați elementul</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Dezactivați</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Adăugați</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Menționați</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Aldin</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Cursiv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Subliniați</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Tăiați textul cu o linie</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Anulați</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Refaceți</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Marcator</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Listă</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Link</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Actualizare element</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Spațiere</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Îndepărtați poziția</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">START</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">SFÂRȘIT</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Spațiu dintre grupuri</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Spațiu dintre elemente</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Fanion</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">S-a făcut clic pe Semnalizați elementul</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Răspundeți</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">S-a făcut clic pe elementul de răspuns</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Redirecționați</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">S-a făcut clic pe Redirecționare element</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Ștergeți</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">S-a făcut clic pe Ștergeți elementul</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Cameră</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Faceți o fotografie</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">S-a făcut clic pe elementul camerei</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerie</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Vizualizați fotografiile</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">S-a făcut clic pe elementul din galerie</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videoclipuri</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Redați videoclipurile</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">S-a făcut clic pe elementul video</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Gestionați</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Gestionați biblioteca media</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">S-a făcut clic pe Gestionați elementul</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Acțiuni pentru e-mail</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documente</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Ultima actualizare la 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Distribuiți</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Distribuiți elementul pe care s-a făcut clic</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Deplasați</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Deplasați elementul pe care s-a făcut clic</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Ștergeți</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">S-a făcut clic pe Ștergeți elementul</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informații</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">S-a făcut clic pe elementul informativ</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Ceas</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">S-a făcut clic pe elementul ceas</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarmă</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">S-a făcut clic pe elementul de alarmă</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Fus orar</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">S-a făcut clic pe elementul de fus orar</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Vizualizări diferite ale butonului</string>
    <string name="button">Buton</string>
    <string name="buttonbar">Bară de buton</string>
    <string name="button_disabled">Exemplu de buton dezactivat</string>
    <string name="button_borderless">Exemplu de buton fără bordură</string>
    <string name="button_borderless_disabled">Exemplu de buton fără bordură dezactivat</string>
    <string name="button_large">Exemplu de buton mare</string>
    <string name="button_large_disabled">Exemplu de buton mare dezactivat</string>
    <string name="button_outlined">Exemplu de buton conturat</string>
    <string name="button_outlined_disabled">Exemplu de buton conturat dezactivat</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Alegeți o dată</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DataTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Dată unică</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nicio dată aleasă</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Afișați selectorul de dată</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Afișați selectorul de dată și oră cu fila Dată selectată</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Afișați selectorul de dată și oră cu fila Oră selectată</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Afișați selectorul de dată și oră</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Interval de date</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Start:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Sfârșit:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nu s-a selectat nicio dată de început</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Fără dată de sfârșit selectată</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Selectați data de început</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Selectați data de sfârșit</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Interval de timp pentru dată</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Selectați intervalul de timp al datei</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Afișați caseta de dialog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Afișați sertarul</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Afișați caseta de dialog a sertarului</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Caseta de dialog fără estompare în partea de jos</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Afișați sertarul de sus</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Fără dialog de estompare în partea de sus</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Afișați caseta de dialog de sus a vizualizării ancoră</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Afișați caseta de dialog de sus fără titlu</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Afișați caseta de dialog de sus a titlului de mai jos</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Afișați sertarul din dreapta</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Afișați sertarul din stânga</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Titlu, text principal</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtitlu, text secundar</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Text de subtitlu personalizat</string>
    <!-- Footer -->
    <string name="list_item_footer">Subsol de pagină, text terțiar</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Listă cu o singură linie cu text sub antet gri</string>
    <string name="list_item_sub_header_two_line">Listă cu două linii</string>
    <string name="list_item_sub_header_two_line_dense">Listă cu două linii cu spațiere densă</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Listă cu două linii cu vizualizare subtitlu secundar personalizată</string>
    <string name="list_item_sub_header_three_line">Listă cu trei linii cu text sub antet negru</string>
    <string name="list_item_sub_header_no_custom_views">Listați elemente fără vizualizări personalizate</string>
    <string name="list_item_sub_header_large_header">Listați elemente cu vizualizări personalizate mari</string>
    <string name="list_item_sub_header_wrapped_text">Listați elementele cu text încadrat</string>
    <string name="list_item_sub_header_truncated_text">Listați elementele cu text trunchiat</string>
    <string name="list_item_sub_header_custom_accessory_text">Acțiune</string>
    <string name="list_item_truncation_middle">Trunchiere la mijloc.</string>
    <string name="list_item_truncation_end">Terminați trunchierea.</string>
    <string name="list_item_truncation_start">Începeți trunchierea.</string>
    <string name="list_item_custom_text_view">Valoare</string>
    <string name="list_item_click">Ați făcut clic pe elementul de listă.</string>
    <string name="list_item_click_custom_accessory_view">Ați făcut clic pe vizualizarea accesoriu personalizată.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Ați făcut clic pe vizualizarea accesoriu personalizată a sub antetului.</string>
    <string name="list_item_more_options">Mai multe opțiuni</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Selectați</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Niciuna</string>
    <string name="people_picker_delete_example">Ștergeți</string>
    <string name="people_picker_custom_persona_description">Acest exemplu arată cum se creează un obiect IPersona particularizat.</string>
    <string name="people_picker_dialog_title_removed">Ați eliminat un personaj:</string>
    <string name="people_picker_dialog_title_added">Ați adăugat un personaj:</string>
    <string name="people_picker_drag_started">Glisarea a început</string>
    <string name="people_picker_drag_ended">Glisare încheiată</string>
    <string name="people_picker_picked_personas_listener">Ascultător de personaje</string>
    <string name="people_picker_suggestions_listener">Ascultător de sugestii</string>
    <string name="people_picker_persona_chip_click">Ați făcut clic pe %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s destinatar</item>
        <item quantity="few">%1$s destinatari</item>
        <item quantity="other">%1$s de destinatari</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Extindeți foaia inferioară persistentă</string>
    <string name="collapse_persistent_sheet_button"> Ascundeți foaia inferioară persistentă</string>
    <string name="show_persistent_sheet_button"> Afișați foaia de jos persistentă</string>
    <string name="new_view">Aceasta este o vizualizare nouă</string>
    <string name="toggle_sheet_content">Comutați conținutul foii de jos</string>
    <string name="switch_to_custom_content">Treceți la conținut personalizat</string>
    <string name="one_line_content">Conținut de foaie de jos cu o linie</string>
    <string name="toggle_disable_all_items">Comutați dezactivarea tuturor elementelor</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Adăugați/eliminați vizualizarea</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Modificați înălțimea restrânsă</string>
    <string name="persistent_sheet_item_create_new_folder_title">Folder nou</string>
    <string name="persistent_sheet_item_create_new_folder_toast">S-a făcut clic pe elementul folder nou</string>
    <string name="persistent_sheet_item_edit_title">Editați</string>
    <string name="persistent_sheet_item_edit_toast">S-a făcut clic pe Editați elementul</string>
    <string name="persistent_sheet_item_save_title">Salvați</string>
    <string name="persistent_sheet_item_save_toast">S-a făcut clic pe Salvați elementul</string>
    <string name="persistent_sheet_item_zoom_in_title">Măriți</string>
    <string name="persistent_sheet_item_zoom_in_toast"> S-a făcut clic pe elementul mărire</string>
    <string name="persistent_sheet_item_zoom_out_title">Micșorați</string>
    <string name="persistent_sheet_item_zoom_out_toast">S-a făcut clic pe micșorare element</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Disponibil</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Proiectant</string>
    <string name="persona_subtitle_engineer">Inginer</string>
    <string name="persona_subtitle_manager">Manager</string>
    <string name="persona_subtitle_researcher">Cercetător</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (exemplu de text lung pentru a testa trunchierea)</string>
    <string name="persona_view_description_xxlarge">Avatar XXLarge cu trei linii de text</string>
    <string name="persona_view_description_large">Avatar mare cu două linii de text</string>
    <string name="persona_view_description_small">Avatar mic cu o linie de text</string>
    <string name="people_picker_hint">Nu s-a afișat niciuna cu indiciu</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Jeton de personaj dezactivat</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Eroare de jeton de personaj</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Jeton de personaj fără pictogramă de închidere</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Jeton de personaj de bază</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Ați făcut clic pe un jeton de personaj selectat.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Distribuiți</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Urmăriți</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Invitați persoane</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Reîmprospătați pagina</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Deschideți în browser</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Acesta este un meniu pop-up cu mai multe linii. Numărul maxim de linii este setat la două, restul textului va fi trunchiat.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Toate știrile</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Știri salvate</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Știri de la site-uri</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Notificați în afara orelor de lucru</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notificați atunci când sunteți inactiv pe desktop</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Ați făcut clic pe elementul:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Meniu simplu</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Meniu simplu2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Meniu cu un element selectabil și un separator</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Meniu cu toate elementele, pictogramele și textul lung care se pot selecta</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Afișați</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Progres circular</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Mic</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Mediu</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Mare</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Progres liniar</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Progres nedeterminat</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Progres determinat</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Bara de căutare</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Apelare inversă de microfon</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autocorecție</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Microfonul a fost apăsat</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Vizualizarea de dreapta a fost apăsată</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Căutarea de la tastatură a fost apăsată</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Afișați bara de gustare</string>
    <string name="fluentui_dismiss_snackbar">Respingeți bara de gustare</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Acțiune</string>
    <string name="snackbar_action_long">Acțiune de text lung</string>
    <string name="snackbar_single_line">Element de interfață cu utilizatorul de tip snackbar cu o singură linie</string>
    <string name="snackbar_multiline">Aceasta este o bară de gustare multilinie. Numărul maxim de linii este setat la două, restul textului va fi trunchiat.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Aceasta este o gustare de anunțuri. Se utilizează pentru comunicarea noilor caracteristici.</string>
    <string name="snackbar_primary">Acesta este un element de interfață cu utilizatorul de tip snackbar principal.</string>
    <string name="snackbar_light">Acesta este un element de interfață cu utilizatorul de tip snackbar ușor.</string>
    <string name="snackbar_warning">Acesta este un element de interfață cu utilizatorul de tip snackbar de avertizare.</string>
    <string name="snackbar_danger">Acesta este un element de interfață cu utilizatorul de tip snackbar de pericol.</string>
    <string name="snackbar_description_single_line">Durată scurtă</string>
    <string name="snackbar_description_single_line_custom_view">Durată lungă cu progres circular ca vizualizare personalizată mică</string>
    <string name="snackbar_description_single_line_action">Durată scurtă cu acțiune</string>
    <string name="snackbar_description_single_line_action_custom_view">Durată scurtă cu acțiune și vizualizare personalizată medie</string>
    <string name="snackbar_description_single_line_custom_text_color">Durată scurtă cu culoare de text personalizată</string>
    <string name="snackbar_description_multiline">Durată lungă</string>
    <string name="snackbar_description_multiline_custom_view">Durată lungă cu vizualizare personalizată mică</string>
    <string name="snackbar_description_multiline_action">Durată nedefinită cu actualizări de acțiune și text</string>
    <string name="snackbar_description_multiline_action_custom_view">Durată scurtă cu acțiune și vizualizare personalizată medie</string>
    <string name="snackbar_description_multiline_action_long">Durată scurtă cu text cu acțiune lungă</string>
    <string name="snackbar_description_announcement">Durată scurtă</string>
    <string name="snackbar_description_updated">Acest text a fost actualizat.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Afișați elementul de interfață cu utilizatorul de tip snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Linie unică</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Linie multiplă</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Stil de anunț</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Stil principal</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Stil luminos</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Stil de avertisment</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Stil de pericol</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Pagina de pornire</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Corespondență</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Setări</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notificare</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mai multe</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Aliniere de text</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Verticală</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Orizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Fără text</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Elemente filă</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Titlu</string>
    <string name="cell_sample_description">Descriere</string>
    <string name="calculate_cells">Încărcați/calculați 100 de celule</string>
    <string name="calculate_layouts">Încărcați/calculați 100 de aspecte</string>
    <string name="template_list">Listă de șabloane</string>
    <string name="regular_list">Listă obișnuită</string>
    <string name="cell_example_title">Titlu: Celulă</string>
    <string name="cell_example_description">Descriere: atingeți pentru a modifica orientarea</string>
    <string name="vertical_layout">Aspect vertical</string>
    <string name="horizontal_layout">Aspect orizontal</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Fila Standard cu 2 segmente</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Fila Standard cu 3 segmente</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Fila Standard cu 4 segmente</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Filă standard cu pager</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Comutați fila</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Fila Tablete </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Atingeți pentru sfat ecran</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Atingeți pentru sfatul ecran de calendar personalizat</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Atingeți sfatul ecran de culoare personalizată</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Atingeți pentru a îndepărta sfatul ecran interior</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Atingeți pentru sfatul ecran de vizualizare personalizată</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Sfat ecran de culoare personalizată de sus</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Sfat ecran de sfârșit de sus cu deplasare 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Sfat ecran de Start de jos</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Sfat ecran de sfârșit de jos cu deplasare 10dpY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Îndepărtați sfatul ecran din interior</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Sfat ecran îndepărtat</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Titlul este cu aspect ușor și cu dimensiunea fontului de 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Titlul 1 este cu aspect mediu și cu dimensiunea fontului de 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Titlul 2 este cu aspect obișnuit și cu dimensiunea fontului de 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Titlul este cu aspect obișnuit și cu dimensiunea fontului de 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Subtitlul 1 este cu aspect obișnuit și cu dimensiunea fontului de 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Subtitlul 2 este cu aspect mediu și cu dimensiunea fontului de 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Corpul 1 este cu dimensiunea fontului normală de 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Corpul 2 este de aspect mediu și cu dimensiunea fontului de 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Subtitrarea este cu aspect obișnuit și cu dimensiunea fontului de 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versiune SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Elementul %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Folder</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">S-a făcut clic</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Schele</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB extins</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB restrâns</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Faceți clic pentru a reîmprospăta lista</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Deschideți sertarul</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Element de meniu</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Deplasare X (în dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Deplasare Y (în dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Text conținut</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Repetarea textului de conținut</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Lățimea meniului se va modifica în ceea ce privește textul de conținut. Maximul
        lățimea este restricționată la 75% dimensiunii ecranului. Marginea de conținut din partea laterală și de jos este guvernată de token. Același text de conținut se va repeta pentru a varia
        înălțimea.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Deschideți meniul</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Fișă de bază</string>
    <!-- UI Label for Card -->
    <string name="file_card">Fișă fișier</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Fișă de anunțuri</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Interfață utilizator aleatoare</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opțiuni</string>
    <!-- UI Label for Title -->
    <string name="card_title">Titlu</string>
    <!-- UI Label for text -->
    <string name="card_text">Text</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Sub-text</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Copia secundară pentru acest banner se poate încadra pe două linii, dacă este necesar.</string>
    <!-- UI Label Button -->
    <string name="card_button">Buton</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Afișați caseta de dialog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Îndepărtați caseta de dialog la clicul în exterior</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Îndepărtați caseta de dialog la apăsarea inversă</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Caseta de dialog îndepărtată</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Anulați</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Un dialog este o fereastră mică care îi solicită utilizatorului să ia o decizie sau să introducă informații suplimentare.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Deschideți sertarul</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Extindeți sertarul</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Închideți sertarul</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Selectați tipul sertarului</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Sus</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Întregul sertar se afișează în regiunea vizibilă.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Jos</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Întregul sertar se afișează în regiunea vizibilă. Conținut de defilare în mișcare care se poate trage cu degetul în sus. Sertarul extensibil se extinde prin handle-ul de glisare.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Partea din stânga peste</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Sertarul se glisează spre regiunea vizibilă din partea stângă.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Partea din dreapta peste</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Sertarul se glisează spre regiunea vizibilă din partea dreaptă.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Partea de jos peste</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Sertarul se glisează spre regiunea vizibilă din partea de jos a ecranului. Mișcarea de tragere cu degetul în sus pe sertarul extensibil afișează restul părții în regiunea vizibilă, &amp; apoi defilați.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim vizibil</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Selectați conținutul sertarului</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Conținut care se poate defila de dimensiune de ecran complet</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mai mult de jumătate de conținut de ecran</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mai puțin de jumătate de conținut de ecran</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Conținut cu dimensiune dinamică</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Conținut de sertar imbricat</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Extensibil</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Omiteți starea de deschidere</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Împiedicați eliminarea la clic pe Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Afișați handle-ul</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Titlu</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Text sfat ecran</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Atingeți pentru sfat ecran de conținut particularizat</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Începere de sus </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Capătul de sus </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Început de jos </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Capătul de jos </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centrați </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Personalizați centrul</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Pentru actualizări privind notele de lansare, </string>
    <string name="click_here">faceți clic aici.</string>
    <string name="open_source_cross_platform">Sistemul de proiectare open-source pe mai multe platforme.</string>
    <string name="intuitive_and_powerful">Intuitiv &amp; puternic.</string>
    <string name="design_tokens">Proiectați tokenuri</string>
    <string name="release_notes">Note de lansare</string>
    <string name="github_repo">Depozitul GitHub</string>
    <string name="github_repo_link">Link la depozitul GitHub</string>
    <string name="report_issue">Raportați problema</string>
    <string name="v1_components">Componente V1</string>
    <string name="v2_components">Componente V2</string>
    <string name="all_components">Toate</string>
    <string name="fluent_logo">Sigla Fluent</string>
    <string name="new_badge">Nou</string>
    <string name="modified_badge">Modificat</string>
    <string name="api_break_badge">Întrerupere API</string>
    <string name="app_bar_more">Mai multe</string>
    <string name="accent">Accent</string>
    <string name="appearance">Aspect</string>
    <string name="choose_brand_theme">Alegeți tema brandului:</string>
    <string name="fluent_brand_theme">Marca Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Alegeți aspectul</string>
    <string name="appearance_system_default">Valoare implicită a sistemului</string>
    <string name="appearance_light">Luminos</string>
    <string name="appearance_dark">Întunecat</string>
    <string name="demo_activity_github_link">Link GitHub pentru activitate demonstrativă</string>
    <string name="control_tokens_details">Detalii tokenuri de control</string>
    <string name="parameters">Parametri</string>
    <string name="control_tokens">Tokenuri de control</string>
    <string name="global_tokens">Tokenuri globale</string>
    <string name="alias_tokens">Tokenuri alias</string>
    <string name="sample_text">Text</string>
    <string name="sample_icon">Pictogramă eșantion</string>
    <string name="color">Culoare</string>
    <string name="neutral_color_tokens">Tokenuri de culori neutre</string>
    <string name="font_size_tokens">Tokenuri dimensiune font</string>
    <string name="line_height_tokens">Tokenuri de înălțime a liniei</string>
    <string name="font_weight_tokens">Tokenuri de grosime font</string>
    <string name="icon_size_tokens">Tokenuri dimensiune pictogramă</string>
    <string name="size_tokens">Tokenuri de dimensiune</string>
    <string name="shadow_tokens">Tokenuri de umbră</string>
    <string name="corner_radius_tokens">Tonkenuri Rază de colț</string>
    <string name="stroke_width_tokens">Tokenuri lățime urmă de cerneală</string>
    <string name="brand_color_tokens">Tokenuri de culoare pentru marcă</string>
    <string name="neutral_background_color_tokens">Tokenuri de culoare neutră de fundal</string>
    <string name="neutral_foreground_color_tokens">Tokenuri de culoare neutre de prim-plan</string>
    <string name="neutral_stroke_color_tokens">Tokenuri de culoare neutră pentru urma de cerneală</string>
    <string name="brand_background_color_tokens">Tokenuri de culoare de fundal pentru marcă</string>
    <string name="brand_foreground_color_tokens">Tokenuri de culoare de prim-plan al mărcii</string>
    <string name="brand_stroke_color_tokens">Tokenuri de culoare pentru urma de cerneală a mărcii</string>
    <string name="error_and_status_color_tokens">Tokenuri de culoare de stare și eroare</string>
    <string name="presence_tokens">Tokenuri de culoare de prezență</string>
    <string name="typography_tokens">Tokenuri tipografice</string>
    <string name="unspecified">Nespecificat</string>

</resources>