/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'

apply from: '../config.gradle'
apply from: '../publish.gradle'

android {
    compileSdkVersion constants.compileSdkVersion
    defaultConfig {
        minSdkVersion constants.minSdkVersion
        targetSdkVersion constants.targetSdkVersion
        versionCode project.ext.fluentui_core_version_code
        versionName project.ext.fluentui_core_versionid
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        vectorDrawables.useSupportLibrary = true
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion composeCompilerVersion
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    productFlavors {
    }
    lint {
        baseline = file("lint-baseline.xml")
    }
    lintOptions {
        abortOnError false
    }
}

gradle.taskGraph.whenReady { taskGraph ->
    taskGraph.allTasks.forEach {
        if (it.name.contains("ReleaseUnitTest")) {
            it.enabled = false
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':fluentui_icons')

    implementation "androidx.appcompat:appcompat:$appCompatVersion"
    implementation "androidx.exifinterface:exifinterface:$exifInterfaceVersion"
    implementation "androidx.recyclerview:recyclerview:$recyclerViewVersion"
    implementation "androidx.cardview:cardview:1.0.0"
    implementation "com.google.android.material:material:$materialVersion"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.constraintlayout:constraintlayout:$constraintLayoutVersion"
    implementation "androidx.compose.foundation:foundation:$composeFoundationVersion"
    implementation "androidx.compose.material:material"

    testImplementation "junit:junit:$junitVersion"
    testImplementation "org.robolectric:robolectric:$robolectric"

    androidTestImplementation "androidx.test.ext:junit:$extJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$espressoVersion"

    //Compose BOM
    implementation platform("androidx.compose:compose-bom:$composeBomVersion")
    implementation "androidx.compose.ui:ui-text"
    implementation "androidx.compose.ui:ui-graphics"
    implementation "androidx.compose.ui:ui-unit"
    implementation "androidx.compose.ui:ui-util"
    implementation "androidx.compose.runtime:runtime"
    implementation "androidx.compose.foundation:foundation"
    implementation "androidx.compose.material:material-icons-core"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-livedata-core-ktx:$lifecycleVersion"
    implementation "androidx.compose.runtime:runtime-livedata"
}

task sourceJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier "sources"
}

project.afterEvaluate {
    project.ext.publishingFunc('fluentui_core')
}



