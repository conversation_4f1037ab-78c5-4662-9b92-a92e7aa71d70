<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Демонстраційна версія інтерфейсу користувача Fluent</string>
    <string name="app_title">Інтерфейс користувача Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Вибрано %s</string>
    <string name="app_modifiable_parameters">Доступні для змінення параметри</string>
    <string name="app_right_accessory_view">Подання правого периферійного пристрою</string>

    <string name="app_style">Стиль</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Піктограму натиснуто</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Почати демонстрацію</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Карусель</string>
    <string name="actionbar_icon_radio_label">Піктограма</string>
    <string name="actionbar_basic_radio_label">Базовий</string>
    <string name="actionbar_position_bottom_radio_label">За нижнім краєм</string>
    <string name="actionbar_position_top_radio_label">За верхнім краєм</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Тип панелі дій</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Розташування панелі дій</string>

    <!--AppBar-->
    <string name="app_bar_style">Стиль панелі програм</string>
    <string name="app_bar_subtitle">Субтитр</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Нижня межа</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Піктограму навігації натиснуто.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Позначка</string>
    <string name="app_bar_layout_menu_settings">Параметри</string>
    <string name="app_bar_layout_menu_search">Пошук</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Поведінка прокручування: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Увімкнути або вимкнути поведінку прокручування</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Піктограма переключення навігації</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Показати аватар</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Показати піктограму "Назад"</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Приховати піктограму</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Показати піктограму</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Переключити стиль макета панелі пошуку</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Показати як подання периферійного пристрою</string>
    <string name="app_bar_layout_searchbar_action_view_button">Показати як подання дії</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Переключення між темами (повторне створення дій)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Перемикання теми</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Елемент</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Додатковий прокручуваний вміст</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Стиль кола</string>
    <string name="avatar_style_square">Квадратний стиль</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Великий</string>
    <string name="avatar_size_medium">Середній</string>
    <string name="avatar_size_small">Малий</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Подвійний дуже великий</string>
    <string name="avatar_size_xlarge_accessibility">Дуже великий</string>
    <string name="avatar_size_xsmall_accessibility">Дуже малий</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Максимальна кількість відображених аватарів</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Кількість аватарів переповнення</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Тип межі</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Група аватара з набором аватарів не відповідатиме максимальному аватару, що відображається.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Стіс обличчя</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Купа облич</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Переповнення натиснуто</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView з індексом клацнуто користувачем %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Значок сповіщення</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Крапка</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Список</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Символ</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Фотографії</string>
    <string name="bottom_navigation_menu_item_news">Новини</string>
    <string name="bottom_navigation_menu_item_alerts">Оповіщення</string>
    <string name="bottom_navigation_menu_item_calendar">Календар</string>
    <string name="bottom_navigation_menu_item_team">Команда</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Переключити мітки</string>
    <string name="bottom_navigation_three_menu_items_button">Показати три пункти меню</string>
    <string name="bottom_navigation_four_menu_items_button">Показати чотири пункти меню</string>
    <string name="bottom_navigation_five_menu_items_button">Показати п’ять пунктів меню</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Мітки: %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Нижній аркуш</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Увімкнути функцію проведення вниз, щоб закрити</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Відображати з елементами одного рядка</string>
    <string name="bottom_sheet_with_double_line_items">Відображати з елементами в два рядки</string>
    <string name="bottom_sheet_with_single_line_header">Відображати з одним рядком заголовка</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Відображати із заголовком у два рядки і роздільниками</string>
    <string name="bottom_sheet_dialog_button">Показати</string>
    <string name="drawer_content_desc_collapse_state">Розгорнути</string>
    <string name="drawer_content_desc_expand_state">Згорнути</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Клацніть %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Довге клацання %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Клацніть "Закрити"</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Вставлення елемента</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Оновити елемент</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Відхилити</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Додати</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Згадка</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Жирний</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Курсив</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Підкреслити</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Закреслення</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Скасувати дію</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Повторити дію</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Маркер</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Список</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Посилання</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Оновлення елемента</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Інтервал</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Закрити позицію</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">ПОЧАТОК</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">КІНЕЦЬ</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Груповий простір</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Простір елемента</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Позначка</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Елемент позначки клацнуто</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Відповідь</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Елемент відповіді натиснуто</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Уперед</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Пересилання елемента натиснуто</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Видалити</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Видалений елемент клацнуто</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Аватар</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Камера</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Зробити фото</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Елемент камери натиснуто</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Галерея</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Переглянути ваші фотографії</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Елемент колекції клацнуто</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Відео</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Відтворення відео</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Елемент відео клацнуто</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Керувати</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Керування медіатекою</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Елемент керування клацнуто</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Дії з електронною поштою</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Документи</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Останнє оновлення: 14:00</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Спільний доступ</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Надати спільний доступ до елемента, на якому натиснуто</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Перемістити</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Переміщення елемента натиснуто</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Видалити</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Видалений елемент клацнуто</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Відомості</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Елемент відомостей клацнуто</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Годинник</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Елемент годинника клацнуто</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Будильник</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Елемент будильника натиснуто</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Часовий пояс</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Елемент часового поясу натиснуто</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Різні подання кнопки</string>
    <string name="button">Кнопка</string>
    <string name="buttonbar">Панель кнопок</string>
    <string name="button_disabled">Приклад кнопки "Вимкнуто"</string>
    <string name="button_borderless">Приклад кнопки без рамок</string>
    <string name="button_borderless_disabled">Приклад кнопки "Вимкнуто" без рамок</string>
    <string name="button_large">Приклад великої кнопки</string>
    <string name="button_large_disabled">Приклад великої кнопки "Вимкнуто"</string>
    <string name="button_outlined">Приклад кнопки з контуром</string>
    <string name="button_outlined_disabled">Приклад кнопки "Вимкнуто" з контуром</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Вибрати дату</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Одна дата</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Дату не вибрано</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Відображати засіб вибору дати</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Показати засіб вибору дати й часу з вибраною вкладкою дати</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Показати засіб вибору дати й часу з вибраною вкладкою часу</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Показати засіб вибору дати й часу</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Проміжок часу</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Початок:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Кінець:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Початок не вибрано</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Кінець не вибрано</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Виберіть дату початку</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Виберіть дату завершення</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Діапазон дати й часу</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Виберіть діапазон дат і часу</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Показати діалогове вікно</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Показати бічну панель</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Показати діалогове вікно бічної панелі</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Діалогове вікно "Без затухання внизу"</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Показати верхню бічну панель</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Діалогове вікно "Без вицвітання вгорі"</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Відображення верхнього діалогового вікна подання прив’язки</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Діалогове вікно "Не відображати заголовок зверху"</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Відображати під верхнім діалоговим вікном заголовка</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Показати праву бічну панель</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Показати ліву бічну панель</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Заголовок, основний текст</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Субтитр, додатковий текст</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Спеціальний текст субтитрів</string>
    <!-- Footer -->
    <string name="list_item_footer">Нижній колонтитул, третинний текст</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Однорядковий список із сірим текстом підзаголовка</string>
    <string name="list_item_sub_header_two_line">Дворядковий список</string>
    <string name="list_item_sub_header_two_line_dense">Дворядковий список зі щільним інтервалом</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Список у два рядки з настроюваним додатковим поданням субтитрів</string>
    <string name="list_item_sub_header_three_line">Трирядковий список із чорним текстом підзаголовка</string>
    <string name="list_item_sub_header_no_custom_views">Елементи списку без настроюваних подань</string>
    <string name="list_item_sub_header_large_header">Елементи списку з великими настроюваними поданнями</string>
    <string name="list_item_sub_header_wrapped_text">Елементи списку із перенесенням тексту</string>
    <string name="list_item_sub_header_truncated_text">Елементи списку із перерваним текстом</string>
    <string name="list_item_sub_header_custom_accessory_text">Дія</string>
    <string name="list_item_truncation_middle">Середнє переривання.</string>
    <string name="list_item_truncation_end">Завершити урізання.</string>
    <string name="list_item_truncation_start">Почати урізання.</string>
    <string name="list_item_custom_text_view">Значення</string>
    <string name="list_item_click">Ви клацнули елемент списку.</string>
    <string name="list_item_click_custom_accessory_view">Ви клацнули спеціальне подання периферійного пристрою.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Ви клацнули спеціальне подання периферійного пристрою для підзаголовка.</string>
    <string name="list_item_more_options">Більше параметрів</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Вибрати</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Немає</string>
    <string name="people_picker_delete_example">Видалити</string>
    <string name="people_picker_custom_persona_description">У цьому прикладі показано, як створити настроюваний об\'єкт IPersona.</string>
    <string name="people_picker_dialog_title_removed">Ви видалили користувача:</string>
    <string name="people_picker_dialog_title_added">Ви додали користувача:</string>
    <string name="people_picker_drag_started">Перетягування розпочато</string>
    <string name="people_picker_drag_ended">Перетягування завершено</string>
    <string name="people_picker_picked_personas_listener">Прослуховувач користувачів</string>
    <string name="people_picker_suggestions_listener">Прослуховувач пропозицій</string>
    <string name="people_picker_persona_chip_click">Ви клацнули %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s одержувач</item>
        <item quantity="few">%1$s одержувачі</item>
        <item quantity="many">%1$s одержувачів</item>
        <item quantity="other">%1$s одержувачі</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Розгорнути постійний нижній аркуш</string>
    <string name="collapse_persistent_sheet_button"> Приховати постійний нижній аркуш</string>
    <string name="show_persistent_sheet_button"> Показати постійний нижній аркуш</string>
    <string name="new_view">Це нове подання</string>
    <string name="toggle_sheet_content">Переключити вміст нижнього аркуша</string>
    <string name="switch_to_custom_content">Переключитися на спеціальний вміст</string>
    <string name="one_line_content">Вміст нижнього аркуша в одному рядку</string>
    <string name="toggle_disable_all_items">Переключити параметр "Вимкнути всі елементи"</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Додати або видалити подання</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Змінити згорнуту висоту</string>
    <string name="persistent_sheet_item_create_new_folder_title">Нова папка</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Елемент "Створити папку" натиснуто</string>
    <string name="persistent_sheet_item_edit_title">Редагувати</string>
    <string name="persistent_sheet_item_edit_toast">Елемент редагування клацнуто</string>
    <string name="persistent_sheet_item_save_title">Зберегти</string>
    <string name="persistent_sheet_item_save_toast">Елемент збереження натиснуто</string>
    <string name="persistent_sheet_item_zoom_in_title">Збільшити</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Елемент збільшення масштабу клацнуто</string>
    <string name="persistent_sheet_item_zoom_out_title">Зменшити</string>
    <string name="persistent_sheet_item_zoom_out_toast">Елемент для зменшення масштабу клацнуто</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Доступно</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Алан Мангер</string>
    <string name="persona_name_amanda_brady">Аманда Брейді</string>
    <string name="persona_name_ashley_mccarthy">Ешлі МакКарті</string>
    <string name="persona_name_carlos_slattery">Карлос Слеттері</string>
    <string name="persona_name_carole_poland">Керол Поланд</string>
    <string name="persona_name_cecil_folk">Сесіль Фолк</string>
    <string name="persona_name_celeste_burton">Селест Бертон</string>
    <string name="persona_name_charlotte_waltson">Шарлотта Уолтсон</string>
    <string name="persona_name_colin_ballinger">Колін Болінгер</string>
    <string name="persona_name_daisy_phillips">Дейзі Філіпс</string>
    <string name="persona_name_elliot_woodward">Еліот Вудвард</string>
    <string name="persona_name_elvia_atkins">Ельвія Аткінс</string>
    <string name="persona_name_erik_nason">Ерік Нейсон</string>
    <string name="persona_name_henry_brill">Генрі Брілл</string>
    <string name="persona_name_isaac_fielder">Айзек Філдер</string>
    <string name="persona_name_johnie_mcconnell">Павло Чумак</string>
    <string name="persona_name_kat_larsson">Олена Прутко</string>
    <string name="persona_name_katri_ahokas">Кетрі Ахокас</string>
    <string name="persona_name_kevin_sturgis">Олексій Шапко</string>
    <string name="persona_name_kristen_patterson">Ольга Тарасюк</string>
    <string name="persona_name_lydia_bauer">Тамара Бабич</string>
    <string name="persona_name_mauricio_august">Маурісіо Аугуст</string>
    <string name="persona_name_miguel_garcia">Іван Колесник</string>
    <string name="persona_name_mona_kane">Вікторія Шуменко</string>
    <string name="persona_name_robin_counts">Робін Каунтс</string>
    <string name="persona_name_robert_tolbert">Роберт Толберт</string>
    <string name="persona_name_tim_deboer">Тім Дебур</string>
    <string name="persona_name_wanda_howard">Ванда Говард</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Дизайнер</string>
    <string name="persona_subtitle_engineer">Інженер</string>
    <string name="persona_subtitle_manager">Керівник</string>
    <string name="persona_subtitle_researcher">Дослідник</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (приклад довгого тексту для тестування скорочення)</string>
    <string name="persona_view_description_xxlarge">Аватар XXLarge із трьома рядками тексту</string>
    <string name="persona_view_description_large">Великий аватар із двома рядками тексту</string>
    <string name="persona_view_description_small">Маленький аватар з одним рядком тексту</string>
    <string name="people_picker_hint">Жодного з відображеною підказкою</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Мікросхема вимкнутого користувача</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Мікросхема користувача з помилкою</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Мікросхема користувача без піктограми "Закрити"</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Проста мікросхема користувача</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Ви клацнули вибрану мікросхему користувача.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Спільний доступ</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Підписатися</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Запросити користувачів</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Оновіть сторінку</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Відкрити в браузері</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Це багаторядкове спливаюче меню. Максимальна кількість рядків: дві. Решту тексту буде скорочено.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Усі новини</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Збережені новини</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Новини із сайтів</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Сповіщати в неробочі години</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Сповіщати про неактивність на робочому столі</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Ви клацнули елемент:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Просте меню</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Просте меню2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Меню з одним елементом, який можна вибрати, і роздільником</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Меню з усіма вибраними елементами, піктограмами та довгим текстом</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Показати</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Циклічний перебіг виконання</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Малий</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Середній</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Великий</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Лінійний перебіг виконання</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Не визначено</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Визначення</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Панельпошуку</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Зворотний виклик із мікрофона</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Автовиправлення</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Мікрофон натиснуто</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Праве подання натиснуте</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Пошук за допомогою клавіатури натиснуто</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Показати Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Закрити Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Дія</string>
    <string name="snackbar_action_long">Дія з довгим текстом</string>
    <string name="snackbar_single_line">Однорядковий перекус</string>
    <string name="snackbar_multiline">Це багаторядковий перекус. Максимальна кількість рядків: дві. Решту тексту буде скорочено.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Це перекус оголошення. Він використовується для обміну новими функціями.</string>
    <string name="snackbar_primary">Це основний перекус.</string>
    <string name="snackbar_light">Це легкий перекус.</string>
    <string name="snackbar_warning">Це попереджувальна панель.</string>
    <string name="snackbar_danger">Це панель небезпеки.</string>
    <string name="snackbar_description_single_line">Коротка тривалість</string>
    <string name="snackbar_description_single_line_custom_view">Довга тривалість із циклічного перебігу виконання як мале настроюване подання</string>
    <string name="snackbar_description_single_line_action">Коротка тривалість дії</string>
    <string name="snackbar_description_single_line_action_custom_view">Коротка тривалість із діями та середнім настроюваним поданням</string>
    <string name="snackbar_description_single_line_custom_text_color">Коротка тривалість із настроюваним кольором тексту</string>
    <string name="snackbar_description_multiline">Довга тривалість</string>
    <string name="snackbar_description_multiline_custom_view">Довга тривалість із невеликим настроюваним поданням</string>
    <string name="snackbar_description_multiline_action">Невизначена тривалість з оновленнями дії та тексту</string>
    <string name="snackbar_description_multiline_action_custom_view">Коротка тривалість із діями та середнім настроюваним поданням</string>
    <string name="snackbar_description_multiline_action_long">Коротка тривалість із текстом із довгою дією</string>
    <string name="snackbar_description_announcement">Коротка тривалість</string>
    <string name="snackbar_description_updated">Цей текст оновлено.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Показати перекус</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Один рядок</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Багаторядкова</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Стиль оголошення</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Основний стиль</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Світлий стиль</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Стиль попередження</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Стиль небезпеки</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Основне</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Пошта</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Параметри</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Сповіщення</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Додатково</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Вирівнювання тексту</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Вертикально</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Горизонтальний</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Немає тексту</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Елементи вкладки</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Назва</string>
    <string name="cell_sample_description">Опис</string>
    <string name="calculate_cells">Завантажити або обчислити 100 клітинок</string>
    <string name="calculate_layouts">Завантажити або обчислити 100 макетів</string>
    <string name="template_list">Список шаблонів</string>
    <string name="regular_list">Звичайний список</string>
    <string name="cell_example_title">Назва: клітинка</string>
    <string name="cell_example_description">Опис: торкніться, щоб змінити орієнтацію</string>
    <string name="vertical_layout">Вертикальна структура</string>
    <string name="horizontal_layout">Горизонтальна структура</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Стандартна вкладка 2-сегмент</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Стандартна вкладка 3-сегмент</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Стандартна вкладка 4-сегмент</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Стандартна вкладка з сторінкою</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Переключити вкладку</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Вкладка "Пігулки" </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Підказка Торкніться, щоб</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Підказка Торкніться, щоб переглянути настроюваний календар</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Підказка Торкніться щоб настроїти колір</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Торкніться, щоб закрити внутрішню підказку</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Підказка Торкніться, щоб переглянути спеціальне подання</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Підказка верхнього спеціального кольору</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Підказка верхнього кінця зі зсувом 10dpX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Підказка про початок знизу</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Підказка нижнього кінця зі зсувом 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Підказка Відхилити внутрішню</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Підказку закрито</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Заголовок світлий 28 п</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Заголовок1 середній 20 п</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Назва 2 – звичайний 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Заголовок – звичайний 18 п</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Підзаголовок 1 – звичайний 16 п</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Підзаголовок 2 – середній 16 п</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Основний текст 1 – Звичайний 14 п</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Основний текст 2 – Середній 14 п</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Підпис звичайний 12 п</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Версія SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Елемент %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Папка</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Натиснуто</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Шаблон</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">Рухома кнопка дії (FAB)</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB розгорнуто</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB згорнуто</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Клацніть, щоб оновити список</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Відкрити бічну панель</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Елемент меню</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Зсув X (дп)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Зсув Y (дп)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Текст вмісту</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Повторити текст вмісту</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Ширина меню змінюватиметься відповідно до тексту вмісту. Максимальна
        ширина обмежена 75 % розміру екрана. Поле вмісту збоку та знизу регулюється маркером. Той самий текст вмісту повторюватиметься, щоб змінювати
        висоту.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Відкрити меню</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Проста картка</string>
    <!-- UI Label for Card -->
    <string name="file_card">Картка файлу</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Картка оголошення</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Випадковий інтерфейс користувача</string>
    <!-- UI Label for Options -->
    <string name="card_options">Параметри</string>
    <!-- UI Label for Title -->
    <string name="card_title">Назва</string>
    <!-- UI Label for text -->
    <string name="card_text">Текст</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Допоміжний текст</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">За потреби додаткову копію цього банера можна перенести на два рядки.</string>
    <!-- UI Label Button -->
    <string name="card_button">Кнопка</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Показати діалогове вікно</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Закрити діалогове вікно після клацання ззовні</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Закрити діалогове вікно після натискання кнопки "Назад"</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Діалогове вікно закрито</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Скасувати</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Діалогове вікно – це невелике вікно, яке пропонує користувачу прийняти рішення або ввести додаткові відомості.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Відкрити бічну панель</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Розгорнути бічну панель</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Закрити бічну панель</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Виберіть тип бічної панелі</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Угорі</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Уся бічна панель відображається у видимій області.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Знизу</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Уся бічна панель відображається у видимій області. Проведіть угору, щоб прокрутити вміст. Бічна панель розгортається за допомогою перетягування маркера.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Розгорнути вправо</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Розгортання бічної панелі до видимої області з лівого боку.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Розгорнути вліво</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Розгортання бічної панелі до видимої області з правого боку.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Розгорнути вгору</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Розгортання бічної панелі до видимої області з нижньої частини екрана. Проведіть угору на бічній панелі, яку можна розгорнути, щоб вона повністю відобразилась у видимій області, і прокрутіть вміст.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Полотно відображається</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Виберіть вміст бічної панелі</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Вміст, доступний для прокручування, у повноекранному режимі</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Вміст більше ніж на половину екрана</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Вміст менше ніж на половину екрана</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Вміст із динамічним розміром</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Вкладений вміст бічної панелі</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Можна розгорнути</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Пропустити відкритий стан</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Заборонити функцію відхилення за допомогою клацання на полотні</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Показати маркер</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Назва</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Текст підказки</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Торкніться, щоб переглянути підказку щодо користувацького вмісту</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Верхній початок</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Верхній кінець </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Нижній початок </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Нижній кінець </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">По центру </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Користувацький центр</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Щоб отримувати оновлення нотаток про випуск,</string>
    <string name="click_here">клацніть тут.</string>
    <string name="open_source_cross_platform">Міжплатформна система розробки з відкритим кодом.</string>
    <string name="intuitive_and_powerful">Інтуїтивна та потужна.</string>
    <string name="design_tokens">Маркери оформлення</string>
    <string name="release_notes">Нотатки про випуск</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">Посилання GitHub Repo</string>
    <string name="report_issue">Повідомити про проблему</string>
    <string name="v1_components">Компоненти V1</string>
    <string name="v2_components">Компоненти V2</string>
    <string name="all_components">Усе</string>
    <string name="fluent_logo">Емблема Fluent</string>
    <string name="new_badge">Створити</string>
    <string name="modified_badge">Змінено</string>
    <string name="api_break_badge">Розрив API</string>
    <string name="app_bar_more">Додатково</string>
    <string name="accent">Акцент</string>
    <string name="appearance">Вигляд</string>
    <string name="choose_brand_theme">Виберіть тему свого бренда:</string>
    <string name="fluent_brand_theme">Бренд Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Вибір вигляду</string>
    <string name="appearance_system_default">Стандартні параметри системи</string>
    <string name="appearance_light">Світле</string>
    <string name="appearance_dark">Темне</string>
    <string name="demo_activity_github_link">Посилання GitHub для демонстраційної діяльності</string>
    <string name="control_tokens_details">Відомості про маркери керування</string>
    <string name="parameters">Параметри</string>
    <string name="control_tokens">Маркери керування</string>
    <string name="global_tokens">Глобальні маркери</string>
    <string name="alias_tokens">Маркери псевдонімів</string>
    <string name="sample_text">Текст</string>
    <string name="sample_icon">Зразок піктограми</string>
    <string name="color">Колір</string>
    <string name="neutral_color_tokens">Маркери нейтрального кольору</string>
    <string name="font_size_tokens">Маркери розміру шрифту</string>
    <string name="line_height_tokens">Маркери висоти рядка</string>
    <string name="font_weight_tokens">Маркери товщини шрифту</string>
    <string name="icon_size_tokens">Маркери розміру піктограми</string>
    <string name="size_tokens">Маркери розміру</string>
    <string name="shadow_tokens">Маркери тіні</string>
    <string name="corner_radius_tokens">Маркери радіусу заокруглення кута</string>
    <string name="stroke_width_tokens">Маркери ширини розчерку</string>
    <string name="brand_color_tokens">Маркери кольору бренда</string>
    <string name="neutral_background_color_tokens">Маркери нейтрального кольору тла</string>
    <string name="neutral_foreground_color_tokens">Маркери нейтрального кольору переднього плану</string>
    <string name="neutral_stroke_color_tokens">Маркери нейтрального кольору штриха</string>
    <string name="brand_background_color_tokens">Маркери кольору тла бренда</string>
    <string name="brand_foreground_color_tokens">Маркери кольору переднього плану бренда</string>
    <string name="brand_stroke_color_tokens">Маркери кольору штриха бренда</string>
    <string name="error_and_status_color_tokens">Маркери кольорів помилок і стану</string>
    <string name="presence_tokens">Маркери кольору присутності</string>
    <string name="typography_tokens">Маркери типографіки</string>
    <string name="unspecified">Не вказано</string>

</resources>