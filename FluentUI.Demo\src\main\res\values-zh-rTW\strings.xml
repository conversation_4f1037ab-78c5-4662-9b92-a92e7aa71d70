<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI 示範</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">已選取「%s」</string>
    <string name="app_modifiable_parameters">可修改的參數</string>
    <string name="app_right_accessory_view">右配件檢視</string>

    <string name="app_style">樣式</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">已點擊圖示</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">開始示範</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">浮動切換</string>
    <string name="actionbar_icon_radio_label">圖示</string>
    <string name="actionbar_basic_radio_label">基本</string>
    <string name="actionbar_position_bottom_radio_label">底部</string>
    <string name="actionbar_position_top_radio_label">頂端</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">ActionBar 類型</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">動作列位置</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar 樣式</string>
    <string name="app_bar_subtitle">字幕</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">下框線</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">已點擊瀏覽圖示。</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">標幟</string>
    <string name="app_bar_layout_menu_settings">設定</string>
    <string name="app_bar_layout_menu_search">搜尋</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">捲動行為: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">切換捲動行為</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">切換功能圖示</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">顯示虛擬人偶</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">顯示 [上一頁] 圖示</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">隱藏圖示</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">顯示圖示</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">切換搜尋列版面配置樣式</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">顯示為配件檢視</string>
    <string name="app_bar_layout_searchbar_action_view_button">顯示為動作檢視</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">在主題之間切換 (重新建立活動)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">切換佈景主題</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">項目</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">額外可捲動的內容</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">圓形樣式</string>
    <string name="avatar_style_square">方形樣式</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">大型</string>
    <string name="avatar_size_medium">中型</string>
    <string name="avatar_size_small">小型</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">雙倍超大</string>
    <string name="avatar_size_xlarge_accessibility">特大</string>
    <string name="avatar_size_xsmall_accessibility">超小</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">顯示的虛擬人偶上限</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">溢位虛擬人偶計數</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">框線類型</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">設定 OverflowAvatarCount 的虛擬人偶群組將不會依循顯示的虛擬人偶上限。</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">臉部堆疊</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">臉部堆</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">已點擊溢位</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">已點擊索引 %d 的 AvatarView</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">通知徽章</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">點</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">清單</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">角色</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">相片</string>
    <string name="bottom_navigation_menu_item_news">新聞</string>
    <string name="bottom_navigation_menu_item_alerts">警示</string>
    <string name="bottom_navigation_menu_item_calendar">行事曆</string>
    <string name="bottom_navigation_menu_item_team">團隊</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">切換標籤</string>
    <string name="bottom_navigation_three_menu_items_button">顯示三個功能表項目</string>
    <string name="bottom_navigation_four_menu_items_button">顯示四個功能表項目</string>
    <string name="bottom_navigation_five_menu_items_button">顯示五個功能表項目</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">標籤為 %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">底端工作表</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">啟用 [向下滑動以關閉]</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">以單行項目顯示</string>
    <string name="bottom_sheet_with_double_line_items">以雙行項目顯示</string>
    <string name="bottom_sheet_with_single_line_header">以單行標頭顯示</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">以雙行標頭和分隔線顯示</string>
    <string name="bottom_sheet_dialog_button">顯示</string>
    <string name="drawer_content_desc_collapse_state">展開</string>
    <string name="drawer_content_desc_expand_state">最小化</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">按一下 %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">長按 %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">按一下關閉</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">插入項目</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">更新項目</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">關閉</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">新增</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">提及</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">粗體</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">斜體</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">加底線</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">刪除線</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">復原</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">重做</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">項目符號</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">清單</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">連結</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">正在更新項目</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">間距</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">關閉位置</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">啟動</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">結束</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">群組空間</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">項目空間</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">標幟</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">已點擊標幟項目</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">回覆</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">已點擊回應項目</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">轉寄</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">已按一下轉寄項目</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">刪除</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">已點擊刪除項目</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">虛擬人偶</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">相機</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">拍攝相片</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">已點擊相機項目</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">圖庫</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">檢視您的相片</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">已點擊圖庫項目</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">影片</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">播放您的影片</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">已點擊影片項目</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">管理</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">管理您的媒體櫃</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">已點擊管理項目</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">電子郵件動作</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">文件</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">上次更新時間: 下午 2:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">共用</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">已點擊共用項目</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">移動</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">已點擊移除項目</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">刪除</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">已點擊刪除項目</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">資訊</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">已點擊資訊項目</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">時鐘</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">已點擊時鐘項目</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">警示</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">已點擊警示項目</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">時區</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">已點擊時區項目</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">按鈕的不同檢視</string>
    <string name="button">按鈕</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">停用按鈕範例</string>
    <string name="button_borderless">無框線按鈕範例</string>
    <string name="button_borderless_disabled">無框線停用按鈕範例</string>
    <string name="button_large">大型按鈕範例</string>
    <string name="button_large_disabled">大型停用按鈕範例</string>
    <string name="button_outlined">有框線按鈕範例</string>
    <string name="button_outlined_disabled">有框線停用按鈕範例</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">選擇日期</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">單一日期</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">未選取日期</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">顯示日期選擇器</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">顯示已選取日期索引標籤的日期時間選擇器</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">顯示已選取時間索引標籤的日期時間選擇器</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">顯示日期時間選擇器</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">日期範圍</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">開始:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">結束:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">未選取開始</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">未選取結束</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">選取開始日期</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">選取結束日期</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">日期時間範圍</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">選取日期時間範圍</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">顯示對話方塊</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">顯示抽屜</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">顯示抽屜對話方塊</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">沒有淡出底部對話方塊</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">顯示頂端抽屜</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">沒有淡出上方對話方塊</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button">顯示錨點檢視上方對話方塊</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button">顯示無標題頂端對話方塊</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> 顯示下方標題上方對話方塊</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">顯示右側抽屜</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">顯示左側抽屜</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">標題，主要文字</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">字幕，次要文字</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">自訂字幕文字</string>
    <!-- Footer -->
    <string name="list_item_footer">頁尾，第三文字</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">含灰色子標題文字的單行清單</string>
    <string name="list_item_sub_header_two_line">雙行清單</string>
    <string name="list_item_sub_header_two_line_dense">含密集間距的雙行清單</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">具有自訂次要字幕檢視的兩行清單</string>
    <string name="list_item_sub_header_three_line">具有黑色子標題文字的三行清單</string>
    <string name="list_item_sub_header_no_custom_views">列出沒有自訂檢視的項目</string>
    <string name="list_item_sub_header_large_header">列出大型自訂檢視的項目</string>
    <string name="list_item_sub_header_wrapped_text">以自動換行文字列出項目</string>
    <string name="list_item_sub_header_truncated_text">列出截斷文字的項目</string>
    <string name="list_item_sub_header_custom_accessory_text">動作</string>
    <string name="list_item_truncation_middle">中間截斷。</string>
    <string name="list_item_truncation_end">結束截斷。</string>
    <string name="list_item_truncation_start">開始截斷。</string>
    <string name="list_item_custom_text_view">值</string>
    <string name="list_item_click">您已按一下清單項目。</string>
    <string name="list_item_click_custom_accessory_view">您已點擊自訂配件檢視。</string>
    <string name="list_item_click_sub_header_custom_accessory_view">您已點擊子標頭自訂配件檢視。</string>
    <string name="list_item_more_options">更多選項</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">選取</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">無</string>
    <string name="people_picker_delete_example">刪除</string>
    <string name="people_picker_custom_persona_description">此範例示範如何建立自訂 IPersona 物件。</string>
    <string name="people_picker_dialog_title_removed">您已移除角色:</string>
    <string name="people_picker_dialog_title_added">您已新增角色:</string>
    <string name="people_picker_drag_started">已開始拖曳</string>
    <string name="people_picker_drag_ended">拖曳已結束</string>
    <string name="people_picker_picked_personas_listener">角色接聽程式</string>
    <string name="people_picker_suggestions_listener">建議接聽程式</string>
    <string name="people_picker_persona_chip_click">您已點擊 %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="other">%1$s 收件者</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">展開持續的底端工作表</string>
    <string name="collapse_persistent_sheet_button"> 隱藏持續的底端工作表</string>
    <string name="show_persistent_sheet_button"> 顯示持續底端工作表</string>
    <string name="new_view">這是新檢視</string>
    <string name="toggle_sheet_content">切換底端工作表內容</string>
    <string name="switch_to_custom_content">切換至自訂內容</string>
    <string name="one_line_content">單行底部工作表內容</string>
    <string name="toggle_disable_all_items">切換停用所有項目</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">新增/移除檢視</string>
    <string name="persistent_sheet_item_change_collapsed_height">變更折迭高度</string>
    <string name="persistent_sheet_item_create_new_folder_title">新增資料夾</string>
    <string name="persistent_sheet_item_create_new_folder_toast">已點擊新資料夾項目</string>
    <string name="persistent_sheet_item_edit_title">編輯</string>
    <string name="persistent_sheet_item_edit_toast">已點擊編輯項目</string>
    <string name="persistent_sheet_item_save_title">儲存</string>
    <string name="persistent_sheet_item_save_toast">已點擊儲存項目</string>
    <string name="persistent_sheet_item_zoom_in_title">放大</string>
    <string name="persistent_sheet_item_zoom_in_toast">已點擊放大項目</string>
    <string name="persistent_sheet_item_zoom_out_title">縮小</string>
    <string name="persistent_sheet_item_zoom_out_toast">已點擊縮小項目</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">可用</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Patten Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">設計工具</string>
    <string name="persona_subtitle_engineer">工程師</string>
    <string name="persona_subtitle_manager">主管</string>
    <string name="persona_subtitle_researcher">研究工具</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (長文字範例來測試截斷)</string>
    <string name="persona_view_description_xxlarge">三行文字的 XXLarge 虛擬人偶</string>
    <string name="persona_view_description_large">有兩行文字的大型虛擬人偶</string>
    <string name="persona_view_description_small">有一行文字的小虛擬人偶</string>
    <string name="people_picker_hint">沒有顯示提示</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">已停用角色晶片</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">錯誤 [角色晶片]</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">沒有關閉圖示的 [角色晶片]</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">基本 [角色晶片]</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">您已點擊選取的 [角色晶片]。</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">共用</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">追蹤</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">邀請人員</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">重新整理頁面</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">以瀏覽器開啟</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">這是多行快顯功能表。最大行數設定為 2，其餘文字將會截斷。
        Lorem ipsum dolor sit amet consectetur adipiscing elit.</string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">所有新聞</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">已儲存的新聞</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">來自網站的新聞</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">注意非工作時間</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">在桌面上非使用中時通知</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">您已按下項目:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">簡單功能表</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">簡單功能表2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">含有一個可選取項目和一個分隔線的功能表</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">包含所有可選取項目、圖示及長文字的功能表</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">顯示</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">迴圈進度</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">小型</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">中型</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">大型</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">線性進度</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">不確定</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">確定</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit, 
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">搜尋列</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">麥克風回呼</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">自動校正</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">麥克風已按下</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">已按下右側檢視</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">按下鍵盤搜尋</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">顯示橫條</string>
    <string name="fluentui_dismiss_snackbar">關閉橫條</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">動作</string>
    <string name="snackbar_action_long">長文字動作</string>
    <string name="snackbar_single_line">單行橫條</string>
    <string name="snackbar_multiline">這是多行橫條。最大行數設定為 2，其餘文字將會截斷。
        Lorem ipsum dolor sit amet consectetur adipiscing elit.</string>
    <string name="snackbar_announcement">這是公告零食欄。用於傳達新功能。</string>
    <string name="snackbar_primary">這是主要橫條。</string>
    <string name="snackbar_light">這是一個淺色橫條。</string>
    <string name="snackbar_warning">這是警告橫條。</string>
    <string name="snackbar_danger">這是危險橫條。</string>
    <string name="snackbar_description_single_line">短期間</string>
    <string name="snackbar_description_single_line_custom_view">長期間，迴圈進度為小型自訂檢視</string>
    <string name="snackbar_description_single_line_action">包含動作的短持續時間</string>
    <string name="snackbar_description_single_line_action_custom_view">具有動作和中等自訂檢視的短期間</string>
    <string name="snackbar_description_single_line_custom_text_color">具有自訂文字色彩的簡短持續時間</string>
    <string name="snackbar_description_multiline">完整持續時間</string>
    <string name="snackbar_description_multiline_custom_view">具有小型自訂檢視的長持續時間</string>
    <string name="snackbar_description_multiline_action">包含動作和文字更新的無限持續時間</string>
    <string name="snackbar_description_multiline_action_custom_view">具有動作和中等自訂檢視的短期間</string>
    <string name="snackbar_description_multiline_action_long">包含長動作文字的短持續時間</string>
    <string name="snackbar_description_announcement">短期間</string>
    <string name="snackbar_description_updated">此文字已更新。</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">顯示橫條</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">單線</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">多行</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">公告樣式</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">主要樣式</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">淺色樣式</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">警告樣式</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">危險樣式</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">首頁</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">郵件</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">設定</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">通知</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">較多</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">文字對齊方式</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">垂直</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">水平</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">沒有文字</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">索引標籤項目</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">標題</string>
    <string name="cell_sample_description">説明</string>
    <string name="calculate_cells">載入/計算 100 個儲存格</string>
    <string name="calculate_layouts">載入/計算 100 個版面配置</string>
    <string name="template_list">範本清單</string>
    <string name="regular_list">一般清單</string>
    <string name="cell_example_title">標題: 儲存格</string>
    <string name="cell_example_description">描述: 點選以變更方向</string>
    <string name="vertical_layout">直式版面配置</string>
    <string name="horizontal_layout">水平版面配置</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">標準索引標籤 2 區段</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">標準索引標籤 3 區段</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">標準索引標籤 4 區段</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">含呼叫器的標準索引標籤</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">切換索引標籤</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">膠囊索引標籤</string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">點選以取得工具提示</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">點選以取得自訂行事曆工具提示</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">點選自訂色彩工具提示</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">點選以關閉內部工具提示</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">點選以自訂檢視工具提示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">頂端自訂色彩工具提示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">具有 10dp offsetX 的頂部工具提示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">底部開始工具提示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">具有 10dp offsetY 的底部工具提示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">關閉內部工具提示</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">工具提示已關閉</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">標題為淺色 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">標題 1 為中等深淺 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">標題 2 是一般 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">標題為一般 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">次標題 1 是一般 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">次標題 2 為中等深淺 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">主體 1 為一般 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">主體 2 為中等 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">字幕為一般 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK 版本: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">項目 %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">資料夾</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">已點選</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">支架</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">已展開的 FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">已摺疊的 FAB</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">按一下以重新整理清單</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">開啟抽屜</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">選單項目</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">位移 X (以 dp 表示)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">位移 Y (以 dp 表示)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">內容文字</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">重複內容文字</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">選單寬度會依循著內容文字而變更。寬度
        上限會限制在 75% 的畫面尺寸。在側邊和底部的內容頁面邊界是由權杖所控管。相同的內容文字將會重複以更改
        其高度。</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">開啟功能表</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">基本卡片</string>
    <!-- UI Label for Card -->
    <string name="file_card">檔案卡片</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">公告卡片</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">隨機 UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">選項</string>
    <!-- UI Label for Title -->
    <string name="card_title">標題</string>
    <!-- UI Label for text -->
    <string name="card_text">文字</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">次文字</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">此橫幅的次要複本可視需要換行成兩行間距。</string>
    <!-- UI Label Button -->
    <string name="card_button">按鈕</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">顯示對話方塊</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">點擊外部時關閉對話方塊</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">按下後關閉對話方塊後</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">對話方塊已關閉</string>
    <!-- UI Label Cancel -->
    <string name="cancel">取消</string>
    <!-- UI Label Ok -->
    <string name="ok">確定</string>
    <!-- A sample description -->
    <string name="dialog_description">對話方塊是提示使用者做出決策或輸入其他資訊的小視窗。</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">開啟抽屜</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">展開抽屜</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">關閉隱藏式選單</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">選取隱藏式選單類型</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">頂端</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">整個隱藏式選單會顯示在可見區域中。</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">底部</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">整個隱藏式選單會顯示在可見區域中。向上撥動捲動內容。可展開的隱藏式選單可透過拖曳控點展開。</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">左側滑動</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">隱藏式選單從左側滑動至可見區域。</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">右側滑動</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">隱藏式選單從右側滑動至可見區域。</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">底部滑動</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">隱藏式選單從螢幕底部滑動至可見區域。在可展開的隱藏式選單向上撥動，將部分剩餘部分移至可見區域 &amp;，然後捲動。</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">可見的 Scrim</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">選取隱藏式選單內容</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">全螢幕大小可捲動的內容</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">超過一半螢幕內容</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">不到一半螢幕內容</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">動態大小內容</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">巢狀隱藏式選單內容</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">可擴充</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">略過開啟狀態</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">防止在點擊 Scrim 時駁回</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">顯示控點</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">標題</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">工具提示文字</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">點一下以取得自訂內容工具提示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">最上層開始 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">最上層結束 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">底部開始 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">底端 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">中心 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">自訂中心</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">如需版本資訊的更新， </string>
    <string name="click_here">按一下這裡。</string>
    <string name="open_source_cross_platform">開放原始碼跨平台設計系統。</string>
    <string name="intuitive_and_powerful">直覺且功能強大。</string>
    <string name="design_tokens">設計權杖</string>
    <string name="release_notes">版本資訊</string>
    <string name="github_repo">GitHub 存放庫</string>
    <string name="github_repo_link">GitHub 存放庫連結</string>
    <string name="report_issue">回報問題</string>
    <string name="v1_components">V1 元件</string>
    <string name="v2_components">V2 元件</string>
    <string name="all_components">全部</string>
    <string name="fluent_logo">Fluent 標誌</string>
    <string name="new_badge">新增</string>
    <string name="modified_badge">已修改</string>
    <string name="api_break_badge">API 中斷</string>
    <string name="app_bar_more">其他</string>
    <string name="accent">輔色</string>
    <string name="appearance">外觀</string>
    <string name="choose_brand_theme">選擇您的品牌佈景主題:</string>
    <string name="fluent_brand_theme">Fluent 品牌</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">選擇外觀</string>
    <string name="appearance_system_default">系統預設值</string>
    <string name="appearance_light">淺色</string>
    <string name="appearance_dark">深色</string>
    <string name="demo_activity_github_link">示範活動 GitHub 連結</string>
    <string name="control_tokens_details">控制項權杖詳細資料</string>
    <string name="parameters">參數</string>
    <string name="control_tokens">控制項權杖</string>
    <string name="global_tokens">全域權杖</string>
    <string name="alias_tokens">別名權杖</string>
    <string name="sample_text">文字</string>
    <string name="sample_icon">範例圖示</string>
    <string name="color">色彩</string>
    <string name="neutral_color_tokens">中性色彩權杖</string>
    <string name="font_size_tokens">字型大小權杖</string>
    <string name="line_height_tokens">行高權杖</string>
    <string name="font_weight_tokens">字型粗細權杖</string>
    <string name="icon_size_tokens">圖示大小權杖</string>
    <string name="size_tokens">大小權杖</string>
    <string name="shadow_tokens">陰影權杖</string>
    <string name="corner_radius_tokens">圓角半徑權杖</string>
    <string name="stroke_width_tokens">筆觸寬度權杖</string>
    <string name="brand_color_tokens">品牌色彩權杖</string>
    <string name="neutral_background_color_tokens">中性背景色彩權杖</string>
    <string name="neutral_foreground_color_tokens">中性前景色彩權杖</string>
    <string name="neutral_stroke_color_tokens">中性筆觸色彩權杖</string>
    <string name="brand_background_color_tokens">品牌背景色彩權杖</string>
    <string name="brand_foreground_color_tokens">品牌前景色彩權杖</string>
    <string name="brand_stroke_color_tokens">品牌筆觸色彩權杖</string>
    <string name="error_and_status_color_tokens">錯誤和狀態色彩權杖</string>
    <string name="presence_tokens">顯示狀態色彩權杖</string>
    <string name="typography_tokens">文字設計權杖</string>
    <string name="unspecified">未指定</string>

</resources>