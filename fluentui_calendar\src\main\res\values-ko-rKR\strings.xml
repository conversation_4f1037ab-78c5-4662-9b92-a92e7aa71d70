<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">월</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">화</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">수</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">목</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">금</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">토</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">일</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">다음 주로 이동</string>
    <string name="accessibility_goto_previous_week">지난주로 이동</string>
    <string name="accessibility_today">오늘</string>
    <string name="accessibility_selected">선택됨</string>

    <!-- *** Shared *** -->
    <string name="done">완료</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">오늘</string>
    <string name="tomorrow">내일</string>
    <string name="yesterday">어제</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">시작 시간</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">종료 시간</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">시작 날짜</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">종료 날짜</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">시간 선택</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">날짜 선택</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">날짜 시간 선택기</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">날짜 선택기</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">날짜 시간 선택기 범위</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">날짜 선택기 범위</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">시작 시간 탭</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">종료 시간 탭</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">시작 날짜 탭</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">종료 날짜 탭</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">대화 상자 닫기</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">월 올리기</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">다음 달 선택</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">월 내리기</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">이전 달 선택</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">일 올리기</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">다음 날 선택</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">일 내리기</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">이전 날 선택</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">연도 올리기</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">내년 선탹</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">연도 내리기</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">이전 연도 선택</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">날짜 올리기</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">다음 날짜 선택</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">날짜 내리기</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">이전 날짜 선택</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">시간 올리기</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">다음 시간 선택</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">시간 내리기</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">이전 시간 선택</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">분 올리기</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">다음 분 선택</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">분 내리기</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">이전 분 선택</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">오전 오후 기간 전환</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">오전 오후 기간 전환</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s 선택됨</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">선택됨</string>
</resources>
