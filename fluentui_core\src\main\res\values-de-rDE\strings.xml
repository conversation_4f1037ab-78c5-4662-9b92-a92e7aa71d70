<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary"><PERSON><PERSON><PERSON>r</string>
    <string name="fluentui_secondary"><PERSON><PERSON><PERSON><PERSON><PERSON></string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Sc<PERSON><PERSON><PERSON>n</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Markiert</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Nicht ausgewählt</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Symbol</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Symbol</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Deaktiviert</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Aktionsschaltfläche</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Aktiviert</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Blatt schließen</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Schließen</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Abbrechen</string>
    <!--name of the icon -->
    <string name="fluentui_search">Suchen</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Mikrofon</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Text löschen</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Zurück</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Aktiviert</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Deaktiviert</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutral</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marke</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Kontrast</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Akzent</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Warnung</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Gefahr</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Leider ist ein Fehler aufgetreten.</string>
    <string name="fluentui_error">Fehler</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Hinweis</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Chevron</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Umriss</string>

    <string name="fluentui_action_button_icon">Symbol für Aktionsschaltfläche</string>
    <string name="fluentui_center">Text zentrieren</string>
    <string name="fluentui_accessory_button">Zubehörschaltflächen</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Optionsschaltfläche</string>
    <string name="fluentui_label">Bezeichnung</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Erweitert</string>
    <string name="fluentui_collapsed">Reduziert</string>

    <!--types of control -->
    <string name="fluentui_large">Groß</string>
    <string name="fluentui_medium">Mittel</string>
    <string name="fluentui_small">Klein</string>
    <string name="fluentui_password_mode">Kennwortmodus</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Untertitel</string>
    <string name="fluentui_assistive_text">Hilfstext</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Titel</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Kurz</string>"
    <string name="fluentui_long">Lang</string>"
    <string name="fluentui_indefinite">Unbegrenzt</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Schaltfläche gedrückt</string>
    <string name="fluentui_dismissed">Geschlossen</string>
    <string name="fluentui_timeout">Zeitüberschreitung</string>
    <string name="fluentui_left_swiped">Nach links gewischt</string>
    <string name="fluentui_right_swiped">Nach rechts gewischt</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Text</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Zahl</string>
    <string name="fluentui_keyboard_phone">Telefon</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">E-Mail</string>
    <string name="fluentui_keyboard_password">Kennwort</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Dezimal</string>
</resources>