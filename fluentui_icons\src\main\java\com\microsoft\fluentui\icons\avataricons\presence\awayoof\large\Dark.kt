package com.microsoft.fluentui.icons.avataricons.presence.awayoof.large

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import com.microsoft.fluentui.icons.avataricons.presence.awayoof.LargeGroup

val LargeGroup.Dark: ImageVector
    get() {
        if (_dark != null) {
            return _dark!!
        }
        _dark = Builder(name = "Dark", defaultWidth = 20.0.dp, defaultHeight = 20.0.dp,
                viewportWidth = 20.0f, viewportHeight = 20.0f).apply {
            path(fill = SolidColor(Color(0xFF000000)), stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(10.0f, 10.0f)
                moveToRelative(-9.0f, 0.0f)
                arcToRelative(9.0f, 9.0f, 0.0f, true, true, 18.0f, 0.0f)
                arcToRelative(9.0f, 9.0f, 0.0f, true, true, -18.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0xFFE959D9)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(10.2071f, 8.2074f)
                curveTo(10.5976f, 7.8168f, 10.5976f, 7.1837f, 10.2071f, 6.7931f)
                curveTo(9.8166f, 6.4026f, 9.1834f, 6.4026f, 8.7929f, 6.7931f)
                lineTo(6.2929f, 9.293f)
                curveTo(6.1054f, 9.4806f, 6.0f, 9.7349f, 6.0f, 10.0002f)
                curveTo(6.0f, 10.2654f, 6.1054f, 10.5197f, 6.2929f, 10.7073f)
                lineTo(8.7929f, 13.2073f)
                curveTo(9.1834f, 13.5978f, 9.8166f, 13.5978f, 10.2071f, 13.2073f)
                curveTo(10.5976f, 12.8167f, 10.5976f, 12.1836f, 10.2071f, 11.7931f)
                lineTo(9.4142f, 11.0002f)
                horizontalLineTo(13.0f)
                curveTo(13.5523f, 11.0002f, 14.0f, 10.5524f, 14.0f, 10.0002f)
                curveTo(14.0f, 9.4479f, 13.5523f, 9.0002f, 13.0f, 9.0002f)
                horizontalLineTo(9.4143f)
                lineTo(10.2071f, 8.2074f)
                close()
                moveTo(10.0f, 2.0f)
                curveTo(5.5817f, 2.0f, 2.0f, 5.5817f, 2.0f, 10.0f)
                curveTo(2.0f, 14.4183f, 5.5817f, 18.0f, 10.0f, 18.0f)
                curveTo(14.4183f, 18.0f, 18.0f, 14.4183f, 18.0f, 10.0f)
                curveTo(18.0f, 5.5817f, 14.4183f, 2.0f, 10.0f, 2.0f)
                close()
                moveTo(4.0f, 10.0f)
                curveTo(4.0f, 6.6863f, 6.6863f, 4.0f, 10.0f, 4.0f)
                curveTo(13.3137f, 4.0f, 16.0f, 6.6863f, 16.0f, 10.0f)
                curveTo(16.0f, 13.3137f, 13.3137f, 16.0f, 10.0f, 16.0f)
                curveTo(6.6863f, 16.0f, 4.0f, 13.3137f, 4.0f, 10.0f)
                close()
            }
        }
                .build()
        return _dark!!
    }

private var _dark: ImageVector? = null
