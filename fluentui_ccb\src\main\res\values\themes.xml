<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->
<resources>
    <!--ContextualCommandBar-->
    <style name="Theme.FluentUI.ContextualCommandBar.Base" parent="Base.Theme.FluentUI">
    <item name="fluentuiContextualCommandBarBackgroundColor">@color/fluentui_gray_50</item>
    <item name="fluentuiContextualCommandBarBackgroundColorPressed">@color/fluentui_gray_100</item>
    <item name="fluentuiContextualCommandBarBackgroundColorSelected">@color/fluentui_communication_tint_40</item>
    <item name="fluentuiContextualCommandBarIconTint">@color/fluentui_gray_900</item>
    <item name="fluentuiContextualCommandBarIconTintDisabled">@color/fluentui_gray_300</item>
    <item name="fluentuiContextualCommandBarIconTintSelected">@color/fluentui_communication_blue</item>
    <item name="fluentuiContextualCommandBarDismissBackgroundColor">#FFFFFF</item>
    <item name="fluentuiContextualCommandBarDismissIconTintColor">@color/fluentui_gray_900</item>
    </style>

    <style name="Theme.FluentUI.ContextualCommandBar" parent="Theme.FluentUI.ContextualCommandBar.Base"/>
</resources>