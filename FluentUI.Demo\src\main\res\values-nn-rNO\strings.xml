<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI Demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Merkt %s</string>
    <string name="app_modifiable_parameters">Modifiserbare parametrar</string>
    <string name="app_right_accessory_view">Høgre tilbehyørsvising</string>

    <string name="app_style">Stil</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikon trykt</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Start demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Ikon</string>
    <string name="actionbar_basic_radio_label">Grunnleggjande</string>
    <string name="actionbar_position_bottom_radio_label">Botn</string>
    <string name="actionbar_position_top_radio_label">Topp</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Aksjonsfelt-type</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Aksjonsfelt-plassering</string>

    <!--AppBar-->
    <string name="app_bar_style">Appfelt-stil</string>
    <string name="app_bar_subtitle">Undertittel</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Nedre kantline</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Navigasjonsikon klikka.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Flagg</string>
    <string name="app_bar_layout_menu_settings">Innstillingar</string>
    <string name="app_bar_layout_menu_search">Søk</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Rulleåtferd: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Veksle rulleåtferd</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Slå på/av navigasjonsikonet</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Vis avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Vis tilbake-ikon</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Gøym-ikon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Vis ikon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Veksle stil på søkefeltet sitt oppsett</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Vis som tilbehøyrsvising</string>
    <string name="app_bar_layout_searchbar_action_view_button">Vis som handlingsvising</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Veksle mellom tema (attskaper aktivitet)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Byt tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Ekstra rullbart innhald</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Sirkelstil</string>
    <string name="avatar_style_square">Kvadratstil</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXStor</string>
    <string name="avatar_size_xlarge">XStor</string>
    <string name="avatar_size_large">Stor</string>
    <string name="avatar_size_medium">Medium</string>
    <string name="avatar_size_small">Liten</string>
    <string name="avatar_size_xsmall">XLiten</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dobbel ekstra stor</string>
    <string name="avatar_size_xlarge_accessibility">Ekstra stor</string>
    <string name="avatar_size_xsmall_accessibility">Ekstra lite</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maks. vist avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Overflyt Avatar tal</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Kantlinetype</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatargruppe med OverflowAvatarCount sett vil ikkje overhalde maks. vist avatar.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Ansiktsstabel</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Ansiktshaug</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Overflyt klikka</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView ved indeks %d klikka</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Varslingsmerke</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punkt</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Liste</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Teikn</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Bilete</string>
    <string name="bottom_navigation_menu_item_news">Nyhende</string>
    <string name="bottom_navigation_menu_item_alerts">Varslingar</string>
    <string name="bottom_navigation_menu_item_calendar">Kalender</string>
    <string name="bottom_navigation_menu_item_team">Gruppe</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Slå på/av etikettar</string>
    <string name="bottom_navigation_three_menu_items_button">Vis tre menyelement</string>
    <string name="bottom_navigation_four_menu_items_button">Vis fire menyelement</string>
    <string name="bottom_navigation_five_menu_items_button">Vis fem menyelement</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Etikettane er %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Botnark</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Aktiver Sveip ned for å avvise</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Vis med enkeltlineelement</string>
    <string name="bottom_sheet_with_double_line_items">Vis med doble lineelement</string>
    <string name="bottom_sheet_with_single_line_header">Vis med overskrift på éi line</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Vis med dobbel lineoverskrift og skiljeliner</string>
    <string name="bottom_sheet_dialog_button">Vis</string>
    <string name="drawer_content_desc_collapse_state">Vid ut</string>
    <string name="drawer_content_desc_expand_state">Minimer</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klikk %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Langt klikk %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klikk lukk</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Set inn element</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Oppdater element</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Lukk</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Legg til</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Omtale</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Feit</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursiv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Understreking</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Gjennomstreking</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Angre</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Gjer om</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Punkt</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Liste</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Kopling</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Element oppdaterar</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Avstand</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Lukk plassering</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">START</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">SLUTT</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Gruppeplass</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Elementområde</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Flagg</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Flaggelement klikka</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Svar</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Svarelement klikka</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Fram</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Vidaresend element klikka</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Slett</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Slett element klikka</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Ta eit bilete</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kameraelementet klikka</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galleri</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Vis bileta dine</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Gallerielement klikka</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videoar</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Spel av videoane dine</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Videoelement klikka</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Handsam</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Handsame mediebiblioteket</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Handsame element klikka</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-posthandlingar</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokument</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Sist oppdatert 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Del</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Del element klikka</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Flytt</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Flytt element klikka</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Slett</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Slett element klikka</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informasjon</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Infoelement klikka</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Klokke</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Klokkeelement klikka</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Alarmelement klikka</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Tidssone</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Tidssoneelement klikka</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Ulike visingar av knapp</string>
    <string name="button">Knapp</string>
    <string name="buttonbar">Knappefelt</string>
    <string name="button_disabled">Eksempel på deaktivert knapp</string>
    <string name="button_borderless">Eksempel på knapp utan kantline</string>
    <string name="button_borderless_disabled">Eksempel på deaktivert knapp utan kantline</string>
    <string name="button_large">Eksempel på stor knapp</string>
    <string name="button_large_disabled">Eksempel på stor deaktivert knapp</string>
    <string name="button_outlined">Eksempel på knapp med kantline</string>
    <string name="button_outlined_disabled">Eksempel på deaktivert knapp med kantline</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Vel ein dato</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Enkeltdato</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Ingen dato vald</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Vis datoveljar</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Vis dato- og klokkeslettveljar med datofana vald</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Vis dato- og klokkeslettveljar med klokkeslettfana vald</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Vis dato-klokkeslett</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datoområde</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Start:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Slutt:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Ingen start vald</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Ingen ende vald</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Vel startdato</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Vel sluttdato</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Dato- og tidsområde</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Vel datoperiode</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Vis dialog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Vis skuff</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Vis skuffedialog</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Ingen fading botndialog</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Vis øvste skuff</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Ingen fading topp-dialog</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Vis øvste dialogboks for ankervising</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Vis ingen tittel øvste dialog</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Vis under tittel øvste dialog</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Vis høgre skuff</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Vis venstre skuff</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Tittel, primærtekst</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Undertittel, sekundær tekst</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Eigendefinert undertitteltekst</string>
    <!-- Footer -->
    <string name="list_item_footer">Botntekst, tertiær tekst</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Enkeltlineliste med grå underoverskrifttekst</string>
    <string name="list_item_sub_header_two_line">To-liners liste</string>
    <string name="list_item_sub_header_two_line_dense">To-liners liste med tett mellomrom</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">To-liners liste med tilpassa sekundær undertittelvising</string>
    <string name="list_item_sub_header_three_line">Tre-liners liste med svart underoverskrifttekst</string>
    <string name="list_item_sub_header_no_custom_views">Listeelement utan eigendefinerte visingar</string>
    <string name="list_item_sub_header_large_header">Listeelement med store eigendefinerte visingar</string>
    <string name="list_item_sub_header_wrapped_text">Listeelement med broten tekst</string>
    <string name="list_item_sub_header_truncated_text">Listeelement med avkorta tekst</string>
    <string name="list_item_sub_header_custom_accessory_text">Handling</string>
    <string name="list_item_truncation_middle">Midtavkorting.</string>
    <string name="list_item_truncation_end">Avslutt trunkering.</string>
    <string name="list_item_truncation_start">Start avkorting.</string>
    <string name="list_item_custom_text_view">Verdi</string>
    <string name="list_item_click">Du klikka på listeelementet.</string>
    <string name="list_item_click_custom_accessory_view">Du klikka på den tilpassa tilbehøyrsvisinga.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Du klikka på underoverskriften for tilpassa tilbehøyrsvising.</string>
    <string name="list_item_more_options">Fleire alternativ</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Vel</string>
    <string name="people_picker_select_deselect_example">Vel / fjern val</string>
    <string name="people_picker_none_example">Ingen</string>
    <string name="people_picker_delete_example">Slett</string>
    <string name="people_picker_custom_persona_description">Dette eksempelet viser korleis du opprettar eit tilpassa IPersona-objekt.</string>
    <string name="people_picker_dialog_title_removed">Du fjerna ein profil:</string>
    <string name="people_picker_dialog_title_added">Du la til ein profil:</string>
    <string name="people_picker_drag_started">Dra starta</string>
    <string name="people_picker_drag_ended">Dra avslutta</string>
    <string name="people_picker_picked_personas_listener">Profil-lyttar</string>
    <string name="people_picker_suggestions_listener">Forslag-lyttar</string>
    <string name="people_picker_persona_chip_click">Du klikka på %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s mottakar</item>
        <item quantity="other">%1$s mottakarar</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Utvid vedvarande botnark</string>
    <string name="collapse_persistent_sheet_button"> Skjul vedvarande botnark</string>
    <string name="show_persistent_sheet_button"> Vis vedvarande botnark</string>
    <string name="new_view">Dette er Ny vising</string>
    <string name="toggle_sheet_content">Veksle mellom innhald på botnark</string>
    <string name="switch_to_custom_content">Byt til tilpassa innhald</string>
    <string name="one_line_content">Ei-lines botnark-innhald</string>
    <string name="toggle_disable_all_items">Veksle Deaktiver alle element</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Legg til/fjern vising</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Endre skjult høgd</string>
    <string name="persistent_sheet_item_create_new_folder_title">Ny mappe</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Ny mappeelement klikka</string>
    <string name="persistent_sheet_item_edit_title">Rediger</string>
    <string name="persistent_sheet_item_edit_toast">Rediger element klikka</string>
    <string name="persistent_sheet_item_save_title">Lagre</string>
    <string name="persistent_sheet_item_save_toast">Lagre element klikka</string>
    <string name="persistent_sheet_item_zoom_in_title">Zoom inn</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Zoom inn element klikka</string>
    <string name="persistent_sheet_item_zoom_out_title">Zoom ut</string>
    <string name="persistent_sheet_item_zoom_out_toast">Zoom ut element klikka</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Tilgjengeleg</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designar</string>
    <string name="persona_subtitle_engineer">Ingeniør</string>
    <string name="persona_subtitle_manager">Leiar</string>
    <string name="persona_subtitle_researcher">Forskar</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (eksempel på lang tekst for å teste trunkering)</string>
    <string name="persona_view_description_xxlarge">XXStor avatar med tre tekstliner</string>
    <string name="persona_view_description_large">Stor avatar med to liner med tekst</string>
    <string name="persona_view_description_small">Lita avatar med éi tekstline</string>
    <string name="people_picker_hint">Ingen med hint vist</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Deaktivert profil-chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Feil profil-chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Profil-chip utan lukkeikon</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Grunnleggjande profil-chip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Du klikka på ein valt profil-chip.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Del</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Følg</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Inviter personar</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Oppdater sida</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Opne i nettlesar</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Dette er ein popup-meny med fleire liner. Maks liner er sett til to, resten av teksten blir avkorta.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Alle nyhende</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Lagra nyhende</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Nyhende frå nettstader</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Gi beskjed utanom arbeidstid</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Varsle når inaktiv på skrivebordet</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Du klikka på elementet:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Enkel meny</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Enkel meny2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Meny med eitt valbart element og ei skiljeline</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Meny med alle valbare element, ikon og lang tekst</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Vis</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Sirkulær framgang</string>
    <string name="circular_progress_xsmall">XLiten</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Liten</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Medium</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Stor</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineær framgang</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Ubestemmeleg</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Bestem</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore eit dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Søkjeline</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofon-tilbakeringing</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autokorrektur</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon trykt</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Høgre vising trykka</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Tastatursøk trykt</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Vis Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Lukk Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Handling</string>
    <string name="snackbar_action_long">Lang teksthandling</string>
    <string name="snackbar_single_line">Snackbar med éi line</string>
    <string name="snackbar_multiline">Dette er ein snackbar med fleire liner. Maks liner er sett til to, resten av teksten blir avkorta.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Dette er ein kunngjeringssnackbar. Den brukast til å kommunisere nye funksjonar.</string>
    <string name="snackbar_primary">Dette er ein primær snackbar.</string>
    <string name="snackbar_light">Dette er ein enkel snackbar.</string>
    <string name="snackbar_warning">Dette er ein åtvaringssnackbar.</string>
    <string name="snackbar_danger">Dette er ein fare-snackbar.</string>
    <string name="snackbar_description_single_line">Kort varigheit</string>
    <string name="snackbar_description_single_line_custom_view">Lang varigheit med sirkulær framgang som lita eigendefinert vising</string>
    <string name="snackbar_description_single_line_action">Kort varigheit med handling</string>
    <string name="snackbar_description_single_line_action_custom_view">Kort varigheit med handling og middels tilpassa vising</string>
    <string name="snackbar_description_single_line_custom_text_color">Kort varigheit med tilpassa tekstfarge</string>
    <string name="snackbar_description_multiline">Lang varigheit</string>
    <string name="snackbar_description_multiline_custom_view">Lang varigheit med lita tilpassa vising</string>
    <string name="snackbar_description_multiline_action">Ubestemt varigheit med handling og tekstoppdateringar</string>
    <string name="snackbar_description_multiline_action_custom_view">Kort varigheit med handling og middels tilpassa vising</string>
    <string name="snackbar_description_multiline_action_long">Kort varigheit med lang handlingstekst</string>
    <string name="snackbar_description_announcement">Kort varigheit</string>
    <string name="snackbar_description_updated">Denne teksten er oppdatert.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Vis Snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Enkel line</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Fleirline</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Kunngjeringsstil</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primær stil</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Enkel stil</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Åtvaringsstil</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Farestil</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Heim</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">E-post</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Innstillingar</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Varsling</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Meir</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Tekstjustering</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Loddrett</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vassrett</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Ingen tekst</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Faneelement</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Tittel</string>
    <string name="cell_sample_description">Skildring</string>
    <string name="calculate_cells">Last inn/berekne 100 celler</string>
    <string name="calculate_layouts">Last inn/beregn 100 oppsett</string>
    <string name="template_list">Malliste</string>
    <string name="regular_list">Vanleg liste</string>
    <string name="cell_example_title">Tittel: Celle</string>
    <string name="cell_example_description">Skildring: Trykk for å endre retning</string>
    <string name="vertical_layout">Loddrett oppsett</string>
    <string name="horizontal_layout">Vassrett oppsett</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standard Fane 2-segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standard fane 3-segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standard fane 4-segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardfane med personsøkjar</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Byt fane</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Piller-fane </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Trykk for verktøytips</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Trykk for verktøytips for tilpassa kalender</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Trykk på verktøytips for tilpassa farge</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Trykk for lukk inne i verktøytips</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Trykk for verktøytips for tilpassa vising</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Øvste verktøytips for tilpassa farge</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Øvste slutt-verktøytips med 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Nedste start-verktøytips</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Nedste slutt-verktøytips med 10 dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Lukk inne i verktøytips</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Verktøytips er lukka</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Overskrifta er Light 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Tittel 1 er Medium 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Tittel 2 er Vanleg 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Overskrifta er Vanleg 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Underoverskrift 1 er Vanleg 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Underoverskrift 2 er Medium 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Brødtekst 1 er Vanleg 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Brødtekst 2 er Medium 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Teksting er Vanleg 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-versjon: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Element %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mappe</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Klikka</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Stillas</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">Utvida FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">Skjult FAB</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klikk for å oppdatere liste</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Opne skuffen</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menyelement</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Forskuva X (i DP)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Forskuva Y (i DP)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Innhaldstekst</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Gjenta innhaldstekst</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menybreidda forandrar seg i samsvar med innhaldsteksten. Maksimal
        breidde er avgrensa til 75 % av skjermstorleiken. Innhaldsmarginane frå sida og botnen er styrte av token. Den same innhaldsteksten blir gjentatt for å variere
        høgda.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Opne meny</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Grunnleggjande kort</string>
    <!-- UI Label for Card -->
    <string name="file_card">Filkort</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Kort for kunngjering</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Tilfeldig brukargrensesnitt</string>
    <!-- UI Label for Options -->
    <string name="card_options">Alternativ</string>
    <!-- UI Label for Title -->
    <string name="card_title">Tittel</string>
    <!-- UI Label for text -->
    <string name="card_text">Tekst</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Undertekst</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekundær tekst for dette banneret kan gå over to liner ved behov.</string>
    <!-- UI Label Button -->
    <string name="card_button">Knapp</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Vis dialog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Lukk dialogen når du klikkar utanfor</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Lukk dialog ved tilbaketrykk</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialogen lukka</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Avbryt</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Ein dialogboks er eit lite vindauge som ber brukaren om å ta ei avgjerd eller leggje inn tilleggsinformasjon.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Opne skuffa</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Utvid skuffa</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Lukk skuffen</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Vel skufftype</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Topp</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Heile skuffen visast i den synlege regionen.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Botn</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Heile skuffen visast i den synlege regionen. Sveip opp-rørsle rulleinnhald. Utvidbar skuff sperra med dra-handtak.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Venstre skyvehandling</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Skuff skyvehandling til den synlege regionen frå venstre side.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Høgre skyvehandling</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Skuff skyvehandling til den synlege regionen frå høgre side.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Høgre skyvehandling</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Skuff skyvehandling til den synlege regionen frå nedst på skjermen. Sveip opp-rørsle på utvidbar skuff for å dra resten til den synlege regionen &amp; og rull</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Synleg </string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Vel skuffinnhald</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Full skjerm-storleik rullbart innhald</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Meir enn innhaldet på halve skjermen</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mindre enn innhaldet på halve skjermen</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dynamisk storleiksinnhald</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Nesta skuffinnhald</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Utvidbart</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Hopp over open tilstand</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Hindre lukking på Scrim-klikk</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Vis handtak</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Tittel</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Verktøytips-tekst</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Trykk for eigendefinert innhald for skjermtips</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Toppstart</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Toppkant </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Nedste start</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Nedre kant</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Midtstilt </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Eigendefinert i midten</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">For oppdateringar på produktmerknader</string>
    <string name="click_here">klikk her.</string>
    <string name="open_source_cross_platform">Open kjelde på tvers av plattformen Design System.</string>
    <string name="intuitive_and_powerful">Intuitiv &amp; sterk.</string>
    <string name="design_tokens">Utformingstokens</string>
    <string name="release_notes">Produktmerknader</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub Repo-kopling</string>
    <string name="report_issue">Rapporter problem</string>
    <string name="v1_components">V1-komponentar</string>
    <string name="v2_components">V2-komponentar</string>
    <string name="all_components">Alle</string>
    <string name="fluent_logo">Fluent-logo</string>
    <string name="new_badge">Ny</string>
    <string name="modified_badge">Endra</string>
    <string name="api_break_badge">API-pause</string>
    <string name="app_bar_more">Meir</string>
    <string name="accent">Uthevingsfarge</string>
    <string name="appearance">Utsjånad</string>
    <string name="choose_brand_theme">Vel ditt merkenamn</string>
    <string name="fluent_brand_theme">Flytande merke</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Vis utsjånad</string>
    <string name="appearance_system_default">Systemstandard</string>
    <string name="appearance_light">Lys</string>
    <string name="appearance_dark">Mørk</string>
    <string name="demo_activity_github_link">Demo-aktivitet GitHub-kopling</string>
    <string name="control_tokens_details">Kontroll-tokens detaljar</string>
    <string name="parameters">Parametrar</string>
    <string name="control_tokens">Kontroll-tokens</string>
    <string name="global_tokens">Globale tokens</string>
    <string name="alias_tokens">Alias-Tokens</string>
    <string name="sample_text">Tekst</string>
    <string name="sample_icon">Eksempel-ikon</string>
    <string name="color">Farge</string>
    <string name="neutral_color_tokens">Tokens i naturlege fargar</string>
    <string name="font_size_tokens">Skriftstorleik på tokens</string>
    <string name="line_height_tokens">Linehøgd-tokens</string>
    <string name="font_weight_tokens">Skriftveks-tokens</string>
    <string name="icon_size_tokens">Tokens i ikonstorleik</string>
    <string name="size_tokens">Storleik på tokens</string>
    <string name="shadow_tokens">Skugge-tokens</string>
    <string name="corner_radius_tokens">Tokens med hjørneradius</string>
    <string name="stroke_width_tokens">Tokens med Strøkbreidd</string>
    <string name="brand_color_tokens">Merkefarge-tokens</string>
    <string name="neutral_background_color_tokens">Nøytral bakgrunnsfarge på merke-tokens</string>
    <string name="neutral_foreground_color_tokens">Nøytral forgrunnsfarge-tokens</string>
    <string name="neutral_stroke_color_tokens">Farge-tokens med naturlege strøk</string>
    <string name="brand_background_color_tokens">Bakgrunnsfarge på merke-tokens</string>
    <string name="brand_foreground_color_tokens">Merke-tokens med forgrunnsfarge</string>
    <string name="brand_stroke_color_tokens">Farge-tokens med merkestrøk</string>
    <string name="error_and_status_color_tokens">Feil- og status-fargetokens</string>
    <string name="presence_tokens">Nærværsfarge-tokens</string>
    <string name="typography_tokens">Typografi-tokens</string>
    <string name="unspecified">Uspesifisert</string>

</resources>