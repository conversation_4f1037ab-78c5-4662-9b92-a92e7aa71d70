<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demonstração da interface do usuário do Fluent</string>
    <string name="app_title">IU do Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s selecionado</string>
    <string name="app_modifiable_parameters">Parâmetros Modificáveis</string>
    <string name="app_right_accessory_view">Modo de Exibição Acessório Direito</string>

    <string name="app_style">Estilo</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ícone Pressionado</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Iniciar Demonstração</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carrossel</string>
    <string name="actionbar_icon_radio_label">Ícone</string>
    <string name="actionbar_basic_radio_label">Básico</string>
    <string name="actionbar_position_bottom_radio_label">Inferior</string>
    <string name="actionbar_position_top_radio_label">Superior</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tipo de Barra de Ação</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Posição da Barra de Ações</string>

    <!--AppBar-->
    <string name="app_bar_style">Estilo da Barra de Aplicativos</string>
    <string name="app_bar_subtitle">Subtítulo</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Borda Inferior</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Ícone de navegação clicado.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Sinalizador</string>
    <string name="app_bar_layout_menu_settings">Configurações</string>
    <string name="app_bar_layout_menu_search">Pesquisar</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Comportamento de rolagem: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Alternar comportamento de rolagem</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Ícone de navegação de alternância</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Mostrar avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Mostrar ícone de voltar</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ocultar ícone</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Mostrar ícone</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Alternar estilo de layout da barra de pesquisa</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Mostrar como modo de exibição acessório</string>
    <string name="app_bar_layout_searchbar_action_view_button">Mostrar como modo de exibição de ação</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Alternar entre temas (recria a atividade)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Alternar o tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Item</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Conteúdo rolável extra</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Estilo de círculo</string>
    <string name="avatar_style_square">Estilo quadrado</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Grande</string>
    <string name="avatar_size_medium">Médio</string>
    <string name="avatar_size_small">Pequeno</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Duplo Extra Grande</string>
    <string name="avatar_size_xlarge_accessibility">Extra Grande</string>
    <string name="avatar_size_xsmall_accessibility">Muito Pequeno</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Avatar Exibido Máximo</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Contagem de Estouro de Avatar</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tipo de Borda</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">O Grupo de Avatares com OverflowAvatarCount definido não seguirá o Avatar Exibido máximo.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Pilha facial</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Pilha facial</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Estouro clicado</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView no índice %d clicado</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Selo de Notificação</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Ponto</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Caractere</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotos</string>
    <string name="bottom_navigation_menu_item_news">Notícias</string>
    <string name="bottom_navigation_menu_item_alerts">Alertas</string>
    <string name="bottom_navigation_menu_item_calendar">Calendário</string>
    <string name="bottom_navigation_menu_item_team">Equipe</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Alternar rótulos</string>
    <string name="bottom_navigation_three_menu_items_button">Mostrar três itens de menu</string>
    <string name="bottom_navigation_four_menu_items_button">Mostrar quatro itens de menu</string>
    <string name="bottom_navigation_five_menu_items_button">Mostrar cinco itens de menu</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Os rótulos são %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Habilitar Deslizar para Baixo para Descartar</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Mostrar com itens de linha única</string>
    <string name="bottom_sheet_with_double_line_items">Mostrar com itens de linha dupla</string>
    <string name="bottom_sheet_with_single_line_header">Mostrar com cabeçalho de linha única</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Mostrar com cabeçalho de linha dupla e divisores</string>
    <string name="bottom_sheet_dialog_button">Mostrar</string>
    <string name="drawer_content_desc_collapse_state">Expandir</string>
    <string name="drawer_content_desc_expand_state">Minimizar</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Clicar em %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Clique Longo %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Clicar em ignorar</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Inserir Item</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Atualizar Item</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Ignorar</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Adicionar</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Mencionar</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Negrito</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Itálico</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Sublinhar</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Tachado</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Desfazer</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Refazer</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Marcador</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Vincular</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Atualizando o Item</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Espaçamento</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Ignorar Posição</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">INICIAR</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">FIM</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Espaço entre grupos</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Espaço do item</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Sinalizador</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Sinalizar item clicado</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Responder</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Item de resposta clicado</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Encaminhar</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Encaminhar item clicado</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Excluir</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Excluir item clicado</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Câmera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Tirar uma foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Item da câmera clicado</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galeria</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Exibir suas fotos</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Item da galeria clicado</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Vídeos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Reproduzir seus vídeos</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Item de vídeos clicado</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Gerenciar</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Gerenciar sua biblioteca de mídia</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Gerenciar item clicado</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Ações de Email</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documentos</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Última atualização às 14h14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Compartilhar</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Item de compartilhamento clicado</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Mover</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Mover item clicado</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Excluir</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Excluir item clicado</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informações</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Item de informações clicado</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Relógio</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Item de relógio clicado</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarme</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Item de alarme clicado</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Fuso horário</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Item de fuso horário clicado</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Diferentes visualizações do botão</string>
    <string name="button">Botão</string>
    <string name="buttonbar">Barra de Botões</string>
    <string name="button_disabled">Exemplo de Botão Desabilitado</string>
    <string name="button_borderless">Exemplo de Botão Sem Borda</string>
    <string name="button_borderless_disabled">Exemplo de Botão Desabilitado Sem Borda</string>
    <string name="button_large">Exemplo de Botão Grande</string>
    <string name="button_large_disabled">Exemplo de Botão Grande Desabilitado</string>
    <string name="button_outlined">Exemplo de Botão Delineado</string>
    <string name="button_outlined_disabled">Exemplo de Botão Desabilitado Descrito</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Escolher uma data</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Data Única</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nenhuma data selecionada</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Mostrar seletor de data</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Mostrar seletor de data e hora com guia de data selecionada</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Mostrar seletor de data e hora com guia de hora selecionada</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Mostrar seletor de data e hora</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Intervalo de Datas</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Início:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Término:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nenhuma data de início selecionada</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Nenhuma final selecionada</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Selecionar data de início</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Selecionar data de término</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Intervalo de Data e Hora</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Selecionar intervalo de data e hora</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Mostrar Caixa de Diálogo</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Mostrar gaveta</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Mostrar caixa de diálogo da gaveta</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Nenhuma caixa de diálogo de esmaecimento inferior</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Mostrar gaveta superior</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Sem caixa de diálogo de esmaecimento superior</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Mostrar caixa de diálogo superior do modo de exibição de âncora</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Não mostrar caixa de diálogo superior de título</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Mostrar a caixa de diálogo superior do título abaixo</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Mostrar gaveta direita</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Mostrar gaveta esquerda</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Título, texto principal</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtítulo, texto secundário</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Texto do subtítulo personalizado</string>
    <!-- Footer -->
    <string name="list_item_footer">Rodapé, texto terciário</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Lista de linha única com texto de subcabeçalho cinza</string>
    <string name="list_item_sub_header_two_line">Lista de duas linhas</string>
    <string name="list_item_sub_header_two_line_dense">Lista de duas linhas com espaçamento denso</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Lista de duas linhas com modo de exibição de subtítulo secundário personalizado</string>
    <string name="list_item_sub_header_three_line">Lista de três linhas com texto de subcabeçalho preto</string>
    <string name="list_item_sub_header_no_custom_views">Listar itens sem exibições personalizadas</string>
    <string name="list_item_sub_header_large_header">Listar itens com exibições personalizadas grandes</string>
    <string name="list_item_sub_header_wrapped_text">Listar itens com texto encapsulado</string>
    <string name="list_item_sub_header_truncated_text">Listar itens com texto truncado</string>
    <string name="list_item_sub_header_custom_accessory_text">Ação</string>
    <string name="list_item_truncation_middle">Truncamento no meio.</string>
    <string name="list_item_truncation_end">Encerrar truncamento.</string>
    <string name="list_item_truncation_start">Iniciar truncamento.</string>
    <string name="list_item_custom_text_view">Valor</string>
    <string name="list_item_click">Você clicou no item de lista.</string>
    <string name="list_item_click_custom_accessory_view">Você clicou no modo de exibição acessório personalizado.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Você clicou no modo de exibição acessório personalizado do sub-cabeçalho.</string>
    <string name="list_item_more_options">Mais opções</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Selecionar</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Nenhum</string>
    <string name="people_picker_delete_example">Excluir</string>
    <string name="people_picker_custom_persona_description">Este exemplo mostra como criar um objeto IPersona personalizado.</string>
    <string name="people_picker_dialog_title_removed">Você removeu uma persona:</string>
    <string name="people_picker_dialog_title_added">Você adicionou uma persona:</string>
    <string name="people_picker_drag_started">Arrastar iniciado</string>
    <string name="people_picker_drag_ended">Arrastar finalizado</string>
    <string name="people_picker_picked_personas_listener">Ouvinte de Personas</string>
    <string name="people_picker_suggestions_listener">Ouvinte de Sugestões</string>
    <string name="people_picker_persona_chip_click">Você clicou em %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s destinatário</item>
        <item quantity="many">%1$s destinatários</item>
        <item quantity="other">%1$s destinatários</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Expandir BottomSheet Persistente</string>
    <string name="collapse_persistent_sheet_button"> Ocultar BottomSheet Persistente</string>
    <string name="show_persistent_sheet_button"> Mostrar BottomSheet Persistente</string>
    <string name="new_view">Este é o Novo Modo de Exibição</string>
    <string name="toggle_sheet_content">Ativar/Desativar Conteúdo de Bottomsheet</string>
    <string name="switch_to_custom_content">Alternar para Conteúdo personalizado</string>
    <string name="one_line_content">Conteúdo do Bottomsheet de uma linha</string>
    <string name="toggle_disable_all_items">Ativar/Desativar Desabilitar Todos os Itens</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Adicionar/Remover Modo de Exibição</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Alterar Altura Recolhida</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nova Pasta</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Novo Item de pasta clicado</string>
    <string name="persistent_sheet_item_edit_title">Editar</string>
    <string name="persistent_sheet_item_edit_toast">Editar item clicado</string>
    <string name="persistent_sheet_item_save_title">Salvar</string>
    <string name="persistent_sheet_item_save_toast">Salvar item clicado</string>
    <string name="persistent_sheet_item_zoom_in_title">Ampliar</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Item Ampliar clicado</string>
    <string name="persistent_sheet_item_zoom_out_title">Reduzir</string>
    <string name="persistent_sheet_item_zoom_out_toast">Item Reduzir clicado</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Disponível</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Mateus Rodrigues</string>
    <string name="persona_name_kat_larsson">Sara Melo</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Carlos Lima</string>
    <string name="persona_name_kristen_patterson">Marina Azevedo</string>
    <string name="persona_name_lydia_bauer">Laura Cunha</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Matheus Silva</string>
    <string name="persona_name_mona_kane">Cecil Lima</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Engenheiro</string>
    <string name="persona_subtitle_manager">Gerente</string>
    <string name="persona_subtitle_researcher">Pesquisador</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (exemplo de texto longo para testar truncamento)</string>
    <string name="persona_view_description_xxlarge">Avatar XXLarge com três linhas de texto</string>
    <string name="persona_view_description_large">Avatar grande com duas linhas de texto</string>
    <string name="persona_view_description_small">Avatar pequeno com uma linha de texto</string>
    <string name="people_picker_hint">Nenhum com dica mostrada</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Chip de Persona Desabilitado</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Erro no Chip de Persona</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Chip de Persona sem ícone de fechamento</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Chip de Persona Básico</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Você clicou em um Chip de Persona selecionado.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Compartilhar</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Seguir</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Convidar pessoas</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Atualizar página</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Abrir no navegador</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Este é um Menu Pop-up multilinha. O número máximo de linhas está definido como dois. O restante do texto será truncado.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Todas as notícias</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Notícias salvas</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Notícias de sites</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Notificar fora do horário de trabalho</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notificar quando estiver inativo na área de trabalho</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Você clicou no item:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menu simples</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menu2 simples</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu com um item selecionável e um divisor</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu com todos os itens selecionáveis, ícones e texto longo</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Mostrar</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Progresso Circular</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Pequeno</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Médio</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Grande</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Progresso Linear</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Indeterminado</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinado</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Retorno de Chamada do Microfone</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autocorreção</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Microfone Pressionado</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Modo de Exibição à Direita Pressionado</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Pesquisa de Teclado Pressionada</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Mostrar Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Ignorar Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Ação</string>
    <string name="snackbar_action_long">Ação de Texto Longo</string>
    <string name="snackbar_single_line">Snackbar de linha única</string>
    <string name="snackbar_multiline">Esta é uma barra de lanches multilinha. O número máximo de linhas está definido como dois. O restante do texto será truncado.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Esta é uma barra de lanches de anúncio. É usado para comunicar novos recursos.</string>
    <string name="snackbar_primary">Esta é uma snackbar primária.</string>
    <string name="snackbar_light">Esta é uma snackbar clara.</string>
    <string name="snackbar_warning">Este é um snackbar de aviso.</string>
    <string name="snackbar_danger">Este é um snackbar de perigo.</string>
    <string name="snackbar_description_single_line">Duração curta</string>
    <string name="snackbar_description_single_line_custom_view">Duração longa com progresso circular como exibição personalizada pequena</string>
    <string name="snackbar_description_single_line_action">Duração curta com ação</string>
    <string name="snackbar_description_single_line_action_custom_view">Duração curta com ação e exibição personalizada média</string>
    <string name="snackbar_description_single_line_custom_text_color">Duração curta com cor de texto personalizada</string>
    <string name="snackbar_description_multiline">Duração longa</string>
    <string name="snackbar_description_multiline_custom_view">Duração longa com exibição personalizada pequena</string>
    <string name="snackbar_description_multiline_action">Duração indefinida com atualizações de ação e texto</string>
    <string name="snackbar_description_multiline_action_custom_view">Duração curta com ação e exibição personalizada média</string>
    <string name="snackbar_description_multiline_action_long">Duração curta com texto de ação longa</string>
    <string name="snackbar_description_announcement">Duração curta</string>
    <string name="snackbar_description_updated">Este texto foi atualizado.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Mostrar Snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Linha única</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Multilinha</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Estilo do comunicado</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Estilo principal</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Estilo claro</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Estilo de aviso</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Estilo de perigo</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Início</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Email</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Configurações</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notificação</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mais</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Alinhamento do Texto</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertical</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Sem Texto</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Itens da Guia</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Título</string>
    <string name="cell_sample_description">Descrição</string>
    <string name="calculate_cells">Carregar/calcular 100 células</string>
    <string name="calculate_layouts">Carregar/calcular 100 layouts</string>
    <string name="template_list">Lista de Modelos</string>
    <string name="regular_list">Lista Regular</string>
    <string name="cell_example_title">Título: Célula</string>
    <string name="cell_example_description">Descrição: Toque para alterar a orientação</string>
    <string name="vertical_layout">Disposição Vertical</string>
    <string name="horizontal_layout">Disposição Horizontal</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Guia Padrão de 2 Segmentos</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Guia Padrão de 3 Segmentos</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Guia Padrão de 4 Segmentos</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Guia Padrão com Pager</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Alternar Guia</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Guia de pílulas</string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Toque para Dica de Ferramenta</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Toque para Dica de Ferramenta de Calendário Personalizado</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Toque em Dica de Ferramenta de Cor Personalizada</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Toque para Ignorar Dica de Ferramenta Interna</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Toque para Dica de Ferramenta de Exibição Personalizada</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Dica de Ferramenta de Cor Personalizada Superior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Dica de ferramenta na parte superior com 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Dica de Ferramenta de Início Inferior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Dica de ferramenta na parte inferior com 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Ignorar Dica de Ferramenta Interna</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Dica de ferramenta ignorada</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">O título é Light 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">O título 1 é Médio 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">O título 2 é Regular 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">O título é Regular 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">O subtítulo 1 é Regular 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">O subtítulo 2 é Médio 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">O corpo 1 é Regular 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">O corpo 2 é Médio 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">A legenda é Regular 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versão do SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Item %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Pasta</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Clicado</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Andaime</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB Expandido</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB Recolhido</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Clique para atualizar a lista</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Abrir Gaveta</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Item de Menu</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Deslocamento X (em dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Deslocamento Y (em dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Texto do Conteúdo</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Repetir Texto do Conteúdo</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">A largura do menu será alterada em relação ao Texto do Conteúdo. A largura
        máxima é restrita a 75% do tamanho da tela. A margem de conteúdo do lado e da parte inferior é governada por token. O mesmo texto de conteúdo será repetido para variar
        a altura.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Menu Abrir</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Cartão Básico</string>
    <!-- UI Label for Card -->
    <string name="file_card">Cartão de arquivo</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Cartão de Anúncio</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Interface do usuário aleatória</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opções</string>
    <!-- UI Label for Title -->
    <string name="card_title">Título</string>
    <!-- UI Label for text -->
    <string name="card_text">Texto</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Sub-texto</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">A cópia secundária dessa faixa pode ser encapsulada em duas linhas, se necessário.</string>
    <!-- UI Label Button -->
    <string name="card_button">Botão</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Mostrar Caixa de Diálogo</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Ignorar caixa de diálogo ao clicar fora</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Ignorar caixa de diálogo ao pressionar voltar</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Caixa de diálogo ignorada</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Cancelar</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Uma caixa de diálogo é uma pequena janela que solicita que o usuário tome uma decisão ou insira informações adicionais.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Abrir Gaveta</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Expandir Gaveta</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Fechar Gaveta</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Selecionar Tipo de Gaveta</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Superior</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">A gaveta inteira é exibida na região visível.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Inferior</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Gaveta inteira mostra na região visível. Deslize para cima o conteúdo de rolagem de movimento. Gaveta expansível expandida via alça de arrastar.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Deslizar para a Esquerda</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Slide da gaveta para a região visível do lado esquerdo.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Deslizar para a Direita</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Slide da gaveta para a região visível do lado direito.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Deslizamento Inferior</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Deslize a gaveta para a região visível na parte inferior da tela. Deslize o movimento para cima na gaveta expansível, traga o restante da peça para a região visível e em seguida, role.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Tela Visível</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Selecionar Conteúdo da Gaveta</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Conteúdo rolável de tamanho de tela inteira</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mais de metade do conteúdo da tela</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Menos de metade do conteúdo da tela</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Conteúdo de tamanho dinâmico</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Conteúdo da Gaveta Aninhada</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Expansível</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Ignorar Estado Aberto</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Impedir Descarte no Clique de Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Mostrar Identificador</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Título</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Texto da Dica de Ferramenta</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Toque para dica de ferramenta de conteúdo personalizado</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Início Superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Extremidade Superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Início Inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Extremidade Inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centralizar </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Personalizar Centro</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Para atualizações nas Notas de Versão, </string>
    <string name="click_here">clique aqui.</string>
    <string name="open_source_cross_platform">Sistema de Design multiplataforma de código aberto.</string>
    <string name="intuitive_and_powerful">Intuitivo e Poderoso.</string>
    <string name="design_tokens">Tokens de Design</string>
    <string name="release_notes">Notas de Versão</string>
    <string name="github_repo">Repositório GitHub</string>
    <string name="github_repo_link">Link do Repositório do GitHub</string>
    <string name="report_issue">Relatar Problema</string>
    <string name="v1_components">Componentes V1</string>
    <string name="v2_components">Componentes V2</string>
    <string name="all_components">Tudo</string>
    <string name="fluent_logo">Logotipo do Fluent</string>
    <string name="new_badge">Novo</string>
    <string name="modified_badge">Modificado</string>
    <string name="api_break_badge">Quebra de API</string>
    <string name="app_bar_more">Mais</string>
    <string name="accent">Sotaque</string>
    <string name="appearance">Aparência</string>
    <string name="choose_brand_theme">Escolha o tema da sua marca:</string>
    <string name="fluent_brand_theme">Marca Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Escolher Aparência</string>
    <string name="appearance_system_default">Padrão do Sistema</string>
    <string name="appearance_light">Claro</string>
    <string name="appearance_dark">Escuro</string>
    <string name="demo_activity_github_link">Link do GitHub da Atividade de Demonstração</string>
    <string name="control_tokens_details">Detalhes de Tokens de Controle</string>
    <string name="parameters">Parâmetros</string>
    <string name="control_tokens">Tokens de Controle</string>
    <string name="global_tokens">Tokens Globais</string>
    <string name="alias_tokens">Tokens de Alias</string>
    <string name="sample_text">Texto</string>
    <string name="sample_icon">Ícone de Exemplo</string>
    <string name="color">Cor</string>
    <string name="neutral_color_tokens">Tokens de Cores Neutras</string>
    <string name="font_size_tokens">Tokens de Tamanho da Fonte</string>
    <string name="line_height_tokens">Tokens de Altura da Linha</string>
    <string name="font_weight_tokens">Tokens de Espessura da Fonte</string>
    <string name="icon_size_tokens">Tokens de Tamanho do Ícone</string>
    <string name="size_tokens">Tokens de Tamanho</string>
    <string name="shadow_tokens">Tokens de Sombra</string>
    <string name="corner_radius_tokens">Tokens de Raio de Canto</string>
    <string name="stroke_width_tokens">Tokens de Largura do Traço</string>
    <string name="brand_color_tokens">Tokens de Cor da Marca</string>
    <string name="neutral_background_color_tokens">Tokens de Cor da Tela de Fundo Neutra</string>
    <string name="neutral_foreground_color_tokens">Tokens de Cor de Primeiro Plano Neutra</string>
    <string name="neutral_stroke_color_tokens">Tokens de Cor do Traço Neutro</string>
    <string name="brand_background_color_tokens">Tokens de Cor da Tela de Fundo da Marca</string>
    <string name="brand_foreground_color_tokens">Tokens de Cor de Primeiro Plano da Marca</string>
    <string name="brand_stroke_color_tokens">Tokens de Cor do Traço da Marca</string>
    <string name="error_and_status_color_tokens">Tokens de Cor de Erro e Status</string>
    <string name="presence_tokens">Tokens de Cor de Presença</string>
    <string name="typography_tokens">Tokens de Tipografia</string>
    <string name="unspecified">Não especificado</string>

</resources>