<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!--
        All semantic colors should be defined here in their light theme state as the default.
        If they have a different semantic color for dark theme, define that color in "themes.xml (night)" as well.
    -->
    <style name="Theme.FluentUI.Calendar.Base" parent="Base.Theme.FluentUI">

        <!--CalendarView-->
        <item name="fluentuiCalendarBackgroundColor">?attr/fluentuiBackgroundColor</item>
        <item name="fluentuiCalendarWeekHeadingBackgroundColor">?attr/fluentuiBackgroundColor</item>
        <item name="fluentuiCalendarWeekHeadingWeekDayTextColor">?attr/fluentuiForegroundSecondaryColor</item>
        <item name="fluentuiCalendarWeekHeadingWeekendTextColor">@color/fluentui_gray_400</item>
        <item name="fluentuiCalendarMonthOverlayBackgroundColor">?attr/fluentuiBackgroundColor</item>
        <item name="fluentuiCalendarMonthOverlayTextColor">?attr/fluentuiForegroundColor</item>
        <item name="fluentuiCalendarOtherMonthBackgroundColor">@color/fluentui_gray_25</item>
        <item name="fluentuiCalendarSelectedColor">?attr/fluentuiForegroundSelectedColor</item>
        <item name="fluentuiCalendarDayTodayBackgroundColor">?attr/fluentuiColorPrimaryLighter</item>

        <!--day selector-->
        <item name="fluentuiCalendarDayTextActiveColor">?attr/fluentuiForegroundSelectedColor</item>
        <item name="fluentuiCalendarDayTextActiveCheckedColor">?attr/fluentuiForegroundOnPrimaryColor</item>
        <item name="fluentuiCalendarDayTextInactiveCheckedColor">?attr/fluentuiForegroundOnPrimaryColor</item>
        <item name="fluentuiCalendarDayTextDefaultColor">?attr/fluentuiForegroundSecondaryColor</item>
        <item name="fluentuiCalendarDayKeyboardFocusTextColor">@color/fluentui_gray_600</item>

        <!--DateTimePicker-->
        <item name="fluentuiDateTimePickerToolbarTitleTextColor">?attr/colorPrimary</item>
        <item name="fluentuiDateTimePickerTabTextColor">?attr/fluentuiForegroundSecondaryColor</item>
        <item name="fluentuiDateTimePickerDialogBackgroundColor">?attr/fluentuiDialogBackgroundColor</item>
        <item name="fluentuiDateTimePickerToolbarIconColor">?attr/colorPrimary</item>

        <!--Dialog-->
        <item name="fluentuiDialogBackgroundColor">?attr/fluentuiBackgroundColor</item>
        <item name="fluentuiDialogCloseIconColor">?attr/colorPrimary</item>
        <item name="fluentuiDialogTabLayoutBackgroundColor">?attr/fluentuiDialogBackgroundColor</item>


        <!--NumberPicker-->
        <item name="fluentuiNumberPickerBackgroundColor">?attr/fluentuiBackgroundColor</item>
        <item name="fluentuiNumberPickerDefaultTextColor">?attr/fluentuiForegroundSecondaryColor</item>
        <item name="fluentuiNumberPickerSelectedTextColor">?attr/fluentuiForegroundSelectedColor</item>

        <!--TabLayout Dialog-->
        <item name="fluentuiDialogTabTextColor">?attr/fluentuiForegroundSecondaryColor</item>
        <item name="fluentuiDialogTabSelectedTextColor">?attr/fluentuiForegroundSelectedColor</item>
        <item name="fluentuiDialogTabIndicatorColor">?attr/fluentuiForegroundSelectedColor</item>

    </style>

    <style name="Theme.FluentUI.Calendar" parent="Theme.FluentUI.Calendar.Base"/>

</resources>