<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI ಡೆಮೋ</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s ಆಯ್ಕೆಮಾಡಲಾಗಿದೆ</string>
    <string name="app_modifiable_parameters">ಮಾರ್ಪಡಿಸಬಹುದಾದ ಪ್ಯಾರಾಮೀಟರ್‌ಗಳು</string>
    <string name="app_right_accessory_view">ಬಲ ಉಪಕರಣ ವೀಕ್ಷಣೆ</string>

    <string name="app_style">ಶೈಲಿ</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">ಐಕಾನ್ ಒತ್ತಲಾಗಿದೆ</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">ಡೆಮೋ ಪ್ರಾರಂಭಿಸಿ</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">ಕರೋಸಲ್</string>
    <string name="actionbar_icon_radio_label">ಐಕಾನ್</string>
    <string name="actionbar_basic_radio_label">ಸಾಮಾನ್ಯ</string>
    <string name="actionbar_position_bottom_radio_label">ಕೆಳಗಡೆ</string>
    <string name="actionbar_position_top_radio_label">ಮೇಲೆ</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">ActionBar ಪ್ರಕಾರ</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar ಸ್ಥಾನ</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar ಶೈಲಿ</string>
    <string name="app_bar_subtitle">ಉಪಶೀರ್ಷಿಕೆ</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">ಕೆಳಗಿನ ಅಂಚುಕಟ್ಟು</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">ನ್ಯಾವಿಗೇಶನ್ ಐಕಾನ್ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">ಫ್ಲ್ಯಾಗ್</string>
    <string name="app_bar_layout_menu_settings">ಸೆಟ್ಟಿಂಗ್‌ಗಳು</string>
    <string name="app_bar_layout_menu_search">ಶೋಧಿಸಿ</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">ನಡವಳಿಕೆಯನ್ನು ಸ್ಕ್ರಾಲ್ ಮಾಡಿ: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">ಸ್ಕ್ರಾಲ್ ನಡವಳಿಕೆಯನ್ನು ಟಾಗಲ್ ಮಾಡಿ</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">ನ್ಯಾವಿಗೇಶನ್ ಐಕಾನ್ ಟಾಗಲ್ ಮಾಡಿ</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">ಅವತಾರ್ ತೋರಿಸಿ</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">ಹಿಂದಿನ ಐಕಾನ್ ತೋರಿಸಿ</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">ಐಕಾನ್ ಮರೆಮಾಡಿ</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">ಐಕಾನ್ ತೋರಿಸಿ</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">ಶೋಧಪಟ್ಟಿ ಲೇಔಟ್ ಶೈಲಿಯನ್ನು ಟಾಗಲ್ ಮಾಡಿ</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">ಉಪಕರಣ ವೀಕ್ಷಣೆಯಾಗಿ ತೋರಿಸಿ</string>
    <string name="app_bar_layout_searchbar_action_view_button">ಕ್ರಿಯೆ ವೀಕ್ಷಣೆಯಾಗಿ ತೋರಿಸಿ</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">ಥೀಮ್‌ಗಳ ನಡುವೆ ಟಾಗಲ್ ಮಾಡಿ (ಚಟುವಟಿಕೆಯನ್ನು ಮರುಸೃಷ್ಟಿಸುತ್ತದೆ)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">ಥೀಮ್ ಟಾಗಲ್ ಮಾಡಿ</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">ಐಟಂ</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">ಹೆಚ್ಚಿನ ಸ್ಕ್ರೋಲ್ ಮಾಡಿದ ವಿಷಯ</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">ವೃತ್ತಾಕರದ ಶೈಲಿಗಳು</string>
    <string name="avatar_style_square">ಚೌಕ ಶೈಲಿ</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">ದೊಡ್ಡದು</string>
    <string name="avatar_size_medium">ಮಧ್ಯಮ</string>
    <string name="avatar_size_small">ಚಿಕ್ಕದು</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">ದುಪ್ಪಟ್ಟು ಹೆಚ್ಚು ದೊಡ್ಡ</string>
    <string name="avatar_size_xlarge_accessibility">ಹೆಚ್ಚು ದೊಡ್ಡ</string>
    <string name="avatar_size_xsmall_accessibility">ತುಂಬ ಚಿಕ್ಕ</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">ಅವತಾರ್ ಪ್ರದರ್ಶಿಸಿರುವ ಗರಿಷ್ಠ</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">ಅವತಾರ್ ಎಣಿಕೆ ಓವರ್ ಫ್ಲೋ</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">ಅಂಚು ಪ್ರಕಾರ</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">‌OverflowAvatarCount ಸೆಟ್ ಹೊಂದಿರುವ ಅವತಾರ್ ಗುಂಪು ಗರಿಷ್ಠ ಪ್ರದರ್ಶಿತ ಅವತಾರಕ್ಕೆ ಬದ್ಧವಾಗಿರುವುದಿಲ್ಲ.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">ಮುಖ ಸ್ಟ್ಯಾಕ್</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">ಮುಖ ಪೈಲ್</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">ಓವರ್‌ಫ್ಲೋ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">ಪದಸೂಚಿ %d ನಲ್ಲಿ AvatarView ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">ಅಧಿಸೂಚನೆ ಬ್ಯಾಡ್ಜ್</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">ಡಾಟ್</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">ಪಟ್ಟಿ</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">ಅಕ್ಷರ</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">ಫೋಟೋಗಳು</string>
    <string name="bottom_navigation_menu_item_news">ಸುದ್ದಿ</string>
    <string name="bottom_navigation_menu_item_alerts">ಎಚ್ಚರಿಕೆಗಳು</string>
    <string name="bottom_navigation_menu_item_calendar">ಕ್ಯಾಲೆಂಡರ್</string>
    <string name="bottom_navigation_menu_item_team">ತಂಡ</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">ಲೇಬಲ್‌ಗಳನ್ನು ಟಾಗಲ್ ಮಾಡಿ</string>
    <string name="bottom_navigation_three_menu_items_button">ಮೂರು ಮೆನು ಐಟಂಗಳನ್ನು ತೋರಿಸಿ</string>
    <string name="bottom_navigation_four_menu_items_button">ನಾಲ್ಕು ಮೆನು ಐಟಂಗಳನ್ನು ತೋರಿಸಿ</string>
    <string name="bottom_navigation_five_menu_items_button">ಐದು ಮೆನು ಐಟಂಗಳನ್ನು ತೋರಿಸಿ</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">ಲೇಬಲ್‌ಗಳು %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">ವಜಾಗೊಳಿಸಲು ಕೆಳಗೆ ಸ್ವೈಪ್ ಮಾಡಿ ಸಕ್ರಿಯಗೊಳಿಸಿ</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">ಏಕ ಸಾಲು ಐಟಂಗಳನ್ನು ತೋರಿಸಿ</string>
    <string name="bottom_sheet_with_double_line_items">ಎರಡು ಸಾಲು ಐಟಂಗಳೊಂದಿಗೆ ತೋರಿಸಿ</string>
    <string name="bottom_sheet_with_single_line_header">ಏಕ ಸಾಲು ಶಿರೋಲೇಖವನ್ನು ತೋರಿಸಿ</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">ಎರಡು ಸಾಲಿನ ಶಿರೋಲೇಖ ಮತ್ತು ಡಿವೈಡರ್‌ಗಳೊಂದಿಗೆ ತೋರಿಸಿ</string>
    <string name="bottom_sheet_dialog_button">ತೋರಿಸಿ</string>
    <string name="drawer_content_desc_collapse_state">ವಿಸ್ತರಿಸಿ</string>
    <string name="drawer_content_desc_expand_state">ಚಿಕ್ಕದಾಗಿಸಿ</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s ಕ್ಲಿಕ್ ಮಾಡಿ</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">%s ದೀರ್ಘ ಕ್ಲಿಕ್ ಮಾಡಿ</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">ವಜಾಗೊಳಿಸಿ ಕ್ಲಿಕ್ ಮಾಡಿ</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">ಐಟಂ ಸೇರಿಸಿ</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">ಐಟಂ ಅನ್ನು ನವೀಕರಿಸಿ</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">ವಜಾಗೊಳಿಸಿ</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">ಸೇರಿಸಿ</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">ಉಲ್ಲೇಖ</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">ಬೋಲ್ಡ್</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">ಇಟಾಲಿಕ್</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">ಅಡಿಗೆರೆ</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">ತೊಡೆದುಹಾಕಿ</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">ರದ್ದುಮಾಡಿ</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">ಮತ್ತೆ ಮಾಡಿ</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">ಬುಲೆಟ್</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">ಪಟ್ಟಿ</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">ಲಿಂಕ್</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">ಐಟಂ ಅನ್ನು ನವೀಕರಿಸಲಾಗುತ್ತಿದೆ</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">ಅಂತರಿಸುವಿಕೆ</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">ಸ್ಥಾನ ವಜಾಗೊಳಿಸಿ</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">ಪ್ರಾರಂಭಿಸಿ</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">ಮುಕ್ತಾಯ</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">ಗುಂಪು ಸ್ಥಳ</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">ಐಟಂನ ಸ್ಥಳ</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">ಫ್ಲ್ಯಾಗ್</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">ಫ್ಲ್ಯಾಗ್ ಐಟಂ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">ಪ್ರತ್ಯುತ್ತರಿಸಿ</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">ಪ್ರತ್ಯುತ್ತರ ಐಟಂ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">ಫಾರ್ವರ್ಡ್</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">ಮುನ್ನೋಟ ಐಟಂ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">ಅಳಿಸಿ</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">ಕ್ಲಿಕ್ ಮಾಡಲಾದ ಐಟಂ ಅಳಿಸಿ</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">ಅವತಾರ್</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">ಕ್ಯಾಮರಾ</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">ಫೋಟೋ ತೆಗೆಯಿರಿ</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">ಕ್ಯಾಮರಾ ಐಟಂ ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">ಗ್ಯಾಲರಿ</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">ನಿಮ್ಮ ಫೋಟೋಗಳನ್ನು ವೀಕ್ಷಿಸಿ</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">ಗ್ಯಾಲರಿ ಐಟಂ ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">ವೀಡಿಯೋಗಳು</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">ನಿಮ್ಮ ವೀಡಿಯೋಗಳನ್ನು ಪ್ಲೇ ಮಾಡಿ</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">ವೀಡಿಯೋಗಳ ಐಟಂ ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">ನಿರ್ವಹಿಸಿ</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">ನಿಮ್ಮ ಮಾಧ್ಯಮ ಲೈಬ್ರರಿ ಬಳಸಿ</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">ಕ್ಲಿಕ್ ಮಾಡಲಾದ ಐಟಂ ನಿರ್ವಹಿಸಿ</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">ಇಮೇಲ್ ಕ್ರಿಯೆಗಳು</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">ದಾಖಲೆಗಳು</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">ಕೊನೆಯದಾಗಿ ನವೀಕರಿಸಿರುವುದು 2:14 PM</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">ಹಂಚಿಕೊಳ್ಳಿ</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">ಹಂಚಿಕೊಳ್ಳಬಹುದಾದ ಐಟಂ ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಿ</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">ಸರಿಸಿ</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">ಕ್ಲಿಕ್ ಮಾಡಲಾದ ಐಟಂ ಸರಿಸಿ</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">ಅಳಿಸಿ</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">ಕ್ಲಿಕ್ ಮಾಡಲಾದ ಐಟಂ ಅಳಿಸಿ</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">ಮಾಹಿತಿ</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">ಮಾಹಿತಿ ಐಟಂ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">ಗಡಿಯಾರ</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">ಗಡಿಯಾರ ಐಟಂ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">ಅಲಾರಂ</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">ಅಲಾರಂ ಐಟಂ ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">ಸಮಯ ವಲಯ</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">ಸಮಯ ವಲಯ ಐಟಂ ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">ಬಟನ್‌ನ ವಿಭಿನ್ನ ವೀಕ್ಷಣೆಗಳು</string>
    <string name="button">ಬಟನ್</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">ನಿಷ್ಕ್ರಿಯಗೊಳಿಸಲಾಗಿದೆ ಬಟನ್ ಉದಾಹರಣೆ</string>
    <string name="button_borderless">ಅಂಚುರಹಿತ ಬಟನ್ ಉದಾಹರಣೆ</string>
    <string name="button_borderless_disabled">ಅಂಚುರಹಿತ ನಿಷ್ಕ್ರಿಯಗೊಳಿಸಲಾಗಿದೆ ಬಟನ್‌ ಉದಾಹರಣೆ</string>
    <string name="button_large">ದೊಡ್ಡ ಬಟನ್ ಉದಾಹರಣೆ</string>
    <string name="button_large_disabled">ದೊಡ್ಡದು ನಿಷ್ಕ್ರಿಯಗೊಳಿಸಲಾಗಿದೆ ಬಟನ್ ಉದಾಹರಣೆ</string>
    <string name="button_outlined">ರೂಪುರೇಷೆಗೊಳಿಸಿದ ಬಟನ್ ಉದಾಹರಣೆ</string>
    <string name="button_outlined_disabled">ರೂಪುರೇಷೆಗೊಳಿಸಿದ ನಿಷ್ಕ್ರಿಯಗೊಳಿಸಲಾಗಿದೆ ಬಟನ್ ಉದಾಹಣೆ</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">ದಿನಾಂಕವನ್ನು ಆರಿಸಿ</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">ಏಕ ದಿನಾಂಕ</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">ಯಾವುದೇ ದಿನಾಂಕ ಪಿಕರ್ ಇಲ್ಲ</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">ಡೇಟ್ ಪಿಕ್ಕರ್ ತೋರಿಸಿ</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">ದಿನಾಂಕ ಟ್ಯಾಬ್ ಅನ್ನು ಆಯ್ಕೆ ಮಾಡಿದ ದಿನಾಂಕ ಸಮಯ ಪಿಕ್ಕರ್ ಅನ್ನು ತೋರಿಸಿ</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">ಸಮಯ ಟ್ಯಾಬ್ ಅನ್ನು ಆಯ್ಕೆ ಮಾಡಿದ ದಿನಾಂಕ ಸಮಯ ಪಿಕ್ಕರ್ ಅನ್ನು ತೋರಿಸಿ</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">ದಿನಾಂಕ/ಸಮಯ ಪಿಕ್ಕರ್ ತೋರಿಸಿ</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">ದಿನಾಂಕ ಶ್ರೇಣಿ</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">ಪ್ರಾರಂಭ:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">ಮುಕ್ತಾಯ:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">ಯಾವುದೇ ಪ್ರಾರಂಭ ಆಯ್ಕೆಮಾಡಿಲ್ಲ</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">ಯಾವುದೇ ಅಂತಿಮ ಆಯ್ಕೆಮಾಡಿಲ್ಲ</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">ಪ್ರಾರಂಭ ದಿನಾಂಕ ಆಯ್ಕೆಮಾಡಿ</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">ಅಂತ್ಯ ದಿನಾಂಕ ಆಯ್ಕೆಮಾಡಿ</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">ದಿನಾಂಕ ಸಮಯ ಶ್ರೇಣಿ</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">ದಿನಾಂಕ ಸಮಯ ಶ್ರೇಣಿ ಆಯ್ಕೆಮಾಡಿ</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">ಸಂವಾದ ತೋರಿಸಿ</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">ಡ್ರಾಯರ್ ತೋರಿಸಿ</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">ಡ್ರಾಯರ್ ಸಂವಾದವನ್ನು ತೋರಿಸಿ</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">ಯಾವುದೇ ಫೇಡ್ ಬಾಟಮ್ ಸಂವಾದ ಇಲ್ಲ</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">ಮೇಲ್ಭಾಗದ ಡ್ರಾಯರ್ ತೋರಿಸಿ</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">ಯಾವುದೇ ಫೇಡ್ ಟಾಪ್ ಸಂವಾದ ಇಲ್ಲ</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> ಆಂಕರ್ ವೀಕ್ಷಣೆ ಟಾಪ್ ಸಂವಾದ ತೋರಿಸಿ</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> ಯಾವುದೇ ಶೀರ್ಷಿಕೆ ಮೇಲಿನ ಸಂವಾದವನ್ನು ಪ್ರದರ್ಶಿಸಬೇಡಿ</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> ಶೀರ್ಷಿಕೆ ಟಾಪ್ ಸಂವಾದವನ್ನು ಕೆಳಗೆ ತೋರಿಸಿ</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">ಬಲ ಡ್ರಾಯರ್ ತೋರಿಸಿ</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">ಎಡ ಡ್ರಾಯರ್ ತೋರಿಸಿ</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">ಶೀರ್ಷಿಕೆ, ಪ್ರಾಥಮಿಕ ಪಠ್ಯ</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">ಉಪಶೀರ್ಷಿಕೆ, ಮಾಧ್ಯಮಿಕ ಪಠ್ಯ</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">ಕಸ್ಟಮ್ ಉಪಶೀರ್ಷಿಕೆ ಪಠ್ಯ</string>
    <!-- Footer -->
    <string name="list_item_footer">ಅಡಿಬರಹ, ತೃತೀಯಕ ಪಠ್ಯ</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">ಬೂದು ಉಪ ಶೀರ್ಷಿಕೆ ಪಠ್ಯದೊಂದಿಗೆ ಏಕ-ಸಾಲು ಪಟ್ಟಿ</string>
    <string name="list_item_sub_header_two_line">ಎರಡು-ಸಾಲಿನ ಪಟ್ಟಿ</string>
    <string name="list_item_sub_header_two_line_dense">ದಟ್ಟವಾದ ಅಂತರದೊಂದಿಗೆ ಎರಡು-ಸಾಲಿನ ಪಟ್ಟಿ</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">ಕಸ್ಟಮ್ ದ್ವಿತೀಯ ಉಪಶೀರ್ಷಿಕೆ ವೀಕ್ಷಣೆಯೊಂದಿಗೆ ಎರಡು ಸಾಲಿನ ಪಟ್ಟಿ</string>
    <string name="list_item_sub_header_three_line">ಕಪ್ಪು ಉಪ ಶಿರೋಲೇಖ ಪಠ್ಯದೊಂದಿಗೆ ಮೂರು-ಸಾಲಿನ ಪಟ್ಟಿ</string>
    <string name="list_item_sub_header_no_custom_views">ಯಾವುದೇ ಕಸ್ಟಮ್ ವೀಕ್ಷಣೆಗಳಿಲ್ಲದ ಐಟಂಗಳನ್ನು ಪಟ್ಟಿ ಮಾಡಿ</string>
    <string name="list_item_sub_header_large_header">ದೊಡ್ಡ ಕಸ್ಟಮ್ ವೀಕ್ಷಣೆಗಳೊಂದಿಗೆ ಐಟಂಗಳನ್ನು ಪಟ್ಟಿ ಮಾಡಿ</string>
    <string name="list_item_sub_header_wrapped_text">ಸುತ್ತಿದ ಪಠ್ಯದೊಂದಿಗೆ ಐಟಂಗಳನ್ನು ಪಟ್ಟಿ ಮಾಡಿ</string>
    <string name="list_item_sub_header_truncated_text">ಮೊಟಕುಗೊಳಿಸಿದ ಪಠ್ಯದೊಂದಿಗೆ ಐಟಂಗಳನ್ನು ಪಟ್ಟಿ ಮಾಡಿ</string>
    <string name="list_item_sub_header_custom_accessory_text">ಕ್ರಿಯೆ</string>
    <string name="list_item_truncation_middle">ಮಧ್ಯಮ ಮೊಟಕುಗೊಳಿಸಲಾಗಿದೆ.</string>
    <string name="list_item_truncation_end">ಮೊಟಕುಗೊಳಿಸುವಿಕೆ ಅಂತ್ಯಗೊಳಿಸಿ.</string>
    <string name="list_item_truncation_start">ಮೊಟಕುಗೊಳಿಸುವಿಕೆ ಪ್ರಾರಂಭಿಸಿ.</string>
    <string name="list_item_custom_text_view">ಮೌಲ್ಯ</string>
    <string name="list_item_click">ನೀವು ಪಟ್ಟಿ ಐಟಂ ನಲ್ಲಿ ಕ್ಲಿಕ್ ಮಾಡಿದ್ದೀರಿ.</string>
    <string name="list_item_click_custom_accessory_view">ನೀವು ಕಸ್ಟಮ್ ಪರಿಕರ ವೀಕ್ಷಣೆಯ ಮೇಲೆ ಕ್ಲಿಕ್ ಮಾಡಿದ್ದೀರಿ.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">ನೀವು ಉಪ ಶೀರ್ಷಿಕೆ ಕಸ್ಟಮ್ ಪರಿಕರ ವೀಕ್ಷಣೆಯ ಮೇಲೆ ಕ್ಲಿಕ್ ಮಾಡಿದ್ದೀರಿ.</string>
    <string name="list_item_more_options">ಇನ್ನಷ್ಟು ಆಯ್ಕೆಗಳು</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">ಆಯ್ಕೆಮಾಡಿ</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">ಯಾವುದು ಇಲ್ಲ</string>
    <string name="people_picker_delete_example">ಅಳಿಸಿ</string>
    <string name="people_picker_custom_persona_description">ಈ ಉದಾಹರಣೆಯು ಕಸ್ಟಮ್ IPersona ವಸ್ತುವನ್ನು ಹೇಗೆ ರಚಿಸುವುದು ಎಂಬುದನ್ನು ತೋರಿಸುತ್ತದೆ.</string>
    <string name="people_picker_dialog_title_removed">ನೀವು ಪರ್ಸೋನಾವನ್ನು ತೆಗೆದುಹಾಕಿದ್ದೀರಿ:</string>
    <string name="people_picker_dialog_title_added">ನೀವು ಪರ್ಸೋನಾವನ್ನು ಸೇರಿಸಿದ್ದೀರಿ:</string>
    <string name="people_picker_drag_started">ಡ್ರ್ಯಾಗ್ ಪ್ರಾರಂಭಿಸಲಾಗಿದೆ</string>
    <string name="people_picker_drag_ended">ಡ್ರ್ಯಾಗ್ ಕೊನೆಗೊಂಡಿದೆ</string>
    <string name="people_picker_picked_personas_listener">ವೈಯಕ್ತಕ ಕೇಳುಗ</string>
    <string name="people_picker_suggestions_listener">ಸಲಹೆಗಳ ಕೇಳುಗ</string>
    <string name="people_picker_persona_chip_click">ನೀವು %s ಕ್ಲಿಕ್ ಮಾಡಿದ್ದೀರಿ</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s ಸ್ವೀಕೃತದಾರ</item>
        <item quantity="other">%1$s ಸ್ವೀಕೃತದಾರರು</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">ನಿರಂತರ BottomSheet ಮರೆಮಾಡಿ</string>
    <string name="collapse_persistent_sheet_button"> ನಿರಂತರ BottomSheet ಮರೆಮಾಡಿ</string>
    <string name="show_persistent_sheet_button"> ನಿರಂತರ BottomSheet ತೋರಿಸಿ</string>
    <string name="new_view">ಇದು ಹೊಸ ವೀಕ್ಷಣೆ</string>
    <string name="toggle_sheet_content">ಕೆಳಹಾಳೆ ವಿಷಯ ಟಾಗಲ್ ಮಾಡಿ</string>
    <string name="switch_to_custom_content">ಕಸ್ಟಮ್ ವಿಷಯಕ್ಕೆ ಸ್ವಿಚ್ ಮಾಡಿ</string>
    <string name="one_line_content">ಏಕ ಸಾಲು ಕೆಳಹಾಳೆ ವಿಷಯ</string>
    <string name="toggle_disable_all_items">ಎಲ್ಲಾ ಐಟಂಗಳ ಟಾಗಲ್ ನಿಷ್ಕ್ರಿಯಗೊಳಿಸಿ</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">ವೀಕ್ಷಣೆ ಸೇರಿಸಿ/ತೆಗೆದುಹಾಕಿ</string>
    <string name="persistent_sheet_item_change_collapsed_height"> ಕುಸಿದ ಎತ್ತರವನ್ನು ಬದಲಾಯಿಸಿ</string>
    <string name="persistent_sheet_item_create_new_folder_title">ಹೊಸ ಫೋಲ್ಡರ್</string>
    <string name="persistent_sheet_item_create_new_folder_toast">ಹೊಸ ಫೋಲ್ಡರ್ ಐಟಂ ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಲಾಗಿದೆ</string>
    <string name="persistent_sheet_item_edit_title">ಸಂಪಾದಿಸಿ</string>
    <string name="persistent_sheet_item_edit_toast">ಕ್ಲಿಕ್ ಮಾಡಲಾದ ಐಟಂ ಸಂಪಾದಿಸಿ</string>
    <string name="persistent_sheet_item_save_title">ಉಳಿಸಿ</string>
    <string name="persistent_sheet_item_save_toast">ಕ್ಲಿಕ್ ಮಾಡಲಾದ ಐಟಂ ಉಳಿಸಿ</string>
    <string name="persistent_sheet_item_zoom_in_title">ಝೂಮ್ ಇನ್</string>
    <string name="persistent_sheet_item_zoom_in_toast"> ಕ್ಲಿಕ್ ಮಾಡಿದ ಐಟಂ ಜೂಮ್ ಮಾಡಿ</string>
    <string name="persistent_sheet_item_zoom_out_title">ಝೂಮ್ ಔಟ್</string>
    <string name="persistent_sheet_item_zoom_out_toast">ಕ್ಲಿಕ್ ಮಾಡಲಾದ ಐಟಂ ಜೂಮ್ ಔಟ್ ಮಾಡಿ</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">ಲಭ್ಯ</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">ಆಲನ್ ಮುಂಗರ್</string>
    <string name="persona_name_amanda_brady">ಅಮಾಂಡಾ ಬ್ರ್ಯಾಡಿ</string>
    <string name="persona_name_ashley_mccarthy">ಆಶ್ಲೆ ಮ್ಯಾಕ್‌ಕಾರ್ಥಿ</string>
    <string name="persona_name_carlos_slattery">ಕಾರ್ಲೋಸ್ ಸ್ಲಾಟರಿ</string>
    <string name="persona_name_carole_poland">ಕ್ಯಾರೋಲ್ ಪೋಲ್ಯಾಂಡ್</string>
    <string name="persona_name_cecil_folk">ಸಿಸಿಲ್ ಜಾನಪದ</string>
    <string name="persona_name_celeste_burton">ಸೆಲೆಸ್ಟೆ ಬರ್ಟನ್</string>
    <string name="persona_name_charlotte_waltson">ಷಾರ್ಲೆಟ್ ವಾಲ್ಟ್‌ಸನ್</string>
    <string name="persona_name_colin_ballinger">ಕಾಲಿನ್ ಬ್ಯಾಲಿಂಗರ್</string>
    <string name="persona_name_daisy_phillips">ಡೈಸಿ ಫಿಲಿಪ್ಸ್</string>
    <string name="persona_name_elliot_woodward">ಎಲಿಯಟ್ ವುಡ್‌ವರ್ಡ್</string>
    <string name="persona_name_elvia_atkins">ಎಲ್ವಿಯಾ ಅಟ್ಕಿನ್ಸ್</string>
    <string name="persona_name_erik_nason">ಎರಿಕ್ ನಾಸನ್</string>
    <string name="persona_name_henry_brill">ಹೆನ್ರಿ ಬ್ರಿಲ್</string>
    <string name="persona_name_isaac_fielder">ಐಸಾಕ್ ಫೀಲ್ಡರ್</string>
    <string name="persona_name_johnie_mcconnell">ಜಾನಿ ಮೆಕ್‌ಕ್ಯಾನೆಲ್</string>
    <string name="persona_name_kat_larsson">ಕ್ಯಾಟ್ ಲಾರ್ಸನ್</string>
    <string name="persona_name_katri_ahokas">ಕತ್ರಿ ಅಹೋಕಾಸ್</string>
    <string name="persona_name_kevin_sturgis">ಕೆವಿನ್ ಸ್ಟರ್ಗಿಸ್</string>
    <string name="persona_name_kristen_patterson">ಕ್ರಿಸ್ಟನ್ ಪ್ಯಾಟರ್ಸನ್</string>
    <string name="persona_name_lydia_bauer">ಲಿಡಿಯಾ ಬಾಯರ್</string>
    <string name="persona_name_mauricio_august">ಮಾರಿಸಿಯೋ ಆಗಸ್ಟ್</string>
    <string name="persona_name_miguel_garcia">ಮಿಗುಯೆಲ್ ಗಾರ್ಸಿಯಾ</string>
    <string name="persona_name_mona_kane">ಮೋನಾ ಕೇನ್</string>
    <string name="persona_name_robin_counts">ರಾಬಿನ್ ಕೌಂಟ್ಸ್</string>
    <string name="persona_name_robert_tolbert">ರಾಬರ್ಟ್ ಟೋಲ್ಬರ್ಟ್</string>
    <string name="persona_name_tim_deboer">ಟಿಮ್ ಡೆಬೋಯರ್</string>
    <string name="persona_name_wanda_howard">ವಾಂಡಾ ಹೊವಾರ್ಡ್</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">ವಿನ್ಯಾಸಕ</string>
    <string name="persona_subtitle_engineer">ಇಂಜಿನಿಯರ್</string>
    <string name="persona_subtitle_manager">ವ್ಯವಸ್ಥಾಪಕ</string>
    <string name="persona_subtitle_researcher">ಸಂಶೋಧಕ</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (ಮೊಟಕುಗೊಳಿಸುವುದು ಪರೀಕ್ಷೆಗೆ ದೀರ್ಘ ಪಠ್ಯ ಉದಾಹರಣೆ)</string>
    <string name="persona_view_description_xxlarge">ಪಠ್ಯದ ಮೂರು ಸಾಲುಗಳ ಮೂಲಕ XXLarge ಅವತಾರ್</string>
    <string name="persona_view_description_large">ಎರಡು ಸಾಲುಗಳ ಪಠ್ಯದೊಂದಿಗೆ ದೊಡ್ಡ ಅವತಾರ್</string>
    <string name="persona_view_description_small">ಒಂದು ಸಾಲಿನ ಪಠ್ಯದೊಂದಿಗೆ ಚಿಕ್ಕ ಅವತಾರ್</string>
    <string name="people_picker_hint">ಯಾವುದೇ ಸೂಚನೆಯನ್ನು ತೋರಿಸಲಾಗಿಲ್ಲ</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">ನಿಷ್ಕ್ರಿಯ ಪರ್ಸೋನಾ ಚಿಪ್</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">ದೋಷ ಪರ್ಸೋನಾ ಚಿಪ್</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">ಯಾವುದೇ ಮುಚ್ಚುವ ಐಕಾನ್ ಇಲ್ಲದ ಪರ್ಸೋನ ಚಿಪ್</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">ಬೇಸಿಕ್ ಪರ್ಸೋನಾ ಚಿಪ್</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">ನೀವು ಆಯ್ಕೆಮಾಡಿದ ಪರ್ಸೋನಾ ಚಿಪ್ ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಿದ್ದೀರಿ.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">ಹಂಚಿಕೊಳ್ಳಿ</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">ಅನುಸರಿಸಿ</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">ಜನರನ್ನು ಆಮಂತ್ರಿಸಿ</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">ಪುಟ ತಾಜಾಮಾಡಿ</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">ಬ್ರೌಸರ್‌ನಲ್ಲಿ ತೆರೆಯಿರಿ</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">ಇದು ಬಹುಸಾಲು ಪಾಪಪ್ ಮೆನು ಆಗಿದೆ. ಗರಿಷ್ಠ ರೇಖೆಗಳನ್ನು ಎರಡಕ್ಕೆ ಸೆಟ್ ಮಾಡಲಾಗಿದೆ, ಉಳಿದ ಪಠ್ಯವು ಮೊಟಕುಗೊಳಿಸುತ್ತದೆ.
         Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">ಎಲ್ಲಾ ಸುದ್ದಿ</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">ಉಳಿಸಿದ ಸುದ್ದಿ</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">ಸೈಟ್‌ಗಳಿಂದ ಸುದ್ದಿ</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">ಕೆಲಸದ ಅವಧಿಯ ಹೊರಗೆ ಸೂಚಿಸಿ</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">ಡೆಸ್ಕ್‌ಟಾಪ್‌ನಲ್ಲಿ ನಿಷ್ಕ್ರಿಯವಾಗಿರುವಾಗ ಸೂಚಿಸಿ</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">ನೀವು ಐಟಂ ಕ್ಲಿಕ್ ಮಾಡಿದ್ದೀರಿ:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">ಸರಳ ಮೆನು</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">ಸರಳ ಮೆನು2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">ಒಂದು ಆಯ್ಕೆಮಾಡಬಹುದಾದ ಐಟಂನೊಂದಿಗೆ ಮೆನು ಮತ್ತು ಒಂದು ಡಿವೈಡರ್</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">ಎಲ್ಲಾ ಆಯ್ಕೆಮಾಡಿದ ಐಟಂಗಳು, ಐಕಾನ್‌ಗಳು ಮತ್ತು ಉದ್ದನೆಯ ಪಠ್ಯದೊಂದಿಗೆ ಮೆನು</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">ತೋರಿಸಿ</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">ವೃತ್ತಾಕಾರದ ಪ್ರಗತಿ</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">ಚಿಕ್ಕದು</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">ಮಧ್ಯಮ</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">ದೊಡ್ಡದು</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">ರೇಖೀಯ ಪ್ರಗತಿ</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">ಅನಿರ್ದಿಷ್ಟ</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">ನಿರ್ಧಾರಣೆ</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">ಮೈಕ್ರೋಫೋನ್ ಕಾಲ್‌ಬ್ಯಾಕ್</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">ಸ್ವಯಂಸರಿ...</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">ಮೈಕ್ರೋಫೋನ್ ಒತ್ತಲಾಗಿದೆ</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">ಬಲ ವೀಕ್ಷಣೆ ಒತ್ತಲಾಗಿದೆ</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">ಕೀಬೋರ್ಡ್ ಶೋಧ ಒತ್ತಲಾಗಿದೆ</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">ಸ್ನ್ಯಾಕ್‌ಬಾರ್‌ ತೋರಿಸಿ</string>
    <string name="fluentui_dismiss_snackbar">ಸ್ನ್ಯಾಕ್‌ಬಾರ್‌ ವಜಾಗೊಳಿಸಿ</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">ಕ್ರಿಯೆ</string>
    <string name="snackbar_action_long">ದೀರ್ಘ ಪಠ್ಯ ಕ್ರಿಯೆ</string>
    <string name="snackbar_single_line">ಏಕ ಸಾಲು ಸ್ನ್ಯಾಕ್‌ಪಟ್ಟಿ</string>
    <string name="snackbar_multiline">ಇದು ಬಹುಸಾಲು ಸ್ನ್ಯಾಕ್‌ಬಾರ್ ಆಗಿದೆ. ಗರಿಷ್ಠ ರೇಖೆಗಳನ್ನು ಎರಡಕ್ಕೆ ಸೆಟ್ ಮಾಡಲಾಗಿದೆ, ಉಳಿದ ಪಠ್ಯವು ಮೊಟಕುಗೊಳಿಸುತ್ತದೆ.
         Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">ಇದು ಸ್ನ್ಯಾಕ್‌ಬಾರ್ ಪ್ರಕಟಣೆ. ಹೊಸ ವೈಶಿಷ್ಟ್ಯಗಳನ್ನು ಸಂವಹನ ಮಾಡಲು ಇದನ್ನು ಬಳಸಲಾಗುತ್ತದೆ.</string>
    <string name="snackbar_primary">ಇದು ಪ್ರಾಥಮಿಕ ಸ್ನ್ಯಾಕ್‌ಪಟ್ಟಿ.</string>
    <string name="snackbar_light">ಇದು ಲೈಟ್ ಸ್ನ್ಯಾಕ್‌ಪಟ್ಟಿ ಆಗಿದೆ.</string>
    <string name="snackbar_warning">ಇದು ಎಚ್ಚರಿಕೆಯ ಸ್ನ್ಯಾಕ್‌ಪಟ್ಟಿ.</string>
    <string name="snackbar_danger">ಇದು ಅಪಾಯದ ಸ್ನ್ಯಾಕ್‌ಪಟ್ಟಿ.</string>
    <string name="snackbar_description_single_line">ಕಡಿಮೆ ಅವಧಿ</string>
    <string name="snackbar_description_single_line_custom_view">ಸಣ್ಣ ಕಸ್ಟಮ್ ವೀಕ್ಷಣೆಯಂತೆ ವೃತ್ತಾಕಾರದ ಪ್ರಗತಿಯೊಂದಿಗೆ ದೀರ್ಘಾವಧಿ</string>
    <string name="snackbar_description_single_line_action">ಕ್ರಿಯೆಯ ಜೊತೆಗೆ ಅಲ್ಪಾವಧಿ</string>
    <string name="snackbar_description_single_line_action_custom_view">ಕ್ರಿಯೆ ಮತ್ತು ಮಧ್ಯಮ ಕಸ್ಟಮ್ ವೀಕ್ಷಣೆಯೊಂದಿಗೆ ಕಡಿಮೆ ಅವಧಿ</string>
    <string name="snackbar_description_single_line_custom_text_color">ಗ್ರಾಹಕೀಯಗೊಳಿಸಿದ ಪಠ್ಯ ಬಣ್ಣದೊಂದಿಗೆ ಕಡಿಮೆ ಅವಧಿ</string>
    <string name="snackbar_description_multiline">ದೀರ್ಘ ಕಾಲಾವಧಿ</string>
    <string name="snackbar_description_multiline_custom_view">ಸಣ್ಣ ಕಸ್ಟಮ್ ವೀಕ್ಷಣೆಯೊಂದಿಗೆ ದೀರ್ಘಾವಧಿ</string>
    <string name="snackbar_description_multiline_action">ಕ್ರಿಯೆ ಮತ್ತು ಪಠ್ಯ ನವೀಕರಣಗಳೊಂದಿಗೆ ಅನಿರ್ದಿಷ್ಟ ಅವಧಿ</string>
    <string name="snackbar_description_multiline_action_custom_view">ಕ್ರಿಯೆ ಮತ್ತು ಮಧ್ಯಮ ಕಸ್ಟಮ್ ವೀಕ್ಷಣೆಯೊಂದಿಗೆ ಕಡಿಮೆ ಅವಧಿ</string>
    <string name="snackbar_description_multiline_action_long">ದೀರ್ಘ ಕ್ರಿಯೆಯ ಪಠ್ಯದೊಂದಿಗೆ ಕಡಿಮೆ ಅವಧಿ</string>
    <string name="snackbar_description_announcement">ಕಡಿಮೆ ಅವಧಿ</string>
    <string name="snackbar_description_updated">ಈ ಪಠ್ಯವನ್ನು ನವೀಕರಿಸಲಾಗಿದೆ.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">ಸ್ನ್ಯಾಕ್‌ಪಟ್ಟಿ ತೋರಿಸಿ</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">ಒಂದೇ ಸಾಲು</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">ಬಹುಸಾಲು</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">ಘೋಷಣೆ ಶೈಲಿ</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">ಪ್ರಾಥಮಿಕ ಶೈಲಿ</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">ತಿಳಿ ಶೈಲಿ</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">ಎಚ್ಚರಿಕೆ ಶೈಲಿ</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">ಅಪಾಯದ ಶೈಲಿ</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">ಹೋಮ್</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">ಮೇಲ್</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">ಸೆಟ್ಟಿಂಗ್‌ಗಳು</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">ಅಧಿಸೂಚನೆ</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">ಇನ್ನಷ್ಟು</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">ಪಠ್ಯ ಒಗ್ಗೂಡಿಸುವಿಕೆ</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">ಲಂಬ</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">ಅಡ್ದವಾಗಿ</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">ಪಠ್ಯವಿಲ್ಲ</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">ಟ್ಯಾಬ್ ಐಟಂಗಳು</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">ಶೀರ್ಷಿಕೆ</string>
    <string name="cell_sample_description">ವಿವರಣೆ</string>
    <string name="calculate_cells">100 ಸೆಲ್‌ಗಳನ್ನು ಲೋಡ್/ಲೆಕ್ಕಾಚಾರ ಮಾಡಿ</string>
    <string name="calculate_layouts">100 ಲೇಔಟ್ ಅನ್ನು ಲೋಡ್ ಮಾಡಿ/ಲೆಕ್ಕಾಚಾರ ಮಾಡಿ</string>
    <string name="template_list">ಟೆಂಪ್ಲೆಟ್ ಪಟ್ಟಿ</string>
    <string name="regular_list">ನಿಯಮಿತ ಪಟ್ಟಿ</string>
    <string name="cell_example_title">ಶೀರ್ಷಿಕೆ: ಸೆಲ್</string>
    <string name="cell_example_description">ವಿವರಣೆ: ದೃಷ್ಟಿಕೋನವನ್ನು ಬದಲಾಯಿಸಲು ಟ್ಯಾಪ್ ಮಾಡಿ</string>
    <string name="vertical_layout">ಲಂಬ ಲೇಔಟ್</string>
    <string name="horizontal_layout">ಅಡ್ಡ ಲೇಔಟ್</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">ಪ್ರಮಾಣ ಟ್ಯಾಬ್ 2-ಸೆಗ್ಮೆಂಟ್</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">ಪ್ರಮಾಣಿತ ಟ್ಯಾಬ್ 3-ಸೆಗ್ಮೆಂಟ್</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">ಪ್ರಮಾಣಿತ ಟ್ಯಾಬ್ 4-ಸೆಗ್ಮೆಂಟ್</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">ಪೇಜರ್ ಜೊತೆಗೆ ಪ್ರಮಾಣಿತ ಟ್ಯಾಬ್</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">ಟ್ಯಾಬ್ ಸ್ವಿಚ್ ಮಾಡಿ</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">ಮಾತ್ರೆಗಳ ಟ್ಯಾಬ್ </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">ಪರಿಕರಸೂಚಿಗಾಗಿ ಟ್ಯಾಪ್ ಮಾಡಿ</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">ಕಸ್ಟಮ್ ಕ್ಯಾಲೆಂಡರ್ ಪರಿಕರಕಸೂಚಿಗಾಗಿ ಟ್ಯಾಪ್ ಮಾಡಿ</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">ಕಸ್ಟಮ್ ಬಣ್ಣ ಪರಿಕರಸೂಚಿ ಟ್ಯಾಪ್ ಮಾಡಿ</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">ಪರಿಕರಸೂಚಿಯ ಒಳಗೆ ವಜಾ ಮಾಡಲು ಟ್ಯಾಪ್ ಮಾಡಿ</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">ಕಸ್ಟಮ್ ವೀಕ್ಷಣೆ ಪರಿಕರಸೂಚಿಗಾಗಿ ಟ್ಯಾಪ್ ಮಾಡಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">ಮೇಲಿನ ಕಸ್ಟಮ್ ಬಣ್ಣ ಪರಿಕರಸೂಚಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp offsetY ಜೊತೆಗೆ ಮೇಲಿನ ಅಂತ್ಯ ಪರಿಕರಪಟ್ಟಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">ಕೆಳಗಿನ ಪ್ರಾರಂಭ ಪರಿಕರಸೂಚಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp offsetY ಜೊತೆಗೆ ಕೆಳಗಿನ ಅಂತ್ಯ ಪರಿಕರಪಟ್ಟಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">ಪರಿಕರಸೂಚಿಯ ಒಳಗೆ ವಜಾ ಮಾಡಿ</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">ಪರಿಕರಸೂಚಿಯನ್ನು ವಜಾಗೊಳಿಸಲಾಗಿದೆ</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">ಶಿರೋನಾಮೆ ಲೈಟ್ 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">ಶೀರ್ಷಿಕೆ 1 ಮಧ್ಯಮ 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">ಶೀರ್ಷಿಕೆ 2 ಸಾಮಾನ್ಯ 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">ಶಿರೋನಾಮೆ ನಿಯಮಿತ 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">ಉಪಶೀರ್ಷಿಕೆ 1 ನಿಯಮಿತ 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">ಉಪಶೀರ್ಷಿಕೆ 2 ಮಧ್ಯಮ 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">ಬಾಡಿ 1 ನಿಯಮಿತ 14sp ಆಗಿದೆ</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">ಬಾಡಿ 2 ಮಧ್ಯಮ 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">ಶೀರ್ಷಿಕೆಯು ನಿಯಮಿತ 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK ಆವೃತ್ತಿ: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">ಐಟಂ %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">ಫೋಲ್ಡರ್</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">ಕ್ಲಿಕ್ ಮಾಡಿದೆ</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">ಸ್ಕ್ಯಾಫೋಲ್ಡ್‌</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB ವಿಸ್ತರಿಸಲಾಗಿದೆ</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB ಸಂಕುಚಿಸಲಾಗಿದೆ</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">ಪಟ್ಟಿ ತಾಜಾಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">ಡ್ರಾಯರ್ ತೆರೆಯಿರಿ</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">ಮೆನು ಐಟಂ</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">ಆಫ್‌ಸೆಟ್ X (ಡಿಪಿ ಯಲ್ಲಿ)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">ಆಫ್‌ಸೆಟ್ Y (ಡಿಪಿ ಯಲ್ಲಿ)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">ವಿಷಯ ಪಠ್ಯ</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">ವಿಷಯ ಪಠ್ಯವನ್ನು ಪುನರಾವರ್ತಿಸಿ</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">ಮೆನು ಅಗಲವು ವಿಷಯ ಪಠ್ಯಕ್ಕೆ ಸಂಬಂಧಿಸಿದಂತೆ ಬದಲಾಗುತ್ತದೆ.
ಗರಿಷ್ಠ
         ಗಾತ್ರವನ್ನು ಪರದೆ ಗಾತ್ರದ 75% ಗೆ ನಿರ್ಬಂಧಿಸಲಾಗಿದೆ. ಟೋಕನ್ ಮೂಲಕ ಬದಿಯಿಂದ ಮತ್ತು ಕೆಳಗಿನಿಂದ ವಿಷಯ ಅಂಚು ನಿಯಂತ್ರಿಸಲಾಗುತ್ತದೆ. ಎತ್ತರವನ್ನು ಬದಲಿಸಲು ಒಂದೇ ವಿಷಯ ಪಠ್ಯವು ಪುನರಾವರ್ತಿಸುತ್ತದೆ.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">ಮೆನು ತೆರೆಯಿರಿ</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">ಮೂಲ ಕಾರ್ಡ್</string>
    <!-- UI Label for Card -->
    <string name="file_card">ಫೈಲ್ ಕಾರ್ಡ್</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">ಪ್ರಕಟಣೆ ಕಾರ್ಡ್</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">ಯಾದೃಚ್ಛಿಕ UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">ಆಯ್ಕೆಗಳು</string>
    <!-- UI Label for Title -->
    <string name="card_title">ಶೀರ್ಷಿಕೆ</string>
    <!-- UI Label for text -->
    <string name="card_text">ಪಠ್ಯ</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">ಉಪ ಪಠ್ಯ</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">ಅಗತ್ಯವಿದ್ದರೆ ಈ ಬ್ಯಾನರ್‌ಗೆ ದ್ವಿತೀಯ ಪ್ರತಿಯು ಎರಡು ಸಾಲುಗಳಿಗೆ ಸೀಮಿತಗೊಳ್ಳಬಹುದು.</string>
    <!-- UI Label Button -->
    <string name="card_button">ಬಟನ್</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">ಸಂವಾದ ತೋರಿಸಿ</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">ಹೊರಗಡೆ ಕ್ಲಿಕ್ ಮಾಡುವ ಮೂಲಕ ಸಂವಾದವನ್ನು ವಜಾಗೊಳಿಸಿ</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">ಹಿಂದೆ ಒತ್ತಿದಾಗ ಬರುವ ಸಂವಾದವನ್ನು ವಜಾಗೊಳಿಸಿ</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">ಸಂವಾದವನ್ನು ವಜಾಗೊಳಿಸಲಾಗಿದೆ</string>
    <!-- UI Label Cancel -->
    <string name="cancel">ರದ್ದುಮಾಡಿ</string>
    <!-- UI Label Ok -->
    <string name="ok">ಸರಿ</string>
    <!-- A sample description -->
    <string name="dialog_description">ಸಂವಾದವು ಒಂದು ಸಣ್ಣ ವಿಂಡೋ ಆಗಿದ್ದು, ಇದು ಬಳಕೆದಾರರನ್ನು ನಿರ್ಧಾರ ತೆಗೆದುಕೊಳ್ಳಲು ಅಥವಾ ಹೆಚ್ಚುವರಿ ಮಾಹಿತಿಯನ್ನು ನಮೂದಿಸಲು ಪ್ರೇರೇಪಿಸುತ್ತದೆ.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">ಡ್ರಾಯರ್ ತೆರೆಯಿರಿ</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">ಡ್ರಾಯರ್ ವಿಸ್ತರಿಸಿ</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">ಡ್ರಾಯರ್ ಮುಚ್ಚಿ</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">ಡ್ರಾಯರ್ ಪ್ರಕಾರವನ್ನು ಆಯ್ಕೆಮಾಡಿ</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">ಮೇಲೆ</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">ಸಂಪೂರ್ಣ ಡ್ರಾಯರ್ ಅನ್ನು ಗೋಚರಿಸುವ ಪ್ರದೇಶದಲ್ಲಿ ತೋರಿಸುತ್ತದೆ.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">ಕೆಳಗಡೆ</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">ಸಂಪೂರ್ಣ ಡ್ರಾಯರ್ ಅನ್ನು ಗೋಚರಿಸುವ ಪ್ರದೇಶದಲ್ಲಿ ತೋರಿಸುತ್ತದೆ. ಮೇಲಕ್ಕೆ ಸ್ವೈಪ್ ಮಾಡಿ ವಿಷಯವನ್ನು ಸ್ಕ್ರಾಲ್ ಮಾಡಿ. ಡ್ರ್ಯಾಗ್ ಹ್ಯಾಂಡಲ್ ಮೂಲಕ ವಿಸ್ತರಿಸಬಹುದಾದ ಡ್ರಾಯರ್ ವಿಸ್ತರಿಸುತ್ತದೆ.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">ಎಡ ಸ್ಲೈಡ್ ಮೇಲೆ</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">ಎಡಭಾಗದಿಂದ ಗೋಚರವಾಗುವ ಪ್ರದೇಶಕ್ಕೆ ಡ್ರಾಯರ್ ಸ್ಲೈಡ್ ಮಾಡಿ.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">ಬಲ ಸ್ಲೈಡ್ ಮೇಲೆ</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">ಬಲಭಾಗದಿಂದ ಗೋಚರವಾಗುವ ಪ್ರದೇಶಕ್ಕೆ ಡ್ರಾಯರ್ ಸ್ಲೈಡ್ ಮಾಡಿ.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">ಕೆಳಗಿನ ಸ್ಲೈಡ್ ಮೇಲೆ</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">ಡ್ರಾಯರ್ ಪರದೆಯ ಕೆಳಗಿನಿಂದ ಗೋಚರಿಸುವ ಪ್ರದೇಶಕ್ಕೆ ಸ್ಲೈಡ್ ಮಾಡಿ. ವಿಸ್ತರಿಸಬಹುದಾದ ಡ್ರಾಯರ್‌ನಲ್ಲಿ ಸ್ವೈಪ್ ಅಪ್ ಮೋಷನ್ ಅದರ ಉಳಿದ ಭಾಗವನ್ನು ಗೋಚರ ಪ್ರದೇಶಕ್ಕೆ ತರಲು &amp; ನಂತರ ಸ್ಕ್ರಾಲ್ ಮಾಡಿ.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">ಸ್ಕ್ರಮ್ ಕಾಣಿಸುತ್ತದೆ</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">ಡ್ರಾಯರ್ ವಿಷಯ ಆಯ್ಕೆಮಾಡಿ</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">ಪೂರ್ಣ ಪರದೆ ಗಾತ್ರ ಸ್ಕ್ರೋಲ್ ಮಾಡಬಹುದಾದ ವಿಷಯ</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">ಅರ್ಧಕ್ಕಿಂತ ಹೆಚ್ಚು ಪರದೆ ವಿಷಯ</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">ಅರ್ಧಕ್ಕಿಂತ ಕಡಿಮೆ ಪರದೆ ವಿಷಯ</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">ಡೈನಾಮಿಕ್ ಗಾತ್ರ ವಿಷಯ</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">ನೆಸ್ಟೆಡ್ ಡ್ರಾಯರ್ ವಿಷಯ</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">ವಿಸ್ತರಿಸಬಹುದು</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">ತೆರೆದ ಸ್ಥಿತಿಯನ್ನು ಬಿಟ್ಟುಬಿಡಿ</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">ಸ್ಕ್ರಿಮ್ ಕ್ಲಿಕ್‌ನಲ್ಲಿ ವಜಾ ತಡೆಯಿರಿ</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">ಹ್ಯಾಂಡಲ್ ತೋರಿಸಿ</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">ಶೀರ್ಷಿಕೆ</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">ಪರಿಕರಸೂಚಿ ಪಠ್ಯ</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">ಕಸ್ಟಮ್ ವಿಷಯ ಪರಿಕರಸೂಚಿಗಾಗಿ ಟ್ಯಾಪ್ ಮಾಡಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">ಮೇಲ್ಭಾಗದ ಪ್ರಾರಂಭ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">ಮೇಲಿನ ತುದಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">ಕೆಳಗಿನ ಪ್ರಾರಂಭ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">ಕೆಳಗಿನ ತುದಿ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">ಮಧ್ಯೆ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">ಕಸ್ಟಮ್ ಕೇಂದ್ರ</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">ಬಿಡುಗಡೆ ಟಿಪ್ಪಣಿಗಳ ನವೀಕರಣಗಳಿಗಾಗಿ, </string>
    <string name="click_here">ಇಲ್ಲಿ ಕ್ಲಿಕ್ ಮಾಡಿ.</string>
    <string name="open_source_cross_platform">ತೆರೆದ ಮೂಲದ ಕ್ರಾಸ್ ಪ್ಲಾಟ್‍ಫಾರ್ಮ್ ವಿನ್ಯಾಸ ಸಿಸ್ಟಂ.</string>
    <string name="intuitive_and_powerful">ಅಂತರ್ಬೋಧೆಯ &amp; ಶಕ್ತಿಶಾಲಿ.</string>
    <string name="design_tokens">ವಿನ್ಯಾಸ ಟೋಕನ್‌ಗಳು</string>
    <string name="release_notes">ಬಿಡುಗಡೆ ಟಿಪ್ಪಣಿಗಳು</string>
    <string name="github_repo">GitHub ರೆಪೋ</string>
    <string name="github_repo_link">GitHub ರೆಪೋ ಲಿಂಕ್</string>
    <string name="report_issue">ಸಮಸ್ಯೆಯನ್ನು ವರದಿ ಮಾಡಿ</string>
    <string name="v1_components">V1 ಕಾಂಪೊನೆಂಟ್‌ಗಳು</string>
    <string name="v2_components">V2 ಕಾಂಪೊನೆಂಟ್‌ಗಳು</string>
    <string name="all_components">ಎಲ್ಲಾ</string>
    <string name="fluent_logo">Fluent ಲೋಗೊ</string>
    <string name="new_badge">ಹೊಸತು</string>
    <string name="modified_badge">ಮಾರ್ಪಡಿಸಲಾಗಿದೆ</string>
    <string name="api_break_badge">API ಬ್ರೇಕ್</string>
    <string name="app_bar_more">ಇನ್ನಷ್ಟು</string>
    <string name="accent">ಆಕ್ಸೆಂಟ್</string>
    <string name="appearance">ನೋಟ</string>
    <string name="choose_brand_theme">ನಿಮ್ಮ ಬ್ರ್ಯಾಂಡ್ ಥೀಮ್ ಆರಿಸಿ:</string>
    <string name="fluent_brand_theme">Fluent ಬ್ರ್ಯಾಂಡ್</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">ನೋಟವನ್ನು ಆರಿಸಿ</string>
    <string name="appearance_system_default">ಸಿಸ್ಟಂ ಡೀಫಾಲ್ಟ್</string>
    <string name="appearance_light">ತಿಳಿ</string>
    <string name="appearance_dark">ಗಾಢ</string>
    <string name="demo_activity_github_link">ಪ್ರಾತ್ಯಕ್ಷಿಕೆ ಚಟುವಟಿಕೆ GitHub ಲಿಂಕ್</string>
    <string name="control_tokens_details">ನಿಯಂತ್ರಣ ಟೋಕನ್‌ಗಳ ವಿವರಗಳು</string>
    <string name="parameters">ಪ್ಯಾರಾಮೀಟರ್‌ಗಳು</string>
    <string name="control_tokens">ನಿಯಂತ್ರಣ ಟೋಕನ್‌ಗಳು</string>
    <string name="global_tokens">ಜಾಗತಿಕ ಟೋಕನ್‌ಗಳು</string>
    <string name="alias_tokens">ಅಲಿಯಾಸ್ ಟೋಕನ್‌ಗಳು</string>
    <string name="sample_text">ಪಠ್ಯ</string>
    <string name="sample_icon">ಮಾದರಿ ಐಕಾನ್</string>
    <string name="color">ಬಣ್ಣ</string>
    <string name="neutral_color_tokens">ತಟಸ್ಥ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="font_size_tokens">ಫಾಂಟ್ ಗಾತ್ರ ಟೋಕನ್‌ಗಳು</string>
    <string name="line_height_tokens">ಸಾಲಿನ ಎತ್ತರದ ಟೋಕನ್‌ಗಳು</string>
    <string name="font_weight_tokens">ಫಾಂಟ್ ತೂಕ ಟೋಕನ್‌ಗಳು</string>
    <string name="icon_size_tokens">ಐಕಾನ್ ಗಾತ್ರ ಟೋಕನ್‌ಗಳು</string>
    <string name="size_tokens">ಗಾತ್ರ ಟೋಕನ್‌ಗಳು</string>
    <string name="shadow_tokens">ಶ್ಯಾಡೋ ಟೋಕನ್‌ಗಳು</string>
    <string name="corner_radius_tokens">ಕಾರ್ನರ್ RadiusTokens</string>
    <string name="stroke_width_tokens">ಸ್ಟ್ರೋಕ್ ಅಗಲ ಟೋಕನ್‌ಗಳು</string>
    <string name="brand_color_tokens">ಬ್ರ್ಯಾಂಡ್ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="neutral_background_color_tokens">ತಟಸ್ಥ ಹಿನ್ನೆಲೆ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="neutral_foreground_color_tokens">ತಟಸ್ಥ ಮುನ್ನೆಲೆ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="neutral_stroke_color_tokens">ತಟಸ್ಥ ಸ್ಟ್ರೋಕ್ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="brand_background_color_tokens">ಬ್ರ್ಯಾಂಡ್ ಹಿನ್ನೆಲೆ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="brand_foreground_color_tokens">ಬ್ರ್ಯಾಂಡ್ ಮುನ್ನೆಲೆ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="brand_stroke_color_tokens">ಬ್ರ್ಯಾಂಡ್ ಸ್ಟ್ರೋಕ್ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="error_and_status_color_tokens">ದೋಷ ಮತ್ತು ಸ್ಥಿತಿ ಬಣ್ಣ ಟೋಕನ್‌ಗಳು</string>
    <string name="presence_tokens">ಉಪಸ್ಥಿತಿ ಬಣ್ಣದ ಟೋಕನ್‌ಗಳು</string>
    <string name="typography_tokens">ಟೈಪೋಗ್ರಾಫಿ ಟೋಕನ್‌ಗಳು</string>
    <string name="unspecified">ನಿರ್ದಿಷ್ಟಪಡಿಸದ</string>

</resources>