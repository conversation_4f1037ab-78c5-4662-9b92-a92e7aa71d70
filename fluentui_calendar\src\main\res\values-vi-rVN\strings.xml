<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">H</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">B</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">T</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">N</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">S</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">B</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">C</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Đi tới tuần sau</string>
    <string name="accessibility_goto_previous_week">Đi tới tuần trước</string>
    <string name="accessibility_today">hôm nay</string>
    <string name="accessibility_selected">Đã chọn</string>

    <!-- *** Shared *** -->
    <string name="done">Xong</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Hôm nay</string>
    <string name="tomorrow">Ngày mai</string>
    <string name="yesterday">Hôm qua</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Thời gian bắt đầu</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Thời gian kết thúc</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Ngày bắt đầu</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Ngày kết thúc</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Chọn Thời gian</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Chọn ngày tháng</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Bộ chọn ngày giờ</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Bộ chọn ngày tháng</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Phạm vi bộ chọn ngày giờ</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Phạm vi của bộ chọn ngày tháng</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Tab Thời gian bắt đầu</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Tab Thời gian kết thúc</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Tab Ngày bắt đầu</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Tab Ngày kết thúc</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Đóng hộp thoại</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Tăng dần tháng</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">chọn tháng tiếp theo</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Giảm dần tháng</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">chọn tháng trước</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Tăng dần ngày</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">chọn ngày tiếp theo</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Giảm dần ngày</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">chọn ngày trước</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Tăng dần năm</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">chọn năm tiếp theo</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Giảm dần năm</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">chọn năm trước</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Tăng dần ngày tháng</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">chọn ngày tháng tiếp theo</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Giảm dần ngày tháng</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">chọn ngày tháng trước</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Tăng dần giờ</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">chọn giờ tiếp theo</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Giảm dần giờ</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">chọn giờ trước</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Tăng dần phút</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">chọn phút tiếp theo</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Giảm dần phút</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">chọn phút trước</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">Bật/tắt thời gian SA CH</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">bật/tắt thời gian SA CH</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">Đã chọn %s</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">đã chọn</string>
</resources>
