<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Tunjuk Cara Fluent UI</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Memilih %s</string>
    <string name="app_modifiable_parameters">Parameter Boleh Ubah Suai</string>
    <string name="app_right_accessory_view">Pandangan Aksesori Kanan</string>

    <string name="app_style">Gaya</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikon Ditekan</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button"><PERSON><PERSON><PERSON></string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Karusel</string>
    <string name="actionbar_icon_radio_label">Ikon</string>
    <string name="actionbar_basic_radio_label">Asas</string>
    <string name="actionbar_position_bottom_radio_label">Bawah</string>
    <string name="actionbar_position_top_radio_label">Atas</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Jenis Bar Tindakan</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Kedudukan Bar Tindakan</string>

    <!--AppBar-->
    <string name="app_bar_style">Gaya Bar Aplikasi</string>
    <string name="app_bar_subtitle">Subtajuk</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Sempadan Bawah</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Ikon navigasi diklik.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Bendera</string>
    <string name="app_bar_layout_menu_settings">Seting</string>
    <string name="app_bar_layout_menu_search">Cari</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Kelakuan skrol: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Togol kelakuan skrol</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Togol ikon navigasi</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Tunjukkan avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Tunjukkan ikon ke belakang</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Sembunyikan ikon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Tunjukkan ikon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Togol gaya tataletak bar carian</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Tunjukkan sebagai pandangan aksesori</string>
    <string name="app_bar_layout_searchbar_action_view_button">Tunjukkan sebagai pandangan tindakan</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Togol antara tema (mencipta semula aktiviti)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Togol tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Item</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Kandungan boleh skrol tambahan</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Gaya bulatan</string>
    <string name="avatar_style_square">Gaya segi empat sama</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXBesar</string>
    <string name="avatar_size_xlarge">XBesar</string>
    <string name="avatar_size_large">Besar</string>
    <string name="avatar_size_medium">Sederhana</string>
    <string name="avatar_size_small">Kecil</string>
    <string name="avatar_size_xsmall">XKecil</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dua Ganda Lebih Besar</string>
    <string name="avatar_size_xlarge_accessibility">Lebih Besar</string>
    <string name="avatar_size_xsmall_accessibility">Sangat Kecil</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Avatar Dipaparkan Maksimum</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Kiraan Avatar Limpahan</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Jenis Sempadan</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Kumpulan Avatar dengan set OverflowAvatarCount tidak akan mematuhi bilangan maksimum Avatar yang Dipaparkan.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Tindanan Muka</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Tindihan Muka</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Limpahan diklik</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Pandangan Avatar pada indeks %d diklik</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Lencana Pemberitahuan</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Titik</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Senarai</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Aksara</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Foto</string>
    <string name="bottom_navigation_menu_item_news">Berita</string>
    <string name="bottom_navigation_menu_item_alerts">Isyarat</string>
    <string name="bottom_navigation_menu_item_calendar">Kalendar</string>
    <string name="bottom_navigation_menu_item_team">Pasukan</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Togol label</string>
    <string name="bottom_navigation_three_menu_items_button">Tunjukkan tiga item menu</string>
    <string name="bottom_navigation_four_menu_items_button">Tunjukkan empat item menu</string>
    <string name="bottom_navigation_five_menu_items_button">Tunjukkan lima item menu</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Label %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Helaian Bawah</string>
    <string name="bottom_sheet_dialog">Dialog Helaian Bawah</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Dayakan Leret ke Bawah untuk Menolak</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Tunjukkan dengan item satu baris</string>
    <string name="bottom_sheet_with_double_line_items">Tunjukkan dengan item dua baris</string>
    <string name="bottom_sheet_with_single_line_header">Tunjukkan dengan pengepala satu baris</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Tunjukkan dengan pengepala dan pembahagi dua baris</string>
    <string name="bottom_sheet_dialog_button">Tunjukkan</string>
    <string name="drawer_content_desc_collapse_state">Kembangkan</string>
    <string name="drawer_content_desc_expand_state">Minimumkan</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klik %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Klik Lama %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klik tolak</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Sisipkan Item</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Kemas Kini Item</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Tolak</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Tambah</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Sebut</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Tebal</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Italik</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Garis bawah</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Garis lorek</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Buat asal</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Buat semula</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Bulet</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Senarai</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Pautan</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Item Mengemas Kini</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Jarak</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Tolak Kedudukan</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">MULA</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">TAMAT</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Ruang kumpulan</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Ruang item</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Bendera</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Item bendera diklik</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Balas</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Item balas diklik</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Majukan</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Item majukan diklik</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Padam</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Item padam diklik</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Ambil foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Item kamera diklik</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galeri</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Lihat foto anda</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Item galeri diklik</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Video</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Mainkan video anda</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Item video diklik</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Urus</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Urus pustaka media anda</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Item urus diklik</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Tindakan E-mel</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumen</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Kali terakhir dikemas kini 2:14PTG</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Kongsi</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Item kongsi diklik</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Alih</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Item alih diklik</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Padam</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Item padam diklik</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Maklumat</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Item maklumat diklik</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Jam</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Item jam diklik</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Penggera</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Item penggera diklik</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Zon waktu</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Item zon waktu diklik</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Pandangan butang berbeza</string>
    <string name="button">Butang</string>
    <string name="buttonbar">Bar Butang</string>
    <string name="button_disabled">Contoh Butang Dinyahdayakan</string>
    <string name="button_borderless">Contoh Butang Tanpa Sempadan</string>
    <string name="button_borderless_disabled">Contoh Butang Dinyahdayakan Tanpa Sempadan</string>
    <string name="button_large">Contoh Butang Besar</string>
    <string name="button_large_disabled">Contoh Butang Dinyahdayakan Besar</string>
    <string name="button_outlined">Contoh Butang Digariskan</string>
    <string name="button_outlined_disabled">Contoh Butang Dinyahdayakan Digariskan</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Pilih tarikh</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">Pemilih Tarikh Masa</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Satu Tarikh</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Tiada tarikh dipilih</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Tunjukkan pemilih tarikh</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Tunjukkan pemilih tarikh masa dengan tab tarikh dipilih</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Tunjukkan pemilih tarikh masa dengan tab masa dipilih</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Tunjukkan pemilih tarikh masa</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Julat Tarikh</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Mula:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Tamat:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Tiada tarikh mula dipilih</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Tiada tarikh tamat dipilih</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Pilih tarikh mula</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Pilih tarikh tamat</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Julat Tarikh Masa</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Pilih julat tarikh masa</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">Dialog Pemilih Tarikh Masa</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Tunjukkan Dialog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Tunjukkan laci</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Tunjukkan dialog laci</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Dialog bawah tiada pudar</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Tunjukkan laci atas</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Dialog atas tiada pudar</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Tunjukkan dialog atas pandangan sauh</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Tunjukkan dialog atas tiada tajuk</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Tunjukkan dialog atas di bawah tajuk</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Tunjukkan laci kanan</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Tunjukkan laci kiri</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Tajuk, teks utama</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtajuk, teks kedua</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Teks subtajuk tersuai</string>
    <!-- Footer -->
    <string name="list_item_footer">Pengaki, teks ketiga</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Senarai satu baris dengan teks sub pengepala kelabu</string>
    <string name="list_item_sub_header_two_line">Senarai dua baris</string>
    <string name="list_item_sub_header_two_line_dense">Senarai dua baris dengan jarak padat</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Senarai dua baris dengan pandangan subtajuk kedua tersuai</string>
    <string name="list_item_sub_header_three_line">Senarai tiga baris dengan teks sub pengepala hitam</string>
    <string name="list_item_sub_header_no_custom_views">Item senarai tanpa pandangan tersuai</string>
    <string name="list_item_sub_header_large_header">Item senarai dengan pandangan tersuai besar</string>
    <string name="list_item_sub_header_wrapped_text">Item senarai dengan teks berbalut</string>
    <string name="list_item_sub_header_truncated_text">Item senarai dengan teks terpangkas</string>
    <string name="list_item_sub_header_custom_accessory_text">Tindakan</string>
    <string name="list_item_truncation_middle">Tengah pemangkasan.</string>
    <string name="list_item_truncation_end">Tamat pemangkasan.</string>
    <string name="list_item_truncation_start">Mula pemangkasan.</string>
    <string name="list_item_custom_text_view">Nilai</string>
    <string name="list_item_click">Anda telah mengklik pada item senarai.</string>
    <string name="list_item_click_custom_accessory_view">Anda telah mengklik pada pandangan aksesori tersuai.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Anda telah mengklik pada pandangan aksesori tersuai sub pengepala.</string>
    <string name="list_item_more_options">Opsyen selanjutnya</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Pilih</string>
    <string name="people_picker_select_deselect_example">Pilih Nyahpilih</string>
    <string name="people_picker_none_example">Tiada</string>
    <string name="people_picker_delete_example">Padam</string>
    <string name="people_picker_custom_persona_description">Contoh ini menunjukkan cara mencipta objek IPersona tersuai.</string>
    <string name="people_picker_dialog_title_removed">Anda telah mengalih keluar persona:</string>
    <string name="people_picker_dialog_title_added">Anda telah menambah persona:</string>
    <string name="people_picker_drag_started">Seret bermula</string>
    <string name="people_picker_drag_ended">Seret tamat</string>
    <string name="people_picker_picked_personas_listener">Pendengar Persona</string>
    <string name="people_picker_suggestions_listener">Pendengar Cadangan</string>
    <string name="people_picker_persona_chip_click">Anda telah mengklik pada %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="other">%1$s penerima</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Kembangkan Helaian Bawah Berterusan</string>
    <string name="collapse_persistent_sheet_button"> Sembunyikan Helaian Bawah Berterusan</string>
    <string name="show_persistent_sheet_button"> Tunjukkan Helaian Bawah Berterusan</string>
    <string name="new_view">Ini ialah Pandangan Baharu</string>
    <string name="toggle_sheet_content">Togol Kandungan Helaian Bawah</string>
    <string name="switch_to_custom_content">Tukar kepada Kandungan tersuai</string>
    <string name="one_line_content">Kandungan Helaian Bawah Sebaris</string>
    <string name="toggle_disable_all_items">Togol Nyahdayakan Semua Item</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Tambah/Alih Keluar Pandangan</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Ubah Ketinggian Diruntuhkan</string>
    <string name="persistent_sheet_item_create_new_folder_title">Folder Baharu</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Item Folder Baharu diklik</string>
    <string name="persistent_sheet_item_edit_title">Edit</string>
    <string name="persistent_sheet_item_edit_toast">Item edit diklik</string>
    <string name="persistent_sheet_item_save_title">Simpan</string>
    <string name="persistent_sheet_item_save_toast">Item simpan diklik</string>
    <string name="persistent_sheet_item_zoom_in_title">Zum Ke Dalam</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Item Zum Ke Dalam diklik</string>
    <string name="persistent_sheet_item_zoom_out_title">Zum Ke Luar</string>
    <string name="persistent_sheet_item_zoom_out_toast">Item Zum Ke Luar diklik</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Tersedia</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Pereka bentuk</string>
    <string name="persona_subtitle_engineer">Jurutera</string>
    <string name="persona_subtitle_manager">Pengurus</string>
    <string name="persona_subtitle_researcher">Penyelidik</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (contoh teks panjang untuk menguji pemangkasan)</string>
    <string name="persona_view_description_xxlarge">Avatar XXBesar dengan tiga baris teks</string>
    <string name="persona_view_description_large">Avatar besar dengan dua baris teks</string>
    <string name="persona_view_description_small">Avatar kecil dengan sebaris teks</string>
    <string name="people_picker_hint">Tiada dengan pembayang ditunjukkan</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Cip Persona Dinyahdayakan</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Ralat Cip Persona</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Cip Persona tanpa ikon tutup</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Cip Persona Asas</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Anda mengklik pada Cip Persona yang dipilih.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Kongsi</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Ikut</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Jemput orang</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Segar semula halaman</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Buka dalam pelayar</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Ini ialah Menu Timbul berbilang baris. Baris maksimum ditetapkan kepada dua, teks yang selebihnya akan dipangkas.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Semua berita</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Berita yang disimpan</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Berita daripada laman</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Maklumkan di luar waktu bekerja</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Maklumkan apabila tidak aktif pada desktop</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Anda telah mengklik pada item:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menu ringkas</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menu ringkas2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu dengan satu item yang boleh dipilih dan pembahagi</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu dengan semua item, ikon dan teks panjang yang boleh dipilih</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Tunjukkan</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Kemajuan Membulat</string>
    <string name="circular_progress_xsmall">XKecil</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Kecil</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Sederhana</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Besar</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Kemajuan Linear</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Tidak Tentu</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Tertentu</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Bar Carian</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Panggil Balik Mikrofon</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autopembetulan</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon Ditekan</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Pandangan Kanan Ditekan</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Carian Papan Kekunci Ditekan</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Tunjukkan Bar Snek</string>
    <string name="fluentui_dismiss_snackbar">Tolak Bar Snek</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Tindakan</string>
    <string name="snackbar_action_long">Tindakan Teks Panjang</string>
    <string name="snackbar_single_line">Bar snek satu baris</string>
    <string name="snackbar_multiline">Ini ialah bar snek berbilang baris. Baris maksimum ditetapkan kepada dua, teks yang selebihnya akan dipangkas.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Ini ialah bar snek pengumuman. Ia digunakan untuk menyampaikan ciri baharu.</string>
    <string name="snackbar_primary">Ini ialah bar snek utama.</string>
    <string name="snackbar_light">Ini ialah bar snek cerah.</string>
    <string name="snackbar_warning">Ini ialah bar snek amaran.</string>
    <string name="snackbar_danger">Ini ialah bar snek bahaya.</string>
    <string name="snackbar_description_single_line">Tempoh pendek</string>
    <string name="snackbar_description_single_line_custom_view">Tempoh panjang dengan kemajuan membulat sebagai pandangan tersuai kecil</string>
    <string name="snackbar_description_single_line_action">Tempoh pendek dengan tindakan</string>
    <string name="snackbar_description_single_line_action_custom_view">Tempoh pendek dengan tindakan dan pandangan tersuai sederhana</string>
    <string name="snackbar_description_single_line_custom_text_color">Tempoh pendek dengan warna teks tersuai</string>
    <string name="snackbar_description_multiline">Tempoh panjang</string>
    <string name="snackbar_description_multiline_custom_view">Tempoh panjang dengan pandangan tersuai kecil</string>
    <string name="snackbar_description_multiline_action">Tempoh tidak tentu dengan tindakan dan kemas kini teks</string>
    <string name="snackbar_description_multiline_action_custom_view">Tempoh pendek dengan tindakan dan pandangan tersuai sederhana</string>
    <string name="snackbar_description_multiline_action_long">Tempoh pendek dengan teks tindakan panjang</string>
    <string name="snackbar_description_announcement">Tempoh pendek</string>
    <string name="snackbar_description_updated">Teks ini telah dikemas kini.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Tunjukkan Bar Snek</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Satu baris</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Berbilang baris</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Gaya pengumuman</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Gaya utama</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Gaya cerah</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Gaya amaran</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Gaya bahaya</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Laman Utama</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Mel</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Seting</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Pemberitahuan</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Lagi</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Penjajaran Teks</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Menegak</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Melintang</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Tiada Teks</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Item Tab</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Tajuk</string>
    <string name="cell_sample_description">Perihalan</string>
    <string name="calculate_cells">Muatkan/kira 100 sel</string>
    <string name="calculate_layouts">Muatkan/kira 100 tataletak</string>
    <string name="template_list">Senarai Templat</string>
    <string name="regular_list">Senarai Biasa</string>
    <string name="cell_example_title">Tajuk: Sel</string>
    <string name="cell_example_description">Perihalan: Ketik untuk mengubah orientasi</string>
    <string name="vertical_layout">Tataletak Menegak</string>
    <string name="horizontal_layout">Tataletak Melintang</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Tab Standard 2-Segmen</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Tab Standard 3-Segmen</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Tab Standard 4-Segmen</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Tab Standard dengan Kawalan Halaman</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Tukar Tab</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Tab Pil </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Ketik untuk Petua Alat</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Ketik untuk Petua Alat Kalendar Tersuai</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Ketik Petua Alat Warna Tersuai</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Ketik untuk Menolak Petua Alat Dalam</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Ketik untuk Petua Alat Pandangan Tersuai</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Petua Alat Warna Tersuai Teratas</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Petua Alat Hujung Atas dengan 10dp ofsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Petua Alat Mula Bawah</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Petua Alat Hujung Bawah dengan 10dp ofsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Tolak Petua Alat dalam</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Petua alat ditolak</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Tajuk utama adalah Cerah 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Tajuk 1 adalah Sederhana 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Tajuk 2 adalah Biasa 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Tajuk adalah Biasa 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Subtajuk 1 adalah Biasa 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Subtajuk 2 adalah Sederhana 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Isi 1 adalah Biasa 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Isi 2 adalah Sederhana 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Kapsyen Biasa 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versi SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Item %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Folder</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Diklik</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Perancah</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB Dikembangkan</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB Diruntuhkan</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klik untuk menyegarkan semula senarai</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Buka Laci</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Item Menu</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Ofset X (dalam dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Ofset Y (dalam dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Teks Kandungan</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Ulangi Teks Kandungan</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Lebar menu akan berubah berhubung dengan Teks Kandungan. Maks
        lebar dihadkan kepada 75% daripada saiz skrin. Jidar kandungan dari sisi dan bawah dikawal oleh token. Teks kandungan yang sama akan berulang untuk berbeza-beza
        ketinggian.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Menu Buka</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Kad Asas</string>
    <!-- UI Label for Card -->
    <string name="file_card">Kad Fail</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Kad Pengumuman</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">UI rawak</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opsyen</string>
    <!-- UI Label for Title -->
    <string name="card_title">Tajuk</string>
    <!-- UI Label for text -->
    <string name="card_text">Teks</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Sub Teks</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Salinan sekunder untuk sepanduk ini boleh dibalut kepada dua baris jika diperlukan.</string>
    <!-- UI Label Button -->
    <string name="card_button">Butang</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Tunjukkan Dialog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Tolak dialog apabila mengklik di luar</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Tolak dialog pada tekanan belakang</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialog dibuang</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Batalkan</string>
    <!-- UI Label Ok -->
    <string name="ok">Ok</string>
    <!-- A sample description -->
    <string name="dialog_description">Dialog ialah tetingkap kecil yang menggesa pengguna untuk membuat keputusan atau memasukkan maklumat tambahan.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Buka Laci</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Kembangkan Laci</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Tutup Laci</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Pilih Jenis Laci</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Atas</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Seluruh laci ditunjukkan dalam rantau yang boleh dilihat.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Bawah</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Seluruh laci ditunjukkan dalam kawasan yang boleh dilihat. Leret ke atas kandungan skrol pergerakan. Laci yang boleh dikembangkan berkembang melalui pemegang seret.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Slaid Kiri Pada</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Slaid laci ke bahagian yang boleh dilihat dari sebelah kiri.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Slaid Kanan Pada</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Slaid laci ke bahagian yang boleh dilihat dari sebelah kanan.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Slaid Bawah Pada</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Slaid laci ke kawasan yang boleh dilihat dari bawah skrin. Leret ke atas pergerakan pada laci yang boleh dikembangkan, bawa bahagiannya yang lain ke rantau yang boleh dilihat &amp; kemudian skrol.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim Boleh Dilihat</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Pilih Kandungan Laci</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Kandungan boleh tatal saiz skrin penuh</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Lebih daripada separuh kandungan skrin</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Kurang daripada separuh kandungan skrin</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Kandungan saiz dinamik</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Kandungan Laci Bersarang</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Boleh kembang</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Langkau Keadaan Terbuka</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Elakkan Pembuangan pada Klik Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Tunjukkan Pemegang</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Tajuk</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Teks Petua Alat</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Ketik untuk Petua Alat Kandungan Tersuai</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Mula Atas </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Hujung Atas </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Mula Bawah </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Hujung Bawah </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Pusat </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Pusat Tersuai</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Untuk kemas kini tentang Nota Keluaran, </string>
    <string name="click_here">klik di sini.</string>
    <string name="open_source_cross_platform">Sistem Reka Bentuk platform silang sumber terbuka.</string>
    <string name="intuitive_and_powerful">Intuitif &amp; Berkuasa.</string>
    <string name="design_tokens">Token Reka Bentuk</string>
    <string name="release_notes">Nota Keluaran</string>
    <string name="github_repo">Repo GitHub</string>
    <string name="github_repo_link">Pautan Repo GitHub</string>
    <string name="report_issue">Laporkan Isu</string>
    <string name="v1_components">Komponen V1</string>
    <string name="v2_components">Komponen V2</string>
    <string name="all_components">Semua</string>
    <string name="fluent_logo">Logo Fluent</string>
    <string name="new_badge">Baharu</string>
    <string name="modified_badge">Diubah suai</string>
    <string name="api_break_badge">Pemisah API</string>
    <string name="app_bar_more">Selanjutnya</string>
    <string name="accent">Aksen</string>
    <string name="appearance">Penampilan</string>
    <string name="choose_brand_theme">Pilih tema jenama anda:</string>
    <string name="fluent_brand_theme">Jenama Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Pilih Penampilan</string>
    <string name="appearance_system_default">Lalai Sistem</string>
    <string name="appearance_light">Cerah</string>
    <string name="appearance_dark">Gelap</string>
    <string name="demo_activity_github_link">Pautan GitHub Aktiviti Demo</string>
    <string name="control_tokens_details">Butiran Token Kawalan</string>
    <string name="parameters">Parameter</string>
    <string name="control_tokens">Token Kawalan</string>
    <string name="global_tokens">Token Global</string>
    <string name="alias_tokens">Token Alias</string>
    <string name="sample_text">Teks</string>
    <string name="sample_icon">Ikon Sampel</string>
    <string name="color">Warna</string>
    <string name="neutral_color_tokens">Token Warna Neutral</string>
    <string name="font_size_tokens">Token Saiz Fon</string>
    <string name="line_height_tokens">Token Ketinggian Baris</string>
    <string name="font_weight_tokens">Token Berat Fon</string>
    <string name="icon_size_tokens">Token Saiz Ikon</string>
    <string name="size_tokens">Token Saiz</string>
    <string name="shadow_tokens">Token Bayang</string>
    <string name="corner_radius_tokens">RadiusToken Sudut</string>
    <string name="stroke_width_tokens">Token Lebar Lejang</string>
    <string name="brand_color_tokens">Token Warna Jenama</string>
    <string name="neutral_background_color_tokens">Token Warna Latar Neutral</string>
    <string name="neutral_foreground_color_tokens">Token Warna Latar Depan Neutral</string>
    <string name="neutral_stroke_color_tokens">Token Warna Lejang Neutral</string>
    <string name="brand_background_color_tokens">Token Warna Latar Jenama</string>
    <string name="brand_foreground_color_tokens">Token Warna Latar Depan Jenama</string>
    <string name="brand_stroke_color_tokens">Token Warna Lejang Jenama</string>
    <string name="error_and_status_color_tokens">Ralat dan Token Warna Status</string>
    <string name="presence_tokens">Token Warna Kehadiran</string>
    <string name="typography_tokens">Token Tipografi</string>
    <string name="unspecified">Tidak dinyatakan</string>

</resources>