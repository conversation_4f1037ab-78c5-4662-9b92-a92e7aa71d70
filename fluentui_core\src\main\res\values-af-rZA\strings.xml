<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Prim<PERSON>r</string>
    <string name="fluentui_secondary">Sekondêr</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Weier</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Geselekteer</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Nie geselekteer nie</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Ikoon</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Ikoon</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Gedeaktiveer</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">A<PERSON><PERSON>-knoppie</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Geaktiveer</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Sluit blad</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Sluit</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Kanselleer</string>
    <!--name of the icon -->
    <string name="fluentui_search">Soek</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Mikrofoon</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Vee teks uit</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Terug</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Geaktiveer</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Gedeaktiveer</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutraal</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Handelsmerk</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Kontras</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Aksent</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Waarskuwing</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Gevaar</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Daar was ’n fout</string>
    <string name="fluentui_error">Fout</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Wenk</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Sjevron</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Styf</string>

    <string name="fluentui_action_button_icon">Ikoon vir aksie-knoppie</string>
    <string name="fluentui_center">Sentreer teks</string>
    <string name="fluentui_accessory_button">Bykomstigheidknoppies</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Radioknoppie</string>
    <string name="fluentui_label">Etiket</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Uitgevou</string>
    <string name="fluentui_collapsed">Ingevou</string>

    <!--types of control -->
    <string name="fluentui_large">Groot</string>
    <string name="fluentui_medium">Medium</string>
    <string name="fluentui_small">Klein</string>
    <string name="fluentui_password_mode">Wagwoordmodus</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Onderskrif</string>
    <string name="fluentui_assistive_text">Bystandsteks</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Titel</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Kort</string>"
    <string name="fluentui_long">Lank</string>"
    <string name="fluentui_indefinite">Onbepaald</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Knoppie is gedruk</string>
    <string name="fluentui_dismissed">Geweier</string>
    <string name="fluentui_timeout">Uitgetel</string>
    <string name="fluentui_left_swiped">Links gevee</string>
    <string name="fluentui_right_swiped">Regs gevee</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Teks</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Getal</string>
    <string name="fluentui_keyboard_phone">Telefoon</string>
    <string name="fluentui_keyboard_uri">Bronadres</string>
    <string name="fluentui_keyboard_email">E-pos</string>
    <string name="fluentui_keyboard_password">Wagwoord</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Desimaal</string>
</resources>