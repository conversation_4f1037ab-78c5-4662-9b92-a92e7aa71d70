<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 7.4.2" type="baseline" client="gradle" dependencies="false" name="AGP (7.4.2)" variant="all" version="7.4.2">

    <issue
        id="ObsoleteLintCustomCheck"
        message="<PERSON><PERSON> found an issue registry (`androidx.compose.runtime.lint.RuntimeIssueRegistry`) which requires a newer API level. That means that the custom lint checks are intended for a newer lint version; please upgrade.">
        <location
            file="C:/Users/<USER>/.gradle/caches/transforms-3/5a95d437489af7b64c794ecfcc0144a2/transformed/jetified-runtime-release/jars/lint.jar"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of org.jetbrains.kotlin:kotlin-stdlib-jdk7 than 1.8.21 is available: 1.9.20"
        errorLine1="    api project(&apos;:fluentui_tablayout&apos;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="53"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0"
        errorLine1="    api project(&apos;:fluentui_topappbars&apos;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="54"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.9.0 is available: 1.12.0"
        errorLine1="    api project(&apos;:fluentui_transients&apos;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="55"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of junit:junit than 4.12 is available: 4.13.2"
        errorLine1="    implementation &quot;org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version&quot;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="56"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        errorLine1="    implementation &quot;androidx.appcompat:appcompat:$appCompatVersion&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="57"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        errorLine1="    implementation &quot;com.google.android.material:material:$materialVersion&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="58"
            column="31"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_FluentUI_Container` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.FluentUI.Container&quot; parent=&quot;Base.Theme.FluentUI&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="12"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_FluentUI` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.FluentUI&quot; parent=&quot;Theme.FluentUI.Container&quot;/>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="242"
            column="12"/>
    </issue>

</issues>
