<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI Demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Pasirinktas %s</string>
    <string name="app_modifiable_parameters">Modifikuojami parametrai</string>
    <string name="app_right_accessory_view">Dešiniojo reikmens rodinys</string>

    <string name="app_style">Stilius</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Paspausta piktograma</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Paleisti demonstracinę versiją</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Piktograma</string>
    <string name="actionbar_basic_radio_label">Bazinė</string>
    <string name="actionbar_position_bottom_radio_label">Apačioje</string>
    <string name="actionbar_position_top_radio_label">Viršus</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Veiksmų juostos tipas</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Veiksmų juostos padėtis</string>

    <!--AppBar-->
    <string name="app_bar_style">Programėlės juostos stilius</string>
    <string name="app_bar_subtitle">Subtitrai</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Apatinė kraštinė</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Spustelėta naršymo piktograma.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Vėliavėlė</string>
    <string name="app_bar_layout_menu_settings">Parametrai</string>
    <string name="app_bar_layout_menu_search">Ieška</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Slinkties veikimas: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Perjungti slinkties veikimo būdą</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Piktograma Perjungti naršymą</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Rodyti pseudoportretą</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Rodyti piktogramą Atgal</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Slėpti piktogramą</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Rodyti piktogramą</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Perjungti ieškos juostos maketo stilių</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Rodyti kaip reikmens rodinį</string>
    <string name="app_bar_layout_searchbar_action_view_button">Rodyti kaip veiksmo rodinį</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Perjungti temas (atkuriama veikla)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Perjungti temą</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Elementas</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Papildomas slenkamas turinys</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Apskritimo stilius</string>
    <string name="avatar_style_square">Kvadrato stilius</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXL</string>
    <string name="avatar_size_xlarge">XL</string>
    <string name="avatar_size_large">Didelis</string>
    <string name="avatar_size_medium">Vidutinis</string>
    <string name="avatar_size_small">Mažas</string>
    <string name="avatar_size_xsmall">XS</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dvigubas itin didelis</string>
    <string name="avatar_size_xlarge_accessibility">Itin didelis</string>
    <string name="avatar_size_xsmall_accessibility">Itin mažas</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Didžiausias rodomas pseudoportretų skaičius</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Perpildos pseudoportretų skaičius</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Kraštinės tipas</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Pseudoportretų grupė su rinkiniu OverflowAvatarCount neatitinka maksimalaus rodomo pseudoportreto.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Veidų dėklas</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Veidų krūva</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Spustelėta perpilda</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Spustelėtas „AvatarView“ indekse %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Pranešimų ženklelis</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Taškas</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Sąrašas</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Simbolis</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Nuotraukos</string>
    <string name="bottom_navigation_menu_item_news">Naujienos</string>
    <string name="bottom_navigation_menu_item_alerts">Įspėjimai</string>
    <string name="bottom_navigation_menu_item_calendar">Kalendorius</string>
    <string name="bottom_navigation_menu_item_team">Komanda</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Perjungti žymas</string>
    <string name="bottom_navigation_three_menu_items_button">Rodyti tris meniu elementus</string>
    <string name="bottom_navigation_four_menu_items_button">Rodyti keturis meniu elementus</string>
    <string name="bottom_navigation_five_menu_items_button">Rodyti penkis meniu elementus</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Žymos yra %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Norėdami išjungti, įgalinkite braukimą žemyn</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Rodyti su vienos eilutės elementais</string>
    <string name="bottom_sheet_with_double_line_items">Rodyti su dvigubos linijos elementais</string>
    <string name="bottom_sheet_with_single_line_header">Rodyti su vienos eilutės antrašte</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Rodyti su dvigubos linijos antrašte ir skyrikliais</string>
    <string name="bottom_sheet_dialog_button">Rodyti</string>
    <string name="drawer_content_desc_collapse_state">Išplėsti</string>
    <string name="drawer_content_desc_expand_state">Minimizuoti</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Spustelėtas %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Ilgas spustelėjimas %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Spustelėkite Atmesti</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Įterpti elementą</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Naujinti elementą</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Išjungti</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Įtraukti</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Paminėjimas</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Paryškintasis</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Pasvirasis</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Pabraukti</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Perbraukimas</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Anuliuoti</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Perdaryti</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Ženklelis</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Sąrašas</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Saitas</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Elementas naujinamas</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Tarpai</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Išjungti padėtį</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">PRADĖTI</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">BAIGTI</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Tarpas tarp grupių</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Tarpas tarp elementų</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Vėliavėlė</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Spustelėtas elemento žymėjimas vėliavėle</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Atsakymas</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Spustelėtas atsakymas į elementą</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Pirmyn</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Spustelėtas elementą persiuntimas</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Naikinti</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Spustelėtas elemento naikinimas</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Pseudonimas</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Fotografuoti</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Spustelėtas fotoaparato elementas</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerija</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Peržiūrėti nuotraukas</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Spustelėtas galerijos elementas</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Vaizdo įrašai</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Leisti vaizdo įrašus</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Spustelėtas vaizdo įrašų elementas</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Tvarkyti</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Valdykite savo medijos biblioteką</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Spustelėtas elemento valdymas</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">El. pašto veiksmai</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumentai</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Paskutinį kartą atnaujinta 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Bendrinti</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Spustelėtas elemento bendrinimas</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Perkelti</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Spustelėtas elemento perkėlimas</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Naikinti</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Spustelėtas elemento naikinimas</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informacija</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Spustelėtas informacijos elementas</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Laikrodis</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Spustelėtas laikrodžio elementas</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Signalas</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Spustelėtas signalo elementas</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Laiko juosta</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Spustelėtas laiko juostos elementas</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Skirtingi mygtuko rodiniai</string>
    <string name="button">Mygtukas</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Mygtukas Išjungtas, pavyzdys</string>
    <string name="button_borderless">Mygtukas Be kraštinių, pavyzdys</string>
    <string name="button_borderless_disabled">Mygtukas Be kraštinių išjungtas, pavyzdys</string>
    <string name="button_large">Mygtukas Didelis, pavyzdys</string>
    <string name="button_large_disabled">Mygtukas Didelis išjungtas, pavyzdys</string>
    <string name="button_outlined">Mygtukas Kontūras, pavyzdys</string>
    <string name="button_outlined_disabled">Mygtukas Kontūras išjungtas, pavyzdys</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Pasirinkti datą</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Viena data</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Data nepasirinkta</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Rodyti datos parinkiklį</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Pasirinktas datos ir laiko parinkiklio su datos skirtuku rodymas</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Pasirinktas datos ir laiko parinkiklio su laiko skirtuku rodymas</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Rodyti datos ir laiko parinkiklį</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datos intervalas</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Pradžia:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Pabaiga:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Pradžia neparinkta</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Pabaigos data nepasirinkta</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Pasirinkti pradžios datą</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Pasirinkti pabaigos datą</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Datos ir laiko intervalas</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Pasirinkti datos ir laiko intervalą</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Rodyti dialogo langą</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Rodyti skydelį</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Rodyti skydelio dialogo langą</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Apatinis dialogo langas be išnykimo</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Rodyti viršutinį skydelį</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Viršutinis dialogo langas be išnykimo</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Rodyti fiksavimo rodinio viršutinį dialogo langą</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Rodyti viršutinį dialogo langą be pavadinimo</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Rodyti viršutinį dialogo langą po pavadinimu</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Rodyti dešinįjį skydelį</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Rodyti kairįjį skydelį</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Pavadinimas, pirminis tekstas</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Paantraštė, antrinis tekstas</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Pasirinktinis paantraštės tekstas</string>
    <!-- Footer -->
    <string name="list_item_footer">Poraštė, tretinis tekstas</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Vienos eilutės sąrašas su pilku paantraštės tekstu</string>
    <string name="list_item_sub_header_two_line">Dviejų eilučių sąrašas</string>
    <string name="list_item_sub_header_two_line_dense">Dviejų eilučių sąrašas su tankiais tarpais</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Dviejų eilučių sąrašas su pasirinktiniu antrinės paantraštės rodiniu</string>
    <string name="list_item_sub_header_three_line">Trijų eilučių sąrašas su juodu paantraštės tekstu</string>
    <string name="list_item_sub_header_no_custom_views">Sąrašo elementai be pasirinktinių rodinių</string>
    <string name="list_item_sub_header_large_header">Sąrašo elementai su dideliais pasirinktiniais rodiniais</string>
    <string name="list_item_sub_header_wrapped_text">Sąrašo elementai su perkeltu tekstu</string>
    <string name="list_item_sub_header_truncated_text">Sąrašo elementai su sutrumpintu tekstu</string>
    <string name="list_item_sub_header_custom_accessory_text">Veiksmas</string>
    <string name="list_item_truncation_middle">Vidurinis sutrumpinimas.</string>
    <string name="list_item_truncation_end">Baigti trumpinimą.</string>
    <string name="list_item_truncation_start">Pradėti trumpinimą.</string>
    <string name="list_item_custom_text_view">Reikšmė</string>
    <string name="list_item_click">Spustelėjote sąrašo elementą.</string>
    <string name="list_item_click_custom_accessory_view">Spustelėjote pasirinktinį reikmens rodinį.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Spustelėjote paantraštės pasirinktinį reikmens rodinį.</string>
    <string name="list_item_more_options">Daugiau parinkčių</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Pasirinkti</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Nėra</string>
    <string name="people_picker_delete_example">Naikinti</string>
    <string name="people_picker_custom_persona_description">Šiame pavyzdyje rodoma, kaip sukurti pasirinktinį „IPersona“ objektą.</string>
    <string name="people_picker_dialog_title_removed">Pašalinote asmenį:</string>
    <string name="people_picker_dialog_title_added">Įtraukėte asmenį:</string>
    <string name="people_picker_drag_started">Vilkimas pradėtas</string>
    <string name="people_picker_drag_ended">Vilkimas baigtas</string>
    <string name="people_picker_picked_personas_listener">Asmenų nukreipimo priemonė</string>
    <string name="people_picker_suggestions_listener">Pasiūlymų nukreipimo priemonė</string>
    <string name="people_picker_persona_chip_click">Spustelėjote %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s gavėjas</item>
        <item quantity="few">%1$s gavėjai</item>
        <item quantity="many">%1$s gavėjo</item>
        <item quantity="other">%1$s gavėjų</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Išplėsti nuolatinį BottomSheet</string>
    <string name="collapse_persistent_sheet_button"> Slėpti nuolatinį BottomSheet</string>
    <string name="show_persistent_sheet_button"> Rodyti nuolatinį apatinį lapą</string>
    <string name="new_view">Tai yra naujas rodinys</string>
    <string name="toggle_sheet_content">Perjungti apatinio lapo turinį</string>
    <string name="switch_to_custom_content">Perjungti į pasirinktinį turinį</string>
    <string name="one_line_content">Vienos eilutės BottomSheet turinys</string>
    <string name="toggle_disable_all_items">Įjungti / išjungti visus elementus</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Įtraukti / šalinti rodinį</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Keisti sutrauktą aukštį</string>
    <string name="persistent_sheet_item_create_new_folder_title">Naujas aplankas</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Spustelėtas naujo aplanko elementas</string>
    <string name="persistent_sheet_item_edit_title">Redaguoti</string>
    <string name="persistent_sheet_item_edit_toast">Spustelėtas elemento redagavimas</string>
    <string name="persistent_sheet_item_save_title">Įrašyti</string>
    <string name="persistent_sheet_item_save_toast">Spustelėtas elemento įrašymas</string>
    <string name="persistent_sheet_item_zoom_in_title">Artinti</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Spustelėtas elemento priartinimas</string>
    <string name="persistent_sheet_item_zoom_out_title">Tolinti</string>
    <string name="persistent_sheet_item_zoom_out_toast">Spustelėtas elemento tolinimas</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Pasiekiama</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Jonas Vitkus</string>
    <string name="persona_name_kat_larsson">Ieva Barkutė</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Karolis Barkus</string>
    <string name="persona_name_kristen_patterson">Kotryna Butkienė</string>
    <string name="persona_name_lydia_bauer">Laima Banienė</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Marijus Bagdonas</string>
    <string name="persona_name_mona_kane">Monika Dapkutė</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Dizaino įrankis</string>
    <string name="persona_subtitle_engineer">Inžinierius</string>
    <string name="persona_subtitle_manager">Vadovas</string>
    <string name="persona_subtitle_researcher">Tyrinėtojas</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (ilgas teksto pavyzdys norint patikrinti sutrumpinimą)</string>
    <string name="persona_view_description_xxlarge">XXL pseudoportretas su trimis teksto eilutėmis</string>
    <string name="persona_view_description_large">Didelis pseudoportretas su dviem teksto eilutėmis</string>
    <string name="persona_view_description_small">Mažas pseudoportretas su viena teksto eilute</string>
    <string name="people_picker_hint">Nieko su rodoma užuomina</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Išjungtas asmens lustas</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Asmens lusto klaida</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Piktograma Asmens lustas be uždarymo</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Bazinis asmens lustas</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Spustelėjote pasirinktą asmens lustą.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Bendrinti</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Stebėti</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Kviesti žmones</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Puslapio atnaujinimas</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Atidaryti naršyklėje</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Tai yra kelių eilučių laikinasis meniu. Didžiausias eilučių skaičius nustatytas kaip dvi, o likusi teksto dalis bus trumpinama.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Visos naujienos</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Įrašytos naujienos</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Naujienos iš svetainių</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Pranešti ne darbo valandomis</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Pranešti, kai neaktyvus darbalaukyje</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Spustelėjote elementą:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Paprastasis meniu</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Paprastasis meniu2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Meniu su vienu pasirenkamu elementu ir skyrikliu</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Meniu su visais pasirenkamais elementais, piktogramomis ir ilgu tekstu</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Rodyti</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Apskrita eiga</string>
    <string name="circular_progress_xsmall">XS</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Maža</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Vidutinis</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Didelis</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linijinė eiga</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Neapibrėžta</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Apibrėžta</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Ieškos juosta</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofono atgalinis skambinimas</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Automatinė taisa</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Paspaustas mikrofonas</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Dešinysis rodinys paspaustas</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Klaviatūros ieška paspausta</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Rodyti įspėjimo juostą</string>
    <string name="fluentui_dismiss_snackbar">Išjungti įspėjimo juostą</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Veiksmas</string>
    <string name="snackbar_action_long">Ilgas teksto veiksmas</string>
    <string name="snackbar_single_line">Vienos eilutės įspėjimo juosta</string>
    <string name="snackbar_multiline">Tai kelių eilučių įspėjimo juosta. Didžiausias eilučių skaičius nustatytas į dvi, o likusi teksto dalis bus trumpinama.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Tai pranešimo įspėjimo juosta. Ji naudojama pranešti apie naujas funkcijas.</string>
    <string name="snackbar_primary">Tai pagrindinė įspėjimo juosta.</string>
    <string name="snackbar_light">Tai šviesi įspėjimo juosta.</string>
    <string name="snackbar_warning">Tai yra įspėjimo juosta.</string>
    <string name="snackbar_danger">Tai yra pavojaus juosta.</string>
    <string name="snackbar_description_single_line">Trumpa trukmė</string>
    <string name="snackbar_description_single_line_custom_view">Ilga trukmė su apskrita eiga kaip mažas pasirinktinis rodinys</string>
    <string name="snackbar_description_single_line_action">Trumpa trukmė su veiksmu</string>
    <string name="snackbar_description_single_line_action_custom_view">Trumpa trukmė su veiksmu ir vidutiniu pasirinktiniu rodiniu</string>
    <string name="snackbar_description_single_line_custom_text_color">Trumpa trukmė su tinkinta teksto spalva</string>
    <string name="snackbar_description_multiline">Ilga trukmė</string>
    <string name="snackbar_description_multiline_custom_view">Ilga trukmė su mažu pasirinktiniu rodiniu</string>
    <string name="snackbar_description_multiline_action">Neribota trukmė su veiksmu ir teksto naujinimais</string>
    <string name="snackbar_description_multiline_action_custom_view">Trumpa trukmė su veiksmu ir vidutiniu pasirinktiniu rodiniu</string>
    <string name="snackbar_description_multiline_action_long">Trumpa trukmė su ilgu veiksmo tekstu</string>
    <string name="snackbar_description_announcement">Trumpa trukmė</string>
    <string name="snackbar_description_updated">Šis tekstas buvo atnaujintas.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Rodyti įspėjimo juostą</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Vienguba linija</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Kelių linijų</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Pranešimo stilius</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Pirminis stilius</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Supaprastintas stilius</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Įspėjimo stilius</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Grėsmės stilius</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Pagrindinis</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Paštas</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Parametrai</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Pranešimas</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Daugiau</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Teksto lygiuotė</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertikalioji</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontaliai</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Nėra teksto</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Skirtuko elementai</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Pavadinimas</string>
    <string name="cell_sample_description">Aprašas</string>
    <string name="calculate_cells">Įkelti / apskaičiuoti 100 langelių</string>
    <string name="calculate_layouts">Įkelti / apskaičiuoti 100 maketų</string>
    <string name="template_list">Šablonų sąrašas</string>
    <string name="regular_list">Įprastas sąrašas</string>
    <string name="cell_example_title">Pavadinimas: langelis</string>
    <string name="cell_example_description">Aprašas: bakstelėkite norėdami pakeisti padėtį</string>
    <string name="vertical_layout">Vertikalusis išdėstymas</string>
    <string name="horizontal_layout">Horizontalus išdėstymas</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standartinis 2 segmentų skirtukas</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standartinis 3 segmentų skirtukas</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standartinis 4 segmentų skirtukas</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standartinis skirtukas su pranešimų gavikliu</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Perjungti skirtuką</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Piliulių skirtukas </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Bakstelėkite, kad gautumėte patarimą</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Bakstelėkite, kad gautumėte pasirinktinio kalendoriaus patarimą</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Bakstelėkite pasirinktinių spalvų patarimą</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Bakstelėkite norėdami išjungti įrankio patarimo viduje</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Bakstelėkite, kad gautumėte pasirinktinio rodinio patarimą</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Populiariausių pasirinktinių spalvų patarimas</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Pabaigos patarimas viršuje su 10dp poslinkiuX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Pradžios patarimas apačioje</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Pabaigos patarimas apačioje su 10dp poslinkiuY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Atmesti patarimo viduje</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Patarimas atmestas</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Antraštė yra supaprastinta 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">1 pavadinimas yra vidutinis 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">2 pavadinimas yra įprastas 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Antraštė yra įprasta 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">1 paantraštė yra įprasta 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">2 paantraštė yra vidutinė 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">1 pagrindinė dalis yra įprasta 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">2 pagrindinis tekstas yra vidutinis 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Antraštė yra įprasta 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK versija: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Elemento %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Aplankas</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Spustelėjo</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Pastoliai</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB išskleistas</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB suskleistas</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Spustelėkite, kad būtų atnaujintas sąrašas</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Atidaryti skydelį</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Meniu elementas</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Poslinkis X (dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Poslinkis Y (dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Turinio tekstas</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Kartoti turinio tekstą</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Meniu plotis keisis turinio teksto atžvilgiu. Maks.
        plotis apribotas iki 75 % ekrano dydžio. Turinio paraštė iš šono ir apačios valdoma atpažinimo ženklu. Tas pats turinio tekstas bus kartojamas, kad keistųsi
        aukštis.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Atidaryti meniu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Pagrindinė kortelė</string>
    <!-- UI Label for Card -->
    <string name="file_card">Failo kortelė</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Skelbimo kortelė</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Atsitiktinė vartotojo sąsaja</string>
    <!-- UI Label for Options -->
    <string name="card_options">Parinktys</string>
    <!-- UI Label for Title -->
    <string name="card_title">Pavadinimas</string>
    <!-- UI Label for text -->
    <string name="card_text">Tekstas</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Antrinis tekstas</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Antrinė šios reklaminės juostos kopija, jei reikia, gali būti keliama į dvi eilutes.</string>
    <!-- UI Label Button -->
    <string name="card_button">Mygtukas</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Rodyti dialogo langą</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Išjungti dialogo langą spustelėjus išorėje</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Išjungti dialogo langą paspaudus atgal</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialogo langas atmestas</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Atšaukti</string>
    <!-- UI Label Ok -->
    <string name="ok">Gerai</string>
    <!-- A sample description -->
    <string name="dialog_description">Dialogas yra mažas langas, raginantis vartotoją priimti sprendimą arba įvesti papildomą informaciją.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Atidaryti skydelį</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Išplėsti skydelį</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Uždaryti stalčių</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Pasirinkti stalčiaus tipą</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Viršus</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Visas stalčius rodomas matomame regione.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Apačia</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Visas stalčius rodomas matomame regione. Braukite aukštyn, kad perslinktumėte turinį. Išplečiamas stalčius išplečiamas naudojant vilkimo rankenėlę.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Kairys stumtelėjimas virš</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Stalčius stumtelimas virš į matomą sritį, iš kairiosios pusės.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Dešinys stumtelėjimas virš</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Skydelio skaidrė pereikite, kad matytumėte sritį iš dešiniojo krašto.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Apatinis stumtelėjimas virš</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Stalčiaus stumtelėjimas virš į matomą sritį iš ekrano apačios. Perbraukite aukštyn išplečiamą stalčių, kad likusi dalis būtų matomoje srityje &amp; tada slinkite.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Matoma skraistė</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Pasirinkti stalčiaus turinį</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Viso ekrano dydžio slenkamas turinys</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Daugiau nei pusė ekrano turinio</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mažiau nei pusė ekrano turinio</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dinaminio dydžio turinys</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Įdėtojo stalčiaus turinys</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Išplečiamas</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Praleisti atvirą būseną</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Drausti atsisakyti „Scrim“ spustelėjimu</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Rodyti rankenėlę</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Pavadinimas</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Ekrano patarimo tekstas</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Bakstelėkite norėdami gauti pasirinktinio turinio patarimą</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Pradžios viršuje </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Viršuje pabaigoje </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Apačioje pradžioje </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Apačioje pabaigoje </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centre </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Pasirinktinis centre</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Jei reikia leidimo pastabų atnaujinimų, </string>
    <string name="click_here">spustelėkite čia.</string>
    <string name="open_source_cross_platform">Atvirojo kodo kelių platformų dizaino sistema.</string>
    <string name="intuitive_and_powerful">Intuityvi ir galinga.</string>
    <string name="design_tokens">Dizaino atpažinimo ženklai</string>
    <string name="release_notes">Leidimo pastabos</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">„GitHub Repo“ saitas</string>
    <string name="report_issue">Pranešti apie problemą</string>
    <string name="v1_components">V1 komponentai</string>
    <string name="v2_components">V2 komponentai</string>
    <string name="all_components">Visi</string>
    <string name="fluent_logo">„Fluent“ logotipas</string>
    <string name="new_badge">Naujas</string>
    <string name="modified_badge">Modifikuota</string>
    <string name="api_break_badge">API lūžis</string>
    <string name="app_bar_more">Daugiau</string>
    <string name="accent">Akcentas</string>
    <string name="appearance">Išvaizda</string>
    <string name="choose_brand_theme">Pasirinkite savo prekės ženklo temą:</string>
    <string name="fluent_brand_theme">„Fluent“ prekės ženklas</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Pasirinkti išvaizdą</string>
    <string name="appearance_system_default">Sistemos numatytoji</string>
    <string name="appearance_light">Šviesi</string>
    <string name="appearance_dark">Tamsi</string>
    <string name="demo_activity_github_link">Demonstracinės veiklos „GitHub“ saitas</string>
    <string name="control_tokens_details">Išsami valdiklio atpažinimo ženklų informacija</string>
    <string name="parameters">Parametrai</string>
    <string name="control_tokens">Valdiklio atpažinimo ženklai</string>
    <string name="global_tokens">Visuotiniai atpažinimo ženklai</string>
    <string name="alias_tokens">Pseudonimo atpažinimo ženklai</string>
    <string name="sample_text">Tekstas</string>
    <string name="sample_icon">Piktogramos pavyzdys</string>
    <string name="color">Spalva</string>
    <string name="neutral_color_tokens">Neutralios spalvos atpažinimo ženklai</string>
    <string name="font_size_tokens">Šrifto dydžio atpažinimo ženklai</string>
    <string name="line_height_tokens">Eilutės aukščio atpažinimo ženklai</string>
    <string name="font_weight_tokens">Šrifto storio atpažinimo ženklai</string>
    <string name="icon_size_tokens">Piktogramų dydžio atpažinimo ženklai</string>
    <string name="size_tokens">Dydžio atpažinimo ženklai</string>
    <string name="shadow_tokens">Šešėlio atpažinimo ženklai</string>
    <string name="corner_radius_tokens">Kampo spindulio atpažinimo ženklai</string>
    <string name="stroke_width_tokens">Brūkštelėjimo stiliumi atpažinimo ženklai</string>
    <string name="brand_color_tokens">Prekės ženklo spalvos atpažinimo ženklai</string>
    <string name="neutral_background_color_tokens">Neutralios fono spalvos atpažinimo ženklai</string>
    <string name="neutral_foreground_color_tokens">Neutralios priekinio plano spalvos atpažinimo ženklai</string>
    <string name="neutral_stroke_color_tokens">Neutralios brūkštelėjimo stiliumi spalvos atpažinimo ženklai</string>
    <string name="brand_background_color_tokens">Prekės ženklo fono spalvos atpažinimo ženklai</string>
    <string name="brand_foreground_color_tokens">Prekės ženklo priekinio plano spalvos atpažinimo ženklai</string>
    <string name="brand_stroke_color_tokens">Prekės ženklo brūkštelėjimo stiliumi spalvos atpažinimo ženklai</string>
    <string name="error_and_status_color_tokens">Klaidos ir būsenos spalvos atpažinimo ženklai</string>
    <string name="presence_tokens">Buvimo spalvos atpažinimo ženklai</string>
    <string name="typography_tokens">Tipografijos atpažinimo ženklai</string>
    <string name="unspecified">Nenurodyta</string>

</resources>