<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI-demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Vald %s</string>
    <string name="app_modifiable_parameters">Ändringsbara parametrar</string>
    <string name="app_right_accessory_view">Höger tillbehörsvy</string>

    <string name="app_style">Format</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikon nedtryckt</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Starta demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Ikon</string>
    <string name="actionbar_basic_radio_label">Grundläggande</string>
    <string name="actionbar_position_bottom_radio_label">Nederkant</string>
    <string name="actionbar_position_top_radio_label">Överkant</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Typ av åtgärdsfält</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar Position</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar Style</string>
    <string name="app_bar_subtitle">Underrubrik</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Nedre kantlinje</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Klickade på navigeringsikonen.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Flagga</string>
    <string name="app_bar_layout_menu_settings">Inställningar</string>
    <string name="app_bar_layout_menu_search">Sök</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Rullningsbeteende: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Växla rullningsbeteende</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Växla navigeringsikon</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Visa avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Visa tillbaka-ikon</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Dölj ikon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Visa ikon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Växla layoutformat för sökfältet</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Visa som tillbehörsvy</string>
    <string name="app_bar_layout_searchbar_action_view_button">Visa som åtgärdsvy</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Växla mellan teman (återskapar aktivitet)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Växla tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Objekt</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Extra rullningsbart innehåll</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Cirkelformat</string>
    <string name="avatar_style_square">Fyrkantig stil</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Stor</string>
    <string name="avatar_size_medium">Mellan</string>
    <string name="avatar_size_small">Liten</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dubbel extra stor</string>
    <string name="avatar_size_xlarge_accessibility">Extra stor</string>
    <string name="avatar_size_xsmall_accessibility">Extra liten</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Max. visade avatarer</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Antal spillavatarer</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Kantlinjetyp</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatargruppen med OverflowAvatarCount-uppsättningen följer inte det maximala antalet visade avatarer.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Ansiktsstack</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Ansiktshög</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Klickade på spill</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Avatarvy vid index %d klickat</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Aviseringsmärke</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Prickad</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Karaktär</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Foton</string>
    <string name="bottom_navigation_menu_item_news">Nyheter</string>
    <string name="bottom_navigation_menu_item_alerts">Aviseringar</string>
    <string name="bottom_navigation_menu_item_calendar">Kalender</string>
    <string name="bottom_navigation_menu_item_team">Team</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Växla etiketter</string>
    <string name="bottom_navigation_three_menu_items_button">Visa tre menyalternativ</string>
    <string name="bottom_navigation_four_menu_items_button">Visa fyra menyalternativ</string>
    <string name="bottom_navigation_five_menu_items_button">Visa fem menyalternativ</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Etiketter är %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Aktivera Svep nedåt för att stänga</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Visa med enradsobjekt</string>
    <string name="bottom_sheet_with_double_line_items">Visa med dubbla radobjekt</string>
    <string name="bottom_sheet_with_single_line_header">Visa med enradsrubrik</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Visa med dubbel radrubrik och avdelare</string>
    <string name="bottom_sheet_dialog_button">Visa</string>
    <string name="drawer_content_desc_collapse_state">Expandera</string>
    <string name="drawer_content_desc_expand_state">Minimera</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klicka på %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Långklicka på %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klicka på Stäng</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Infoga objekt</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Uppdatera objekt</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Avvisa</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Lägg till</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Omnämnande</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Fet</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursiv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Understrykning</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Genomstruken</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Ångra</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Gör om</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Punkt</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Länk</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Objektet uppdateras</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Avstånd</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Stäng position</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">START</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Grupputrymme</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Objektutrymme</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Flagga</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Klickade på flagga objekt</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Svar</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Klickade på svarsobjekt</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Vidarebefordra</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Klickade på objekt framåt</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Ta bort</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Klickade på ta bort objekt</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Ta en bild</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Klickade på kameraobjekt</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galleri</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Visa dina foton</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Klickade på galleriobjekt</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videor</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Spela upp dina videor</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Klickade på videoobjekt</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Hantera</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Hantera ditt mediebibliotek</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Klickade på Hantera objekt</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-poståtgärder</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokument</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Senast uppdaterad 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Dela</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Klickade på dela objekt</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Flytta</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Klickade på flytta objekt</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Ta bort</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Klickade på ta bort objekt</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Info</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Klickade på informationsobjekt</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Klocka</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Klickade på klockobjekt</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">larm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Klickade på alarmobjekt</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Tidszon</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Klickade på tidszonsobjekt</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Olika vyer för knapp</string>
    <string name="button">Knapp</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Exempel på inaktiverad knapp</string>
    <string name="button_borderless">Exempel på knapp utan kantlinje</string>
    <string name="button_borderless_disabled">Exempel på inaktiverad knapp utan kantlinje</string>
    <string name="button_large">Exempel på stor knapp</string>
    <string name="button_large_disabled">Exempel på stor inaktiverad knapp</string>
    <string name="button_outlined">Exempel på konturerad knapp</string>
    <string name="button_outlined_disabled">Exempel på inaktiverad konturerad knapp</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Välj ett datum</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Enstaka datum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Inget datum valt</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Visa datumväljare</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Visa datum-/tidsväljaren med fliken Datum markerad</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Visa datum-/tidsväljaren med fliken Tid markerad</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Visa datum/tidsväljare</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datumintervall</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Start:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Sluta:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Ingen start har valts</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Inget slut har valts</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Välj startdatum</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Välj slutdatum</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Datum/tid-intervall</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Välj datum/tid-intervall</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Visa dialogruta</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Visa lådan</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Visa låddialogruta</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Ingen nedtoningsdialogruta</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Visa översta lådan</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Ingen toningsdialogruta överst</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Visa dialogrutan överst i fästpunktsvyn</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Visa ingen övre dialogruta för rubrik</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Visa dialogrutan nedan överst i rubriken</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Visa höger låda</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Visa vänster låda</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Rubrik, primär text</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Underrubrik, sekundär text</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Anpassad text för undertext</string>
    <!-- Footer -->
    <string name="list_item_footer">Sidfot, tertiär text</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Enradslista med grå underrubriktext</string>
    <string name="list_item_sub_header_two_line">Tvåradslista</string>
    <string name="list_item_sub_header_two_line_dense">Tvåradslista med tätt avstånd</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Tvåradslista med anpassad sekundär undertextvy</string>
    <string name="list_item_sub_header_three_line">Treradslista med svart underrubriktext</string>
    <string name="list_item_sub_header_no_custom_views">Lista objekt utan anpassade vyer</string>
    <string name="list_item_sub_header_large_header">Lista objekt med stora anpassade vyer</string>
    <string name="list_item_sub_header_wrapped_text">Lista objekt med radbruten text</string>
    <string name="list_item_sub_header_truncated_text">Lista objekt med trunkerad text</string>
    <string name="list_item_sub_header_custom_accessory_text">Åtgärd</string>
    <string name="list_item_truncation_middle">Mellantrunkering.</string>
    <string name="list_item_truncation_end">Avsluta trunkering.</string>
    <string name="list_item_truncation_start">Starta trunkering.</string>
    <string name="list_item_custom_text_view">Värde</string>
    <string name="list_item_click">Du klickade på listobjektet.</string>
    <string name="list_item_click_custom_accessory_view">Du klickade på den anpassade tillbehörsvyn.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Du klickade på den anpassade tillbehörsvyn för underrubriken.</string>
    <string name="list_item_more_options">Fler alternativ</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Välj</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Ingen</string>
    <string name="people_picker_delete_example">Ta bort</string>
    <string name="people_picker_custom_persona_description">Det här exemplet visar hur du skapar ett anpassat IPersona-objekt.</string>
    <string name="people_picker_dialog_title_removed">Du har tagit bort en profil:</string>
    <string name="people_picker_dialog_title_added">Du har lagt till en profil:</string>
    <string name="people_picker_drag_started">Dra har startats</string>
    <string name="people_picker_drag_ended">Dra slutade</string>
    <string name="people_picker_picked_personas_listener">Personlyssnare</string>
    <string name="people_picker_suggestions_listener">Förslagslyssnare</string>
    <string name="people_picker_persona_chip_click">Du klickade på %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s mottagare</item>
        <item quantity="other">%1$s mottagare</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Expandera beständigt BottomSheet</string>
    <string name="collapse_persistent_sheet_button"> Dölj beständigt BottomSheet</string>
    <string name="show_persistent_sheet_button"> Visa beständigt BottomSheet</string>
    <string name="new_view">Det här är en ny vy</string>
    <string name="toggle_sheet_content">Växla innehåll i Bottomsheet</string>
    <string name="switch_to_custom_content">Växla till anpassat innehåll</string>
    <string name="one_line_content">Innehåll i en rad i nedre bladet</string>
    <string name="toggle_disable_all_items">Växla Inaktivera alla objekt</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Lägg till/ta bort vy</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Ändra dold höjd</string>
    <string name="persistent_sheet_item_create_new_folder_title">Ny mapp</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Klickade på nytt mappobjekt</string>
    <string name="persistent_sheet_item_edit_title">Redigera</string>
    <string name="persistent_sheet_item_edit_toast">Klickade på redigera objekt</string>
    <string name="persistent_sheet_item_save_title">Spara</string>
    <string name="persistent_sheet_item_save_toast">Klickade på spara objekt</string>
    <string name="persistent_sheet_item_zoom_in_title">Zooma in</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Klickade på zooma in objekt</string>
    <string name="persistent_sheet_item_zoom_out_title">Zooma ut</string>
    <string name="persistent_sheet_item_zoom_out_toast">Klickade på objektet Zooma ut</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Tillgängligt</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Tekniker</string>
    <string name="persona_subtitle_manager">Chef</string>
    <string name="persona_subtitle_researcher">Researcher</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (långt textexempel för att testa trunkering)</string>
    <string name="persona_view_description_xxlarge">XXLarge-avatar med tre rader text</string>
    <string name="persona_view_description_large">Stor avatar med två rader text</string>
    <string name="persona_view_description_small">Liten avatar med en rad text</string>
    <string name="people_picker_hint">Inget med tips visas</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Inaktiverat persona-chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Fel persona-chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Persona Chip utan stängningsikon</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Grundläggande persona-chip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Du klickade på ett markerat persona-chip.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Dela</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Följ</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Bjud in personer</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Uppdatera sida</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Öppna i webbläsare</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Det här är en flerradsmeny för popup-fönster. Maximalt antal rader är två. Resten av texten trunkeras.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Alla nyheter</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Sparade nyheter</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Nyheter från webbplatser</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Meddela utanför arbetstid</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Meddela när det är inaktivt på skrivbordet</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Du klickade på objektet:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Enkel meny</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Enkel meny2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Meny med ett valbart objekt och en avgränsare</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Meny med alla valbara objekt, ikoner och lång text</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Visa</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Cirkulärt förlopp</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Liten</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Mellan</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Stor</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linjärt förlopp</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Obestämd</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinera</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Motringning av mikrofon</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autokorrigering</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofonen är nedtryckt</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Höger vy nedtryckt</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Tangentbordssökning nedtryckt</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Visa textfält</string>
    <string name="fluentui_dismiss_snackbar">Stäng textfält</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Åtgärd</string>
    <string name="snackbar_action_long">Åtgärd för lång text</string>
    <string name="snackbar_single_line">Enradig tilltuggbar</string>
    <string name="snackbar_multiline">Det här är en tilltuggbar med flera rader. Maximalt antal rader är två. Resten av texten trunkeras.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Det här är en tilltuggbar för meddelanden. Den används för att kommunicera nya funktioner.</string>
    <string name="snackbar_primary">Det här är en primär tilltuggbar.</string>
    <string name="snackbar_light">Det här är en lätt tilltuggbar.</string>
    <string name="snackbar_warning">Det här är ett varningssnacksfält.</string>
    <string name="snackbar_danger">Det här är en risk för snacksfält.</string>
    <string name="snackbar_description_single_line">Kort varaktighet</string>
    <string name="snackbar_description_single_line_custom_view">Lång varaktighet med cirkulärt förlopp som liten anpassad vy</string>
    <string name="snackbar_description_single_line_action">Kort varaktighet med åtgärd</string>
    <string name="snackbar_description_single_line_action_custom_view">Kort varaktighet med åtgärd och medelstor anpassad vy</string>
    <string name="snackbar_description_single_line_custom_text_color">Kort varaktighet med anpassad textfärg</string>
    <string name="snackbar_description_multiline">Lång varaktighet</string>
    <string name="snackbar_description_multiline_custom_view">Lång varaktighet med liten anpassad vy</string>
    <string name="snackbar_description_multiline_action">Obegränsad varaktighet med åtgärds- och textuppdateringar</string>
    <string name="snackbar_description_multiline_action_custom_view">Kort varaktighet med åtgärd och medelstor anpassad vy</string>
    <string name="snackbar_description_multiline_action_long">Kort varaktighet med lång åtgärdstext</string>
    <string name="snackbar_description_announcement">Kort varaktighet</string>
    <string name="snackbar_description_updated">Den här texten har uppdaterats.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Visa tilltuggbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Enkel linje</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Flera linjer</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Meddelandeformat</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primärt format</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Ljust format</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Varningsformat</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Stil för fara</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Startsida</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Post</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Inställningar</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Meddelande</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mer</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Textjustering</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Lodrät</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vågrätt</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Ingen text</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Flikobjekt</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Rubrik</string>
    <string name="cell_sample_description">Beskrivning</string>
    <string name="calculate_cells">Läsa in/beräkna 100 celler</string>
    <string name="calculate_layouts">Läs in/beräkna 100 layouter</string>
    <string name="template_list">Mallista</string>
    <string name="regular_list">Vanlig lista</string>
    <string name="cell_example_title">Rubrikcell</string>
    <string name="cell_example_description">Beskrivning: Tryck för att ändra orientering</string>
    <string name="vertical_layout">Lodrät layout</string>
    <string name="horizontal_layout">Vågrät layout</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standardflik 2-segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardflik 3-segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardflik 4-segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardflik med personsökare</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Växla flik</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Fliken Pills </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tryck för knappbeskrivning</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tryck för knappbeskrivning för anpassad kalender</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Tryck på Knappbeskrivning för anpassad färg</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tryck för knappbeskrivning för att stänga inuti</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tryck knappbeskrivning för anpassad vy</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Tryck för knappbeskrivning för anpassad färg</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Knappbeskrivning i överkant med 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Knappbeskrivning för nedre start</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Knappbeskrivning i nederkant med 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Knappbeskrivning för att stänga inuti</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Knappbeskrivningen har stängts</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Rubriken är ljus 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Rubrik 1 är medelstor 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Rubrik 2 är vanlig 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Rubriken är normal 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Underrubrik 1 är normal 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Underrubrik 2 är mellanstor 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Brödtext 1 är normal 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Brödtext 2 är medium 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Undertext är normal 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-version: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Objekt %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mapp</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Klickad</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Byggnadsställning</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB utökad</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB dold</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klicka för att uppdatera listan</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Öppna lådan</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menykommando</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Förskjutning X (i dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Förskjutning Y (i dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Innehållstext</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Upprepa innehållstext</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menybredden ändras vad gäller innehållstext. 
        Maxbredden är begränsad till 75 % av skärmstorleken. Innehållsmarginalen från sidan och nederkanten styrs av token. Samma innehållstext upprepas för att variera
        höjden.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Öppna-meny</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Basic-kort</string>
    <!-- UI Label for Card -->
    <string name="file_card">Filkort</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Meddelandekort</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Random UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">Alternativ</string>
    <!-- UI Label for Title -->
    <string name="card_title">Rubrik</string>
    <!-- UI Label for text -->
    <string name="card_text">Text</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Undertext</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Den sekundära kopian för den här banderollen kan radbrytas till två rader om det behövs.</string>
    <!-- UI Label Button -->
    <string name="card_button">Knapp</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Visa dialogruta</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Stäng dialogrutan när du klickar utanför</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Stäng dialogrutan vid bakåttryckning</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialogrutan har stängts</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Avbryt</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">En dialogruta är ett litet fönster som uppmanar användaren att fatta ett beslut eller ange ytterligare information.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Öppna lådan</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Expandera lådan</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Stäng lådan</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Välj lådtyp</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Överkant</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Hela lådan visas i det synliga området.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Nederkant</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Hela lådan visas i det synliga området. Svep uppåt för att scrolla rullningsinnehåll. Expanderbar låda expanderas via draghandtag.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Vänster bild över</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Lådan skjuts över till synligt område från vänster sida.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Höger bild över</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Lådan skjuts över till synligt område från höger sida.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Nederkant bild över</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Lådan skjuts över till synligt område från skärmens nederkant. Svep uppåt på den expanderbara lådan så att resten av delen hamnar i ett synligt område och bläddra sedan.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Skrimsynlig</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Välj lådinnehåll</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Rullningsbart innehåll i helskärmsstorlek</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mer än halvskärmsinnehåll</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mindre än halvskärmsinnehåll</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Innehåll i dynamisk storlek</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Innehåll i staplad låda</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Utökningsbar</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Hoppa över öppet tillstånd</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Förhindra avstängning på Scrim Click</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Visa handtag</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Rubrik</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Knappbeskrivningstext</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tryck för knappbeskrivning för anpassat innehåll</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Övre start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Övre ände </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Nedre start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Nedre ände </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centrera </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Anpassat center</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">För uppdateringar av Viktig information </string>
    <string name="click_here">klickar du här.</string>
    <string name="open_source_cross_platform">Designsystem med öppen källkod mellan plattformar.</string>
    <string name="intuitive_and_powerful">Intuitivt och kraftfullt.</string>
    <string name="design_tokens">Designtoken</string>
    <string name="release_notes">Viktig information</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub-lagringsplatslänk</string>
    <string name="report_issue">Rapportera problem</string>
    <string name="v1_components">V1-komponenter</string>
    <string name="v2_components">V2-komponenter</string>
    <string name="all_components">Alla</string>
    <string name="fluent_logo">Fluent-logotyp</string>
    <string name="new_badge">Nytt</string>
    <string name="modified_badge">Ändrades</string>
    <string name="api_break_badge">API-avbrott</string>
    <string name="app_bar_more">Mer</string>
    <string name="accent">Accentfärg</string>
    <string name="appearance">Utseende</string>
    <string name="choose_brand_theme">Välj ditt varumärkestema:</string>
    <string name="fluent_brand_theme">Fluent-varumärke</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Välj utseende</string>
    <string name="appearance_system_default">Systemstandard</string>
    <string name="appearance_light">Ljust</string>
    <string name="appearance_dark">Mörkt</string>
    <string name="demo_activity_github_link">GitHub-länk för demoaktivitet</string>
    <string name="control_tokens_details">Information om kontrolltoken</string>
    <string name="parameters">Parametrar</string>
    <string name="control_tokens">Kontrolltoken</string>
    <string name="global_tokens">Globala tokens</string>
    <string name="alias_tokens">Aliastoken</string>
    <string name="sample_text">Text</string>
    <string name="sample_icon">Exempelikon</string>
    <string name="color">Färg</string>
    <string name="neutral_color_tokens">Neutrala färgtoken</string>
    <string name="font_size_tokens">Token för teckenstorlek</string>
    <string name="line_height_tokens">Linjehöjdstoken</string>
    <string name="font_weight_tokens">Teckenviktstoken</string>
    <string name="icon_size_tokens">Token för ikonstorlek</string>
    <string name="size_tokens">Storlekstoken</string>
    <string name="shadow_tokens">Skuggtoken</string>
    <string name="corner_radius_tokens">RadiusTokens i hörn</string>
    <string name="stroke_width_tokens">Token för streckbredd</string>
    <string name="brand_color_tokens">Färgtoken för varumärke</string>
    <string name="neutral_background_color_tokens">Neutral bakgrundsfärgtoken</string>
    <string name="neutral_foreground_color_tokens">Neutrala färgtoken för förgrund</string>
    <string name="neutral_stroke_color_tokens">Färgtoken för neutrala streck</string>
    <string name="brand_background_color_tokens">Färgtoken för varumärkesbakgrund</string>
    <string name="brand_foreground_color_tokens">Färgtoken för varumärkesförgrund</string>
    <string name="brand_stroke_color_tokens">Färgtoken för varumärkesstreck</string>
    <string name="error_and_status_color_tokens">Färgtoken för fel och status</string>
    <string name="presence_tokens">Närvarofärgtoken</string>
    <string name="typography_tokens">Typografitoken</string>
    <string name="unspecified">Ospecificerad</string>

</resources>