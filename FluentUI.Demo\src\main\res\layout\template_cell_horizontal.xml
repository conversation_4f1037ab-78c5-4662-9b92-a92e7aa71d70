<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/template_view_cell_horizontal_background"
    android:baselineAligned="true"
    android:orientation="horizontal"
    android:padding="@dimen/default_view_margin">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/cell_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.DemoListItem"
        tools:text="@string/cell_sample_title" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/cell_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/cell_horizontal_spacing"
        android:textAppearance="@style/TextAppearance.DemoListItemSubtitle"
        tools:text="@string/cell_sample_description" />

</LinearLayout>