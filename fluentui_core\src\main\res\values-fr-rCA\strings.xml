<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Principal</string>
    <string name="fluentui_secondary">Secondaire</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Ignorer</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Sélectionnée</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Non sélectionné</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Icône</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Icône</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Désactivé</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Bouton d’action</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Activé</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Fermer la feuille</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Fermer</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Annuler</string>
    <!--name of the icon -->
    <string name="fluentui_search">Rechercher</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Micro</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Effacer le texte</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Précédent</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Activé</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Désactivé</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutre</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marque</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contraste</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Accentuation</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Avertissement</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Danger</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Une erreur s’est produite.</string>
    <string name="fluentui_error">Erreur</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Conseil</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Chevron</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Plan</string>

    <string name="fluentui_action_button_icon">Icône du bouton Action</string>
    <string name="fluentui_center">Centrer le texte</string>
    <string name="fluentui_accessory_button">Boutons d’accessoire</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Case d’option</string>
    <string name="fluentui_label">Étiquette</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Étendu</string>
    <string name="fluentui_collapsed">Réduit</string>

    <!--types of control -->
    <string name="fluentui_large">Grande</string>
    <string name="fluentui_medium">Moyen</string>
    <string name="fluentui_small">Petite</string>
    <string name="fluentui_password_mode">Mode mot de passe</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Sous-titre</string>
    <string name="fluentui_assistive_text">Texte auxiliaire</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Titre</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Court</string>"
    <string name="fluentui_long">Long</string>"
    <string name="fluentui_indefinite">Indéfinie</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Bouton enfoncé</string>
    <string name="fluentui_dismissed">Ignoré</string>
    <string name="fluentui_timeout">Expiration du délai</string>
    <string name="fluentui_left_swiped">Balayage vers la gauche</string>
    <string name="fluentui_right_swiped">Balayage vers la droite</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Texte</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Nombre</string>
    <string name="fluentui_keyboard_phone">Téléphone</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">E-mail</string>
    <string name="fluentui_keyboard_password">Mot de passe</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Décimal</string>
</resources>