<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/persistent_bottom_sheet_outlined"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/fluentui_transparent">

    <com.microsoft.fluentui.drawer.DrawerView
        android:id="@+id/persistent_bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/fluentuiDrawerBackgroundColor"
        android:clipToPadding="true"
        android:elevation="@dimen/fluentui_persistentbottomsheet_elevation"
        android:orientation="vertical"
        app:fluentui_behaviorType="BOTTOM"
        app:cornerRadius="@dimen/fluentui_persistentbottomsheet_corner_radius"
        app:layout_behavior="@string/bottom_sheet_behavior">

        <ImageView
            android:id="@+id/sheet_drawer_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingTop="@dimen/fluentui_drawer_handle_vertical_margin"
            android:paddingBottom="@dimen/fluentui_drawer_handle_vertical_margin"
            android:paddingStart="@dimen/fluentui_drawer_handle_horizontal_margin"
            android:contentDescription=" "
            android:paddingEnd="@dimen/fluentui_drawer_handle_horizontal_margin"
            android:src="@drawable/ic_drawer_handle"
            app:tint="?attr/fluentuiDrawerHandleColor"
            tools:ignore="HardcodedText" />

        <com.microsoft.fluentui.persistentbottomsheet.LockableNestedScrollView
            android:id="@+id/scroll_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/persistent_sheet_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:paddingBottom="@dimen/fluentui_persistent_bottomsheet_content_padding_vertical"
                android:orientation="vertical" />
        </com.microsoft.fluentui.persistentbottomsheet.LockableNestedScrollView>
    </com.microsoft.fluentui.drawer.DrawerView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>