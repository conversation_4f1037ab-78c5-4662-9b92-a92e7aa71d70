<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demostració de la interfície d\'usuari de Fluent</string>
    <string name="app_title">Interfície d\'usuari de Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">S\'ha seleccionat %s</string>
    <string name="app_modifiable_parameters">Paràmetres modificables</string>
    <string name="app_right_accessory_view">Visualització de l’accessori dret</string>

    <string name="app_style">Estil</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">S\'ha premut la icona</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Inicia la demostració</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Seqüència</string>
    <string name="actionbar_icon_radio_label">Icona</string>
    <string name="actionbar_basic_radio_label">Bàsic</string>
    <string name="actionbar_position_bottom_radio_label">Inferior</string>
    <string name="actionbar_position_top_radio_label">Superior</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tipus de barra d\'accions</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Posició de la barra d\'accions</string>

    <!--AppBar-->
    <string name="app_bar_style">Estil de la barra d\'aplicacions</string>
    <string name="app_bar_subtitle">Subtítol</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Vora inferior</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">S\'ha fet clic a la icona de navegació.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Indicador</string>
    <string name="app_bar_layout_menu_settings">Configuració</string>
    <string name="app_bar_layout_menu_search">Cerca</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Comportament del desplaçament: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Commutador del comportament del desplaçament</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Icona Activa la navegació</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Mostra l\'avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Mostra la icona d’enrere</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Amaga la icona</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Mostra la icona</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Commuta l\'estil de disposició de la barra de cerca</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Mostra com a visualització per a accessori</string>
    <string name="app_bar_layout_searchbar_action_view_button">Mostra com a visualització d\'accions</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Commuta entre temes (torna a crear l\'activitat)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Commuta el tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Contingut extra desplaçable</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Estil de cercle</string>
    <string name="avatar_style_square">Estil quadrat</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">Extragran</string>
    <string name="avatar_size_xlarge">Supergran</string>
    <string name="avatar_size_large">Gran</string>
    <string name="avatar_size_medium">Mitjana</string>
    <string name="avatar_size_small">Petita</string>
    <string name="avatar_size_xsmall">Superpetita</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Doble molt gran</string>
    <string name="avatar_size_xlarge_accessibility">Molt gran</string>
    <string name="avatar_size_xsmall_accessibility">Molt petit</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Avatar màxim mostrat</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Recompte d\'avatars de desbordament</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tipus de vora</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">El grup d\'avatars amb OverflowAvatarCount definit no s\'adherirà al màxim de l\'avatar que es mostra.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Pila de cares</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Pila de cara</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">S\'ha fet clic en un desbordament</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">S’ha fet clic a l\'índex %d de AvatarView</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Distintiu de notificació</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punt</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Llista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Caràcter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotos</string>
    <string name="bottom_navigation_menu_item_news">Notícies</string>
    <string name="bottom_navigation_menu_item_alerts">Avisos</string>
    <string name="bottom_navigation_menu_item_calendar">Calendari</string>
    <string name="bottom_navigation_menu_item_team">Equip</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Commuta les etiquetes</string>
    <string name="bottom_navigation_three_menu_items_button">Mostra tres elements del menú</string>
    <string name="bottom_navigation_four_menu_items_button">Mostra quatre elements del menú</string>
    <string name="bottom_navigation_five_menu_items_button">Mostra cinc elements del menú</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Les etiquetes són %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Full inferior</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Habiliteu l’opció de passar el dit cap avall per ignorar</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Mostra amb elements d\'una sola línia</string>
    <string name="bottom_sheet_with_double_line_items">Mostra amb elements de doble línia</string>
    <string name="bottom_sheet_with_single_line_header">Mostra amb la capçalera d\'una línia</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Mostra amb la capçalera i els divisors de doble línia</string>
    <string name="bottom_sheet_dialog_button">Mostra</string>
    <string name="drawer_content_desc_collapse_state">Expandeix</string>
    <string name="drawer_content_desc_expand_state">Minimitza</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Fes clic a %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Clic llarg %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Clic a Descartar</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Inserció d\'elements</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Actualitza l\'element</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Ignora</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Afegeix</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Menció</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Negreta</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Cursiva</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Subratllat</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Ratllat</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Desfés</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Refés</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Pic</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Llista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Enllaç</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">S\'està actualitzant l\'element</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Espaiat</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Descarta la posició</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">INICI</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Espai de grup</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Espai de l\'element</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Indicador</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">S\'ha fet clic a l’element de marca</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Respon</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">S\'ha fet clic a l\'element de resposta</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Endavant</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">S\'ha fet clic a l’element de reenviar</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Suprimeix</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">S\'ha fet clic a l’element de suprimir</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Càmera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Fes una foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">S\'ha fet clic a l\'element de càmera</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galeria</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Visualitza les fotos</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">S\'ha fet clic a l\'element de la galeria</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Vídeos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Reprodueix els vídeos</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">S\'ha fet clic a l\'element de vídeos</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Administra</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Administra la biblioteca multimèdia</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">S\'ha fet clic a l\'element d’administració</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Accions de correu</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documents</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Darrera actualització: 14:14 h</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Comparteix</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">S\'ha fet clic a l’element de compartir</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Desplaça</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">S\'ha fet clic a l’element de moure</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Suprimeix</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">S\'ha fet clic a l’element de suprimir</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informació</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">S\'ha fet clic a l\'element d\'informació</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Rellotge de paret</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">S\'ha fet clic a l\'element del rellotge</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarma</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">S\'ha fet clic a l\'element d’alarma</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Fus horari</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">S\'ha fet clic a l\'element de fus horari</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Visualitzacions diferents dels botons</string>
    <string name="button">Botó</string>
    <string name="buttonbar">Barra de botons</string>
    <string name="button_disabled">Exemple de botó inhabilitat</string>
    <string name="button_borderless">Exemple de botó sense vores</string>
    <string name="button_borderless_disabled">Exemple de botó sense vores inhabilitat</string>
    <string name="button_large">Exemple de botó gran</string>
    <string name="button_large_disabled">Exemple de botó gran inhabilitat</string>
    <string name="button_outlined">Exemple de botó amb contorn</string>
    <string name="button_outlined_disabled">Exemple de botó inhabilitat amb contorn</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Tria una data</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Data única</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">No s\'ha triat cap data</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Mostra el selector de data</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Mostra el selector de data i hora amb la pestanya de data seleccionada</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Mostra el selector de data i hora amb la pestanya d\'hora seleccionada</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Mostra el selector de data i hora</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Interval de dades</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Inici:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Fi:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">No s\'ha triat cap inici</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">No s\'ha seleccionat cap final</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Selecciona la data d\'inici</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Selecciona la data de finalització</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Interval de dates</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Seleccioneu l\'interval de dates</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Mostra el quadre de diàleg</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Mostra el calaix</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Mostra el quadre de diàleg del calaix</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">No hi ha cap quadre de diàleg de fosa a la part inferior</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Mostra el calaix superior</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">No hi ha cap quadre de diàleg superior de fosa</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Mostra el quadre de diàleg de la visualització d\'ancoratge superior</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Mostra quadre de diàleg superior sense títol</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Mostra el quadre de diàleg de la part superior a sota del títol</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Mostra el calaix dret</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Mostra el calaix esquerre</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Títol, text principal</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtítol, text secundari</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Text de subtítol personalitzat</string>
    <!-- Footer -->
    <string name="list_item_footer">Peu, text terciari</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Llista d\'una sola línia amb text de subcapçalera gris</string>
    <string name="list_item_sub_header_two_line">Llista de dues línies</string>
    <string name="list_item_sub_header_two_line_dense">Llista de dues línies amb espaiat dens</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Llista de dues línies amb visualització de subtítols secundària personalitzada</string>
    <string name="list_item_sub_header_three_line">Llista de tres línies amb text de subtítol negre</string>
    <string name="list_item_sub_header_no_custom_views">Elements de la llista sense visualitzacions personalitzades</string>
    <string name="list_item_sub_header_large_header">Enumera elements amb visualitzacions personalitzades grans</string>
    <string name="list_item_sub_header_wrapped_text">Enumera elements amb text ajustat</string>
    <string name="list_item_sub_header_truncated_text">Elements de la llista amb text truncat</string>
    <string name="list_item_sub_header_custom_accessory_text">Acció</string>
    <string name="list_item_truncation_middle">Truncament mitjà.</string>
    <string name="list_item_truncation_end">Finalitza el truncament.</string>
    <string name="list_item_truncation_start">Inicia el truncament.</string>
    <string name="list_item_custom_text_view">Valor</string>
    <string name="list_item_click">Heu fet clic a l\'element de la llista.</string>
    <string name="list_item_click_custom_accessory_view">Heu fet clic a la visualització per a accessoris personalitzada.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Heu fet clic a la visualització d\'accessori personalitzat de subtítols.</string>
    <string name="list_item_more_options">Més opcions</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Selecciona</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Cap</string>
    <string name="people_picker_delete_example">Suprimeix</string>
    <string name="people_picker_custom_persona_description">En aquest exemple es mostra com crear un objecte personalitzat d’IPersona.</string>
    <string name="people_picker_dialog_title_removed">Heu suprimit una imatge:</string>
    <string name="people_picker_dialog_title_added">Heu afegit una imatge:</string>
    <string name="people_picker_drag_started">L\'arrossegament ha començat</string>
    <string name="people_picker_drag_ended">L\'arrossegament ha finalitzat</string>
    <string name="people_picker_picked_personas_listener">Oient d’imatges</string>
    <string name="people_picker_suggestions_listener">Oient de suggeriments</string>
    <string name="people_picker_persona_chip_click">Heu fet clic a %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s destinatari</item>
        <item quantity="other">%1$s destinataris</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Expandeix el full inferior persistent</string>
    <string name="collapse_persistent_sheet_button"> Amaga el full inferior persistent</string>
    <string name="show_persistent_sheet_button"> Mostra el full inferior persistent</string>
    <string name="new_view">Aquesta és una visualització nova</string>
    <string name="toggle_sheet_content">Commuta el contingut del full inferior</string>
    <string name="switch_to_custom_content">Canvia al contingut personalitzat</string>
    <string name="one_line_content">Contingut d\'una línia del full de càlcul inferior</string>
    <string name="toggle_disable_all_items">Commuta Inhabilita tots els elements</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Afegeix o suprimeix la visualització</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Canvia l\'alçada replegada</string>
    <string name="persistent_sheet_item_create_new_folder_title">Crea una carpeta</string>
    <string name="persistent_sheet_item_create_new_folder_toast">S\'ha fet clic a un element de carpeta nou</string>
    <string name="persistent_sheet_item_edit_title">Edita</string>
    <string name="persistent_sheet_item_edit_toast">S\'ha fet clic a l\'element d’edició</string>
    <string name="persistent_sheet_item_save_title">Desa</string>
    <string name="persistent_sheet_item_save_toast">S\'ha fet clic a l\'element de desar</string>
    <string name="persistent_sheet_item_zoom_in_title">Amplia</string>
    <string name="persistent_sheet_item_zoom_in_toast"> S\'ha fet clic a l\'element d’ampliar</string>
    <string name="persistent_sheet_item_zoom_out_title">Redueix</string>
    <string name="persistent_sheet_item_zoom_out_toast">S\'ha fet clic a l\'element de reduir</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Disponible</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Joan Portet</string>
    <string name="persona_name_kat_larsson">Kati Martínez</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Pere Carbonell</string>
    <string name="persona_name_kristen_patterson">Cristina Mundó</string>
    <string name="persona_name_lydia_bauer">Lídia Garcia</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Esteve Desvalls</string>
    <string name="persona_name_mona_kane">Maria Padrós</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Dissenyador</string>
    <string name="persona_subtitle_engineer">Enginyer</string>
    <string name="persona_subtitle_manager">Administrador</string>
    <string name="persona_subtitle_researcher">Cercador</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (exemple de text llarg per provar el truncament)</string>
    <string name="persona_view_description_xxlarge">Avatar extragran amb tres línies de text</string>
    <string name="persona_view_description_large">Avatar gros amb dues línies de text</string>
    <string name="persona_view_description_small">Avatar petit amb una línia de text</string>
    <string name="people_picker_hint">Cap amb suggeriment mostrat</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Xip d’imatge inhabilitat</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Xip d’imatge erroni</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Xip d’imatge sense icona de tancament</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Xip d’imatge bàsic</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Heu fet clic en un xip d’imatge seleccionat.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Comparteix</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Segueix</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Convida persones</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Actualitza la pàgina</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Obre al navegador</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Aquest és un menú emergent de diverses línies. Les línies màximes estan definides en dos; la resta del text es truncarà.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Totes les notícies</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Notícies desades</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Notícies de llocs</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Notifica-ho fora de l\'horari laboral</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notifica\'m quan estigui inactiu a l\'escriptori</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Heu fet clic a l\'element:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menú senzill</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menú senzill2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menú amb un element selectable i un divisor</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menú amb tots els elements seleccionables, icones i text llarg</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Mostra</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Progrés circular</string>
    <string name="circular_progress_xsmall">Superpetita</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Petita</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Mitjana</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Gran</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Progrés lineal</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Indeterminat</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinades</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Barra de cerca</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Resposta de trucada del micròfon</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Correcció automàtica</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">S\'ha premut el micròfon</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">S\'ha premut la visualització dreta</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">S\'ha premut la cerca del teclat</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Mostra la barra de notificacions</string>
    <string name="fluentui_dismiss_snackbar">Ignora la barra d\'aperitius</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Acció</string>
    <string name="snackbar_action_long">Acció de text llarg</string>
    <string name="snackbar_single_line">Barra de notificacions d\'una sola línia</string>
    <string name="snackbar_multiline">Aquesta és una barra de notificacions amb diverses línies. El nombre màxim de línies està establert en dues; la resta del text es truncarà.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Aquesta és una barra de notificacions d’anuncis. Es fa servir per comunicar característiques noves.</string>
    <string name="snackbar_primary">Aquesta és una barra de notificacions principal.</string>
    <string name="snackbar_light">Això és una barra de notificacions clara.</string>
    <string name="snackbar_warning">Això és una barra de notificacions d\'advertiment.</string>
    <string name="snackbar_danger">Això és una barra de notificacions de perill.</string>
    <string name="snackbar_description_single_line">Durada curta</string>
    <string name="snackbar_description_single_line_custom_view">Durada llarga amb progrés circular com a visualització personalitzada petita</string>
    <string name="snackbar_description_single_line_action">Durada curta amb acció</string>
    <string name="snackbar_description_single_line_action_custom_view">Durada curta amb acció i visualització personalitzada mitjana</string>
    <string name="snackbar_description_single_line_custom_text_color">Durada curta amb color de text personalitzat</string>
    <string name="snackbar_description_multiline">Durada llarga</string>
    <string name="snackbar_description_multiline_custom_view">Durada llarga amb una visualització personalitzada petita</string>
    <string name="snackbar_description_multiline_action">Durada indefinida amb actualitzacions d\'acció i text</string>
    <string name="snackbar_description_multiline_action_custom_view">Durada curta amb acció i visualització personalitzada mitjana</string>
    <string name="snackbar_description_multiline_action_long">Durada curta amb text d\'acció llarg</string>
    <string name="snackbar_description_announcement">Durada curta</string>
    <string name="snackbar_description_updated">Aquest text s\'ha actualitzat.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Mostra la barra de notificacions</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Línia única</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Multilínia</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Estil de l\'anunci</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Estil principal</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Estil clar</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Estil d\'advertiment</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Estil de perill</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Inici</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Correu electrònic</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Configuració</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notificació</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Més</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Alineació del text</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertical</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horitzontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Sense text</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Elements de la pestanya</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Títol</string>
    <string name="cell_sample_description">Descripció</string>
    <string name="calculate_cells">Carrega/calcula 100 cel·les</string>
    <string name="calculate_layouts">Carrega/calcula 100 disposicions</string>
    <string name="template_list">Llista de plantilles</string>
    <string name="regular_list">Llista normal</string>
    <string name="cell_example_title">Títol: cel·la</string>
    <string name="cell_example_description">Descripció: toqueu per canviar l\'orientació</string>
    <string name="vertical_layout">Disposició vertical</string>
    <string name="horizontal_layout">Disposició horitzontal</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Segment 2 de pestanya estàndard</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Pestanya estàndard de 3 segments</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Pestanya estàndard de 4 segments</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Pestanya estàndard amb cercapersones</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Canvia la pestanya</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Pestanya de píndoles </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Toca per obtenir l’indicador de funció</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Toca per obtenir l’indicador de funció del calendari personalitzat</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Toca l’indicador de funció de color personalitzat</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Toca per ignorar l’indicador de funció interior</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Toca per obtenir l’indicador de funció d’eines de visualització personalitzada</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Indicador de funció de color personalitzat superior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Indicador de funció de l’extrem superior amb 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Indicador de funció de l\'Inici inferior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Indicador de funció de l’extrem inferior amb 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Ignora l’indicador de funció interior</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Indicador de funció descartat</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">El títol és clar 28p</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">El títol 1 és mitjà de 20p</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">El títol 2 és regular 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">El títol és normal 18p</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">El subtítol 1 és normal de 16p</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">El subtítol 2 és mitjà de 16p</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">El cos 1 és normal 14p</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">El cos 2 és mitjà 14p</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">La llegenda és normal 12p</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versió de l\'SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Element %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Carpeta</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">S\'hi ha fet clic</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Plec de fulles</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB expandida</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB reduïda</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Fes clic per actualitzar la llista</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Obre el calaix</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Element de menú</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Desplaçament d\'X (en dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Desplaçament de l\'Y (en dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Text de contingut</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Repeteix el text del contingut</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">L\'amplària del menú canviarà respecte al text de contingut. L’amplària
        màxima està restringida al 75% de la mida de la pantalla. El marge de contingut del costat i de la part inferior està governant pel testimoni. El mateix text de contingut es repetirà per variar
        l\'alçària.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Obre el menú</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Targeta bàsica</string>
    <!-- UI Label for Card -->
    <string name="file_card">Targeta de fitxer</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Targeta d\'anunci</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Interfície d\'usuari aleatòria</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opcions</string>
    <!-- UI Label for Title -->
    <string name="card_title">Títol</string>
    <!-- UI Label for text -->
    <string name="card_text">Text</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Text secundari</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Si cal, la còpia secundària d\'aquest bàner es pot ajustar a dos línies.</string>
    <!-- UI Label Button -->
    <string name="card_button">Botó</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Mostrar el quadre de diàleg</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Descartar el quadre de diàleg en fer clic a fora</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Descartar el quadre de diàleg en prémer enrere</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Quadre de diàleg descartat</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Cancel·lar</string>
    <!-- UI Label Ok -->
    <string name="ok">D’acord</string>
    <!-- A sample description -->
    <string name="dialog_description">Un quadre de diàleg és una finestra petita que sol·licita a l’usuari que prengui una decisió o introdueixi informació addicional.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Obre el calaix</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Desplega el calaix</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Tanca el calaix</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Seleccioneu el tipus de calaix</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Superior</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Tot el calaix es mostra a la regió visible.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Inferior</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Tot el calaix es mostra a la regió visible. Passa el dit cap amunt per desplaçar el contingut. El calaix es pot expandir amb la nansa d\'arrossegament.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Desplaçament esquerre</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">El calaix es desplaça cap a la regió visible des del costat esquerre.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Desplaçament dret</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">El calaix es desplaça cap a la regió visible des del costat dret.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Desplaçament inferior</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">El calaix es desplaça cap a la regió visible des de la part inferior de la pantalla. En passar el dit cap amunt al calaix expandible es porta la part restant a la regió visible i després &amp; es desplaça.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim Visible</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Selecciona el contingut del calaix</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Contingut desplaçable de mida de pantalla sencera</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Més de la meitat del contingut de la pantalla</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Menys de la meitat del contingut de la pantalla</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Contingut de mida dinàmica</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Contingut del calaix imbricat</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Expansible</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Omet l\'estat obert</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Impedeix l\'apagament en fer clic a l\'scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Mostra la nansa</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Títol</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Text de l\'indicador de funció</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Toqueu per mostrar l’indicador de funció del contingut personalitzat</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Inici superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Extrem superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Inici inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Extrem inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centre </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Centre personalitzat</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Per obtenir actualitzacions sobre les notes de la versió, </string>
    <string name="click_here">feu clic aquí.</string>
    <string name="open_source_cross_platform">Sistema de disseny multiplataforma de codi obert.</string>
    <string name="intuitive_and_powerful">Intuïtiu i eficaç.</string>
    <string name="design_tokens">Testimonis de disseny</string>
    <string name="release_notes">Notes de la versió</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">Enllaç a GitHub Repo</string>
    <string name="report_issue">Informa d\'un problema</string>
    <string name="v1_components">Components de V1</string>
    <string name="v2_components">Components de V2</string>
    <string name="all_components">Tot</string>
    <string name="fluent_logo">Logotip Fluent</string>
    <string name="new_badge">Nou</string>
    <string name="modified_badge">Modificat</string>
    <string name="api_break_badge">Error en l\'API</string>
    <string name="app_bar_more">Més</string>
    <string name="accent">Ressalta</string>
    <string name="appearance">Aspecte</string>
    <string name="choose_brand_theme">Trieu el tema de la vostra marca:</string>
    <string name="fluent_brand_theme">Marca Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Trieu l\'aspecte</string>
    <string name="appearance_system_default">Valor per defecte del sistema</string>
    <string name="appearance_light">Clar</string>
    <string name="appearance_dark">Fosc</string>
    <string name="demo_activity_github_link">Enllaç del GitHub a l\'activitat de demostració</string>
    <string name="control_tokens_details">Detalls dels testimonis de control</string>
    <string name="parameters">Paràmetres</string>
    <string name="control_tokens">Testimonis de control</string>
    <string name="global_tokens">Testimonis globals</string>
    <string name="alias_tokens">Testimonis d\'àlies</string>
    <string name="sample_text">Text</string>
    <string name="sample_icon">Icona d\'exemple</string>
    <string name="color">Color</string>
    <string name="neutral_color_tokens">Testimonis de color neutre</string>
    <string name="font_size_tokens">Testimonis de cos de lletra</string>
    <string name="line_height_tokens">Testimonis d\'alçada de línia</string>
    <string name="font_weight_tokens">Testimonis de gruix de tipus de lletra</string>
    <string name="icon_size_tokens">Testimonis de mida d’icona</string>
    <string name="size_tokens">Testimonis de mida</string>
    <string name="shadow_tokens">Testimonis d\'ombra</string>
    <string name="corner_radius_tokens">Testimonis de radi de cantonada</string>
    <string name="stroke_width_tokens">Testimonis d\'amplada del traç</string>
    <string name="brand_color_tokens">Testimonis de color de la marca</string>
    <string name="neutral_background_color_tokens">Testimonis de color de fons neutre</string>
    <string name="neutral_foreground_color_tokens">Testimonis de color de primer pla neutre</string>
    <string name="neutral_stroke_color_tokens">Testimonis de color de traç neutre</string>
    <string name="brand_background_color_tokens">Testimonis de color de fons de la marca</string>
    <string name="brand_foreground_color_tokens">Testimonis de color de primer pla de la marca</string>
    <string name="brand_stroke_color_tokens">Testimonis de color de traç de la marca</string>
    <string name="error_and_status_color_tokens">Testimonis de color d\'estat i error</string>
    <string name="presence_tokens">Testimonis de color de presència</string>
    <string name="typography_tokens">Testimonis de tipografia</string>
    <string name="unspecified">Sense especificar</string>

</resources>