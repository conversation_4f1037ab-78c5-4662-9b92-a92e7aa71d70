<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Primary</string>
    <string name="fluentui_secondary">Secondary</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Dismiss</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Selected</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Not Selected</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Icon</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Icon</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Disabled</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Action Button</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Enabled</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Close Sheet</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Close</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Cancel</string>
    <!--name of the icon -->
    <string name="fluentui_search">Search</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Microphone</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Clear Text</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Back</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Activated</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">De-Activated</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutral</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Brand</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contrast</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Accent</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Warning</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Danger</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">There has been an error</string>
    <string name="fluentui_error">Error</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Hint</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Chevron</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Outline</string>

    <string name="fluentui_action_button_icon">Action button icon</string>
    <string name="fluentui_center">Center text</string>
    <string name="fluentui_accessory_button">Accessory Buttons</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Radio Button</string>
    <string name="fluentui_label">Label</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Expanded</string>
    <string name="fluentui_collapsed">Collapsed</string>

    <!--types of control -->
    <string name="fluentui_large">Large</string>
    <string name="fluentui_medium">Medium</string>
    <string name="fluentui_small">Small</string>
    <string name="fluentui_password_mode">Password Mode</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Subtitle</string>
    <string name="fluentui_assistive_text">Assistive Text</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Title</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Short</string>"
    <string name="fluentui_long">Long</string>"
    <string name="fluentui_indefinite">Indefinite</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Button Pressed</string>
    <string name="fluentui_dismissed">Dismissed</string>
    <string name="fluentui_timeout">Timed Out</string>
    <string name="fluentui_left_swiped">Left Swiped</string>
    <string name="fluentui_right_swiped">Right Swiped</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Text</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Number</string>
    <string name="fluentui_keyboard_phone">Phone</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Email</string>
    <string name="fluentui_keyboard_password">Password</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimal</string>
</resources>