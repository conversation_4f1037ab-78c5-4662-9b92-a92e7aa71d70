<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">نسخه نمایشی رابط کاربری Fluent</string>
    <string name="app_title">رابط کاربری Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s را انتخاب کرد</string>
    <string name="app_modifiable_parameters">پارامترهای قابل‌تغییر</string>
    <string name="app_right_accessory_view">نمای جانبی راست</string>

    <string name="app_style">سبک</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">نماد فشار داده شد</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">شروع نسخه آزمایشی</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">چرخ‌فلک</string>
    <string name="actionbar_icon_radio_label">نماد</string>
    <string name="actionbar_basic_radio_label">پایه</string>
    <string name="actionbar_position_bottom_radio_label">پایین</string>
    <string name="actionbar_position_top_radio_label">تاپ</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">نوع نوار عملکرد</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">موقعیت نوار اقدام</string>

    <!--AppBar-->
    <string name="app_bar_style">سبک نوار برنامه</string>
    <string name="app_bar_subtitle">زیرنویس</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">حاشیه پایین</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">نماد پیمایش کلیک شد.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">پرچم‌گذاری</string>
    <string name="app_bar_layout_menu_settings">تنظیمات</string>
    <string name="app_bar_layout_menu_search">جستجو</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">عملکرد پیمایش: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">تغییر رفتار پیمایش</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">نماد تغییر وضعیت پیمایش</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">نمایش آواتار</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">نمایش نماد بازگشت</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">پنهان‌کردن نماد</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">نمایش نماد</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">تغییر سبک چیدمان نوار جستجو</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">نمایش به عنوان نمای جانبی</string>
    <string name="app_bar_layout_searchbar_action_view_button">نمایش به عنوان نمای اقدام</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">تغییر بین طرح‌های زمینه (فعالیت را دوباره ایجاد می‌کند)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">تغییر طرح زمینه</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">مورد</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">محتوای قابل‌پیمایش اضافی</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">سبک دایره</string>
    <string name="avatar_style_square">سبک مربع</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">بزرگ</string>
    <string name="avatar_size_medium">متوسط</string>
    <string name="avatar_size_small">کوچک</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">دو برابر بسیار بزرگ</string>
    <string name="avatar_size_xlarge_accessibility">بسیار بزرگ</string>
    <string name="avatar_size_xsmall_accessibility">بسیار کوچک</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">حداکثر آواتار نمایش‌داده‌شده</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">تعداد آواتار سرریز</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">نوع کادر</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">گروه آواتار با مجموعه OverflowAvatarCount به حداکثر آواتار نمایش‌داده‌شده وصل نخواهد بود.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">گروه صورت</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">توده صورت</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">سرریز کلیک شد</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">نمای آواتار در فهرست %d کلیک شد</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">نشان اعلان</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">نقطه</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">فهرست</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">نویسه</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">عکس‌ها</string>
    <string name="bottom_navigation_menu_item_news">اخبار</string>
    <string name="bottom_navigation_menu_item_alerts">هشدارها</string>
    <string name="bottom_navigation_menu_item_calendar">تقویم</string>
    <string name="bottom_navigation_menu_item_team">تیم</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">تغییر برچسب‌ها</string>
    <string name="bottom_navigation_three_menu_items_button">نمایش سه مورد منو</string>
    <string name="bottom_navigation_four_menu_items_button">نمایش چهار مورد منو</string>
    <string name="bottom_navigation_five_menu_items_button">نمایش پنج مورد منو</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">برچسب‌ها %s هستند</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">ورقه پایین</string>
    <string name="bottom_sheet_dialog">کادر محاوره ورقه پایین</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">فعال کردن کشیدن به پایین برای رد کردن</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">نمایش با موارد تک خطی</string>
    <string name="bottom_sheet_with_double_line_items">نمایش با موارد دوخطی</string>
    <string name="bottom_sheet_with_single_line_header">نمایش با سرصفحه تک خطی</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">نمایش با سرصفحه و جداکننده‌های دوخطی</string>
    <string name="bottom_sheet_dialog_button">نمایش</string>
    <string name="drawer_content_desc_collapse_state">بازکردن</string>
    <string name="drawer_content_desc_expand_state">کوچک کردن</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">کلیک روی %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">کلیک طولانی روی %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">روی «نادیده گرفتن» کلیک کنید</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">درج مورد</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">به‌روزرسانی مورد</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">نادیده گرفتن</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">افزودن</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">ذکر نام</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">پررنگ</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">مورب</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">زیرخط</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">خط‌خورده</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">لغو انجام</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">انجام مجدد</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">نقطه</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">فهرست</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">پیوند</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">به‌روزرسانی مورد</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">فاصله‌گذاری</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">موقعیت نادیده گرفتن</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">شروع</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">پایان</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">فضای گروه</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">فضای مورد</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d ddp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">پرچم‌گذاری</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">مورد پرچم‌گذاری کلیک شد</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">پاسخ</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">مورد پاسخ کلیک شد</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">هدایت</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">مورد هدایت کلیک شد</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">حذف</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">مورد حذف کلیک شد</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">آواتار</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">دوربین</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">عکس بگیرید</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">مورد دوربین کلیک شد</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">گالری</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">عکس‌هایتان را مشاهده کنید</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">مورد گالری کلیک شد</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">ویدیوها</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">ویدیوهای خود را پخش کنید</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">مورد ویدیوها کلیک شد</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">مدیریت</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">مدیریت کتابخانه رسانه‌های شما</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">مورد مدیریت کلیک شد</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">اقدامات ایمیل</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">اسناد</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">آخرین به‌روزرسانی 2:14 ب.ظ</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">اشتراک‌گذاری</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">مورد اشتراک‌گذاری کلیک شد</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">انتقال</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">مورد انتقال کلیک شد</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">حذف</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">مورد حذف کلیک شد</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">اطلاعات</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">مورد اطلاعات کلیک شد</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">ساعت</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">مورد ساعت کلیک شد</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">هشدار</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">مورد هشدار کلیک شد</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">منطقه زمانی</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">مورد منطقه زمانی کلیک شد</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">نماهای مختلف دکمه</string>
    <string name="button">دکمه</string>
    <string name="buttonbar">نوار دکمه</string>
    <string name="button_disabled">نمونه دکمه غیرفعال</string>
    <string name="button_borderless">نمونه دکمه بدون کادر</string>
    <string name="button_borderless_disabled">نمونه دکمه غیرفعال بدون کادر</string>
    <string name="button_large">نمونه دکمه بزرگ</string>
    <string name="button_large_disabled">نمونه دکمه بزرگ غیرفعال</string>
    <string name="button_outlined">نمونه دکمه برون‌نمایی‌شده</string>
    <string name="button_outlined_disabled">نمونه دکمه غیرفعال برون‌نمایی‌شده</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">انتخاب یک تاریخ</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">انتخابگر تاریخ ساعت</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">تاریخ تکی</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">هیچ تاریخی انتخاب نشده است</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">نمایش انتخابگر تاریخ</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">نمایش انتخابگر تاریخ ساعت با زبانه تاریخ انتخاب‌شده</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">نمایش انتخابگر تاریخ ساعت با زبانه ساعت انتخاب‌شده</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">نمایش انتخابگر تاریخ ساعت</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">محدوده تاریخ</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">‏‏آغاز:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">‏‏پایان:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">هیچ شروعی انتخاب نشود</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">هیچ پایانی انتخاب نشده است</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">انتخاب تاریخ شروع</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">انتخاب تاریخ پایان</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">محدوده تاریخ ساعت</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">انتخاب محدوده تاریخ ساعت</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">کادر محاوره انتخابگر تاریخ ساعت</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">نمایش کادر محاوره</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">نمایش کشو</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">نمایش کادر محاوره کشو</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">کادر محاوره پایین بدون محوشدن</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">نمایش کشوی بالا</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">کادری محاوره بالای بدون محوشدن</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> نمایش کادر محاوره نمای لنگر بالا</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> کادر محاوره بالای بدون عنوان نمایش داده شود</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> نمایش زیر کادر محاوره بالای عنوان</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">نمایش کشوی سمت راست</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">نمایش کشوی سمت چپ</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">عنوان، متن اصلی</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">زیرنویس، متن دوم</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">متن زیرنویس سفارشی</string>
    <!-- Footer -->
    <string name="list_item_footer">پانویس، متن سوم</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">فهرست تک‌خطی با متن سرصفحه فرعی خاکستری</string>
    <string name="list_item_sub_header_two_line">لیست دو خطی</string>
    <string name="list_item_sub_header_two_line_dense">فهرست دوخطی با فاصله‌گذاری متراکم</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">فهرست دوخطی با نمای سفارشی زیرنویس ثانویه</string>
    <string name="list_item_sub_header_three_line">فهرست سه‌خطی با متن سرصفحه فرعی سیاه</string>
    <string name="list_item_sub_header_no_custom_views">موارد بدون نماهای سفارشی فهرست شود</string>
    <string name="list_item_sub_header_large_header">موارد با نماهای سفارشی بزرگ فهرست شوند</string>
    <string name="list_item_sub_header_wrapped_text">موارد با متن قرارگرفته در کادر فهرست شود</string>
    <string name="list_item_sub_header_truncated_text">موارد با متن کوتاه‌شده فهرست شود</string>
    <string name="list_item_sub_header_custom_accessory_text">اقدام</string>
    <string name="list_item_truncation_middle">کوتاه کردن از وسط.</string>
    <string name="list_item_truncation_end">پایان برش.</string>
    <string name="list_item_truncation_start">کوتاه‌کردن را شروع کنید.</string>
    <string name="list_item_custom_text_view">مقدار</string>
    <string name="list_item_click">روی مورد فهرست کلیک کردید.</string>
    <string name="list_item_click_custom_accessory_view">شما روی نمای جانبی سفارشی کلیک کردید.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">شما روی نمای جانبی سفارشی سرصفحه فرعی کلیک کرده‌اید.</string>
    <string name="list_item_more_options">گزینه‌های بیشتر</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">انتخاب</string>
    <string name="people_picker_select_deselect_example">انتخاب لغوانتخاب</string>
    <string name="people_picker_none_example">هیچ‌کدام</string>
    <string name="people_picker_delete_example">حذف</string>
    <string name="people_picker_custom_persona_description">این مثال نشان می‌دهد چگونه یک شئ سفارشی IPersona ایجاد کنید.</string>
    <string name="people_picker_dialog_title_removed">شما یک شخصیت را حذف کرده بودید:</string>
    <string name="people_picker_dialog_title_added">شما یک شخصیت اضافه کردید:</string>
    <string name="people_picker_drag_started">کشیدن شروع شد</string>
    <string name="people_picker_drag_ended">کشیدن به پایان رسید</string>
    <string name="people_picker_picked_personas_listener">شنونده شخصیت</string>
    <string name="people_picker_suggestions_listener">شنونده پیشنهادها</string>
    <string name="people_picker_persona_chip_click">روی %s کلیک کردید</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s گیرنده</item>
        <item quantity="other">%1$s گیرنده</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">بازکردن ورقه پایین ثابت</string>
    <string name="collapse_persistent_sheet_button"> پنهان کردن ورقه پایین ثابت</string>
    <string name="show_persistent_sheet_button"> نمایش پایین صفحه ثابت</string>
    <string name="new_view">این نمای جدید است</string>
    <string name="toggle_sheet_content">تغییر محتوای پایین صفحه</string>
    <string name="switch_to_custom_content">تغییر وضعیت به محتوای سفارشی</string>
    <string name="one_line_content">محتوای ورقه پایین تک‌خطی</string>
    <string name="toggle_disable_all_items">تغییر غیرفعال کردن همه موارد</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">افزودن/حذف نما</string>
    <string name="persistent_sheet_item_change_collapsed_height"> تغییر ارتفاع جمع‌شده</string>
    <string name="persistent_sheet_item_create_new_folder_title">پوشه جدید</string>
    <string name="persistent_sheet_item_create_new_folder_toast">مورد پوشه جدید کلیک شد</string>
    <string name="persistent_sheet_item_edit_title">ویرایش</string>
    <string name="persistent_sheet_item_edit_toast">مورد ویرایش کلیک شد</string>
    <string name="persistent_sheet_item_save_title">ذخیره</string>
    <string name="persistent_sheet_item_save_toast">مورد ذخیره کلیک شد</string>
    <string name="persistent_sheet_item_zoom_in_title">بزرگنمایی</string>
    <string name="persistent_sheet_item_zoom_in_toast"> مورد بزرگنمایی کلیک شد</string>
    <string name="persistent_sheet_item_zoom_out_title">کوچک‌نمایی</string>
    <string name="persistent_sheet_item_zoom_out_toast">مورد کوچک‌نمایی کلیک شد</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">دردسترس</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">آلن مانگر</string>
    <string name="persona_name_amanda_brady">اماندا بریدی</string>
    <string name="persona_name_ashley_mccarthy">اشلی مک‌کارتی</string>
    <string name="persona_name_carlos_slattery">کارلوس اسلاتری</string>
    <string name="persona_name_carole_poland">کارول پولند</string>
    <string name="persona_name_cecil_folk">سیسیل فولک</string>
    <string name="persona_name_celeste_burton">سلست برتون</string>
    <string name="persona_name_charlotte_waltson">شارلوت والتسون</string>
    <string name="persona_name_colin_ballinger">کالین بالینگر</string>
    <string name="persona_name_daisy_phillips">دیزی فیلیپس</string>
    <string name="persona_name_elliot_woodward">الیوت وودورد</string>
    <string name="persona_name_elvia_atkins">الویا التکینس</string>
    <string name="persona_name_erik_nason">اریک ناسون</string>
    <string name="persona_name_henry_brill">هنری بیل</string>
    <string name="persona_name_isaac_fielder">ایساک فیلدر</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">کاتری آهوکاس</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">موریسیو آگوست</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">روبین کانتس</string>
    <string name="persona_name_robert_tolbert">رابرت تولبرت</string>
    <string name="persona_name_tim_deboer">تیم دبوئر</string>
    <string name="persona_name_wanda_howard">واندا هاوارد</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">طراح</string>
    <string name="persona_subtitle_engineer">مهندس</string>
    <string name="persona_subtitle_manager">مدیر</string>
    <string name="persona_subtitle_researcher">محقق</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (مثال متن طولانی برای تست کوتاه کردن)</string>
    <string name="persona_view_description_xxlarge">آواتار XXLarge با سه خط متن</string>
    <string name="persona_view_description_large">آواتار بزرگ با دو خط متن</string>
    <string name="persona_view_description_small">آواتار کوچک با یک خط متن</string>
    <string name="people_picker_hint">هیچکدام با نکته نشان‌داده‌شده</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">چیپ شخصی غیرفعال‌شده</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">چیپ شخصیت خطا</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">چیپ شخصیت بدون نماد بستن</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">چیپ شخصیت ساده</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">شما روی یک چیپ شخصیت انتخاب‌شده کلیک کردید.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">اشتراک‌گذاری</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">دنبال کردن</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">دعوت کردن از دیگران</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">تازه‌سازی صفحه</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">باز کردن در مرورگر</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">این یک منوی بازشوی خطی است. حداکثر خطوط روی دو تنظیم شده است، بقیه متن کوتاه می‌شود.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">همه اخبار</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">اخبار ذخیره‌شده</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">اخبار از سایت‌ها</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">خارج از ساعات کاری اطلاع‌رسانی کنید</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">زمانیکه در دسکتاپ غیرفعال است، اطلاع دهید</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">روی مورد کلیک کرده بودید:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">منوی ساده</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">منوی ساده ۲</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">منو با یک مورد قابل‌انتخاب و یک جداکننده</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">منو با تمام موارد قابل انتخاب، نمادها و متن طولانی</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">نمایش</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">پیشرفت دایره‌ای</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">کوچک</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">متوسط</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">بزرگ</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">پیشرفت خطی</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">نامعین</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">معین</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">نوار جستجو</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">پاسخ به تماس میکروفن</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">تصحیح خودکار</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">میکروفون فشار داده شد</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">نمای راست فشار داده شد</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">«جستجوی صفحه کلید» فشار داده شد</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">نمایش اسنک‌بار</string>
    <string name="fluentui_dismiss_snackbar">بستن نوار اسنک‌بار</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">اقدام</string>
    <string name="snackbar_action_long">عملکرد متن بلند</string>
    <string name="snackbar_single_line">اسنک‌بار تک‌خطی</string>
    <string name="snackbar_multiline">این یک اسنک‌بار چند خطی است. حداکثر خطوط روی دو تنظیم شده است، بقیه متن کوتاه می‌شود.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">این یک اسنک‌بار اعلان است. برای برقراری ارتباط با ویژگی‌های جدید استفاده می‌شود.</string>
    <string name="snackbar_primary">این یک اسنک‌بار اصلی است.</string>
    <string name="snackbar_light">این یک اسنک‌بار روشن است.</string>
    <string name="snackbar_warning">این یک اسنک‌بار هشدار است.</string>
    <string name="snackbar_danger">این یک اسنک‌بار خطر است.</string>
    <string name="snackbar_description_single_line">مدت زمان کوتاه</string>
    <string name="snackbar_description_single_line_custom_view">مدت زمان طولانی با پیشرفت دایره‌ای به صورت نمای سفارشی کوچک</string>
    <string name="snackbar_description_single_line_action">مدت زمان کوتاه عملکرد</string>
    <string name="snackbar_description_single_line_action_custom_view">مدت زمان کوتاه با نمای متوسط اقدام و سفارشی</string>
    <string name="snackbar_description_single_line_custom_text_color">مدت زمان کوتاه با رنگ متن سفارشی</string>
    <string name="snackbar_description_multiline">مدت زمان طولانی</string>
    <string name="snackbar_description_multiline_custom_view">مدت زمان طولانی با نمای سفارشی کوچک</string>
    <string name="snackbar_description_multiline_action">مدت زمان نامعین با به‌روزرسانی‌های اقدام و متن</string>
    <string name="snackbar_description_multiline_action_custom_view">مدت زمان کوتاه با نمای متوسط اقدام و سفارشی</string>
    <string name="snackbar_description_multiline_action_long">مدت زمان کوتاه با متن اقدام طولانی</string>
    <string name="snackbar_description_announcement">مدت زمان کوتاه</string>
    <string name="snackbar_description_updated">این متن به‌روزرسانی شده است.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">نمایش اسنک‌بار</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">خط تکی</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">چندخطی</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">سبک اعلام</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">سبک اصلی</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">سبک روشن</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">سبک هشدار</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">سبک خطر</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">صفحه اصلی</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">پست</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">تنظیمات</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">اعلان</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">موارد بیشتر</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">ترازبندی متن</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">عمودی</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">افقی</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">متنی وجود ندارد</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">موارد زبانه</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">عنوان</string>
    <string name="cell_sample_description">توضیحات</string>
    <string name="calculate_cells">بارگیری/محاسبه ۱۰۰ سلول</string>
    <string name="calculate_layouts">بارگیری/محاسبه ۱۰۰ چیدمان</string>
    <string name="template_list">لیست الگو</string>
    <string name="regular_list">فهرست منظم</string>
    <string name="cell_example_title">عنوان: سلول</string>
    <string name="cell_example_description">توضیحات: برای تغییر جهت ضربه بزنید</string>
    <string name="vertical_layout">چیدمان عمودی</string>
    <string name="horizontal_layout">چیدمان افقی</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">زبانه استاندارد ۲ بخشی</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">زبانه استاندارد 3 بخشی</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">زبانه استاندارد ۴ بخشی</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">زبانه استاندارد با پیجر</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">جابجایی زبانه</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">زبانه قرص‌ها </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">برای نکته صفحه ضربه بزنید</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">برای نکته صفحه تقویم سفارشی ضربه بزنید</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">روی نکته صفحه رنگی سفارشی ضربه بزنید</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">برای بستن نکته صفحه داخلی ضربه بزنید</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">برای نکته صفحه نمای سفارشی ضربه بزنید</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">نکته صفحه رنگی سفارشی بالا</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">نکته صفحه انتهای بالا با افست X ‏10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">نکته صفحه شروع پایین</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">نکته صفحه انتهای پایین با افست Y ‏10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">بستن نکته صفحه داخلی</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">نکته صفحه بسته شد</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">عنوان Light 28sp است</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">عنوان Medium 20sp است</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">عنوان ۲ Regular 20sp است</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">سرعنوان Regular 18sp است</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">عنوان فرعی ۱ Regular 16sp است</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">عنوان فرعی ۲ Medium 16sp است</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">متن ۱ Regular 14sp است</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">متن ۲ Regular 14sp است</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">زیرنویس Regular 12sp است</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">نسخه SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">مورد %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">پوشه</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">کلیک‌شده</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">داربست</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB گسترش یافت</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB بسته شد</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">برای تازه کردن فهرست، کلیک کنید</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">باز کردن کشو</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">مورد منو</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">فاصله از مرکز X (به dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">فاصله از مرکز Y (به dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">متن محتوا</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">تکرار متن محتوا</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">عرض منو نسبت به «متن محتوا» تغییر می‌کند. حداکثر
        عرض برای اندازه ۷۵٪ صفحه محدود شده است. حاشیه محتوا از کناره و پایین بر اساس نشانه اعمال می‌شود.همان متن محتوا برای تغییر
ارتفاع تکرار می‌شود.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">باز کردن منو</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">کارت پایه</string>
    <!-- UI Label for Card -->
    <string name="file_card">کارت فایل</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">کارت اعلام</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">رابط کاربری تصادفی</string>
    <!-- UI Label for Options -->
    <string name="card_options">گزینه‌ها</string>
    <!-- UI Label for Title -->
    <string name="card_title">عنوان</string>
    <!-- UI Label for text -->
    <string name="card_text">متن</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">متن فرعی</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">نسخه دوم برای این بنر می‌تواند در صورت نیاز در دو خط قرار دهد.</string>
    <!-- UI Label Button -->
    <string name="card_button">دکمه</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">نمایش کادر محاوره‌ای</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">رد کردن کادر محاوره‌ای در هنگام کلیک کردن در خارج</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">رد کردن کادر محاوره هنگام فشار دادن دکمه برگشت</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">کادر محاوره‌ای رد شد</string>
    <!-- UI Label Cancel -->
    <string name="cancel">لغو</string>
    <!-- UI Label Ok -->
    <string name="ok">تأیید</string>
    <!-- A sample description -->
    <string name="dialog_description">کادر محاوره‌ای پنجره‌ی کوچکی است که از کاربر می‌خواهد تا تصمیم بگیرد یا اطلاعات بیشتری را وارد کند.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">باز کردن کشو</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">باز شدن کشو</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">بستن کشو</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">انتخاب نوع کشو</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">بالا</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">کل کشو در منطقه قابل رؤیت نشان داده می‌شود.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">پایین</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">کل کشو در منطقه قابل رؤیت نشان داده می‌شود. محتوای پیمایش حرکت را به بالا بکشید. کشوی قابل گسترش از طریق دستگیره کشیدن گسترش می‌یابد.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">اسلاید چپ روی</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">کشوی روی اسلاید تا منطقه قابل رؤیت از سمت چپ.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">اسلاید راست روی</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">کشوی روی اسلاید تا منطقه قابل رؤیت از سمت راست.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">اسلاید پایین روی</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">کشوی روی اسلاید تا منطقه قابل رؤیت از پایین صفحه نمایش. با کشیدن انگشت روی کشوی قابل گسترش، بقیه بخش‌های آن را به منطقه قابل روئیت می‌آورد و سپس پیمایش می‌کند.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">مشبک قابل رؤیت</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">انتخاب محتوای کشو</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">محتوای قابل پیمایش اندازه تمام صفحه</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">محتوای بیشتر از نیم صفحه</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">محتوای کمتر از نیم صفحه</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">محتوای اندازه پویا</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">محتوای کشوی تودرتو</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">قابل گسترش یافتن</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">رد شدن از وضعیت باز</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">جلوگیری از نادیده گرفتن کلیک Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">نمایش دستگیره</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">عنوان</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">متن راهنمای ابزار</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">برای دسترسی به نوار ابزار محتوای سفارشی، ضربه بزنید</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">شروع بالا </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">انتهای بالا </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">شروع پایین </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">انتهای پایین </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">مرکز </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">مرکز سفارشی</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">برای به‌روزرسانی‌های مربوط به یادداشت‌های ترخیص، </string>
    <string name="click_here">اینجا کلیک کنید.</string>
    <string name="open_source_cross_platform">سیستم طراحی متقاطع منبع باز.</string>
    <string name="intuitive_and_powerful">بصری و قدرتمند.</string>
    <string name="design_tokens">نشانه‌های طراحی</string>
    <string name="release_notes">يادداشت‌های ترخيص</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">پیوند GitHub Repo</string>
    <string name="report_issue">گزارش مشکل</string>
    <string name="v1_components">مؤلفه‌های V1</string>
    <string name="v2_components">مؤلفه‌های V2</string>
    <string name="all_components">همه موارد</string>
    <string name="fluent_logo">آرم روان</string>
    <string name="new_badge">جدید</string>
    <string name="modified_badge">تغییر یافت</string>
    <string name="api_break_badge">وقفه API</string>
    <string name="app_bar_more">موارد بیشتر</string>
    <string name="accent">آکسان</string>
    <string name="appearance">ظاهر</string>
    <string name="choose_brand_theme">طرح زمینه برند خود را انتخاب کنید:</string>
    <string name="fluent_brand_theme">برند روان</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">انتخاب ظاهر</string>
    <string name="appearance_system_default">پیش‌فرض سیستم</string>
    <string name="appearance_light">روشن</string>
    <string name="appearance_dark">تیره</string>
    <string name="demo_activity_github_link">پیوند GitHub فعالیت نمایشی</string>
    <string name="control_tokens_details">جزئیات نشانه‌های کنترل</string>
    <string name="parameters">پارامترها</string>
    <string name="control_tokens">نشانه‌های کنترل</string>
    <string name="global_tokens">نشانه‌های عمومی</string>
    <string name="alias_tokens">نشانه‌های نام مستعار</string>
    <string name="sample_text">متن</string>
    <string name="sample_icon">نماد نمونه</string>
    <string name="color">رنگ</string>
    <string name="neutral_color_tokens">نشانه‌های رنگ خنثی</string>
    <string name="font_size_tokens">نشانه‌های اندازه قلم</string>
    <string name="line_height_tokens">نشانه‌های ارتفاع خط</string>
    <string name="font_weight_tokens">نشانه‌های وزن قلم</string>
    <string name="icon_size_tokens">نشانه‌های اندازه نماد</string>
    <string name="size_tokens">نشانه‌های اندازه</string>
    <string name="shadow_tokens">نشانه‌های سایه</string>
    <string name="corner_radius_tokens">نشانه‌های شعاع گوشه</string>
    <string name="stroke_width_tokens">نشانه‌های عرض ضربه</string>
    <string name="brand_color_tokens">نشانه‌های رنگ برند</string>
    <string name="neutral_background_color_tokens">نشانه‌های رنگ پس‌زمینه خنثی</string>
    <string name="neutral_foreground_color_tokens">نشانه‌های رنگ پیش‌زمینه خنثی</string>
    <string name="neutral_stroke_color_tokens">نشانه‌های رنگ ضربه‌ای خنثی</string>
    <string name="brand_background_color_tokens">نشانه‌های رنگ پس‌زمینه برند</string>
    <string name="brand_foreground_color_tokens">نشانه‌های رنگ پیش‌زمینه برند</string>
    <string name="brand_stroke_color_tokens">نشانه‌های رنگ ضربه‌ای برند</string>
    <string name="error_and_status_color_tokens">نشانه‌های رنگ وضعیت و خطا</string>
    <string name="presence_tokens">نشانه‌های رنگ حضور</string>
    <string name="typography_tokens">نشانه‌های فن چاپ</string>
    <string name="unspecified">نامشخص</string>

</resources>