package com.microsoft.fluentui.icons.avataricons.presence.awayoof.medium

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import com.microsoft.fluentui.icons.avataricons.presence.awayoof.MediumGroup

val MediumGroup.Dark: ImageVector
    get() {
        if (_dark != null) {
            return _dark!!
        }
        _dark = Builder(name = "Dark", defaultWidth = 16.0.dp, defaultHeight = 16.0.dp,
                viewportWidth = 16.0f, viewportHeight = 16.0f).apply {
            path(fill = SolidColor(Color(0xFF000000)), stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(8.0f, 8.0f)
                moveToRelative(-7.0f, 0.0f)
                arcToRelative(7.0f, 7.0f, 0.0f, true, true, 14.0f, 0.0f)
                arcToRelative(7.0f, 7.0f, 0.0f, true, true, -14.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0xFFE959D9)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(8.2812f, 6.5284f)
                curveTo(8.5741f, 6.2355f, 8.5741f, 5.7606f, 8.2812f, 5.4677f)
                curveTo(7.9883f, 5.1748f, 7.5134f, 5.1748f, 7.2205f, 5.4677f)
                lineTo(5.2185f, 7.4698f)
                curveTo(4.9256f, 7.7627f, 4.9256f, 8.2376f, 5.2185f, 8.5304f)
                lineTo(7.2205f, 10.5325f)
                curveTo(7.5134f, 10.8254f, 7.9883f, 10.8254f, 8.2812f, 10.5325f)
                curveTo(8.5741f, 10.2396f, 8.5741f, 9.7647f, 8.2812f, 9.4719f)
                lineTo(7.5594f, 8.7501f)
                horizontalLineTo(10.2499f)
                curveTo(10.6641f, 8.7501f, 10.9999f, 8.4143f, 10.9999f, 8.0001f)
                curveTo(10.9999f, 7.5859f, 10.6641f, 7.2501f, 10.2499f, 7.2501f)
                horizontalLineTo(7.5594f)
                lineTo(8.2812f, 6.5284f)
                close()
                moveTo(8.0f, 2.0f)
                curveTo(4.6863f, 2.0f, 2.0f, 4.6863f, 2.0f, 8.0f)
                curveTo(2.0f, 11.3137f, 4.6863f, 14.0f, 8.0f, 14.0f)
                curveTo(11.3137f, 14.0f, 14.0f, 11.3137f, 14.0f, 8.0f)
                curveTo(14.0f, 4.6863f, 11.3137f, 2.0f, 8.0f, 2.0f)
                close()
                moveTo(3.5f, 8.0f)
                curveTo(3.5f, 5.5147f, 5.5147f, 3.5f, 8.0f, 3.5f)
                curveTo(10.4853f, 3.5f, 12.5f, 5.5147f, 12.5f, 8.0f)
                curveTo(12.5f, 10.4853f, 10.4853f, 12.5f, 8.0f, 12.5f)
                curveTo(5.5147f, 12.5f, 3.5f, 10.4853f, 3.5f, 8.0f)
                close()
            }
        }
                .build()
        return _dark!!
    }

private var _dark: ImageVector? = null
