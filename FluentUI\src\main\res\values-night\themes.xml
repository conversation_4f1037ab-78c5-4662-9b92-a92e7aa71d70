<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>
    <!--
    All dark theme specific semantic colors should be defined here.
    Light theme semantic colors should be defined in themes.xml as the default.
-->
    <style name="Theme.FluentUI" parent="Theme.FluentUI.Container">
        <item name="android:textColorPrimary">@color/fluentui_white</item>

        <!-- *** Base Semantic Colors *** -->

        <!--Backgrounds-->
        <item name="fluentuiBackgroundColor">@color/fluentui_black</item>
        <item name="fluentuiBackgroundPressedColor">@color/fluentui_gray_900</item>

        <!--Foregrounds-->
        <item name="fluentuiForegroundColor">@color/fluentui_white</item>
        <item name="fluentuiForegroundOnPrimaryColor">@color/fluentui_black</item>
        <item name="fluentuiDividerColor">@color/fluentui_gray_800</item>

        <!-- *** Semantic Colors *** -->

        <!--AppBarLayout-->
        <item name="fluentuiAppBarLayoutBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>

        <!--BottomSheet-->
        <item name="fluentuiBottomSheetBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiBottomSheetBackgroundPressedColor">?attr/fluentuiBackgroundSecondaryPressedColor</item>
        <item name="fluentuiBottomSheetDividerColor">@color/fluentui_gray_700</item>

        <!--Button-->
        <item name="fluentuiButtonBackgroundDisabledColor">?attr/fluentuiBackgroundPrimaryColor</item>
        <item name="fluentuiButtonBackgroundPressedColor">?attr/fluentuiColorPrimaryDarker</item>
        <item name="fluentuiButtonTextDisabledColor">?attr/colorPrimaryDark</item>

        <!--ButtonBorderless-->
        <item name="fluentuiButtonBorderlessBackgroundPressedColor">@android:color/transparent</item>
        <item name="fluentuiButtonBorderlessTextDisabledColor">@color/fluentui_gray_500</item>
        <item name="fluentuiButtonBorderlessTextPressedColor">?attr/colorPrimaryDark</item>

        <!--Button Outlined-->
        <item name="fluentuiButtonOutlinedTextDefaultColor">@color/fluentui_communication_blue</item>
        <item name="fluentuiButtonOutlinedTextPressedColor">@color/fluentui_communication_tint_20</item>
        <item name="fluentuiButtonOutlinedTextDisabledColor">@color/fluentui_gray_600</item>
        <item name="fluentuiButtonOutlinedStrokeDefaultColor">@color/fluentui_communication_tint_20</item>
        <item name="FluentuiButtonOutlinedStrokePressedColor">@color/fluentui_communication_tint_40</item>
        <item name="FluentuiButtonOutlinedStrokeDisabledColor">@color/fluentui_gray_800</item>

        <!--ContextualCommandBar-->
        <item name="fluentuiContextualCommandBarBackgroundColor">@color/fluentui_gray_600</item>
        <item name="fluentuiContextualCommandBarBackgroundColorPressed">@color/fluentui_gray_900</item>
        <item name="fluentuiContextualCommandBarBackgroundColorSelected">@color/fluentui_communication_blue</item>
        <item name="fluentuiContextualCommandBarIconTint">@color/fluentui_gray_100</item>
        <item name="fluentuiContextualCommandBarIconTintDisabled">@color/fluentui_gray_400</item>
        <item name="fluentuiContextualCommandBarIconTintSelected">@color/fluentui_black</item>
        <item name="fluentuiContextualCommandBarDismissBackgroundColor">@color/fluentui_black</item>
        <item name="fluentuiContextualCommandBarDismissIconTintColor">@color/fluentui_gray_100</item>

        <!--Dialog-->
        <item name="fluentuiDialogBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiDialogCloseIconColor">?attr/colorPrimary</item>

        <!--Drawer-->
        <item name="fluentuiDrawerBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiDrawerHandleColor">@color/fluentui_gray_500</item>

        <!--ListItemView-->
        <item name="fluentuiListItemTitleColor">@color/fluentui_gray_100</item>

        <!--NumberPicker-->
        <item name="fluentuiNumberPickerBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>

        <!--Persona Chip-->
        <!--TODO add hex values to the colors file when they get added to the toolkit-->
        <item name="fluentuiPersonaChipBackgroundNormalColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiPersonaChipBackgroundErrorColor">#4DFF474C</item>
        <item name="fluentuiPersonaChipBackgroundErrorActiveColor">#FF474C</item>
        <item name="fluentuiPersonaChipTextNormalColor">@color/fluentui_gray_100</item>
        <item name="fluentuiPersonaChipTextErrorColor">#FF474C</item>

        <!--PopupMenu-->
        <item name="fluentuiPopupMenuBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiPopupMenuBackgroundPressedColor">?attr/fluentuiBackgroundSecondaryPressedColor</item>
        <item name="fluentuiPopupMenuItemTitleColor">@color/fluentui_gray_100</item>
        <item name="fluentuiPopupMenuItemCheckBackgroundRippleColor">?attr/fluentuiBackgroundSecondaryPressedColor</item>
        <item name="fluentuiPopupMenuItemCheckboxTint">@color/fluentui_gray_300</item>
        <item name="fluentuiPopupMenuItemRadiobuttonTint">@color/fluentui_gray_300</item>
        <item name="fluentuiPopupMenuItemIconTint">@color/fluentui_gray_500</item>

        <!--Progress-->
        <item name="fluentuiProgressBackgroundColor">@color/fluentui_gray_900</item>
        <!--Searchbar-->
        <item name="fluentuiSearchbarBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiSearchbarSearchViewContainerBackgroundColor">@color/fluentui_gray_700</item>
        <item name="fluentuiSearchbarSearchViewTextColor">?attr/fluentuiForegroundOnSecondaryColor</item>
        <item name="fluentuiSearchbarBackButtonColor">?attr/fluentuiForegroundOnSecondaryColor</item>
        <item name="fluentuiSearchbarSearchViewCursorColor">?attr/fluentuiForegroundOnSecondaryColor</item>

        <!--Snackbar-->
        <item name="fluentuiSnackbarBackgroundPrimaryColor">?attr/fluentuiBackgroundPrimaryColor</item>
        <item name="fluentuiSnackbarBackgroundLightColor">@color/fluentui_gray_900</item>
        <item name="fluentuiSnackbarBackgroundWarningColor">@color/fluentui_yellow</item>
        <item name="fluentuiSnackbarBackgroundDangerColor">@color/fluentui_red</item>
        <item name="fluentuiSnackbarTextPrimaryColor">?attr/fluentuiForegroundOnPrimaryColor</item>
        <item name="fluentuiSnackbarTextLightColor">@color/fluentui_white</item>
        <item name="fluentuiSnackbarTextWarningColor">@color/fluentui_black</item>
        <item name="fluentuiSnackbarTextDangerColor">@color/fluentui_black</item>

        <!--Toolbar-->
        <item name="fluentuiToolbarBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiToolbarTitleTextColor">?attr/fluentuiForegroundOnSecondaryColor</item>
        <item name="fluentuiToolbarSubtitleTextColor">?attr/fluentuiForegroundOnSecondaryColor</item>
        <item name="fluentuiToolbarIconColor">?attr/fluentuiForegroundOnSecondaryColor</item>

        <!--TabLayout-->
        <item name="fluentuiTabSelectedTextColor">@color/fluentui_white</item>
        <item name="fluentuiTabUnselectedTextColor">@color/fluentui_gray_100</item>
        <item name="fluentuiTabLayoutContainerBackgroundColor">?attr/fluentuiBackgroundSecondaryColor</item>
        <item name="fluentuiTabLayoutBackgroundColor">@color/fluentui_gray_950</item>
        <item name="fluentuiTabSelectedBackgroundColor">@color/fluentui_gray_600</item>

        <!--Tooltip-->
        <item name="fluentuiTooltipBackgroundColor">?attr/fluentuiBackgroundPrimaryColor</item>
        <item name="fluentuiTooltipTextColor">?attr/fluentuiForegroundOnPrimaryColor</item>
    </style>
</resources>