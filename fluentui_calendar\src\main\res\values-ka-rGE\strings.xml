<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">M</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">T</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">W</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">T</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">F</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">S</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">S</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">შემდეგ კვირაზე გადასვლა</string>
    <string name="accessibility_goto_previous_week">წინა კვირაზე გადასვლა</string>
    <string name="accessibility_today">დღეს</string>
    <string name="accessibility_selected">არჩეულია</string>

    <!-- *** Shared *** -->
    <string name="done">მზადაა</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">დღეს</string>
    <string name="tomorrow">ხვალ</string>
    <string name="yesterday">გუშინ</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">დაწყების დრო</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">დასრულების დრო</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">დაწყების თარიღი</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">დასრულების თარიღი</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">აირჩიეთ დრო</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">თარიღის არჩევა</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">თარიღისა და დროის ამომრჩეველი</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">თარიღის ამომრჩეველი</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">თარიღისა დიაპაზონის ამომრჩეველი</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">თარიღის ამომრჩევლის დიაპაზონი</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">დაწყების დროის ჩანართი</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">დასრულების დროის ჩანართი</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">დაწყების თარიღის ჩანართი</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">დასრულების თარიღის ჩანართი</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">დიალოგის დახურვა</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">თვის ზრდა</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">მომდევნო თვის არჩევა</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">თვის შემცირება</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">წინა თვის არჩევა</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">დღის ზრდა</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">მომდევნო დღის არჩევა</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">დღის შემცირება</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">წინა დღის არჩევა</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">წლის ზრდა</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">მომდევნო წლის არჩევა</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">წლის შემცირება</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">წინა წლის არჩევა</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">თარიღის ზრდა</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">მომდევნო თარიღის არჩევა</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">თარიღის შემცირება</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">წინა თარიღის არჩევა</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">საათის ზრდა</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">მომდევნო საათის არჩევა</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">საათის შემცირება</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">წინა საათის არჩევა</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">წუთის ზრდა</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">მომდევნო წუთის არჩევა</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">წუთის შემცირება</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">წინა წუთის არჩევა</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">AM PM გადამრთველი</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">AM PM გადამრთველი</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">არჩეულია %s</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">არჩეული</string>
</resources>
