<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Primário</string>
    <string name="fluentui_secondary">Secundário</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Liberar</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Selecionada</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Não Selecionada</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Ícone</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Ícone</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Desabilitada</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Botão de Ação</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Habilitada</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Fechar Planilha</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Fechar</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Cancelar</string>
    <!--name of the icon -->
    <string name="fluentui_search">Pesquisar</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Microfone</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Limpar Texto</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Voltar</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Ativada</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Desativada</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutro</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marca</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contraste</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Sotaque</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Aviso</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Perigo</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Houve um erro</string>
    <string name="fluentui_error">Erro</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Dica</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Divisa</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Contorno</string>

    <string name="fluentui_action_button_icon">Ícone do botão de ação</string>
    <string name="fluentui_center">Centralizar o texto</string>
    <string name="fluentui_accessory_button">Botões de Acessório</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Botão de Opção</string>
    <string name="fluentui_label">Rótulo</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Expandido</string>
    <string name="fluentui_collapsed">Recolhido</string>

    <!--types of control -->
    <string name="fluentui_large">Grande</string>
    <string name="fluentui_medium">Médio</string>
    <string name="fluentui_small">Pequeno</string>
    <string name="fluentui_password_mode">Modo de Senha</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Subtítulo</string>
    <string name="fluentui_assistive_text">Texto Assistivo</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Título</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Curto</string>"
    <string name="fluentui_long">Longo</string>"
    <string name="fluentui_indefinite">Indeterminado</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Botão Pressionado</string>
    <string name="fluentui_dismissed">Ignorado</string>
    <string name="fluentui_timeout">Tempo Atingido</string>
    <string name="fluentui_left_swiped">Deslizado para a Esquerda</string>
    <string name="fluentui_right_swiped">Deslizou para a Direita</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Texto</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Número</string>
    <string name="fluentui_keyboard_phone">Telefone</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Email</string>
    <string name="fluentui_keyboard_password">Senha</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimal</string>
</resources>