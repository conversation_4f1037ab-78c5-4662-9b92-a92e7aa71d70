<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">主要</string>
    <string name="fluentui_secondary">次坐标轴</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">关闭</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">已选择</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">未选择</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">图标</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">图标</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">已禁用</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">动作按钮</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">已启用</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">关闭工作表</string>
    <!-- Describes close action -->
    <string name="fluentui_close">关闭</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">取消</string>
    <!--name of the icon -->
    <string name="fluentui_search">搜索</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">麦克风</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">清除文本</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">返回</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">已激活</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">已停用</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">中性</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">品牌</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">对比度</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">突显</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">警告</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">危险</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">出现错误</string>
    <string name="fluentui_error">错误</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">提示</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">V 形</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">分级显示</string>

    <string name="fluentui_action_button_icon">操作按钮图标</string>
    <string name="fluentui_center">文本居中</string>
    <string name="fluentui_accessory_button">附件按钮</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">单选按钮</string>
    <string name="fluentui_label">标签</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">加宽</string>
    <string name="fluentui_collapsed">已折叠</string>

    <!--types of control -->
    <string name="fluentui_large">大型</string>
    <string name="fluentui_medium">中等</string>
    <string name="fluentui_small">小型</string>
    <string name="fluentui_password_mode">密码模式</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">副标题</string>
    <string name="fluentui_assistive_text">辅助文本</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">标题</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">短</string>"
    <string name="fluentui_long">长</string>"
    <string name="fluentui_indefinite">无限期</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">已按下按钮</string>
    <string name="fluentui_dismissed">已忽略</string>
    <string name="fluentui_timeout">已超时</string>
    <string name="fluentui_left_swiped">向左轻扫</string>
    <string name="fluentui_right_swiped">向右轻扫</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">文本</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">数字</string>
    <string name="fluentui_keyboard_phone">电话</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">电子邮件</string>
    <string name="fluentui_keyboard_password">密码</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">小数</string>
</resources>