package com.microsoft.fluentui.icons.avataricons.presence.dark

import androidx.compose.ui.graphics.vector.ImageVector
import com.microsoft.fluentui.icons.avataricons.presence.DarkGroup
import kotlin.collections.List as ____KtList

object SmallGroup

val DarkGroup.Small: SmallGroup
    get() = SmallGroup

private var __AllIcons: ____KtList<ImageVector>? = null

val SmallGroup.AllIcons: ____KtList<ImageVector>
    get() {
        if (__AllIcons != null) {
            return __AllIcons!!
        }
        __AllIcons = listOf()
        return __AllIcons!!
    }
