<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Principale</string>
    <string name="fluentui_secondary">Secondario</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Ignora</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Selezionato</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Non selezionata</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Icona</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Icona</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Disabilitato</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Pulsante di azione</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Abilitato</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Chiudi foglio di lavoro</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Chiudi</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Annulla</string>
    <!--name of the icon -->
    <string name="fluentui_search">Cerca</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Microfono</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Cancella testo</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Indietro</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Attivata</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Disattivata</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Né soddisfacente né insoddisfacente</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marchio</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contrasto</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Colore</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Avviso</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Pericolo</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Si è verificato un errore</string>
    <string name="fluentui_error">Errore</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Suggerimento</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Gallone</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Struttura</string>

    <string name="fluentui_action_button_icon">Icona del pulsante Azione</string>
    <string name="fluentui_center">Allinea il testo al centro</string>
    <string name="fluentui_accessory_button">Pulsanti accessori</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Pulsante di scelta</string>
    <string name="fluentui_label">Etichetta</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Espanso</string>
    <string name="fluentui_collapsed">Compresso</string>

    <!--types of control -->
    <string name="fluentui_large">Grande</string>
    <string name="fluentui_medium">Medio</string>
    <string name="fluentui_small">Piccola</string>
    <string name="fluentui_password_mode">Modalità password</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Sottotitolo</string>
    <string name="fluentui_assistive_text">Testo facilitato</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Titolo</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Breve</string>"
    <string name="fluentui_long">Lungo</string>"
    <string name="fluentui_indefinite">Tempo illimitato</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Pulsante premuto</string>
    <string name="fluentui_dismissed">Ignorate</string>
    <string name="fluentui_timeout">Timeout</string>
    <string name="fluentui_left_swiped">Scorrimento rapido a sinistra</string>
    <string name="fluentui_right_swiped">Scorrimento rapido a destra</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Testo</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Numero</string>
    <string name="fluentui_keyboard_phone">Telefono</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Email</string>
    <string name="fluentui_keyboard_password">Password</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimale</string>
</resources>