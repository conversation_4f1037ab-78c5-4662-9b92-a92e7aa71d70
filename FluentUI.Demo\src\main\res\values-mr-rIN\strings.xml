<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI डेमो</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s निवडले</string>
    <string name="app_modifiable_parameters">सुधारित करण्यायोग्य मापदंड</string>
    <string name="app_right_accessory_view">उजवे ऍक्सेसरी दृश्य</string>

    <string name="app_style">शैली</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">प्रतीक दाबले</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">डेमो प्रारंभ करा</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">कॅराऊसेल</string>
    <string name="actionbar_icon_radio_label">प्रतीक</string>
    <string name="actionbar_basic_radio_label">मूलभूत</string>
    <string name="actionbar_position_bottom_radio_label">तळ</string>
    <string name="actionbar_position_top_radio_label">शीर्ष</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">क्रियापट्टी प्रकार</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">क्रियापट्टी स्थिती</string>

    <!--AppBar-->
    <string name="app_bar_style">अनुप्रयोग पट्टी शैली</string>
    <string name="app_bar_subtitle">उपशीर्षक</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">तळ सीमा</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">नेव्हिगेशन प्रतीक क्लिक केले.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">ध्वजांकित करा</string>
    <string name="app_bar_layout_menu_settings">सेटिंग्ज</string>
    <string name="app_bar_layout_menu_search">शोध</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">वर्तणूक स्क्रोल करा: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">स्क्रोल वर्तन टॉगल करा</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">नेव्हिगेशन प्रतीक टॉगल करा</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">अवतार दर्शवा</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">मागे दर्शवा प्रतीक</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">प्रतीक लपवा</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">प्रतीक दर्शवा</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">शोधपट्टी मांडणी शैली टॉगल करा</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">ऍक्सेसरी दृश्य म्हणून दर्शवा</string>
    <string name="app_bar_layout_searchbar_action_view_button">क्रिया दृश्य म्हणून दर्शवा</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">थीम्स दरम्यान टॉगल करा (कार्यकलाप पुन्हा तयार करते)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">थीम टॉगल करा</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">आयटम</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">अतिरिक्त स्क्रोल करण्यायोग्य सामुग्री</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">वर्तुळ शैली</string>
    <string name="avatar_style_square">चौरस शैली</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">मोठा</string>
    <string name="avatar_size_medium">मध्यम</string>
    <string name="avatar_size_small">लहान</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">दुहेरी अधिक मोठा</string>
    <string name="avatar_size_xlarge_accessibility">अधिक मोठा</string>
    <string name="avatar_size_xsmall_accessibility">अधिक लहान</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">कमाल प्रदर्शित अवतार</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">ओव्हरफ्लो अवतार गणना</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">सीमा प्रकार</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">OverflowAvatarCount संचासह अवतार समूह कमाल प्रदर्शित अवतारचे अनुसरण करणार नाही.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">फेस स्टॅक</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">फेस पाइल</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">ओव्हरफ्लो क्लिक केले</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">%d अनुक्रमणिकेला अवतारदृश्य क्लिक केले</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">अधिसूचना बॅज</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">बिंदू</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">यादी</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">भूमिका</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">फोटो</string>
    <string name="bottom_navigation_menu_item_news">बातम्या</string>
    <string name="bottom_navigation_menu_item_alerts">सतर्क</string>
    <string name="bottom_navigation_menu_item_calendar">दिनदर्शिका</string>
    <string name="bottom_navigation_menu_item_team">संघ</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">लेबल्स टॉगल करा</string>
    <string name="bottom_navigation_three_menu_items_button">तीन मेनू आयटम्स दर्शवा</string>
    <string name="bottom_navigation_four_menu_items_button">चार मेनू आयटम्स दर्शवा</string>
    <string name="bottom_navigation_five_menu_items_button">पाच मेनू आयटम्स दर्शवा</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">लेबल %s आहेत</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">तळपत्रक</string>
    <string name="bottom_sheet_dialog">तळपत्रकसंभाषण</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">डिसमिस करण्यासाठी खाली स्वाइप करा</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">एकेरी ओळ आयटम्ससह दर्शवा</string>
    <string name="bottom_sheet_with_double_line_items">दुहेरी ओळ आयटम्ससह दर्शवा</string>
    <string name="bottom_sheet_with_single_line_header">एकेरी ओळ शीर्षलेखासह दर्शवा</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">दुहेरी ओळ शीर्षलेख आणि विभाजकांसह दर्शवा</string>
    <string name="bottom_sheet_dialog_button">दर्शवा</string>
    <string name="drawer_content_desc_collapse_state">विस्तृत करा</string>
    <string name="drawer_content_desc_expand_state">लहान करा</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s क्लिक करा</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">%s दीर्घ क्लिक करा</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">डिसमिस क्लिक करा</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">आयटम समाविष्ट करा</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">आयटम अद्ययावत करा</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">डिसमिस करा</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">जोडा</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">उल्लेख करा</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">ठळक</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">तिर्यक</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">अधोरेखन</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">मध्यरेखीत</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">पूर्ववत करा</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">पुुन्हा करा</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">बुलेट</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">यादी</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">लिंक</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">आयटम अद्ययावत करत आहे</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">अंतरण</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">स्थिती डिसमिस करा</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">प्रारंभ करा</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">समाप्त</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">समूह स्थान</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">आयटम स्थान</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">ध्वजांकित करा</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">आयटम ध्वजांकित करा क्लिक केले</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">प्रत्युत्तर द्या</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">प्रत्युत्तर आयटम क्लिक केला</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">अग्रेषित करा</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">आयटम अग्रेषित करा क्लिक केले</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">हटवा</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">आयटम हटवा क्लिक केले</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">अवतार</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">कॅमेरा</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">फोटो घ्या</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">कॅमेरा आयटम क्लिक केला</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">गॅलरी</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">आपले फोटोज पहा</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">गॅलरी आयटम क्लिक केला</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">व्हिडिओज</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">आपले व्हिडिओ प्ले करा</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">व्हिडिओ आयटम क्लिक केला</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">व्यवस्थापित करा</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">आपली मीडिया लायब्ररी व्यवस्थापित करा</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">आयटम व्यवस्थापित करा क्लिक केले</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">ईमेल क्रिया</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">दस्तऐवज</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">दुपारी 2:14 ला शेवटचे अद्ययावत केले</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">सामायिक करा</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">आयटम सामायिक करा क्लिक केले</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">हलवा</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">आयटम हलवा क्लिक केले</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">हटवा</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">आयटम हटवा क्लिक केले</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">माहिती</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">माहिती आयटम क्लिक केला</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">घड्याळ</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">घड्याळ आयटम क्लिक केला</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">अलार्म</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">आयटम हलवा क्लिक केला</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">वेळ विभाग</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">वेळ विभाग आयटम क्लिक केला</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">बटणाची भिन्न दृश्ये</string>
    <string name="button">बटण</string>
    <string name="buttonbar">बटणपट्टी</string>
    <string name="button_disabled">अक्षम केलेल्या बटणाचे उदाहरण</string>
    <string name="button_borderless">कडारहित बटणाचे उदाहरण</string>
    <string name="button_borderless_disabled">कडारहित अक्षम केलेल्या बटणाचे उदाहरण</string>
    <string name="button_large">मोठ्या बटणाचे उदाहरण</string>
    <string name="button_large_disabled">मोठ्या अक्षम केलेल्या बटणाचे उदाहरण</string>
    <string name="button_outlined">बाह्यरेखांकित बटण उदाहरण</string>
    <string name="button_outlined_disabled">बाह्यरेखांकित अक्षम केलेल्या बटणाचे उदाहरण</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">तारीख निवडा</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">तारीखवेळपिकर</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">एकल तारीख</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">कोणतीही तारीख निवडली नाही</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">तारीख पिकर दर्शवा</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">निवडलेल्या तारीख टॅबसह तारीख वेळ पिकर दर्शवा</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">निवडलेल्या वेळ टॅबसह तारीख वेळ पिकर दर्शवा</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">तारीख वेळ पिकर दर्शवा</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">तारीख परिक्षेत्र</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">प्रारंभ:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">समाप्त:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">कोणताही प्रारंभ निवडलेला नाही</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">कोणतीही समाप्ती निवडली नाही</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">प्रारंभ तारीख निवडा</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">समाप्ती तारीख निवडा</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">तारीख वेळ परिक्षेत्र</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">तारीख वेळ परिक्षेत्र निवडा</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">तारीखवेळपिकरसंवाद</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">संवाद दर्शवा</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">ड्रॉवर दर्शवा</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">ड्रॉवर डायलॉग दर्शवा</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">तळाशी कोणताही फिकट संवाद नाही</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">शीर्ष ड्रॉवर दर्शवा</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">कोणताही फिकट शीर्ष डायलॉग नाही</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> अँकर दृश्य शीर्ष डायलॉग दर्शवा</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> शीर्षक नसलेला शीर्ष डायलॉग दर्शवा</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> शीर्षक शीर्ष संवाद खाली दर्शवा</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">उजवा ड्रॉवर दर्शवा</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">डावा ड्रॉवर दर्शवा</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">शीर्षक, प्राथमिक मजकूर</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">उपशीर्षक, दुय्यम मजकूर</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">सानुकूल उपशीर्षक मजकूर</string>
    <!-- Footer -->
    <string name="list_item_footer">तळटीप, तळटीप मजकूर</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">करडा उप शीर्षलेख मजकूरासह एकेरी-रेखा यादी</string>
    <string name="list_item_sub_header_two_line">द्वि-रेखा यादी</string>
    <string name="list_item_sub_header_two_line_dense">गच्च अंतरण असलेली द्वि-रेखा यादी</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">सानुकूल दुय्यम उपशीर्षक दृश्यासह द्वि रेखा यादी</string>
    <string name="list_item_sub_header_three_line">काळा उप शीर्षलेख मजकूर असलेली त्रि-रेखा यादी</string>
    <string name="list_item_sub_header_no_custom_views">कोणत्याही सानुकूल दृश्यांशिवायच्या आयटम्सची यादी करा</string>
    <string name="list_item_sub_header_large_header">मोठ्या सानुकूल दृश्यांसह आयटम्सची यादी करा</string>
    <string name="list_item_sub_header_wrapped_text">रॅप केलेला मजकूर असलेले आयटम्स यादीबद्ध करा</string>
    <string name="list_item_sub_header_truncated_text">कापलेला मजकूर असलेले आयटम्स यादीबद्ध करा</string>
    <string name="list_item_sub_header_custom_accessory_text">क्रिया</string>
    <string name="list_item_truncation_middle">मध्यभागी कापणे.</string>
    <string name="list_item_truncation_end">ट्रंकेशन समाप्त.</string>
    <string name="list_item_truncation_start">ट्रंकेशन प्रारंभ करा.</string>
    <string name="list_item_custom_text_view">मूल्य</string>
    <string name="list_item_click">आपण यादी आयटमवर क्लिक केले.</string>
    <string name="list_item_click_custom_accessory_view">आपण सानुकूल ऍक्सेसरी दृश्यावर क्लिक केले.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">आपण उप शीर्षलेख सानुकूल ऍक्सेसरी दृश्यावर क्लिक केले.</string>
    <string name="list_item_more_options">अधिक पर्याय</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">निवडा</string>
    <string name="people_picker_select_deselect_example">निवडानिवडरद्दकरा</string>
    <string name="people_picker_none_example">काहीही नाही</string>
    <string name="people_picker_delete_example">हटवा</string>
    <string name="people_picker_custom_persona_description">सानुकूल IPersona ऑब्जेक्ट कसे तयार करायचे हे हे उदाहरण दर्शवते.</string>
    <string name="people_picker_dialog_title_removed">आपण एक व्यक्तिगत काढून टाकले:</string>
    <string name="people_picker_dialog_title_added">आपण एक व्यक्तिगत जोडले:</string>
    <string name="people_picker_drag_started">ड्रॅग प्रारंभ झाले</string>
    <string name="people_picker_drag_ended">ड्रॅग समाप्त झाले</string>
    <string name="people_picker_picked_personas_listener">व्यक्तिगत श्रोता</string>
    <string name="people_picker_suggestions_listener">सूचना श्रोता</string>
    <string name="people_picker_persona_chip_click">आपण %s वर क्लिक केले</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s प्राप्तकर्ता</item>
        <item quantity="other">%1$s प्राप्‍तकर्ते</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">पर्स्टिस्टंट तळपत्रक विस्तृत करा</string>
    <string name="collapse_persistent_sheet_button"> पर्स्टिस्टंट तळपत्रक लपवा</string>
    <string name="show_persistent_sheet_button"> पर्स्टिस्टंट तळपत्रक दर्शवा</string>
    <string name="new_view">हे नवीन दृश्य आहे</string>
    <string name="toggle_sheet_content">तळशीट सामुग्री टॉगल करा</string>
    <string name="switch_to_custom_content">सानुकूल सामुग्रीवर स्विच करा</string>
    <string name="one_line_content">एकेरी रेखा तळपत्रक सामुग्री</string>
    <string name="toggle_disable_all_items">सर्व आयटम अक्षम करा टॉगल करा</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">दृश्य जोडा/दूर करा</string>
    <string name="persistent_sheet_item_change_collapsed_height"> संकुचित उंची बदला</string>
    <string name="persistent_sheet_item_create_new_folder_title">नवीन फोल्डर</string>
    <string name="persistent_sheet_item_create_new_folder_toast">नवीन फोल्डर आयटम क्लिक केला</string>
    <string name="persistent_sheet_item_edit_title">संपादित करा</string>
    <string name="persistent_sheet_item_edit_toast">आयटम संपादित करा क्लिक केले</string>
    <string name="persistent_sheet_item_save_title">सुरक्षित करा</string>
    <string name="persistent_sheet_item_save_toast">आयटम सुरक्षित करा क्लिक केले</string>
    <string name="persistent_sheet_item_zoom_in_title">झूम इन</string>
    <string name="persistent_sheet_item_zoom_in_toast"> झूम इन आयटम क्लिक केला</string>
    <string name="persistent_sheet_item_zoom_out_title">झूम आऊट</string>
    <string name="persistent_sheet_item_zoom_out_toast">झूम आउट आयटम क्लिक केला</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">उपलब्ध</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">ऍलन मुंगर</string>
    <string name="persona_name_amanda_brady">अमांडा ब्रॅडी</string>
    <string name="persona_name_ashley_mccarthy">ऍशली मॅकार्थी</string>
    <string name="persona_name_carlos_slattery">कार्लोस स्लेटरी</string>
    <string name="persona_name_carole_poland">कॅरोल पोलंड</string>
    <string name="persona_name_cecil_folk">सिसिल फोक</string>
    <string name="persona_name_celeste_burton">सेलेस्ट बर्टन</string>
    <string name="persona_name_charlotte_waltson">चार्लेट वॅटसन</string>
    <string name="persona_name_colin_ballinger">कॉलिन बॅलिंजर</string>
    <string name="persona_name_daisy_phillips">डेझी फिलिप्स</string>
    <string name="persona_name_elliot_woodward">इलियट वूडवर्ड</string>
    <string name="persona_name_elvia_atkins">एल्व्हिया ॲतकिन्स</string>
    <string name="persona_name_erik_nason">एरिक नॅसॉन</string>
    <string name="persona_name_henry_brill">हेन्री ब्रिल</string>
    <string name="persona_name_isaac_fielder">आयझॅक फील्डर</string>
    <string name="persona_name_johnie_mcconnell">जॉनी मॅककॉनेल</string>
    <string name="persona_name_kat_larsson">कॅट लारसन</string>
    <string name="persona_name_katri_ahokas">कॅत्री अहोकास</string>
    <string name="persona_name_kevin_sturgis">केविन स्टर्जिस</string>
    <string name="persona_name_kristen_patterson">क्रिस्टन पॅटर्शन</string>
    <string name="persona_name_lydia_bauer">लिडिया बॉयर</string>
    <string name="persona_name_mauricio_august">मॉरीशीयो ऑगस्ट</string>
    <string name="persona_name_miguel_garcia">मायग्‍युएल गार्सिया</string>
    <string name="persona_name_mona_kane">मोना काने</string>
    <string name="persona_name_robin_counts">रॉबिन काउंट्स</string>
    <string name="persona_name_robert_tolbert">रॉबर्ट टॉल्बर्ट</string>
    <string name="persona_name_tim_deboer">टिम डेबोअर</string>
    <string name="persona_name_wanda_howard">वांडा हॉवर्ड</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">डिझाइनर</string>
    <string name="persona_subtitle_engineer">इंजिनियर</string>
    <string name="persona_subtitle_manager">व्यवस्थापक</string>
    <string name="persona_subtitle_researcher">संशोधक</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (विखंडनाची चाचणी करण्यासाठी दीर्घ मजकूर उदाहरण)</string>
    <string name="persona_view_description_xxlarge">मजकूराच्या तीन ओळींसह XXLarge अवतार</string>
    <string name="persona_view_description_large">मजकूराच्या दोन ओळींसह मोठा अवतार</string>
    <string name="persona_view_description_small">एका ओळीच्या मजकूरासह लहान अवतार</string>
    <string name="people_picker_hint">दर्शविलेल्या संकेतासह काहीही नाही</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">व्यक्तिगत चिप अक्षम केली</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">व्यक्तिगत चिप त्रुटी</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">कोणत्याही बंद प्रतीकाशिवाय व्यक्तिगत चिप</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">मुलभूत व्यक्तिगत चिप</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">निवडलेल्या व्यक्तिगत चिपवर आपण क्लिक केले.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">सामायिक करा</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">अनुसरण करा</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">लोकांना आमंत्रित करा</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">पृष्ठ रिफ्रेश करा</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">ब्राउझरमध्ये उघडा</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">हा एक बहुरेखी स्नॅकबार आहे. कमाल रेखा दोन वर सेट केल्या आहेत, उर्वरित मजकूर कापला जाईल.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">सर्व बातम्या</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">सुरक्षित केलेल्या बातम्या</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">साइट्सवरून बातम्या</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">कार्यालयीन तासांच्या बाहेर सूचित करा</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">डेस्कटॉपवर निष्क्रिय असेल तेव्हा सूचित करा</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">आपण या आयटमवर क्लिक केले:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">साधा मेनू</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">साधा मेनू2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">एक निवडक आयटम आणि डिव्हायडरसह मेनू</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">सर्व निवडक आयटम्स, प्रतीके आणि दीर्घ मजकूर असलेला मेनू</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">दर्शवा</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">वर्तुळाकार प्रगती</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">लहान</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">मध्यम</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">मोठा</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">रेखीय प्रगती</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">अनिश्चित</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">निर्दिष्ट करा</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">शोधपट्टी</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">मायक्रोफोन कॉलबॅक</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">स्‍वयं-सुधारणा</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">मायक्रोफोन दाबला</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">उजवे दृश्य दाबले</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">कीबोर्ड शोध दाबला</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">स्नॅकबार दर्शवा</string>
    <string name="fluentui_dismiss_snackbar">स्नॅकबार डिसमिस करा</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">क्रिया</string>
    <string name="snackbar_action_long">दीर्घ मजकूर क्रिया</string>
    <string name="snackbar_single_line">एकेरी रेखा स्नॅकबार</string>
    <string name="snackbar_multiline">ही एक बहुरेखी स्नॅकबार आहे. कमाल रेखा दोन वर सेट केल्या आहेत, उर्वरित मजकूर कापला जाईल.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">हा घोषणा स्नॅकबार आहे. हे नवीन वैशिष्ट्ये सांगण्यासाठी वापरले जाते.</string>
    <string name="snackbar_primary">हा प्राथमिक स्नॅकबार आहे.</string>
    <string name="snackbar_light">हा एक लाइट स्नॅकबार आहे.</string>
    <string name="snackbar_warning">ही चेतावणी स्नॅकबार आहे.</string>
    <string name="snackbar_danger">हा एक धोकादायक स्नॅकबार आहे.</string>
    <string name="snackbar_description_single_line">लहान कालावधी</string>
    <string name="snackbar_description_single_line_custom_view">लहान सानुकूल दृश्य म्हणून वर्तुळाकार प्रगतीसह दीर्घ कालावधी</string>
    <string name="snackbar_description_single_line_action">क्रियेसह लहान कालावधी</string>
    <string name="snackbar_description_single_line_action_custom_view">क्रिया आणि मध्यम सानुकूल दृश्यासह लघु कालावधी</string>
    <string name="snackbar_description_single_line_custom_text_color">सानुकूल मजकूर रंगासह लहान कालावधी</string>
    <string name="snackbar_description_multiline">दीर्घ कालावधी</string>
    <string name="snackbar_description_multiline_custom_view">लहान सानुकूल दृश्यासह दीर्घ कालावधी</string>
    <string name="snackbar_description_multiline_action">क्रिया आणि मजकूर अद्यतनांसह अमर्याद कालावधी</string>
    <string name="snackbar_description_multiline_action_custom_view">क्रिया आणि मध्यम सानुकूल दृश्यासह लघु कालावधी</string>
    <string name="snackbar_description_multiline_action_long">दीर्घ क्रिया मजकूरासह लहान कालावधी</string>
    <string name="snackbar_description_announcement">लहान कालावधी</string>
    <string name="snackbar_description_updated">हा मजकूर अद्ययावत केला गेला आहे.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">स्नॅकबार दर्शवा</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">एकेरी रेखा</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">एकाधिकरेखा</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">घोषणा शैली</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">प्राथमिक शैली</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">फिकट शैली</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">चेतावणी शैली</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">धोका शैली</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">होम</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">मेल</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">सेटिंग्ज</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">अधिसूचना</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">अधिक</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">मजकूर संरेखन</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">अनुलंब</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">क्षैतिज</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">कोणताही मजकूर नाही</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">टॅब आयटम्स</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">शीर्षक</string>
    <string name="cell_sample_description">वर्णन</string>
    <string name="calculate_cells">100 कक्ष लोड करा/गणना करा</string>
    <string name="calculate_layouts">100 मांडणी लोड करा/गणना करा</string>
    <string name="template_list">टेम्प्लेट यादी</string>
    <string name="regular_list">नियमित यादी</string>
    <string name="cell_example_title">शीर्षक: कक्ष</string>
    <string name="cell_example_description">वर्णन: ओरीएंटेशन बदलण्यासाठी टॅप करा</string>
    <string name="vertical_layout">अनुलंब मांडणी</string>
    <string name="horizontal_layout">क्षैतिज मांडणी</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">मानक टॅब 2-खंड</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">मानक टॅब 3-खंड</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">मानक टॅब 4-खंड</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">पेजरसह मानक टॅब</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">टॅब स्विच करा</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">गोळी टॅब </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">टूलटिपसाठी टॅप करा</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">सानुकूल दिनदर्शिका टूलटिपसाठी टॅप करा</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">सानुकूल रंग टूलटिपवर टॅप करा</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">आतील टूलटिप डिसमिस करण्यासाठी टॅप करा</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">सानुकूल दृश्य टूलटिपसाठी टॅप करा</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">सानुकूल रंग टूलटिपवर टॅप करा</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp ऑफसेटY सह शीर्ष समाप्त टूलटिप</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">तळ प्रारंभ टूलटिप</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp ऑफसेटY सह तळ समाप्त टूलटिप</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">आतील टूलटिप डिसमिस करा</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">टूलटिप डिसमिस केले</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">हेडलाइन फिकट 28sp आहे</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">शीर्षक 1 मध्यम 20sp आहे</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">शीर्षक 2 नियमित 20sp आहे</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">शीर्षक नियमित 18sp आहे</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">उपशीर्षक 1 नियमित 16sp आहे</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">उपशीर्षक 2 मध्यम 16sp आहे</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">मुख्य भाग 1 नियमित 14sp आहे</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">मुख्य भाग 2 मध्यम 14sp आहे</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">मथळा नियमित 12sp आहे</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK आवृत्ती: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">आयटम %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">फोल्डर</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">क्लिक केले</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">स्कॅफोल्ड</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB विस्तृत केले</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB संक्षिप्त केले</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">यादी रिफ्रेश करण्यासाठी क्लिक करा</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">ड्रॉवर उघडा</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">मेनू आयटम</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">ऑफसेट X (dp मध्ये)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">ऑफसेट Y (dp मध्ये)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">सामुग्री मजकूर</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">सामुग्री मजकूराची पुनरावृत्ती करा</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">सामुग्री मजकूराच्या संदर्भात मेनूची रूंदी बदलेल. स्क्रीन आकाराच्या 75% कमाल
        रूंदी प्रतिबंधित आहे. बाजूकडून आणि तळाकडील सामुग्री समास टोकननुसार नियंत्रित केले जाते. उंची बदलण्यासाठी समान सामुग्री मजकूराची पुनरावृत्ती
        होईल.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">मेनू उघडा</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">मुलभूत कार्ड</string>
    <!-- UI Label for Card -->
    <string name="file_card">फाइल कार्ड</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">घोषणा कार्ड</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">यादृच्छिक UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">पर्याय</string>
    <!-- UI Label for Title -->
    <string name="card_title">शीर्षक</string>
    <!-- UI Label for text -->
    <string name="card_text">मजकूर</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">उप मजकूर</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">या बॅनरसाठी दुय्यम प्रतिलिपी आवश्यक असल्यास दोन रेषांपर्यंत ओघ दर्शवू शकतात.</string>
    <!-- UI Label Button -->
    <string name="card_button">बटण</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">डायलॉग दर्शवा</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">बाहेर क्लिक करून डायलॉग डिसमिस करा</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">मागे जा दाबून डायलॉग डिसमिस करा</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">डायलॉग डिसमिस केला</string>
    <!-- UI Label Cancel -->
    <string name="cancel">रद्द करा</string>
    <!-- UI Label Ok -->
    <string name="ok">ठीक आहे</string>
    <!-- A sample description -->
    <string name="dialog_description">डायलॉग एक लहान विंडो असते जी प्रयोक्त्यास निर्णय घेण्याची किंवा अतिरिक्त माहिती प्रविष्ट करण्याची सूचना देते.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">ड्रॉवर उघडा</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">ड्रॉवर विस्तृत करा</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">ड्रॉवर बंद करा</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">ड्रॉवर प्रकार निवडा</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">शीर्ष</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">संपूर्ण ड्रॉवर दृश्यमान प्रदेशात दर्शवेल.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">तळ</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">संपूर्ण ड्रॉवर दृश्यमान प्रदेशात दर्शवतो. मोशन स्क्रोल सामुग्री स्वाइप करा. विस्तारण्यायोग्य ड्रॉवर ड्रॅग हँडलद्वारे विस्तृत करा.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">डावीकडून स्लाइड करा</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">ड्रॉवर डावीकडून दृश्यमान प्रदेशाकडे स्लाइड करा.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">उजवीकडून स्लाइड करा</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">ड्रॉवर उजवीकडून दृश्यमान प्रदेशाकडे स्लाइड करा.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">तळाकडून स्लाइड करा</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">ड्रॉवर स्क्रीनच्या तळापासून दृश्यमान प्रदेशावर स्लाइड करा. विस्तारण्यायोग्य ड्रॉवरवर स्वाइप अप मोशन त्याचा उर्वरित भाग दृश्यमान प्रदेशात आणा आणि नंतर स्क्रोल करा.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">स्क्रिम दृश्यमान</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">ड्रॉवर सामुग्री निवडा</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">संपूर्ण स्क्रीन आकार स्क्रोल करण्यायोग्य सामुग्री</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">अर्ध्याहून अधिक स्क्रीन सामुग्री</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">अर्ध्यापेक्षा कमी स्क्रीन सामुग्री</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">डायनॅमिक आकार असलेली सामुग्री</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">नेस्टेड ड्रॉवर सामुग्री</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">विस्तृत करण्यायोग्य</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">खुली स्थिती वगळा</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">स्क्रिम क्लिकवर डिसमिस करणे प्रतिबंधित करा</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">हँडल दर्शवा</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">शीर्षक</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">टूलटिप मजकूर</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">सानुकूल सामुग्री टूलटिपसाठी टॅप करा</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">शीर्ष प्रारंभ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">शीर्ष शेवट </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">तळ प्रारंभ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">तळ समाप्ती </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">मध्य </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">सानुकूल केंद्र</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">रीलिझ नोट्सवरील अद्यतनांसाठी, </string>
    <string name="click_here">येथे क्लिक करा.</string>
    <string name="open_source_cross_platform">ओपन सोर्स क्रॉस प्लॅटफॉर्म डिझाइन सिस्टम.</string>
    <string name="intuitive_and_powerful">आवेदनशील &amp; प्रभावी.</string>
    <string name="design_tokens">डिझाइन टोकन्स</string>
    <string name="release_notes">रीलिझ नोट्‍स</string>
    <string name="github_repo">GitHub रेपो</string>
    <string name="github_repo_link">GitHub रेपोची लिंक</string>
    <string name="report_issue">समस्येची तक्रार करा</string>
    <string name="v1_components">V1 घटक</string>
    <string name="v2_components">V2 घटक</string>
    <string name="all_components">सर्व</string>
    <string name="fluent_logo">Fluent लोगो</string>
    <string name="new_badge">नवीन</string>
    <string name="modified_badge">सुधारित</string>
    <string name="api_break_badge">API ब्रेक</string>
    <string name="app_bar_more">अधिक</string>
    <string name="accent">ऍक्सेंट</string>
    <string name="appearance">दृश्यस्वरूप</string>
    <string name="choose_brand_theme">आपली ब्रँड थीम निवडा:</string>
    <string name="fluent_brand_theme">Fluent ब्रँड</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">दृश्यस्वरूप निवडा</string>
    <string name="appearance_system_default">सिस्टम डिफॉल्ट</string>
    <string name="appearance_light">फिकट</string>
    <string name="appearance_dark">गडद</string>
    <string name="demo_activity_github_link">डेमो कार्यकलाप GitHub लिंक</string>
    <string name="control_tokens_details">नियंत्रण टोकन तपशील</string>
    <string name="parameters">परिमापने</string>
    <string name="control_tokens">नियंत्रण टोकन्स</string>
    <string name="global_tokens">ग्लोबल टोकन्स</string>
    <string name="alias_tokens">उपनाम टोकन्स</string>
    <string name="sample_text">मजकूर</string>
    <string name="sample_icon">नमुना प्रतीक</string>
    <string name="color">रंग</string>
    <string name="neutral_color_tokens">तटस्थ रंग टोकन्स</string>
    <string name="font_size_tokens">फॉन्ट आकार टोकन्स</string>
    <string name="line_height_tokens">रेखा उंची टोकन्स</string>
    <string name="font_weight_tokens">फॉन्ट वेट टोकन</string>
    <string name="icon_size_tokens">प्रतीक आकार टोकन्स</string>
    <string name="size_tokens">आकार टोकन्स</string>
    <string name="shadow_tokens">शॅडो टोकन्स</string>
    <string name="corner_radius_tokens">Corner RadiusTokens</string>
    <string name="stroke_width_tokens">स्ट्रोक रुंदी टोकन</string>
    <string name="brand_color_tokens">ब्रँड रंग टोकन्स</string>
    <string name="neutral_background_color_tokens">तटस्थ पार्श्वभूमी रंग टोकन्स</string>
    <string name="neutral_foreground_color_tokens">तटस्थ अग्रभूमी रंग टोकन्स</string>
    <string name="neutral_stroke_color_tokens">तटस्थ स्ट्रोक रंग टोकन्स</string>
    <string name="brand_background_color_tokens">ब्रँड पार्श्वभूमी रंग टोकन्स</string>
    <string name="brand_foreground_color_tokens">ब्रँड अग्रभाग रंग टोकन्स</string>
    <string name="brand_stroke_color_tokens">ब्रँड स्ट्रोक रंग टोकन्स</string>
    <string name="error_and_status_color_tokens">त्रुटी आणि स्थिती रंग टोकन्स</string>
    <string name="presence_tokens">उपस्थिती रंग टोकन्स</string>
    <string name="typography_tokens">टायपोग्राफी टोकन्स</string>
    <string name="unspecified">अनिर्दिष्ट</string>

</resources>