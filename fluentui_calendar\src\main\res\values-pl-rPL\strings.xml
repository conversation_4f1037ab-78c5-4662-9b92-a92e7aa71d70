<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">P</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">W</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">Ś</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">C</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">P</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">S</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">N</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Przejdź do następnego tygodnia</string>
    <string name="accessibility_goto_previous_week">Przejdź do poprzedniego tygodnia</string>
    <string name="accessibility_today">dzisiaj</string>
    <string name="accessibility_selected">Wybrane</string>

    <!-- *** Shared *** -->
    <string name="done">Gotowe</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Dzisiaj</string>
    <string name="tomorrow">Jutro</string>
    <string name="yesterday">Wczoraj</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Godzina rozpoczęcia</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Godzina zakończenia</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Data rozpoczęcia</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Data zakończenia</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Wybierz godzinę</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Wybieranie daty</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Selektor dat i godzin</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Selektor dat</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Zakres selektora dat i godzin</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Zakres selektora dat</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Karta Godzina rozpoczęcia</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Karta Godzina zakończenia</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Karta Data rozpoczęcia</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Karta Data zakończenia</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Zamknij okno dialogowe</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Zwiększ miesiąc</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">wybierz następny miesiąc</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Zmniejsz miesiąc</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">wybierz poprzedni miesiąc</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Zwiększ dzień</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">wybierz następny dzień</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Zmniejsz dzień</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">wybierz poprzedni dzień</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Zwiększ rok</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">wybierz następny rok</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Zmniejsz roku</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">wybierz poprzedni rok</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Zwiększ datę</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">wybierz następną datę</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Zmniejsz datę</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">wybierz poprzednią datę</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Zwiększ godzinę</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">wybierz następną godzinę</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Zmniejsz godzinę</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">wybierz poprzednią godzinę</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Zwiększ minutę</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">wybierz następną minutę</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Zmniejsz minutę</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">wybierz poprzednią minutę</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">Przełącz okres AM PM</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">przełącz okres AM PM</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">Wybrano %s</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">wybrano</string>
</resources>
