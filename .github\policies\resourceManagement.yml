id: 
name: GitO<PERSON>.PullRequestIssueManagement
description: GitOps.PullRequestIssueManagement primitive
owner: 
resource: repository
disabled: false
where: 
configuration:
  resourceManagementConfiguration:
    scheduledSearches:
    - description: 
      frequencies:
      - hourly:
          hour: 8
      filters:
      - isIssue
      - isOpen
      - hasLabel:
          label: 'Needs: Author Feedback'
      - noActivitySince:
          days: 5
      - isNotLabeledWith:
          label: 'Status: No Recent Activity'
      actions:
      - addLabel:
          label: 'Status: No Recent Activity'
      - addReply:
          reply: This issue has been automatically marked as stale because it has marked as requiring author feedback but has not had any activity for **4 days**. It will be closed if no further activity occurs **within 3 days of this comment**. Thank you for your contributions to Fluent UI Android !
    - description: 
      frequencies:
      - hourly:
          hour: 8
      filters:
      - isPullRequest
      - isOpen
      - hasLabel:
          label: 'Needs: Author Feedback'
      - hasLabel:
          label: 'Status: No Recent Activity'
      - noActivitySince:
          days: 5
      actions:
      - closeIssue
    - description: 
      frequencies:
      - hourly:
          hour: 3
      filters:
      - isPullRequest
      - isOpen
      - hasLabel:
          label: 'Needs: Author Feedback'
      - noActivitySince:
          days: 7
      - isNotLabeledWith:
          label: 'Status: No Recent Activity'
      actions:
      - addLabel:
          label: 'Status: No Recent Activity'
      - addReply:
          reply: This pull request has been automatically marked as stale because it was marked as requiring author feedback but has not had any activity for **7 days**. It will be closed if no further activity occurs **within 5 days of this comment**. Thank you for your contributions to Fluent UI Android!
    - description: 
      frequencies:
      - hourly:
          hour: 8
      filters:
      - hasLabel:
          label: 'Needs: Attention'
      - noActivitySince:
          days: 14
      - isOpen
      - isNotLabeledWith:
          label: Issue Pinged
      actions:
      - addReply:
          reply: "${assignees}\n\nGentle ping that this issue needs attention. "
      - addLabel:
          label: Issue Pinged
    - description: 
      frequencies:
      - hourly:
          hour: 8
      filters:
      - hasLabel:
          label: 'Resolution: Duplicate'
      - isOpen
      - noActivitySince:
          days: 3
      actions:
      - addReply:
          reply: Because this issue is marked as a duplicate and has not had activity for over 3 days, we're closing this issue for house-keeping purposes. Please refer to the issue that this issue was duplicated to for the purposes of tracking progress. Thank you.
      - closeIssue
    - description: 
      frequencies:
      - hourly:
          hour: 6
      filters:
      - isOpen
      - isIssue
      - hasLabel:
          label: 'Needs: Actionable Feedback :female_detective:'
      - isNotLabeledWith:
          label: 'Status: No Recent Activity'
      - noActivitySince:
          days: 2
      actions:
      - addReply:
          reply: This issue has been automatically marked as stale because it has marked as requiring actionable feedback but has not had any activity for **2 days**. It will be closed if no further activity occurs **within 3 days of this comment**. Thank you for your contributions to Fluent UI Android !
      - addLabel:
          label: 'Status: No Recent Activity'
    - description: 
      frequencies:
      - hourly:
          hour: 8
      filters:
      - isOpen
      - isIssue
      - hasLabel:
          label: "Resolution: Won't Fix"
      - noActivitySince:
          days: 3
      actions:
      - closeIssue
    - description: 
      frequencies:
      - hourly:
          hour: 8
      filters:
      - isOpen
      - hasLabel:
          label: 'Resolution: By Design'
      - noActivitySince:
          days: 3
      actions:
      - addReply:
          reply: "Because this issue is marked as by design and has not had activity for over 3 days, we're automatically closing it for house-keeping purposes. "
      - closeIssue
    - description: 
      frequencies:
      - hourly:
          hour: 3
      filters:
      - isOpen
      - isIssue
      - noActivitySince:
          days: 180
      - hasLabel:
          label: 'Type: Bug :bug:'
      actions:
      - addReply:
          reply: >-
            Because this reported issue has not had any activity for over 180 days, we're automatically closing it for house-keeping reasons.


            *Still require assistance? Please, create a new issue with up-to date details and latest version of Fluent.*
      - addLabel:
          label: 'Resolution: Soft Close'
      - closeIssue
    eventResponderTasks:
    - if:
      - payloadType: Pull_Request
      then:
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_controls/src/main/java/com/microsoft/fluentui/tokenized/controls/Button.kt
        then:
        - addLabel:
            label: 'Component: Button'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_controls/src/main/java/com/microsoft/fluentui/tokenized/controls/Checkbox.kt
        then:
        - addLabel:
            label: 'Component: Checkbox'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_controls/src/main/java/com/microsoft/fluentui/tokenized/controls/FloatingActionButton.kt
        then:
        - addLabel:
            label: 'Component: Floating Action Button (FAB)'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_controls/src/main/java/com/microsoft/fluentui/tokenized/controls/RadioButton.kt
        then:
        - addLabel:
            label: 'Component: RadioButton'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_controls/src/main/java/com/microsoft/fluentui/tokenized/controls/ToggleSwitch.kt
        then:
        - addLabel:
            label: 'Component: ToggleSwitch'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_ccb/src/main/java/com/microsoft/fluentui/tokenized/contextualcommandbar/ContextualCommandBar.kt
        then:
        - addLabel:
            label: 'Component: Contextual Command Bar (CCB)'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_listitem/src/main/java/com/microsoft/fluentui/tokenized/divider/Divider.kt
        then:
        - addLabel:
            label: 'Component: Divider'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_core/src/main/java/com/microsoft/fluentui/compose/Swipeable.kt
        then:
        - addLabel:
            label: 'Component: Swipeable'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_progress/src/main/java/com/microsoft/fluentui/tokenized/progress/CircularProgressIndicator.kt
        then:
        - addLabel:
            label: 'Component: Circular Progress Indicator'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_progress/src/main/java/com/microsoft/fluentui/tokenized/progress/LinearProgressIndicator.kt
        then:
        - addLabel:
            label: 'Component: Linear Progress Indicator'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_progress/src/main/java/com/microsoft/fluentui/tokenized/shimmer/Shimmer.kt
        then:
        - addLabel:
            label: 'Component: Shimmer'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_tablayout/src/main/java/com/microsoft/fluentui/tablayout/TabLayout.kt
        then:
        - addLabel:
            label: 'Component: Tab Layout'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_topappbars/src/main/java/com/microsoft/fluentui/appbarlayout/AppBarLayout.kt
        then:
        - addLabel:
            label: 'Component: Appbar'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_topappbars/src/main/java/com/microsoft/fluentui/search/Searchbar.kt
        then:
        - addLabel:
            label: 'Component: Searchbar'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_topappbars/src/main/java/com/microsoft/fluentui/toolbar/Toolbar.kt
        then:
        - addLabel:
            label: 'Component: Toolbar'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_topappbars/src/main/java/com/microsoft/fluentui/tokenized/AppBar.kt
        then:
        - addLabel:
            label: ' Component: Appbar'
      - if:
        - includesModifiedFiles:
            files:
            - fluentui_topappbars/src/main/java/com/microsoft/fluentui/tokenized/SearchBar.kt
        then:
        - addLabel:
            label: 'Component: Searchbar'
      - if:
        - includesModifiedFiles:
            files:
            - settings.gradle
            - gradle.properties
            - gradlew
            - gradlew.bat
            - publish-check.gradle
            - publish.gradle
            - config.gradle
            - cgmanifest.json
            - build.gradle
        then:
        - addLabel:
            label: 'Area: SDK/Config'
      description: 
    - if:
      - payloadType: Issue_Comment
      - isAction:
          action: Created
      - isActivitySender:
          issueAuthor: True
      - hasLabel:
          label: 'Needs: Author Feedback'
      - isOpen
      then:
      - addLabel:
          label: 'Needs: Attention'
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Issues
      - not:
          isAction:
            action: Closed
      - hasLabel:
          label: 'Status: No Recent Activity'
      - not:
          labelAdded:
            label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Issue_Comment
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Pull_Request_Review
      - isAction:
          action: Submitted
      - isReviewState:
          reviewState: Changes_requested
      then:
      - addLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Pull_Request
      - isActivitySender:
          issueAuthor: True
      - not:
          isAction:
            action: Closed
      - hasLabel:
          label: 'Needs: Author Feedback'
      then:
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Issue_Comment
      - isActivitySender:
          issueAuthor: True
      - hasLabel:
          label: 'Needs: Author Feedback'
      then:
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Pull_Request_Review
      - isActivitySender:
          issueAuthor: True
      - hasLabel:
          label: 'Needs: Author Feedback'
      then:
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Pull_Request
      - not:
          isAction:
            action: Closed
      - hasLabel:
          label: 'Status: No Recent Activity'
      - not:
          labelAdded:
            label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Issue_Comment
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Pull_Request_Review
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Pull_Request
      - hasLabel:
          label: AutoMerge
      then:
      - enableAutoMerge:
          mergeMethod: Squash
      description: 
    - if:
      - payloadType: Pull_Request
      - labelRemoved:
          label: AutoMerge
      then:
      - disableAutoMerge
      description: 
    - if:
      - payloadType: Pull_Request
      then:
      - inPrLabel:
          label: 'Status: In PR'
      description: 
    - if:
      - payloadType: Issues
      - or:
        - isAction:
            action: Opened
        - isAction:
            action: Reopened
      - or:
        - bodyContains:
            pattern: ^\s*$
            isRegex: True
      then:
      - addReply:
          reply: "Hi! Thanks for attempting to open an issue. Unfortunately, you didn't write anything in the body which makes it impossible to understand your concern. You are welcome to try again by opening a new issue. "
      - closeIssue
      description: 
    - if:
      - payloadType: Issue_Comment
      - isAction:
          action: Created
      - isActivitySender:
          issueAuthor: True
      - hasLabel:
          label: 'Needs: Actionable Feedback :female_detective:'
      - isOpen
      then:
      - removeLabel:
          label: 'Needs: Actionable Feedback :female_detective:'
      - addLabel:
          label: 'Needs: Attention'
      description: 
    - if:
      - payloadType: Issues
      - or:
        - labelAdded:
            label: 'Needs: Author Feedback'
        - labelAdded:
            label: 'Needs: Actionable Feedback :female_detective:'
      - isOpen
      then:
      - removeLabel:
          label: 'Needs: Triage :mag:'
      - removeLabel:
          label: 'Needs: Attention'
      description: 
onFailure: 
onSuccess: 
