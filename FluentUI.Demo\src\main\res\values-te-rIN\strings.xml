<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI డెమో</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%sని ఎంచుకున్నారు</string>
    <string name="app_modifiable_parameters">సవరించదగిన పారామితులు</string>
    <string name="app_right_accessory_view">కుడి అనుబంధ వీక్షణ</string>

    <string name="app_style">శైలి</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">సూక్ష్మచిత్రం ప్రెస్ చేయబడింది</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">డెమో ప్రారంభించు</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">రంగులరాట్నం</string>
    <string name="actionbar_icon_radio_label">సూక్ష్మచిత్రం</string>
    <string name="actionbar_basic_radio_label">ప్రాథమికం</string>
    <string name="actionbar_position_bottom_radio_label">దిగువ</string>
    <string name="actionbar_position_top_radio_label">ఎగువ</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">యాక్షన్ బార్ రకం</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">యాక్షన్ బార్ స్థానం</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar శైలి</string>
    <string name="app_bar_subtitle">ఉపశీర్షిక</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">దిగువ హద్దు</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">నావిగేషన్ చిహ్నం క్లిక్ చేయబడింది.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">ఫ్లాగ్ చేయండి</string>
    <string name="app_bar_layout_menu_settings">సెట్టింగ్‌లు</string>
    <string name="app_bar_layout_menu_search">శోధించు</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">స్క్రోల్ ప్రవర్తన: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">స్క్రోల్ ప్రవర్తనను టోగుల్ చేయండి</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">నావిగేషన్ పేన్‌ని టోగుల్ చేయి</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">అవతార్ చూపించు</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">తిరిగి చిహ్నాన్ని చూపించు</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">సూక్ష్మచిత్రాన్ని దాచండి</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">సూక్ష్మచిత్రాన్ని చూపించు</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">శోధన పట్టీ లేఅవుట్ శైలిని టోగుల్ చేయండి</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">అనుబంధ వీక్షణగా చూపు</string>
    <string name="app_bar_layout_searchbar_action_view_button">చర్య వీక్షణగా చూపు</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">థీమ్‌ల మధ్య టోగుల్ చేయండి (కార్యకలాపాన్ని పునఃసృష్టిస్తుంది)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">టోగుల్ థీమ్</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">అంశం</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">అదనపు స్క్రోల్ చేయగల కంటెంట్</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">వృత్తం శైలి</string>
    <string name="avatar_style_square">చతురస్ర శైలి</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">ఎక్స్ఎక్స్‌లార్జ్</string>
    <string name="avatar_size_xlarge">ఎక్స్‌లార్జ్</string>
    <string name="avatar_size_large">పెద్దది</string>
    <string name="avatar_size_medium">మధ్యస్థం</string>
    <string name="avatar_size_small">చిన్నది</string>
    <string name="avatar_size_xsmall">ఎక్స్‌స్మాల్</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">రెట్టింపు పెద్దది</string>
    <string name="avatar_size_xlarge_accessibility">చాలా పెద్దది</string>
    <string name="avatar_size_xsmall_accessibility">అత్యంత చిన్నది</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">గరిష్టంగా ప్రదర్శించబడిన అవతార్</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">ఓవర్ ఫ్లో అవతార్ కౌంట్</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">బోర్డర్ రకం:</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">ఓవర్‌ఫ్లో అవతార్ కౌంట్ సెట్‌తో ఉన్న అవతార్ గ్రూప్ గరిష్టంగా ప్రదర్శించబడిన అవతార్‌కు కట్టుబడి ఉండదు.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">ఫేస్ స్టాక్</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">ఫేస్ పైల్</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">ఓవర్‌ఫ్లో క్లిక్ చేయబడింది</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">%d ఇండెక్స్ వద్ద అవతార్ వ్యూ క్లిక్ చేయబడింది</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">నోటిఫికేషన్ బ్యాడ్జ్</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">డాట్</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">జాబితా</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">అక్షరం</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">ఫోటోలు</string>
    <string name="bottom_navigation_menu_item_news">వార్తలు</string>
    <string name="bottom_navigation_menu_item_alerts">హెచ్చరికలు</string>
    <string name="bottom_navigation_menu_item_calendar">క్యాలెండర్</string>
    <string name="bottom_navigation_menu_item_team">జట్టు</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">లేబుల్‌లను టోగుల్ చేయండి</string>
    <string name="bottom_navigation_three_menu_items_button">మూడు మెనూ ఐటమ్ లను చూపించు</string>
    <string name="bottom_navigation_four_menu_items_button">నాలుగు మెను అంశాలను చూపు</string>
    <string name="bottom_navigation_five_menu_items_button">ఐదు మెనూ ఐటమ్‌లను చూపించు</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">లేబుల్‌లు %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">విస్మరించడానికి క్రిందికి స్వైప్‌ చేయిని ప్రారంభించండి</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">సింగిల్ లైన్ అంశాలతో చూపించు</string>
    <string name="bottom_sheet_with_double_line_items">డబుల్ లైన్ అంశాలతో చూపించు</string>
    <string name="bottom_sheet_with_single_line_header">సింగిల్ లైన్ హెడర్‌తో చూపించు</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">డబుల్ లైన్ హెడర్ మరియు డివైడర్‌లతో చూపించు</string>
    <string name="bottom_sheet_dialog_button">ప్రదర్శన</string>
    <string name="drawer_content_desc_collapse_state">విస్తరించండి</string>
    <string name="drawer_content_desc_expand_state">కనిష్టీకరించు</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s క్లిక్ చేయండి</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">లాంగ్ క్లిక్ %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">తీసివేయి క్లిక్ చేయండి</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">అంశాన్ని చొప్పించు</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">అంశాన్ని నవీకరించండి</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">విస్మరించు</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">జోడించు</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">ప్రస్తావించండి</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">బోల్డ్</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">ఏటవాలు</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">అండర్‌లైన్</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">కొట్టివేత</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">చర్య రద్దు చేయి</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">చర్య పునరావృత్తం</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">బుల్లెట్</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">జాబితా</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">లింక్</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">అంశం నవీకరించబడుతోంది</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">అంతరం</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">పదవిని తొలగించండి</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">ప్రారంభించు</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">సమూహ స్థలం</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">అంశం స్థలం</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">ఫ్లాగ్ చేయండి</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">ఫ్లాగ్ అంశం క్లిక్ చేయబడింది</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">ప్రత్యుత్తరం పంపు</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">ప్రత్యుత్తర అంశం క్లిక్ చేయబడింది</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">ఫార్వార్డ్ చేయి</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">ఫార్వర్డ్ ఐటమ్ క్లిక్ చేయబడింది</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">తొలగించు</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">క్లిక్ చేసిన అంశాన్ని తొలగించండి</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">అవతార్</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">కెమెరా</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">ఫోటో తీసుకోండి</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">కెమెరా అంశం క్లిక్ చేయబడింది</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">గ్యాలరీ</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">మీ ఫోటోలను వీక్షించండి</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">గ్యాలరీ అంశం క్లిక్ చేయబడింది</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">వీడియోలు</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">మీ వీడియోలను ప్లే చేయండి</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">వీడియోల అంశం క్లిక్ చేయబడింది</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">నిర్వహించండి</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">మీ మీడియా లైబ్రరీని నిర్వహించండి</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">క్లిక్ చేసిన అంశాన్ని నిర్వహించండి</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">ఇమెయిల్ చర్యలు</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">పత్రాలు</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">చివరిగా మధ్యాహ్నం 2:14కి అప్‌డేట్ చేయబడింది</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">భాగస్వామ్యం చేయండి</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">షేర్ ఐటెమ్ క్లిక్ చేయబడింది</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">తరలించు</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">మూవ్ ఐటెమ్ క్లిక్ చేయబడింది</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">తొలగించు</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">క్లిక్ చేసిన అంశాన్ని తొలగించండి</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">సమాచారం</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">సమాచార అంశం క్లిక్ చేయబడింది</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">గడియారం</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">గడియారం అంశం క్లిక్ చేయబడింది</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">అలారం</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">అలారం అంశం క్లిక్ చేయబడింది</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">సమయ మండలి</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">సమయ మండలం అంశం క్లిక్ చేయబడింది</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">బటన్ విభిన్న వీక్షణలు</string>
    <string name="button">బటన్</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">నిలిపివేసిన బటన్ ఉదాహరణ</string>
    <string name="button_borderless">బార్డర్ లేని బటన్ ఉదాహరణ</string>
    <string name="button_borderless_disabled">బార్డర్ లేని నిలిపివేసిన బటన్ ఉదాహరణ</string>
    <string name="button_large">పెద్ద బటన్ ఉదాహరణ</string>
    <string name="button_large_disabled">పెద్ద నిలిపివేసిన బటన్ ఉదాహరణ</string>
    <string name="button_outlined">ఔట్‌లైన్ కలిగిన బటన్ ఉదాహరణ</string>
    <string name="button_outlined_disabled">ఔట్‌లైన్ కలిగిన నిలిపివేసిన బటన్ ఉదాహరణ</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">తేదీని ఎంచుకోండి</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">ఒకే తేదీ</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">తేదీ ఎంచుకోబడలేదు</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">తేదీ ఎంపికను చూపు</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">ఎంచుకున్న తేదీ ట్యాబ్‌తో తేదీ సమయ పికర్‌ని చూపండి</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">ఎంచుకున్న టైమ్ ట్యాబ్‌తో తేదీ సమయ ఎంపికను చూపండి</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">తేదీ సమయం ఎంపికను చూపు</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">తేదీ పరిధి</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">ప్రారంభం:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">ముగింపు:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">ప్రారంభం ఏదీ ఎంచుకోబడలేదు</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">ముగింపు ఎంచుకోబడలేదు</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">ప్రారంభ తేదీని ఎంచుకోండి</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">ముగింపు తేదీని ఎంచుకోండి</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">తేదీ సమయ పరిధి</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">తేదీ సమయ పరిధిని ఎంచుకోండి</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">డైలాగ్ చూపించు</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">డ్రాయర్ చూపించు</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">డ్రాయర్ డైలాగ్‌ని చూపించు</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">ఫేడ్ కానీ బాటమ్ డైలాగ్</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">టాప్ డ్రాయర్‌ని చూపించు</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">ఫేడ్ లేని టాప్ డైలాగ్</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> యాంకర్ వీక్షణ టాప్ డైలాగ్‌ని చూపు</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> టైటిల్ టాప్ డైలాగ్‌ను చూపవద్దు</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> టైటిల్ టాప్ డైలాగ్‌ని క్రింద చూపించు</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">కుడి డ్రాయర్ చూపించు</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">ఎడమ డ్రాయర్ చూపించు</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">శీర్షిక, ప్రాథమిక వచనం</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">ఉపశీర్షిక, ద్వితీయ వచనం</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">అనుకూల ఉపశీర్షిక వచనం</string>
    <!-- Footer -->
    <string name="list_item_footer">ఫుటర్, తృతీయ వచనం</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">బూడిద ఉప శీర్షిక వచనంతో సింగిల్-లైన్ జాబితా</string>
    <string name="list_item_sub_header_two_line">రెండు లైన్ల జాబితా</string>
    <string name="list_item_sub_header_two_line_dense">దట్టమైన అంతరంతో రెండు-లైన్ల జాబితా</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">అనుకూల ద్వితీయ ఉపశీర్షిక వీక్షణతో రెండు లైన్ల జాబితా</string>
    <string name="list_item_sub_header_three_line">నలుపు ఉప శీర్షిక వచనంతో మూడు-లైన్ల జాబితా</string>
    <string name="list_item_sub_header_no_custom_views">అనుకూల వీక్షణలు లేని అంశాలను జాబితా చేయండి</string>
    <string name="list_item_sub_header_large_header">పెద్ద అనుకూల వీక్షణలతో అంశాలను జాబితా చేయండి</string>
    <string name="list_item_sub_header_wrapped_text">చుట్టబడిన వచనంతో అంశాలను జాబితా చేయండి</string>
    <string name="list_item_sub_header_truncated_text">కత్తిరించబడిన వచనంతో అంశాలను జాబితా చేయండి</string>
    <string name="list_item_sub_header_custom_accessory_text">చర్య</string>
    <string name="list_item_truncation_middle">మధ్య కత్తిరింపు.</string>
    <string name="list_item_truncation_end">కత్తిరింపు ముగించండి</string>
    <string name="list_item_truncation_start">కత్తిరించడం ప్రారంభించండి.</string>
    <string name="list_item_custom_text_view">విలువ</string>
    <string name="list_item_click">మీరు జాబితా ఐటెమ్‌పై క్లిక్ చేసారు.</string>
    <string name="list_item_click_custom_accessory_view">మీరు అనుకూల అనుబంధ వీక్షణపై క్లిక్ చేసారు.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">మీరు ఉప శీర్షిక అనుకూల అనుబంధ వీక్షణపై క్లిక్ చేసారు.</string>
    <string name="list_item_more_options">మరిన్ని ఐచ్ఛికాలు</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">ఎంచుకోండి</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">ఏదీకాదు</string>
    <string name="people_picker_delete_example">తొలగించు</string>
    <string name="people_picker_custom_persona_description">ఈ ఉదాహరణ కస్టమ్ IPersona వస్తువును ఎలా సృష్టించాలో చూపుతుంది.</string>
    <string name="people_picker_dialog_title_removed">మీరు ఒక పర్సొనాని తీసివేసారు:</string>
    <string name="people_picker_dialog_title_added">మీరు ఒక వ్యక్తిని జోడించారు:</string>
    <string name="people_picker_drag_started">డ్రాగ్ మొదలైంది</string>
    <string name="people_picker_drag_ended">డ్రాగ్ ముగిసింది</string>
    <string name="people_picker_picked_personas_listener">పర్సొనాస్ లిజనర్</string>
    <string name="people_picker_suggestions_listener">సలహాల లిసనర్</string>
    <string name="people_picker_persona_chip_click">మీరు %s మీద క్లిక్ చేశారు</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s గ్రహీత</item>
        <item quantity="other">%1$s గ్రహీతలు</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">పెర్సిస్టెంట్ బాటమ్‌షీట్‌ని విస్తరించండి</string>
    <string name="collapse_persistent_sheet_button"> పెర్సిస్టెంట్ బాటమ్‌షీట్‌ను దాచండి</string>
    <string name="show_persistent_sheet_button"> నిరంతర బాటమ్‌షీట్‌ని చూపించు</string>
    <string name="new_view">ఇది కొత్త వీక్షణ</string>
    <string name="toggle_sheet_content">దిగువ షీట్ విషయాన్ని టోగుల్ చేయండి</string>
    <string name="switch_to_custom_content">అనుకూల విషయానికి మారండి</string>
    <string name="one_line_content">ఒక లైన్ బాటమ్‌షీట్ కంటెంట్</string>
    <string name="toggle_disable_all_items">అన్ని అంశాలను నిలిపివేయి టోగుల్ చేయండి</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">వీక్షణను జోడించండి/తీసివేయండి</string>
    <string name="persistent_sheet_item_change_collapsed_height"> కుదించిన ఎత్తును మార్చండి</string>
    <string name="persistent_sheet_item_create_new_folder_title">కొత్త ఫోల్డర్</string>
    <string name="persistent_sheet_item_create_new_folder_toast">కొత్త ఫోల్డర్ అంశం క్లిక్ చేయబడింది</string>
    <string name="persistent_sheet_item_edit_title">సవరించు</string>
    <string name="persistent_sheet_item_edit_toast">ఎడిట్ అంశం క్లిక్ చేయబడింది</string>
    <string name="persistent_sheet_item_save_title">సేవ్ చేయి</string>
    <string name="persistent_sheet_item_save_toast">అంశాన్ని సేవ్ చేయి క్లిక్ చేయబడింది</string>
    <string name="persistent_sheet_item_zoom_in_title">జూమ్ ఇన్</string>
    <string name="persistent_sheet_item_zoom_in_toast"> జూమ్ ఇన్ ఐటెమ్ క్లిక్ చేయబడింది</string>
    <string name="persistent_sheet_item_zoom_out_title">జూమ్ అవుట్</string>
    <string name="persistent_sheet_item_zoom_out_toast">జూమ్ అవుట్ ఐటెమ్ క్లిక్ చేయబడింది</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">అందుబాటులో ఉంది</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">ఆలం ముంగర్</string>
    <string name="persona_name_amanda_brady">అమండా బ్రాడీ</string>
    <string name="persona_name_ashley_mccarthy">యాష్లీ మెక్‌కార్తీ</string>
    <string name="persona_name_carlos_slattery">కార్లోస్ స్లాటరీ</string>
    <string name="persona_name_carole_poland">కరోల్ పోలాండ్</string>
    <string name="persona_name_cecil_folk">సిసిల్ ఫోక్</string>
    <string name="persona_name_celeste_burton">సెలెస్టే బర్టన్</string>
    <string name="persona_name_charlotte_waltson">షార్లెట్ వాల్ట్సన్</string>
    <string name="persona_name_colin_ballinger">కోలిన్ బాలింజర్</string>
    <string name="persona_name_daisy_phillips">డైసీ ఫిలిప్స్</string>
    <string name="persona_name_elliot_woodward">ఇలియట్ వుడ్‌వార్డ్</string>
    <string name="persona_name_elvia_atkins">ఎల్వియా అట్కిన్స్</string>
    <string name="persona_name_erik_nason">ఎరిక్ నాసన్</string>
    <string name="persona_name_henry_brill">హెన్రీ బ్రిల్</string>
    <string name="persona_name_isaac_fielder">ఐజాక్ ఫీల్డర్</string>
    <string name="persona_name_johnie_mcconnell">జానీ మెక్‌కాన్నెల్</string>
    <string name="persona_name_kat_larsson">కాట్ లార్సన్</string>
    <string name="persona_name_katri_ahokas">కత్రి అహోకాస్</string>
    <string name="persona_name_kevin_sturgis">కెవిన్ స్టర్గిస్</string>
    <string name="persona_name_kristen_patterson">క్రిస్టెన్ ప్యాటర్సన్</string>
    <string name="persona_name_lydia_bauer">లిడియా బావర్</string>
    <string name="persona_name_mauricio_august">మారిసియో ఆగస్టు</string>
    <string name="persona_name_miguel_garcia">మిగేల్ గార్సియా</string>
    <string name="persona_name_mona_kane">మోనా కేన్</string>
    <string name="persona_name_robin_counts">రాబిన్ కౌంట్స్</string>
    <string name="persona_name_robert_tolbert">రాబర్ట్ టోల్బర్ట్</string>
    <string name="persona_name_tim_deboer">టిమ్ డెబోర్</string>
    <string name="persona_name_wanda_howard">వాండా హోవార్డ్</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">డిజైనర్</string>
    <string name="persona_subtitle_engineer">ఇంజనీర్</string>
    <string name="persona_subtitle_manager">నిర్వాహకుడు</string>
    <string name="persona_subtitle_researcher">పరిశోధకులు</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (కత్తిరించడాన్ని పరీక్షించడానికి పొడవైన వచన ఉదాహరణ)</string>
    <string name="persona_view_description_xxlarge">మూడు లైన్ల వచనంతో ఎక్స్ఎక్స్‌లార్జ్ అవతార్</string>
    <string name="persona_view_description_large">రెండు లైన్ల వచనంతో పెద్ద అవతార్</string>
    <string name="persona_view_description_small">వచనం యొక్క ఒక పంక్తితో చిన్న అవతార్</string>
    <string name="people_picker_hint">సూచనతో ఏదీ చూపబడలేదు</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">పర్సొనా చిప్ డిజేబుల్ అయ్యింది </string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">పర్సనా చిప్‌లో లోపం</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">పర్సొనా చిప్ క్లోజ్ లేని సూక్ష్మచిత్రం</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">ప్రాథమిక వ్యక్తి చిప్</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">మీరు ఎంచుకున్న వ్యక్తి చిప్‌పై క్లిక్ చేసారు.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">భాగస్వామ్యం చేయండి</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">అనుసరించు</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">వ్యక్తులను ఆహ్వానించు</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">పేజీని రిఫ్రెష్ చేయండి</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">బ్రౌజర్‌లో తెరవండి</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">ఇది మల్టీలైన్ పాప్అప్ మెనూ. గరిష్ట పంక్తులు రెండుకి సెట్ చేయబడ్డాయి, మిగిలిన వచనం కత్తిరించబడుతుంది.
        కస్టమర్ కస్టమర్ యొక్క కస్టమర్ సేవను అనుసరించగలగాలి.</string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">అన్ని వార్తలు</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">సేవ్ చేసిన వార్తలు</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">సైట్ల నుండి వార్తలు</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">కాంటోసో</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">పని గంటల వెలుపల తెలియజేయండి</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">డెస్క్‌టాప్‌లో నిష్క్రియంగా ఉన్నప్పుడు తెలియజేయండి</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">మీరు ఈ అంశంపై క్లిక్ చేసారు:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">సాధారణ మెను</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">సాధారణ మెను 2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">ఒక ఎంచుకోదగిన అంశం మరియు డివైడర్‌తో మెనూ</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">అన్ని ఎంచుకోదగిన అంశాలు, చిహ్నాలు మరియు పొడవైన వచనంతో మెను</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">ప్రదర్శన</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">వృత్తాకార పురోగతి</string>
    <string name="circular_progress_xsmall">ఎక్స్‌స్మాల్</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">చిన్నది</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">మధ్యస్థం</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">పెద్దది</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">లీనియర్ ప్రోగ్రెస్</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">మధ్యంతరం</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">నిర్దిష్ట</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">కస్టమర్ చాలా ముఖ్యం, కస్టమర్ కస్టమర్‌ను అనుసరిస్తాడు,
        కానీ అలాంటి సమయంలో అవి గొప్ప శ్రమ మరియు నొప్పితో సంభవిస్తాయి.
        ఇన్నాళ్లు, నేను వస్తాను.</string>

    <!--SearchBar-->
    <string name="searchbar">శోధన బార్</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">మైక్రోఫోన్ కాల్ బ్యాక్</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">స్వీయ దిద్దుబాటు</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">మైక్రోఫోన్ నొక్కబడింది</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">కుడి వీక్షణ నొక్కబడింది</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">కీబోర్డ్ శోధన నొక్కబడింది</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">స్నాక్‌బార్‌ని చూపించు</string>
    <string name="fluentui_dismiss_snackbar">స్నాక్‌బార్‌ని తీసివేయండి</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">చర్య</string>
    <string name="snackbar_action_long">పొడవైన వచనం చర్య</string>
    <string name="snackbar_single_line">సింగిల్ లైన్ స్నాక్‌బార్</string>
    <string name="snackbar_multiline">ఇది మల్టీలైన్ స్నాక్‌బార్. గరిష్ట పంక్తులు రెండుకి సెట్ చేయబడ్డాయి, మిగిలిన వచనం కత్తిరించబడుతుంది.
       Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">ఇది ప్రకటన స్నాక్‌బార్. ఇది కొత్త ఫీచర్లను కమ్యూనికేట్ చేయడానికి ఉపయోగించబడుతుంది.</string>
    <string name="snackbar_primary">ఇది ప్రాథమిక స్నాక్‌బార్.</string>
    <string name="snackbar_light">ఇది తేలికపాటి స్నాక్‌బార్.</string>
    <string name="snackbar_warning">ఇది హెచ్చరిక స్నాక్‌బార్.</string>
    <string name="snackbar_danger">ఇది ప్రమాదకరమైన స్నాక్‌బార్.</string>
    <string name="snackbar_description_single_line">తక్కువ వ్యవధి</string>
    <string name="snackbar_description_single_line_custom_view">చిన్న అనుకూల వీక్షణ వలె వృత్తాకార పురోగతితో దీర్ఘకాలం</string>
    <string name="snackbar_description_single_line_action">చర్యతో తక్కువ వ్యవధి</string>
    <string name="snackbar_description_single_line_action_custom_view">చర్య మరియు మధ్యస్థ అనుకూల వీక్షణతో తక్కువ వ్యవధి</string>
    <string name="snackbar_description_single_line_custom_text_color">అనుకూలీకరించిన వచన రంగుతో తక్కువ వ్యవధి</string>
    <string name="snackbar_description_multiline">సుదీర్ఘ వ్యవధి</string>
    <string name="snackbar_description_multiline_custom_view">చిన్న అనుకూల వీక్షణతో ఎక్కువ కాలం</string>
    <string name="snackbar_description_multiline_action">చర్య మరియు వచన నవీకరణలతో నిరవధిక వ్యవధి</string>
    <string name="snackbar_description_multiline_action_custom_view">చర్య మరియు మధ్యస్థ అనుకూల వీక్షణతో తక్కువ వ్యవధి</string>
    <string name="snackbar_description_multiline_action_long">సుదీర్ఘ చర్య వచనంతో తక్కువ వ్యవధి</string>
    <string name="snackbar_description_announcement">తక్కువ వ్యవధి</string>
    <string name="snackbar_description_updated">ఈ వచనం నవీకరించబడింది.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">స్నాక్‌బార్‌ని చూపించు</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">ఒక పంక్తి</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">బహుళ లైన్</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">ప్రకటన శైలి</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">ప్రాథమిక శైలి</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">తేలికపాటి శైలి</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">హెచ్చరిక శైలి</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">ప్రమాద శైలి</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">హోమ్</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">మెయిల్</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">సెట్టింగ్‌లు</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">నోటిఫికేషన్</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">మరిన్ని</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">వచన సమలేఖనం</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">నిలువు</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">సమతలం</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">వచనం లేదు</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">ట్యాబ్ అంశాలు</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">శీర్షిక</string>
    <string name="cell_sample_description">వివరణ</string>
    <string name="calculate_cells">100 సెల్‌లను లోడ్ చేయండి/లెక్కించండి</string>
    <string name="calculate_layouts">100 లేఅవుట్‌లను లోడ్ చేయండి/లెక్కించండి</string>
    <string name="template_list">టెంప్లేట్ జాబితా</string>
    <string name="regular_list">సాధారణ జాబితా</string>
    <string name="cell_example_title">శీర్షిక: సెల్</string>
    <string name="cell_example_description">వివరణ: విన్యాసాన్ని మార్చడానికి నొక్కండి</string>
    <string name="vertical_layout">క్షితిజ లంబ లేఅవుట్</string>
    <string name="horizontal_layout">క్షితిజ సమాంతర లేఅవుట్</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">ప్రామాణిక ట్యాబ్ 2-విభాగం</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">ప్రామాణిక ట్యాబ్ 3-విభాగం</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">ప్రామాణిక ట్యాబ్ 4-విభాగం</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">పేజర్‌తో ప్రామాణిక ట్యాబ్</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">టాబ్ మారండి</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">మాత్రలు ట్యాబ్ </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">సత్వర చిట్కా కోసం నొక్కండి</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">అనుకూల క్యాలెండర్ సత్వర చిట్కా కోసం నొక్కండి</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">అనుకూల రంగు సత్వర చిట్కా నొక్కండి</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">సత్వర చిట్కా లోపల తీసివేయడం కోసం నొక్కండి</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">అనుకూల వీక్షణ సత్వర చిట్కా కోసం నొక్కండి</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">అగ్ర అనుకూల రంగు సత్వర చిట్కా</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp ఆఫ్‌సెట్‌ఎక్స్‌తో పై తుది సత్వర చిట్కా</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">దిగువన ప్రారంభించు సత్వర చిట్కా</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp ఆఫ్‌సెట్‌తో దిగువ తుది సత్వర చిట్కా</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">సత్వర చిట్కా లోపల తీసివేయండి</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">సత్వర చిట్కా విస్మరించబడింది</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">హెడ్‌లైన్ లైట్ 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">శీర్షిక 1 మీడియం 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">టైటిల్ 2 రెగ్యులర్ 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">శీర్షిక రెగ్యులర్ 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">ఉపశీర్షిక 1 సాధారణ 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">సబ్ హెడ్డింగ్ 2 అంటే మీడియం 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">బాడీ 1 రెగ్యులర్ 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">బాడీ 2 మీడియం 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">ఉపశీర్షిక రెగ్యులర్ 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK సంస్కరణ: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">అంశం %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">ఫోల్డర్</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">క్లిక్ చేయబడింది</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">పరంజా</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB విస్తరించబడింది</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB కుదించబడింది</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">రిఫ్రెష్ చేయడానికి క్లిక్ చేయండి</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">డ్రాయర్‌ని తెరువు</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">మెను అంశం</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">ఆఫ్‌సెట్ X (dpలో)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">అఫ్‌సెట్ Y (dpలో)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">కంటెంట్ టెక్స్ట్</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">కంటెంట్ టెక్స్ట్‌ని రిపీట్ చేయండి</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">వచనం టెక్స్ట్‌కి సంబంధించి మెను వెడల్పు మారుతుంది.
        గరిష్ట వెడల్పు స్క్రీన్ పరిమాణంలో 75%కి పరిమితం చేయబడింది.పక్కలలో మరియు దిగువ నుండి కంటెంట్ మార్జిన్ అనేది టోకెన్ ద్వారా నిర్వహించబడుతుంది. అదే కంటెంట్ టెక్స్ట్ అనేది ఎత్తు మారడానికి 
        పునరావృతమవుతుంది.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">మెను తెరువు</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">బేసిక్ కార్డ్</string>
    <!-- UI Label for Card -->
    <string name="file_card">ఫైల్ కార్డ్</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">ప్రకటన కార్డు</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">యాదృచ్ఛిక UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">ఐచ్ఛికాలు</string>
    <!-- UI Label for Title -->
    <string name="card_title">శీర్షిక</string>
    <!-- UI Label for text -->
    <string name="card_text">టెక్స్ట్</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">సబ్ టెక్స్ట్</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">అవసరమైతే ఈ బ్యానర్‌కి సంబంధించిన సెకండరీ కాపీని రెండు లైన్లకు చుట్టవచ్చు.</string>
    <!-- UI Label Button -->
    <string name="card_button">బటన్</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">డైలాగ్ చూపించు</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">బయట క్లిక్ చేసినప్పుడు డైలాగ్‌ను విస్మరించు</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">వెనుకకు నొక్కినప్పుడు డైలాగ్‌ను విస్మరించు</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">డైలాగ్ విస్మరించబడింది</string>
    <!-- UI Label Cancel -->
    <string name="cancel">రద్దు చేయి</string>
    <!-- UI Label Ok -->
    <string name="ok">సరే</string>
    <!-- A sample description -->
    <string name="dialog_description">డైలాగ్ అనేది వినియోగదారుని నిర్ణయం తీసుకోవడానికి లేదా అదనపు సమాచారాన్ని నమోదు చేయడానికి ప్రాంప్ట్ చేసే చిన్న విండో.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">డ్రాయర్‌ని తెరువు</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">డ్రాయర్‌ని విస్తరించు</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">డ్రాయర్‌ని మూసివేయి</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">డ్రాయర్ రకం ఎంచుకోండి</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">ఎగువ</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">విజబుల్ రీజియన్‌లో మొత్తం డ్రాయర్ కనిపిస్తోంది.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">దిగువ</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">విజిబుల్ ప్రాంతంలో మొత్తం డ్రాయర్ కనిపిస్తుంది. మోషన్ స్క్రోల్ కంటెంట్‌ను స్వైప్ చేయండి. విస్తరించదగిన డ్రాయర్ అనేది డ్రాగ్ హ్యాండిల్ ద్వారా విస్తరించబడుతుంది.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">ఎడమ స్లైడ్ ఓవర్</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">ఎడమ వైపు నుండి కనిపించే ప్రాంతానికి డ్రాయర్ స్లైడ్ అవుతుంది.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">కుడి స్లైడ్ ఓవర్</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">కుడి వైపు నుండి కనిపించే ప్రాంతానికి డ్రాయర్ స్లైడ్ అవుతుంది.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">దిగువ స్లైడ్ ఓవర్</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">స్క్రీన్ దిగువ నుండి కనిపించే ప్రాంతానికి డ్రాయర్ స్లైడ్ అవుతుంది. విస్తరించదగిన డ్రాయర్ పై కదలికను స్వైప్ చేయండి, దాని మిగిలిన భాగాన్ని కనిపించే ప్రాంతానికి తీసుకురండి మరియు తరువాత స్క్రోల్ చేయండి.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">స్క్రిమ్ కనిపిస్తోంది</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">డ్రాయర్ కంటెంట్ ఎంచుకోండి</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">పూర్తి స్క్రీన్ పరిమాణం స్క్రోలబుల్ కంటెంట్</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">సగం కంటే ఎక్కువ స్క్రీన్ కంటెంట్</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">సగం కంటే తక్కువ స్క్రీన్ కంటెంట్</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">డైనమిక్ సైజ్ కంటెంట్</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">నెస్టెడ్ డ్రాయర్ కంటెంట్</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">విస్తరించదగినది</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">తెరచిన స్థితిని స్కిప్ చేయండి</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">స్క్రిం క్లిక్ పై తొలగింపును నిరోధించండి</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">హ్యాండిల్‌ని చూపించు</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">శీర్షిక</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">సత్వర చిట్కా టెక్స్ట్</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">కస్టమ్ కంటెంట్ సత్వర చిట్కా కోసం ట్యాప్ చేయండి</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">టాప్ ప్రారంభం </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">పై కొన </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">దిగువ ప్రారంభం </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">దిగువ కొన </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">మధ్య </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">అనుకూలం కేంద్రం</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">రిలీజ్ గమనికలపై నవీకరణల కొరకు, </string>
    <string name="click_here">ఇక్కడ క్లిక్ చేయండి.</string>
    <string name="open_source_cross_platform">ఓపెన్ సోర్స్ క్రాస్ ప్లాట్ ఫామ్ డిజైన్ సిస్టమ్.</string>
    <string name="intuitive_and_powerful">అర్థవంతమైన &amp; శక్తివంతమైన.</string>
    <string name="design_tokens">డిజైన్ టోకెన్‌లు</string>
    <string name="release_notes">విడుదల గమనికలు</string>
    <string name="github_repo">GitHub రెపో</string>
    <string name="github_repo_link">GitHub రెపో లింక్</string>
    <string name="report_issue">సమస్యను నివేదించు</string>
    <string name="v1_components">V1 విడిభాగాలు</string>
    <string name="v2_components">V2 విడిభాగాలు</string>
    <string name="all_components">మొత్తం</string>
    <string name="fluent_logo">Fluent లోగో</string>
    <string name="new_badge">కొత్త</string>
    <string name="modified_badge">సవరించబడింది</string>
    <string name="api_break_badge">API బ్రేక్</string>
    <string name="app_bar_more">మరిన్ని</string>
    <string name="accent">విలక్షణత</string>
    <string name="appearance">కనిపించే తీరు</string>
    <string name="choose_brand_theme">మీ బ్రాండ్ థీమ్‌ని ఎంచుకోండి:</string>
    <string name="fluent_brand_theme">Fluent బ్రాండ్</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">కనిపించే తీరును ఎంచుకోండి</string>
    <string name="appearance_system_default">సిస్టమ్ డిఫాల్ట్</string>
    <string name="appearance_light">లైట్</string>
    <string name="appearance_dark">ముదురు</string>
    <string name="demo_activity_github_link">డెమో కార్యకలాపం GitHub లింక్</string>
    <string name="control_tokens_details">నియంత్రణ టోకెన్‌ల వివరాలు</string>
    <string name="parameters">పారామీటర్‌లు</string>
    <string name="control_tokens">నియంత్రణ టోకెన్‌లు</string>
    <string name="global_tokens">గ్లోబల్ టోకెన్‌లు</string>
    <string name="alias_tokens">అలియాస్ టోకెన్‌లు</string>
    <string name="sample_text">టెక్స్ట్</string>
    <string name="sample_icon">నమూనా సూక్ష్మచిత్రం</string>
    <string name="color">రంగు</string>
    <string name="neutral_color_tokens">న్యూట్రల్ వర్ణం టోకెన్‌లు</string>
    <string name="font_size_tokens">ఫాంట్ పరిమాణం టోకెన్‌లు</string>
    <string name="line_height_tokens">లైన్ ఎత్తు టోకెన్‌లు</string>
    <string name="font_weight_tokens">ఫాంట్ బరువు టోకెన్‌లు</string>
    <string name="icon_size_tokens">సూక్ష్మచిత్రం పరిమాణం టోకెన్‌లు</string>
    <string name="size_tokens">పరిమాణం టోకెన్‌లు</string>
    <string name="shadow_tokens">షాడో టోకెన్‌లు</string>
    <string name="corner_radius_tokens">కార్నర్ రేడియస్ టోకెన్‌లు</string>
    <string name="stroke_width_tokens">స్ట్రోక్ వెడల్పు టోకెన్‌లు</string>
    <string name="brand_color_tokens">బ్రాండ్ రంగు టోకెన్‌లు</string>
    <string name="neutral_background_color_tokens">న్యూట్రల్ నేపథ్య వర్ణం టోకెన్‌లు</string>
    <string name="neutral_foreground_color_tokens">న్యూట్రల్ ఫోర్ గ్రౌండ్ కలర్ టోకెన్‌లు</string>
    <string name="neutral_stroke_color_tokens">న్యూట్రల్ స్ట్రోక్ వర్ణం టోకెన్‌లు</string>
    <string name="brand_background_color_tokens">బ్రాండ్ నేపథ్య వర్ణం టోకెన్‌లు</string>
    <string name="brand_foreground_color_tokens">బ్రాండ్ ఫోర్ గ్రౌండ్ వర్ణం టోకెన్‌లు</string>
    <string name="brand_stroke_color_tokens">బ్రాండ్ స్ట్రోక్ వర్ణం టోకెన్‌లు</string>
    <string name="error_and_status_color_tokens">దోషం మరియు స్థితి రంగు టోకెన్‌లు</string>
    <string name="presence_tokens">ఉనికి రంగు టోకెన్‌లు</string>
    <string name="typography_tokens">టైపోగ్రఫీ టోకెన్‌లు</string>
    <string name="unspecified">పేర్కొనని</string>

</resources>