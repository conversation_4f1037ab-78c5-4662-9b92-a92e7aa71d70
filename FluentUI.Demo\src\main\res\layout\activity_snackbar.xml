<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:divider="@drawable/demo_divider"
    android:orientation="vertical"
    android:padding="@dimen/default_layout_margin"
    android:showDividers="middle"
    tools:context=".demos.SnackbarActivity">

    <!--Single line-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/snackbar_headline_single_line" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_single_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line_action" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_single_line_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line_action" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line_custom_view" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_single_line_custom_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line_custom_view" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line_action_custom_view" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_single_line_action_custom_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line_action_custom_view" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line_custom_text_color" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_single_line_custom_text_color"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line_custom_text_color" />

    <!--Multiline-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/snackbar_headline_multiline" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_multiline" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_multiline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_multiline" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_multiline_action" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_multiline_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_multiline_action" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_multiline_custom_view" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_multiline_custom_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_multiline_custom_view" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_multiline_action_custom_view" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_multiline_action_custom_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_multiline_action_custom_view" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_multiline_action_long" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_multiline_long_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_multiline_action_long" />

    <!--Announcement style-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/snackbar_headline_announcement_style" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_announcement" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_announcement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_announcement" />

    <!--Primary style-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/snackbar_headline_primary_style" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_primary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line" />

    <!--Light style-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/snackbar_headline_light_style" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_light"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line" />

    <!--Warning style-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/snackbar_headline_warning_style" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_warning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line" />

    <!--Danger style-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/snackbar_headline_danger_style" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TextView
        style="@style/TextAppearance.DemoDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_description_single_line" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/btn_snackbar_danger"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snackbar_button_show"
        android:hint="@string/snackbar_description_single_line" />

</LinearLayout>