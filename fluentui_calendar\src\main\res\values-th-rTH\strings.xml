<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">จ.</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">อ.</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">พ.</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">พฤ.</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">ศ.</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">ส.</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">อา.</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">ไปยังสัปดาห์ถัดไป</string>
    <string name="accessibility_goto_previous_week">ไปยังสัปดาห์ก่อนหน้า</string>
    <string name="accessibility_today">วันนี้</string>
    <string name="accessibility_selected">เลือกแล้ว</string>

    <!-- *** Shared *** -->
    <string name="done">เสร็จสิ้น</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">วันนี้</string>
    <string name="tomorrow">พรุ่งนี้</string>
    <string name="yesterday">เมื่อวาน</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">เวลาเริ่มต้น</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">เวลาสิ้นสุด</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">วันที่เริ่มต้น</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">วันที่สิ้นสุด</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">เลือกเวลา</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">เลือกวันที่</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">ตัวเลือกวันที่และเวลา</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">ตัวใช้เลือกวันที่</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">ช่วงตัวเลือกวันที่และเวลา</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">ช่วงตัวเลือกวันที่</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">แท็บเวลาเริ่มต้น</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">แท็บเวลาสิ้นสุด</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">แท็บวันที่เริ่มต้น</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">แท็บวันที่สิ้นสุด</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">ปิดกล่องโต้ตอบ</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">เพิ่มเดือน</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">เลือกเดือนถัดไป</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">ลดเดือน</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">เลือกเดือนก่อนหน้า</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">เพิ่มวัน</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">เลือกวันถัดไป</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">ลดวัน</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">เลือกวันก่อนหน้า</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">เพิ่มปี</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">เลือกปีหน้า</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">ลดปี</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">เลือกปีก่อน</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">เพิ่มวันที่</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">เลือกวันที่ถัดไป</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">ลดวันที่</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">เลือกวันที่ก่อนหน้า</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">เพิ่มชั่วโมง</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">เลือกชั่วโมงถัดไป</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">ลดชั่วโมง</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">เลือกชั่วโมงก่อนหน้า</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">เพิ่มนาที</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">เลือกนาทีต่อไป</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">ลดนาที</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">เลือกนาทีก่อนหน้า</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">สลับช่วงเวลา AM PM</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">สลับช่วงเวลา AM PM</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">เลือก %s แล้ว</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">เลือกแล้ว</string>
</resources>
