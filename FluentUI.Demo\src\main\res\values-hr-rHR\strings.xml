<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Pokazna verzija aplikacije Fluent UI</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Odabrana je stavka %s</string>
    <string name="app_modifiable_parameters">Parametri koji se mogu mijenjati</string>
    <string name="app_right_accessory_view">Desni prikaz dodataka</string>

    <string name="app_style">Stil</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikona je pritisnuta</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Pokreni pokaznu verziju</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Vrtuljak</string>
    <string name="actionbar_icon_radio_label">Ikona</string>
    <string name="actionbar_basic_radio_label">Osnovno</string>
    <string name="actionbar_position_bottom_radio_label">Donji</string>
    <string name="actionbar_position_top_radio_label">Gornji</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Vrsta akcijske trake</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Položaj akcijske trake</string>

    <!--AppBar-->
    <string name="app_bar_style">Stil trake aplikacije</string>
    <string name="app_bar_subtitle">Podnaslov</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Donji obrub</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Kliknuta je ikona navigacije.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Zastavica</string>
    <string name="app_bar_layout_menu_settings">Postavke</string>
    <string name="app_bar_layout_menu_search">Pretraživanje</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Ponašanje pomicanja: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Uključivanje/isključivanje ponašanja pomicanja</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Uključivanje/isključivanje navigacije</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Prikaži avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Prikaži ikonu natrag</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Sakrij ikonu</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Prikaži ikonu</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Uključivanje/isključivanje stila rasporeda trake za pretraživanje</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Prikaži kao prikaz dodataka</string>
    <string name="app_bar_layout_searchbar_action_view_button">Prikaži kao prikaz radnji</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Prebacivanje između tema (ponovo stvara aktivnost)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Uključivanje/isključivanje teme</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Stavka</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Sadržaj koji se može dodatno pomicati</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Kružni stil</string>
    <string name="avatar_style_square">Kvadratni stil</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Veliki</string>
    <string name="avatar_size_medium">Srednji</string>
    <string name="avatar_size_small">Mali</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dvostruko vrlo veliko</string>
    <string name="avatar_size_xlarge_accessibility">Vrlo veliko</string>
    <string name="avatar_size_xsmall_accessibility">Vrlo malo</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maksimalni prikazani avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Broj avatara preljeva</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Vrsta obruba</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Grupa avatara sa skupom OverflowAvatarCount neće se pridržavati maksimalnog prikazanog avatara.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Grupa lica</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Hrpa lica</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Kliknuto na preljev</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Kliknut je prikaz avatara na indeksu %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Obavijesna značka</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Točka</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Popis</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Znak</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotografije</string>
    <string name="bottom_navigation_menu_item_news">Vijesti</string>
    <string name="bottom_navigation_menu_item_alerts">Upozorenja</string>
    <string name="bottom_navigation_menu_item_calendar">Kalendar</string>
    <string name="bottom_navigation_menu_item_team">Tim</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Uključivanje/isključivanje oznaka</string>
    <string name="bottom_navigation_three_menu_items_button">Prikaži tri stavke izbornika</string>
    <string name="bottom_navigation_four_menu_items_button">Prikaži četiri stavke izbornika</string>
    <string name="bottom_navigation_five_menu_items_button">Prikaži pet stavki izbornika</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Oznake su %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Donji list</string>
    <string name="bottom_sheet_dialog">Dijaloški okvir donjeg lista</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Omogući prelazak prstom prema dolje za odbacivanje</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Prikaži sa stavkama u jednom retku</string>
    <string name="bottom_sheet_with_double_line_items">Prikaži sa stavkama u dva retka</string>
    <string name="bottom_sheet_with_single_line_header">Prikaži sa zaglavljem u jednom retku</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Prikaži sa zaglavljem u dva retka i razdjelnicima</string>
    <string name="bottom_sheet_dialog_button">Prikaži</string>
    <string name="drawer_content_desc_collapse_state">Proširi</string>
    <string name="drawer_content_desc_expand_state">Minimiziraj</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Kliknite %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Dugo kliknite stavku %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Kliknite Odbaci</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Umetni stavku</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Ažuriraj stavku</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Odbaci</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Dodaj</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Spominjanje</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Podebljano</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kurziv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Podcrtaj</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Precrtaj</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Poništi</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Ponovi poništeno</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Grafička oznaka</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Popis</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Veza</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Ažuriranje stavke</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Prored</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Odbaci položaj</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">POKRENI</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">KRAJ</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Prostor grupe</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Prostor stavke</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Zastavica</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Kliknuta je stavka zastavica</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Odgovor</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Kliknuta je stavka odgovori</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Prosljeđivanje</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Kliknuto je prosljeđivanje stavke</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Brisanje</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Kliknuto je brisanje stavke</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Snimanje fotografije</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kliknuta je stavka kamera</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerija</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Prikaz fotografija</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Kliknuta je stavka galerija</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videozapisi</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Reproduciranje videozapisa</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Kliknuta je stavka videozapisi</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Upravljanje</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Upravljanje bibliotekom medijskih sadržaja</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Kliknuto je upravljanje stavkom</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Radnje za e-poštu</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumenti</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Zadnji put ažurirano u 14.14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Zajedničko korištenje</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Kliknuto je zajedničko korištenje stavke</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Premještanje</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Kliknuto je premještanje stavke</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Brisanje</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Kliknuto je brisanje stavke</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informacije</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Kliknuta je stavka informacije</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Sat</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Kliknuta je stavka sat</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Kliknuta je stavka alarma</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Vremenska zona</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Kliknuta je stavka vremenska zona</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Različiti prikazi gumba</string>
    <string name="button">Gumb</string>
    <string name="buttonbar">Traka gumba</string>
    <string name="button_disabled">Primjer gumba Onemogućeno</string>
    <string name="button_borderless">Primjer gumba bez obruba</string>
    <string name="button_borderless_disabled">Primjer gumba bez obruba Onemogućeno</string>
    <string name="button_large">Primjer velikog gumba</string>
    <string name="button_large_disabled">Primjer velikog gumba Onemogućeno</string>
    <string name="button_outlined">Primjer gumba s konturom</string>
    <string name="button_outlined_disabled">Primjer gumba s obrubom Onemogućeno</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Odaberite datum</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">Alat za odabir datuma i vremena</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Jedan datum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nije odabran nijedan datum</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Prikaži alat za odabir datuma</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Prikaži alat za odabir datuma i vremena s odabranom karticom datuma</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Prikaži alat za odabir datuma i vremena s odabranom karticom vremena</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Prikaži alat za odabir datuma i vremena</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Raspon datuma</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Početak:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Kraj:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nije odabran početak</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Nije odabran kraj</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Odaberite datum početka</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Odaberite datum završetka</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Raspon datuma i vremena</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Odaberite raspon datuma i vremena</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">Dijaloški okvir alata za odabir datuma i vremena</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Prikaži dijaloški okvir</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Prikaži ladicu</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Prikaži dijaloški okvir ladice</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Donji dijaloški okvir bez iščezavanja</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Prikaži gornju ladicu</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Gornji dijaloški okvir bez iščezavanja</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Prikaži gornji dijaloški okvir prikaza sidrišta</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Prikaži gornji dijaloški okvir bez naslova</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Prikaži gornji dijaloški okvir ispod naslova</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Prikaži desnu ladicu</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Prikaži lijevu ladicu</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Naslov, primarni tekst</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Podnaslov, sekundarni tekst</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Tekst prilagođenog podnaslova</string>
    <!-- Footer -->
    <string name="list_item_footer">Podnožje, tercijarni tekst</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Popis u jednom retku sa sivim tekstom podnaslova</string>
    <string name="list_item_sub_header_two_line">Popis u dva retka</string>
    <string name="list_item_sub_header_two_line_dense">Popis u dva retka s gustim proredom</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Popis u dva retka s prilagođenim sekundarnim prikazom podnaslova</string>
    <string name="list_item_sub_header_three_line">Popis u dva retka s crnim tekstom podnaslova</string>
    <string name="list_item_sub_header_no_custom_views">Popis stavki bez prilagođenih prikaza</string>
    <string name="list_item_sub_header_large_header">Stavke popisa s velikim prilagođenim prikazima</string>
    <string name="list_item_sub_header_wrapped_text">Stavke popisa s prelomljenim tekstom</string>
    <string name="list_item_sub_header_truncated_text">Stavke popisa sa skraćenim tekstom</string>
    <string name="list_item_sub_header_custom_accessory_text">Radnja</string>
    <string name="list_item_truncation_middle">Srednje kraćenje.</string>
    <string name="list_item_truncation_end">Završite kraćenje.</string>
    <string name="list_item_truncation_start">Započnite kraćenje.</string>
    <string name="list_item_custom_text_view">Vrijednost</string>
    <string name="list_item_click">Kliknuli ste stavku popisa.</string>
    <string name="list_item_click_custom_accessory_view">Kliknuli ste prilagođeni prikaz dodataka.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Kliknuli ste prilagođeni prikaz dodataka podnaslova.</string>
    <string name="list_item_more_options">Dodatne mogućnosti</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Odaberi</string>
    <string name="people_picker_select_deselect_example">Odaberi/Poništi odabir</string>
    <string name="people_picker_none_example">Ništa</string>
    <string name="people_picker_delete_example">Izbriši</string>
    <string name="people_picker_custom_persona_description">Ovaj primjer pokazuje kako stvoriti prilagođeni objekt IPersona.</string>
    <string name="people_picker_dialog_title_removed">Uklonili ste osobu:</string>
    <string name="people_picker_dialog_title_added">Dodali ste osobu:</string>
    <string name="people_picker_drag_started">Povlačenje je pokrenuto</string>
    <string name="people_picker_drag_ended">Povlačenje je završeno</string>
    <string name="people_picker_picked_personas_listener">Osluškivač osoba</string>
    <string name="people_picker_suggestions_listener">Osluškivač prijedloga</string>
    <string name="people_picker_persona_chip_click">Kliknuli ste %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s primatelj</item>
        <item quantity="few">%1$s primatelja</item>
        <item quantity="other">%1$s primatelja</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Proširi trajni donji list</string>
    <string name="collapse_persistent_sheet_button"> Sakrij trajni donji list</string>
    <string name="show_persistent_sheet_button"> Prikaži trajni donji list</string>
    <string name="new_view">Ovo je novi prikaz</string>
    <string name="toggle_sheet_content">Uključivanje/isključivanje sadržaja donjeg lista</string>
    <string name="switch_to_custom_content">Prijeđi na prilagođeni sadržaj</string>
    <string name="one_line_content">Sadržaj donjeg lista u jednom retku</string>
    <string name="toggle_disable_all_items">Uključivanje/isključivanje onemogućivanja svih stavki</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Dodaj/ukloni prikaz</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Promijeni sažetu visinu</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nova mapa</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Kliknuta je stavka nova mapa</string>
    <string name="persistent_sheet_item_edit_title">Uređivanje</string>
    <string name="persistent_sheet_item_edit_toast">Kliknuto je uređivanje stavke</string>
    <string name="persistent_sheet_item_save_title">Spremanje</string>
    <string name="persistent_sheet_item_save_toast">Kliknuto je spremanje stavke</string>
    <string name="persistent_sheet_item_zoom_in_title">Povećavanje</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Kliknuta je stavka povećaj</string>
    <string name="persistent_sheet_item_zoom_out_title">Smanjivanje</string>
    <string name="persistent_sheet_item_zoom_out_toast">Kliknuta je stavka smanji</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Dostupno</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Dizajner</string>
    <string name="persona_subtitle_engineer">Inženjer</string>
    <string name="persona_subtitle_manager">Voditelj</string>
    <string name="persona_subtitle_researcher">Istraživač</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (primjer dugog teksta za testiranje kraćenja)</string>
    <string name="persona_view_description_xxlarge">Avatar XXLarge s tri retka teksta</string>
    <string name="persona_view_description_large">Veliki avatar s dva retka teksta</string>
    <string name="persona_view_description_small">Mali avatar s jednim retkom teksta</string>
    <string name="people_picker_hint">Ništa s prikazanim savjetom</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Onemogućeni žeton osobe</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Pogreška žetona osobe</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Žeton osobe bez ikone za zatvaranje</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Osnovni žeton osobe</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Kliknuli ste žeton odabrane osobe.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Zajedničko korištenje</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Prati</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Pozovi osobe</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Osvježi stranicu</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Otvori u pregledniku</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Ovo je skočni izbornik u više redaka. Maksimalni broj redaka postavljen je na dva, a ostatak teksta bit će skraćen.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Sve vijesti</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Spremljene vijesti</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Vijesti s web-mjesta</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Obavijesti izvan radnog vremena</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Obavijesti kada je neaktivna na radnoj površini</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Kliknuli ste na stavku:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Jednostavni izbornik</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Jednostavni izbornik 2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Izbornik s jednom stavkom koju je moguće odabrati i razdjelnikom</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Izbornik sa svim stavkama koje se mogu odabrati, ikonama i dugim tekstom</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Prikaži</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Kružni tijek</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20 dp</string>
    <string name="circular_progress_small">Mali</string>
    <string name="circular_progress_small_size">24 dp</string>
    <string name="circular_progress_medium">Srednji</string>
    <string name="circular_progress_medium_size">36 dp</string>
    <string name="circular_progress_large">Veliki</string>
    <string name="circular_progress_large_size">44 dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linearni tijek</string>
    <string name="linear_progress_size">4 dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Neodređeni</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Određeni</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Traka za pretraživanje</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Povratni poziv mikrofona</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Samoispravak</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon je pritisnut</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Pritisnut je desni prikaz</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Pretraživanje tipkovnice je pritisnuto</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Prikaži kratku poruku</string>
    <string name="fluentui_dismiss_snackbar">Odbaci kratku poruku</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Radnja</string>
    <string name="snackbar_action_long">Radnja s dugim tekstom</string>
    <string name="snackbar_single_line">Kratka poruka u jednom retku</string>
    <string name="snackbar_multiline">Ovo je kratka poruka u više redaka. Maksimalni broj redaka postavljen je na dva, a ostatak teksta bit će skraćen.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Ovo je traka za najavu. Koristi se za komunikaciju novih značajki.</string>
    <string name="snackbar_primary">Ovo je primarna kratka poruka.</string>
    <string name="snackbar_light">Ovo je kratka poruka sa svijetlim stilom.</string>
    <string name="snackbar_warning">Ovo je kratka poruka upozorenja.</string>
    <string name="snackbar_danger">Ovo je kratka poruka opasnosti.</string>
    <string name="snackbar_description_single_line">Kratko trajanje</string>
    <string name="snackbar_description_single_line_custom_view">Dugo trajanje s kružnim tijekom kao malim prilagođenim prikazom</string>
    <string name="snackbar_description_single_line_action">Kratko trajanje s radnjom</string>
    <string name="snackbar_description_single_line_action_custom_view">Kratko trajanje s radnjom i srednjim prilagođenim prikazom</string>
    <string name="snackbar_description_single_line_custom_text_color">Kratko trajanje s prilagođenom bojom teksta</string>
    <string name="snackbar_description_multiline">Dugo trajanje</string>
    <string name="snackbar_description_multiline_custom_view">Dugo trajanje s malim prilagođenim prikazom</string>
    <string name="snackbar_description_multiline_action">Neograničeno trajanje s ažuriranjima radnje i teksta</string>
    <string name="snackbar_description_multiline_action_custom_view">Kratko trajanje s radnjom i srednjim prilagođenim prikazom</string>
    <string name="snackbar_description_multiline_action_long">Kratko trajanje s dugim tekstom radnje</string>
    <string name="snackbar_description_announcement">Kratko trajanje</string>
    <string name="snackbar_description_updated">Tekst je ažuriran.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Prikaži kratku poruku</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Jedan redak</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Više redaka</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Stil objave</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primarni stil</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Svijetli stil</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Stil upozorenja</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Stil opasnosti</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Početna stranica</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Pošta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Postavke</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Obavijest</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Više</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Poravnanje teksta</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Okomito</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vodoravno</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Nema teksta</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Stavke kartice</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Naslov</string>
    <string name="cell_sample_description">Opis</string>
    <string name="calculate_cells">Učitaj/izračunaj 100 ćelija</string>
    <string name="calculate_layouts">Učitaj/izračunaj 100 rasporeda</string>
    <string name="template_list">Popis predložaka</string>
    <string name="regular_list">Običan popis</string>
    <string name="cell_example_title">Naslov: ćelija</string>
    <string name="cell_example_description">Opis: dodirnite da biste promijenili usmjerenje</string>
    <string name="vertical_layout">Okomiti raspored</string>
    <string name="horizontal_layout">Vodoravni raspored</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standardna kartica s dva segmenta</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardna kartica s tri segmenta</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardna kartica s četiri segmenta</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardna kartica sa sučeljem za pregled stranica</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Prebaci na drugu karticu</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Kartica s tabletama </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Dodirnite za opis elementa</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Dodirnite za opis elementa prilagođenog kalendara</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Dodirnite opis elementa prilagođene boje</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Dodirnite da biste odbacili unutar opisa elementa</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Dodirnite za opis elementa prilagođenog prikaza</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Opis elementa prilagođene boje gornje strane elementa</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Opis elementa s gornje strane kraja elementa s pomakom od 10 dp po osi X</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Opis elementa s donje strane početka elementa</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Opis elementa s donje strane kraja elementa s pomakom od 10 dp po osi Y</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Odbaci unutar opisa elementa</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Opis elementa je odbačen</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Naslov je svijetli 28 sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Naslov 1 je srednji 20 sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Naslov 2 je običan 20 sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Naslov je običan 18 sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Podnaslov 1 je obični 16 sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Podnaslov 2 je srednji 16 sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Tijelo 1 je obično 14 sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Tijelo 2 je srednje 14 sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Opis je običan 12 sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Verzija SDK-a: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Stavka %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mapa</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Kliknuto</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Skele</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB je proširen</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB je sažet</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Kliknite da biste osvježili popis</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Otvori ladicu</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Stavka izbornika</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Pomak X (u dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Pomak Y (u dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Tekst sadržaja</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Ponovi tekst sadržaja</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Širina izbornika promijenit će se u odnosu na tekst sadržaja. Maksimalna
        širina ograničena je na 75 % veličine zaslona. Margina sadržaja s bočne i donje strane regulirana je tokenom. Isti tekst sadržaja ponavljat će se kako bi se mijenjala
        visina.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Otvori izbornik</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Osnovna kartica</string>
    <!-- UI Label for Card -->
    <string name="file_card">Kartica datoteke</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Kartica objave</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Nasumično korisničko sučelje</string>
    <!-- UI Label for Options -->
    <string name="card_options">Mogućnosti</string>
    <!-- UI Label for Title -->
    <string name="card_title">Naslov</string>
    <!-- UI Label for text -->
    <string name="card_text">Tekst</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Podtekst</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekundarna kopija ovog natpisa po potrebi se može prelomiti u dva retka.</string>
    <!-- UI Label Button -->
    <string name="card_button">Gumb</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Prikaži dijaloški okvir</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Odbacivanje dijaloškog okvira klikom izvan</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Odbacivanje dijaloškog okvira pritiskom gumba natrag</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dijaloški je okvir odbačen</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Odustani</string>
    <!-- UI Label Ok -->
    <string name="ok">U redu</string>
    <!-- A sample description -->
    <string name="dialog_description">Dijaloški okvir mali je prozor koji od korisnika traži donošenje odluke ili unos dodatnih informacija.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Otvori ladicu</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Proširi ladicu</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Zatvori ladicu</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Odabir vrste ladice</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Vrh</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Cijela ladica prikazuje se u vidljivom području.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Dno</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Cijela ladica prikazuje se u vidljivom području. Prijeđite prstom prema gore za pomicanje sadržaja. Proširiva ladica proširuje se povlačenjem.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Lijevi slajd iznad</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Ladica klizi do vidljivog područja s lijeve strane.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Desni slajd iznad</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Ladica klizi do vidljivog područja s desne strane.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Donji slajd iznad</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Ladica klizi do vidljivog područja od dna zaslona. Prijeđite prstom prema gore po proširivim ladicama i premjestite ostatak u vidljivo područje i zatim se pomičite.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Vidljiv sloj</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Odabir sadržaja ladice</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Sadržaj koji se može pomicati po cijelom zaslonu</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Više od pola sadržaja zaslona</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Manje od pola sadržaja zaslona</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Sadržaj dinamične veličine</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Sadržaj ugniježđene ladice</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Proširivo</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Preskoči otvoreno stanje</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Spriječi odbacivanje prilikom klika izvan dijaloškog okvira</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Pokaži dršku</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Naslov</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Tekst opisa elementa</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Dodirnite za opis elementa prilagođenog sadržaja</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Gornji početak </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Gornji rub </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Donji početak </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Donji rub </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Sredina </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Prilagođena sredina</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Za ažuriranja napomena uz izdavanje, </string>
    <string name="click_here">kliknite ovdje.</string>
    <string name="open_source_cross_platform">Sustav dizajna na različitim platformama otvorenog koda.</string>
    <string name="intuitive_and_powerful">Intuitivno i snažno.</string>
    <string name="design_tokens">Tokeni dizajna</string>
    <string name="release_notes">Napomene uz izdavanje</string>
    <string name="github_repo">Spremište servisa GitHub</string>
    <string name="github_repo_link">Veza na spremište servisa GitHub</string>
    <string name="report_issue">Prijavi problem</string>
    <string name="v1_components">Komponente V1</string>
    <string name="v2_components">Komponente V2</string>
    <string name="all_components">Sve</string>
    <string name="fluent_logo">Logotip jezika Fluent</string>
    <string name="new_badge">Novo</string>
    <string name="modified_badge">Izmijenjeno</string>
    <string name="api_break_badge">Prekid API-ja</string>
    <string name="app_bar_more">Više</string>
    <string name="accent">Isticanje</string>
    <string name="appearance">Izgled</string>
    <string name="choose_brand_theme">Odaberite temu robne marke:</string>
    <string name="fluent_brand_theme">Robna marka Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Odabir izgleda</string>
    <string name="appearance_system_default">Zadana postavka sustava</string>
    <string name="appearance_light">Svijetlo</string>
    <string name="appearance_dark">Tamno</string>
    <string name="demo_activity_github_link">Veza na pokaznu aktivnost servisa GitHub</string>
    <string name="control_tokens_details">Pojedinosti kontrolnih tokena</string>
    <string name="parameters">Parametri</string>
    <string name="control_tokens">Kontrolni tokeni</string>
    <string name="global_tokens">Globalni tokeni</string>
    <string name="alias_tokens">Tokeni pseudonima</string>
    <string name="sample_text">Tekst</string>
    <string name="sample_icon">Ogledna ikona</string>
    <string name="color">Boja</string>
    <string name="neutral_color_tokens">Tokeni neutralnih boja</string>
    <string name="font_size_tokens">Tokeni veličine fonta</string>
    <string name="line_height_tokens">Tokeni visine retka</string>
    <string name="font_weight_tokens">Tokeni debljine fonta</string>
    <string name="icon_size_tokens">Tokeni veličine ikone</string>
    <string name="size_tokens">Tokeni veličine</string>
    <string name="shadow_tokens">Tokeni sjene</string>
    <string name="corner_radius_tokens">Tokeni promjera kuta</string>
    <string name="stroke_width_tokens">Tokeni za širinu poteza</string>
    <string name="brand_color_tokens">Tokeni boje robne marke</string>
    <string name="neutral_background_color_tokens">Tokeni neutralne boje pozadine</string>
    <string name="neutral_foreground_color_tokens">Tokeni neutralne boje prednjeg plana</string>
    <string name="neutral_stroke_color_tokens">Tokeni neutralne boje rukopisnog poteza</string>
    <string name="brand_background_color_tokens">Tokeni boje pozadine robne marke</string>
    <string name="brand_foreground_color_tokens">Tokeni boje prednjeg plana robne marke</string>
    <string name="brand_stroke_color_tokens">Tokeni boje poteza robne marke</string>
    <string name="error_and_status_color_tokens">Tokeni boja pogreške i statusa</string>
    <string name="presence_tokens">Tokeni boje prisutnosti</string>
    <string name="typography_tokens">Tipografski tokeni</string>
    <string name="unspecified">Neodređeno</string>

</resources>