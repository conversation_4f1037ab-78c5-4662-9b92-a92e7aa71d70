<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">ძირითადი</string>
    <string name="fluentui_secondary">მეორადი</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">დახურვა</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">არჩეულია</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">არ არის არჩეული</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">ხატულა</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">ხატულა</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">გამორთულია</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">მოქმედების ღილაკი</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">ჩართულია</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">ფურცლის დახურვა</string>
    <!-- Describes close action -->
    <string name="fluentui_close">დახურვა</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">გაუქმება</string>
    <!--name of the icon -->
    <string name="fluentui_search">ძიება</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">მიკროფონი</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">ტექსტის გასუფთავება</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">უკან</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">გააქტიურებულია</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">დეაქტივირებულია</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">ნეიტრალური</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">ბრენდი</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">კონტრასტი</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">აქცენტი</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">გაფრთხილება</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">საფრთხე</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">მოხდა შეცდომა</string>
    <string name="fluentui_error">შეცდომა</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">მინიშნება</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">შევრონი</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">სქემა</string>

    <string name="fluentui_action_button_icon">მოქმედების ღილაკის ხატულა</string>
    <string name="fluentui_center">ტექსტის ცენტრირება</string>
    <string name="fluentui_accessory_button">აქსესუარის ღილაკები</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">არჩევანის ღილაკი</string>
    <string name="fluentui_label">წარწერა</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">გაშლილი</string>
    <string name="fluentui_collapsed">აკეცილი</string>

    <!--types of control -->
    <string name="fluentui_large">დიდი</string>
    <string name="fluentui_medium">საშუალო</string>
    <string name="fluentui_small">პატარა</string>
    <string name="fluentui_password_mode">პაროლის რეჟიმი</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">ქვესათაური</string>
    <string name="fluentui_assistive_text">დამხმარე ტექსტი</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">სათაური</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">მოკლე</string>"
    <string name="fluentui_long">გრძელი</string>"
    <string name="fluentui_indefinite">განუსაზღვრელი</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">ღილაკზე დააჭირეს</string>
    <string name="fluentui_dismissed">გაუქმდა</string>
    <string name="fluentui_timeout">დრო ამოიწურა</string>
    <string name="fluentui_left_swiped">მარცხნივ გადაფურცლეს</string>
    <string name="fluentui_right_swiped">მარჯვნივ გადაფურცლეს</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">ტექსტი</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">რიცხვი</string>
    <string name="fluentui_keyboard_phone">ტელეფონი</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">ელფოსტა</string>
    <string name="fluentui_keyboard_password">პაროლი</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">ათობითი</string>
</resources>