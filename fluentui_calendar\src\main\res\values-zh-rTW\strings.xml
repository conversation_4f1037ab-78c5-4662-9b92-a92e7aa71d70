<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">一</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">二</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">三</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">四</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">五</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">六</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">日</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">移至下一週</string>
    <string name="accessibility_goto_previous_week">移至上一週</string>
    <string name="accessibility_today">今天</string>
    <string name="accessibility_selected">已選取</string>

    <!-- *** Shared *** -->
    <string name="done">完成</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">今天</string>
    <string name="tomorrow">明天</string>
    <string name="yesterday">昨天</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">開始時間</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">結束時間</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">開始日期</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">結束日期</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">選擇時間</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">選擇日期</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">日期時間選擇器</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">日期選擇器</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">日期時間選擇器範圍</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">日期選擇器範圍</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">[開始時間] 索引標籤</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">[結束時間] 索引標籤</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">[開始日期] 索引標籤</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">[結束日期] 索引標籤</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">關閉對話方塊</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">遞增月</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">選取下個月</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">遞減月</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">選取上個月</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">遞增日</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">選取後一天</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">遞減日</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">選取前一天</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">遞增年</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">選取下一年</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">遞減年</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">選取上一年</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">遞增日期</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">選取下一個日期</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">遞減日期</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">選取上一個日期</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">遞增小時</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">選取下一小時</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">遞減小時</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">選取前一小時</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">遞增分鐘</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">選取下一分鐘</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">遞減分鐘</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">選取前一分鐘</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">切換上午/下午期間</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">切換上午/下午期間</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">已選取 %s</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">已選取</string>
</resources>
