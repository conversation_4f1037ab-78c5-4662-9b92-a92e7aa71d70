<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI Tanıtımı</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s seçildi</string>
    <string name="app_modifiable_parameters">Değiştirilebilir Parametreler</string>
    <string name="app_right_accessory_view">Sağ Donatı Görünümü</string>

    <string name="app_style">Stil</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Simgeye Basıldı</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Tan<PERSON>tımı Başlat</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Döngü</string>
    <string name="actionbar_icon_radio_label">Simge</string>
    <string name="actionbar_basic_radio_label">Temel</string>
    <string name="actionbar_position_bottom_radio_label">Alt</string>
    <string name="actionbar_position_top_radio_label">Üst</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Eylem Çubuğu Türü</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Eylem Çubuğu Konumu</string>

    <!--AppBar-->
    <string name="app_bar_style">Uygulama Çubuğu Stili</string>
    <string name="app_bar_subtitle">Alt Yazı</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Alt Kenarlık</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Gezinti simgesine tıklandı.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Bayrak</string>
    <string name="app_bar_layout_menu_settings">Ayarlar</string>
    <string name="app_bar_layout_menu_search">Arama</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Kaydırma davranışı: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Kaydırma davranışını değiştir</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Gezinti simgesini değiştir</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Avatarı göster</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Geri simgesini göster</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Simgeyi gizle</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Simgeyi göster</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Arama çubuğu düzen stilini değiştir</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Donatı görünümü olarak göster</string>
    <string name="app_bar_layout_searchbar_action_view_button">Eylem görünümü olarak göster</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Temalar arasında geçiş (etkinliği yeniden oluştur)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Temayı değiştir</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Öğe</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Ekstra kaydırılabilir içerik</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Daire stili</string>
    <string name="avatar_style_square">Kare stili</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Büyük</string>
    <string name="avatar_size_medium">Orta</string>
    <string name="avatar_size_small">Küçük</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Çift Çok Büyük</string>
    <string name="avatar_size_xlarge_accessibility">Çok Büyük</string>
    <string name="avatar_size_xsmall_accessibility">Çok Küçük</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">En Fazla Görüntülenen Avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Taşma Avatar Sayısı</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Kenarlık Türü</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">OverflowAvatarCount ayarlanmış Avatar Grubu, Görüntülenen En Yüksek Avatar\'a bağlı değil.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Yüz Yığını</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Yüz Yığını</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Taşmaya tıklandı</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">%d dizinindeki AvatarView’a tıklandı</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Bildirim Rozeti</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Nokta</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Liste</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Karakter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotoğraflar</string>
    <string name="bottom_navigation_menu_item_news">Haberler</string>
    <string name="bottom_navigation_menu_item_alerts">Uyarılar</string>
    <string name="bottom_navigation_menu_item_calendar">Takvim</string>
    <string name="bottom_navigation_menu_item_team">Ekip</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Etiketleri değiştir</string>
    <string name="bottom_navigation_three_menu_items_button">Üç menü öğesini göster</string>
    <string name="bottom_navigation_four_menu_items_button">Dört menü öğesini göster</string>
    <string name="bottom_navigation_five_menu_items_button">Beş menü öğesini göster</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Etiketler: %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Kapatmak için Aşağı Çekmeyi Etkinleştir</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Tek satırlı öğelerle göster</string>
    <string name="bottom_sheet_with_double_line_items">Çift satırlı öğelerle göster</string>
    <string name="bottom_sheet_with_single_line_header">Tek satırlı üst bilgiyle göster</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Çift satırlı üst bilgi ve ayırıcıyla göster</string>
    <string name="bottom_sheet_dialog_button">Göster</string>
    <string name="drawer_content_desc_collapse_state">Genişlet</string>
    <string name="drawer_content_desc_expand_state">Simge Durumuna Küçült</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s öğesine tıklama</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">%s öğesinde uzun tıklama</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Kapat\'a tıklama</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Öğe Ekle</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Öğeyi Güncelleştir</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Kapat</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Ekle</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Bahsetme</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Kalın</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">İtalik</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Altı çizili</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Üstü Çizili</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Geri Alma</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Yineleme</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Madde İşareti</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Liste</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Bağlantı</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Öğe Güncelleştiriliyor</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Aralık</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Konumu Kapat</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">BAŞLAT</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">SONLANDIR</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Grup alanı</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Öğe alanı</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Bayrak ekle</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Öğeye bayrak eklemeye tıklandı</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Yanıt</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Yanıt öğesine tıklandı</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">İlet</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Öğeyi ilete tıklandı</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Sil</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Öğeyi silmeye tıklandı</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Fotoğraf çek</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kamera öğesine tıklandı</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galeri</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Fotoğrafları görüntüleme</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Galeri öğesine tıklandı</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videolar</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Videolarınızı oynatın</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Videolar öğesine tıklandı</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Yönet</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Medya kitaplığınızı yönetme</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Öğeyi yönetmeye tıklandı</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-posta Eylemleri</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Belgeler</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Son güncelleştirme: 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Paylaş</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Öğeyi paylaşmaya tıklandı</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Taşı</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Öğeyi taşımaya tıklandı</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Sil</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Öğeyi silmeye tıklandı</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Bilgi</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Bilgi öğesine tıklandı</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Saat</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Saat öğesine tıklandı</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Alarm öğesine tıklandı</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Saat dilimi</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Saat dilimi öğesine tıklandı</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Düğmenin farklı görünümleri</string>
    <string name="button">Düğme</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Devre Dışı Düğmesi Örneği</string>
    <string name="button_borderless">Kenarlıksız Düğme Örneği</string>
    <string name="button_borderless_disabled">Kenarlıksız Devre Dışı Düğmesi Örneği</string>
    <string name="button_large">Büyük Düğme Örneği</string>
    <string name="button_large_disabled">Büyük Devre Dışı Düğmesi Örneği</string>
    <string name="button_outlined">Ana Hatlı Düğme Örneği</string>
    <string name="button_outlined_disabled">Ana Hatlı Devre Dışı Düğmesi Örneği</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Tarih seçin</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Tek Tarih</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Tarih seçilmedi</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Tarih seçiciyi göster</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Tarih sekmesi seçiliyken tarih saat seçiciyi göster</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Saat sekmesi seçiliyken tarih saat seçiciyi göster</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Tarih saat seçiciyi göster</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Tarih Aralığı</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Başlangıç:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Bitiş:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Başlangıç ​​seçilmedi</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Bitiş seçilmedi</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Başlangıç tarihi seç</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Bitiş tarihi seç</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Tarih Saat Aralığı</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Tarih saat aralığını seç</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">İletişim Kutusunu Göster</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Çekmeceyi göster</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Çekmece iletişim kutusunu göster</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Solan alt iletişim kutusu yok</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Üst çekmeceyi göster</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Solan üst iletişim kutusu yok</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Bağlayıcı görünümü üst iletişim kutusunu göster</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Üst başlık iletişim kutusu gösterme</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Başlık üst iletişim kutusunun altında göster</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Sağ çekmeceyi göster</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Sol çekmeceyi göster</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Başlık, birincil metin</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Alt yazı, ikincil metin</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Özel alt yazı metni</string>
    <!-- Footer -->
    <string name="list_item_footer">Alt bilgi, üçüncül metin</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Gri alt üst bilgi metniyle tek satırlı liste</string>
    <string name="list_item_sub_header_two_line">İki satırlı liste</string>
    <string name="list_item_sub_header_two_line_dense">Yoğun aralıklı iki satırlı liste</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Özel ikincil alt yazı görünümüne sahip iki satırlı liste</string>
    <string name="list_item_sub_header_three_line">Siyah alt üst bilgi metniyle üç satırlı liste</string>
    <string name="list_item_sub_header_no_custom_views">Özel görünümlere sahip liste öğeleri</string>
    <string name="list_item_sub_header_large_header">Büyük görünümlere sahip liste öğeleri</string>
    <string name="list_item_sub_header_wrapped_text">Kaydırılmış metin içeren liste öğeleri</string>
    <string name="list_item_sub_header_truncated_text">Kesilmiş metin içeren liste öğeleri</string>
    <string name="list_item_sub_header_custom_accessory_text">Eylem</string>
    <string name="list_item_truncation_middle">Orta kesme.</string>
    <string name="list_item_truncation_end">Kesmeyi sonlandır.</string>
    <string name="list_item_truncation_start">Kesmeyi başlat.</string>
    <string name="list_item_custom_text_view">Değer</string>
    <string name="list_item_click">Liste öğesine tıkladınız.</string>
    <string name="list_item_click_custom_accessory_view">Özel donatı görünümüne tıkladınız.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Alt üst bilgi özel donatı görünümüne tıkladınız.</string>
    <string name="list_item_more_options">Diğer seçenekler</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Seçin</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Yok</string>
    <string name="people_picker_delete_example">Sil</string>
    <string name="people_picker_custom_persona_description">Bu örnek, özel bir IPersona nesnesinin nasıl oluşturulamadı olduğunu gösterir.</string>
    <string name="people_picker_dialog_title_removed">Bir kişiliği kaldırdınız:</string>
    <string name="people_picker_dialog_title_added">Bir kişilik eklediniz:</string>
    <string name="people_picker_drag_started">Sürükleme başlatıldı</string>
    <string name="people_picker_drag_ended">Sürükleme sona erdi</string>
    <string name="people_picker_picked_personas_listener">Kişilik Dinleyicisi</string>
    <string name="people_picker_suggestions_listener">Öneri Dinleyicisi</string>
    <string name="people_picker_persona_chip_click">%s öğesine tıkladınız</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s alıcı</item>
        <item quantity="other">%1$s alıcı</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Kalıcı Alt Sayfayı Genişlet</string>
    <string name="collapse_persistent_sheet_button"> Kalıcı Alt Sayfayı Gizle</string>
    <string name="show_persistent_sheet_button"> Kalıcı Alt Sayfayı Göster</string>
    <string name="new_view">Bu Yeni Görünüm</string>
    <string name="toggle_sheet_content">Alt Sayfa İçeriğini Değiştir</string>
    <string name="switch_to_custom_content">Özel İçerik’e geç</string>
    <string name="one_line_content">Tek Satırlı Alt Sayfa İçeriği</string>
    <string name="toggle_disable_all_items">Tüm Öğeleri Devre Dışı Bırakmayı Değiştir</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Görünüm Ekle/Kaldır</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Daraltılmış Yüksekliği Değiştir</string>
    <string name="persistent_sheet_item_create_new_folder_title">Yeni Klasör</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Yeni Klasör öğesine tıklandı</string>
    <string name="persistent_sheet_item_edit_title">Düzenleme</string>
    <string name="persistent_sheet_item_edit_toast">Öğeyi düzenlemeye tıklandı</string>
    <string name="persistent_sheet_item_save_title">Kaydetme</string>
    <string name="persistent_sheet_item_save_toast">Öğeyi kaydetmeye tıklandı</string>
    <string name="persistent_sheet_item_zoom_in_title">Yakınlaştırma</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Yakınlaştır öğesine tıklandı</string>
    <string name="persistent_sheet_item_zoom_out_title">Uzaklaştırma</string>
    <string name="persistent_sheet_item_zoom_out_toast">Uzaklaştır öğesine tıklandı</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Kullanılabilir</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Tasarımcı</string>
    <string name="persona_subtitle_engineer">Mühendis</string>
    <string name="persona_subtitle_manager">Yönetici</string>
    <string name="persona_subtitle_researcher">Araştırmacı</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (kesmeyi test etmek için uzun metin örneği)</string>
    <string name="persona_view_description_xxlarge">Üç satır metin içeren XXLarge avatar</string>
    <string name="persona_view_description_large">İki satır metin içeren büyük avatar</string>
    <string name="persona_view_description_small">Tek satır metin içeren küçük avatar</string>
    <string name="people_picker_hint">İpucu gösterilen yok</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Devre Dışı Kişilik Yongası</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Hata Kişilik Yongası</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Yakın simgesiz Kişilik Yongası</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Basit Kişilik Yongası</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Seçili bir Kişilik Yongası\'na tıkladınız.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Paylaş</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Takip Et</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Kişileri davet et</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Sayfayı yenile</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Tarayıcıda aç</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Bu, çok satırlı bir Açılır Menü\'dır. En fazla satır iki olarak ayarlanır, metnin geri kalanı kesilir.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Tüm haberler</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Kaydedilen haberler</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Sitelerden gelen haberler</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Çalışma saatleri dışında bildir</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Masaüstünde etkin olmadığında bildir</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Öğeye tıkladınız:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Basit menü</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Basit menü 2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Tek bir seçilebilir öğe ve ayırıcı içeren menü</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Tüm seçilebilir öğeleri, simgeleri ve uzun metni içeren menü</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Göster</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Döngüsel İlerleme</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Küçük</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Orta</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Büyük</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Doğrusal İlerleme</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Belirsiz</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Belirli</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Arama Çubuğu</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofonla Geri Arama</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Otomatik düzeltme</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofona Basıldı</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Sağ Görünüme Basıldı</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Klavye Aramaya Basıldı</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Snackbar’ı göster</string>
    <string name="fluentui_dismiss_snackbar">Snackbar\'ı kapat</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Eylem</string>
    <string name="snackbar_action_long">Uzun Metin Eylemi</string>
    <string name="snackbar_single_line">Tek satırlı çubuk (snackbar)</string>
    <string name="snackbar_multiline">Bu çok satırlı bir kaydırma çubuğu. En fazla satır iki olarak ayarlanır, metnin geri kalanı kesilir.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Bu bir duyuru yer çubuğu. Yeni özelliklerle iletişim kurmak için kullanılır.</string>
    <string name="snackbar_primary">Bu birincil çubuk (snackbar).</string>
    <string name="snackbar_light">Bu açık bir çubuk (snackbar).</string>
    <string name="snackbar_warning">Bu bir uyarı çubuğu (snackbar).</string>
    <string name="snackbar_danger">Bu bir sürükleme çubuğu (snackbar).</string>
    <string name="snackbar_description_single_line">Kısa süre</string>
    <string name="snackbar_description_single_line_custom_view">Küçük özel görünüm olarak döngüsel ilerlemeyle uzun süre</string>
    <string name="snackbar_description_single_line_action">Eylem içeren kısa süre</string>
    <string name="snackbar_description_single_line_action_custom_view">Eylem ve orta düzeyde özel görünümle kısa süre</string>
    <string name="snackbar_description_single_line_custom_text_color">Özelleştirilmiş metin rengiyle kısa süre</string>
    <string name="snackbar_description_multiline">Uzun süre</string>
    <string name="snackbar_description_multiline_custom_view">Küçük özel görüntüleme uzun süre</string>
    <string name="snackbar_description_multiline_action">Eylem ve metin güncelleştirmeleriyle belirsiz süre</string>
    <string name="snackbar_description_multiline_action_custom_view">Eylem ve orta düzeyde özel görünümle kısa süre</string>
    <string name="snackbar_description_multiline_action_long">Uzun eylem metniyle kısa süre</string>
    <string name="snackbar_description_announcement">Kısa süre</string>
    <string name="snackbar_description_updated">Bu metin güncelleştirildi.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Çubuğu (Snackbar) Göster</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Tek çizgi</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Çok satırlı</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Duyuru stili</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Birincil stil</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Açık Stil</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Uyarı stili</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Tehlike stili</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Giriş</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Posta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Ayarlar</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Bildirim</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Daha fazla</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Metin Hizalama</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Dikey</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Yatay</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Metin Yok</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Sekme Öğeleri</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Başlık</string>
    <string name="cell_sample_description">Açıklama</string>
    <string name="calculate_cells">100 hücreyi yükleme/hesaplama</string>
    <string name="calculate_layouts">100 düzeni yükleme/hesaplama</string>
    <string name="template_list">Şablon Listesi</string>
    <string name="regular_list">Normal Liste</string>
    <string name="cell_example_title">Başlık: Hücre</string>
    <string name="cell_example_description">Açıklama: Yönü değiştirmek için dokunun</string>
    <string name="vertical_layout">Dikey Düzen</string>
    <string name="horizontal_layout">Yatay Düzen</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standart Sekme 2 Segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standart Sekme 3 Segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standart Sekme 4 Segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Çağrı Cihazı ile Standart Sekme</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Sekme Değiştir</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Hap Sekmesi </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Araç İpucu için dokunun</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Özel Takvim Araç İpucu için dokunun</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Özel Renk Araç İpucuna Dokunun</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">İç Araç İpucunu Kapatmak için Dokunun</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Özel Görünüm Araç İpucu için dokunun</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Üst Özel Renk Araç İpucu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp offsetX ile Üst Uç Araç İpucu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Alt Başlangıç Araç İpucu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp ofsetY ile Alt Uç Araç İpucu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">İç Araç İpucunu Kapatın</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Araç ipucu kapatıldı</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Başlık Açık 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Başlık 1 Orta 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Başlık 2 Normal 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Başlık Normal 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Alt başlık 1 Normal 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Alt başlık 2 Orta 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Gövde 1 Normal 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Gövde 2 Orta 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Açıklamalı Alt Yazı Normal 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK Sürümü: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Öğe %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Klasör</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Tıklayanlar</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">İskele</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB Genişletilmiş</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB Daraltılmış</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Listeyi yenilemek için tıklayın</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Çekmeceyi Aç</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menü Öğesi</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">X Uzaklığı (dp Cinsinden)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Y Uzaklığı (dp Cinsinden)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">İçerik Metni</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Yinelenen İçerik Metni</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menü genişliği, İçerik Metnine uyacak şekilde değişiklik gösterir. Genişlik,
        ekran boyutunun en fazla %75\'iyle sınırlıdır. İçeriğin yan ve alt kenar boşluğu belirteç tarafından düzenlenir. Aynı içerik metni, yüksekliği değişiklik gösterecek şekilde
        yinelenir.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Menüyü Aç</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Temel Kart</string>
    <!-- UI Label for Card -->
    <string name="file_card">Dosya Kartı</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Duyuru Kartı</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Rastgele UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">Seçenekler</string>
    <!-- UI Label for Title -->
    <string name="card_title">Başlık</string>
    <!-- UI Label for text -->
    <string name="card_text">Metin</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Alt Metin</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Bu başlığın ikinci kopyası, gerekirse iki satıra kaydırılabilir.</string>
    <!-- UI Label Button -->
    <string name="card_button">Düğme</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">İletişim Kutusunu Göster</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Dışına tıkladığında iletişim kutusunu kapat</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Geri düğmesine basıldığında iletişim kutusunu kapat</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">İletişim kutusu kapatıldı</string>
    <!-- UI Label Cancel -->
    <string name="cancel">İptal</string>
    <!-- UI Label Ok -->
    <string name="ok">Tamam</string>
    <!-- A sample description -->
    <string name="dialog_description">İletişim kutusu, kullanıcıdan bir karar vermesini veya ek bilgi girmesini isteyen küçük bir penceredir.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Çekmeceyi Aç</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Çekmeceyi Genişlet</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Çekmeceyi Kapat</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Çekmece Türünü Seçin</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Üst</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Tüm çekmece görünür bölgede görünür.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Alt</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Görünür bölgede çekmecenin tamamı görünür. Hareketli kaydırma içeriğini yukarı kaydırın. Genişletilebilir çekmece, sürükleme tutamacı ile genişler.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Sola Kaydır</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Çekmece sol taraftan görünen bölgeye kaydırılır.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Sağa Kaydır</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Çekmece sağ taraftan görünen bölgeye kaydırılır.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Alta Kaydır</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Çekmece, ekranın alt kısmından görünür bölgeye kaydırılır. Genişletilebilir çekmecede yukarı doğru çekme hareketi, kalan kısmını görünür bölgeye getirin &amp; kaydırın.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim Görünür</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Çekmece İçeriğini Seçin</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Tam ekran boyutu kaydırılabilir içerik</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Yarım ekran içeriğinden fazla</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Yarım ekran içeriğinden az</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dinamik boyut içeriği</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">İç İçe Çekmece İçeriği</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Genişletilebilir</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Açık Durumu Atla</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Hafif Dokunuşlu Tıklamada Kapatmayı Önle</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Tutamacı Göster</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Başlık</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Araç İpucu Metni</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Özel İçerik Araç İpucu için dokunun</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Üst Başlangıç </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Üst Uç </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Alt Başlangıç </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Alt Uç </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Orta </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Özel Merkez</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Sürüm Notlarına yönelik güncelleştirmeler için, </string>
    <string name="click_here">buraya tıklayın.</string>
    <string name="open_source_cross_platform">Açık kaynak çoklu platform Tasarım Sistemi.</string>
    <string name="intuitive_and_powerful">Sezgisel &amp; Güçlü.</string>
    <string name="design_tokens">Tasarım Belirteçleri</string>
    <string name="release_notes">Sürüm Notları</string>
    <string name="github_repo">GitHub Deposu</string>
    <string name="github_repo_link">GitHub Depo Bağlantısı</string>
    <string name="report_issue">Sorunu Bildirme</string>
    <string name="v1_components">V1 Bileşenleri</string>
    <string name="v2_components">V2 Bileşenleri</string>
    <string name="all_components">Tümü</string>
    <string name="fluent_logo">Fluent Logosu</string>
    <string name="new_badge">Yeni</string>
    <string name="modified_badge">Değiştirme tarihi</string>
    <string name="api_break_badge">API Sonu</string>
    <string name="app_bar_more">Daha fazla</string>
    <string name="accent">Vurgu</string>
    <string name="appearance">Görünüm</string>
    <string name="choose_brand_theme">Marka temanızı seçin:</string>
    <string name="fluent_brand_theme">Fluent Markası</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Görünüm Seçme</string>
    <string name="appearance_system_default">Sistem Varsayılanı</string>
    <string name="appearance_light">Açık</string>
    <string name="appearance_dark">Koyu</string>
    <string name="demo_activity_github_link">Tanıtım Etkinliği GitHub Bağlantısı</string>
    <string name="control_tokens_details">Denetim Belirteçleri ayrıntıları</string>
    <string name="parameters">Parametreler</string>
    <string name="control_tokens">Denetim Belirteçleri</string>
    <string name="global_tokens">Genel Belirteçler</string>
    <string name="alias_tokens">Diğer Ad Belirteçleri</string>
    <string name="sample_text">Metin</string>
    <string name="sample_icon">Örnek Simge</string>
    <string name="color">Renk</string>
    <string name="neutral_color_tokens">Nötr Renk Belirteçleri</string>
    <string name="font_size_tokens">Yazı Tipi Boyutu Belirteçleri</string>
    <string name="line_height_tokens">Çizgi Yüksekliği Belirteçleri</string>
    <string name="font_weight_tokens">Yazı Tipi Ağırlığı Belirteçleri</string>
    <string name="icon_size_tokens">Simge Boyutu Belirteçleri</string>
    <string name="size_tokens">Boyut Belirteçleri</string>
    <string name="shadow_tokens">Gölge Belirteçleri</string>
    <string name="corner_radius_tokens">Corner Yarıçap Belirteçleri</string>
    <string name="stroke_width_tokens">Mürekkep Vuruşu Genişliği Belirteçleri</string>
    <string name="brand_color_tokens">Marka Rengi Belirteçleri</string>
    <string name="neutral_background_color_tokens">Nötr Arka Plan Renk Belirteçleri</string>
    <string name="neutral_foreground_color_tokens">Nötr Ön Plan Renk Belirteçleri</string>
    <string name="neutral_stroke_color_tokens">Nötr Mürekkep Vuruşu Rengi Belirteçleri</string>
    <string name="brand_background_color_tokens">Marka Arka Plan Rengi Belirteçleri</string>
    <string name="brand_foreground_color_tokens">Marka Ön Plan Renk Belirteçleri</string>
    <string name="brand_stroke_color_tokens">Marka Mürekkep Vuruşu Rengi Belirteçleri</string>
    <string name="error_and_status_color_tokens">Hata ve Durum Rengi Belirteçleri</string>
    <string name="presence_tokens">İletişim Durumu Renk Belirteçleri</string>
    <string name="typography_tokens">Tipografi Belirteçleri</string>
    <string name="unspecified">Belirtilmemiş</string>

</resources>