on: workflow_dispatch

jobs:
  Localize:
    runs-on: ubuntu-20.04
    environment: Auth

    steps:
    - uses: actions/checkout@v3
    - shell: bash
      name: Localize
      env:
        TDBUILD_TEAM_ID: ${{ secrets.TDBUILD_TEAM_ID }}
        TDBUILD_AAD_APPLICATION_CLIENT_ID: ${{ secrets.TDBUILD_AAD_APPLICATION_CLIENT_ID }}
        TDBUILD_AAD_APPLICATION_CLIENT_SECRET: ${{ secrets.TDBUILD_AAD_APPLICATION_CLIENT_SECRET }}
      run: ./localize.sh
    - name: Create Pull Request
      env:
        GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
      run: |
        set -e
        echo "Start."
        # Configure git and Push updates
        git config --global user.email ${{ secrets.EMAIL }}
        git config --global user.name ${{ secrets.NAME }}
        git config pull.rebase false
        branch=Localize-$GITHUB_RUN_ID
        message='TDBuild Localization'
        echo "Creating branch $branch"
        git branch $branch
        echo "Checkout $branch"
        git checkout $branch
        # Add / update and commit
        git add .
        if [[ `git status --porcelain` ]]; then
            echo "Adding git commit"
            git commit -m 'Adding localized files'
            echo "Pushing to origin"
            git push origin $branch
            echo "Creating PR"
            gh api \
                --method POST \
                -H "Accept: application/vnd.github+json" \
                /repos/${GITHUB_REPOSITORY}/pulls \
                -f title="$message" \
              -f body="Localized files" \
              -f head="$branch" \
              -f base='master'
        else
            echo "No changes since last run"
        fi
