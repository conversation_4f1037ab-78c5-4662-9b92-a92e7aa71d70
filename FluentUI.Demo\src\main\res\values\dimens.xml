<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>
    <dimen name="default_layout_margin">@dimen/fluentui_content_inset</dimen>
    <dimen name="default_wide_menu_margin">300dp</dimen>
    <dimen name="default_view_margin">8dp</dimen>
    <dimen name="cell_horizontal_spacing">8dp</dimen>
    <dimen name="cell_vertical_spacing">2dp</dimen>
    <dimen name="calendar_date_title_text_size">18sp</dimen>
    <dimen name="button_list_item_vertical_padding">8dp</dimen>

    <!--Avatar-->
    <dimen name="avatar_size_text_margin_end">36dp</dimen>

    <!--ContextualCommandBar-->
    <dimen name="contextual_command_bar_space_title_width">100dp</dimen>

    <!--DatePickerDialog-->
    <dimen name="date_picker_range_title_padding_end">4dp</dimen>
    <dimen name="date_picker_range_title_width">50dp</dimen>

    <!--DemoListItem-->
    <dimen name="demo_list_item_padding_horizontal">@dimen/default_layout_margin</dimen>
    <dimen name="demo_list_item_padding_vertical">13.5dp</dimen>

    <!--Drawer-->
    <dimen name="drawer_persona_list_height">400dp</dimen>
    <dimen name="drawer_persona_list_width">300dp</dimen>
    <dimen name="drawer_button_top_margin">180dp</dimen>

    <!--Launch Screen-->
    <dimen name="fluentui_launch_logo_bottom_spacing">53dp</dimen>
    <dimen name="fluentui_launch_microsoft_logo_bottom_spacing">32dp</dimen>

    <!--Persistent BottomSheet-->
    <dimen name="fluentui_persistent_bottom_sheet_peek_height">110dp</dimen>
    <dimen name="fluentui_persistent_horizontal_item_right_margin">35dp</dimen>

    <!--Progress-->
    <dimen name="circular_progress_text_area_min_height">48dp</dimen>
    <dimen name="circular_progress_text_area_spacing">36dp</dimen>
    <dimen name="circular_progress_spacing">16dp</dimen>
    <!--Margin top is used to space the circular progress indicators evenly-->
    <dimen name="circular_progress_margin_top_medium">18dp</dimen>
    <dimen name="circular_progress_margin_top_small">8dp</dimen>
    <!--Linear Progress-->
    <dimen name="linear_progress_margin">20dp</dimen>

    <!--PopupMenu-->
    <dimen name="default_layout_margin_start_duo">@dimen/fluentui_popup_menu_duo_margin_start</dimen>>
    <dimen name="default_layout_margin_top_duo">@dimen/fluentui_popup_menu_duo_margin_top</dimen>>

    <!--Shared-->
    <dimen name="demo_headline_divider_height">@dimen/fluentui_divider_height</dimen>
    <dimen name="demo_headline_padding_bottom">4dp</dimen>

    <!--TabLayout-->
    <dimen name="tab_layout_pager_height">250dp</dimen>

    <!--Tooltip-->
    <dimen name="tooltip_example_offset_x">10dp</dimen>
    <dimen name="tooltip_example_offset_y">10dp</dimen>

    <!--TemplateView-->
    <dimen name="template_view_list_height">500dp</dimen>

    <!--image dimen-->
    <dimen name="image_size">24dp</dimen>
</resources>