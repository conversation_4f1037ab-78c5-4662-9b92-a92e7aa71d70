<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demo verzija Fluent UI</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Izabrano %s</string>
    <string name="app_modifiable_parameters">Parametri koji se mogu izmeniti</string>
    <string name="app_right_accessory_view">Desni prikaz dodataka</string>

    <string name="app_style">Stil</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Pritisnuta ikona</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Pokreni demonstraciju</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Vrteš<PERSON></string>
    <string name="actionbar_icon_radio_label">Ikona</string>
    <string name="actionbar_basic_radio_label">Osnovno</string>
    <string name="actionbar_position_bottom_radio_label">Na dnu</string>
    <string name="actionbar_position_top_radio_label">Vrh</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tip trake sa radnjama</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Položaj trake sa radnjama</string>

    <!--AppBar-->
    <string name="app_bar_style">Stil trake sa aplikacijama</string>
    <string name="app_bar_subtitle">Titl</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Donja ivica</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Kliknuto je na ikonu za navigaciju.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Zastava</string>
    <string name="app_bar_layout_menu_settings">Postavke</string>
    <string name="app_bar_layout_menu_search">Pretraži</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Ponašanje pri pomeranju: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Uključi/isključi ponašanje pomeranja</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Uključi/isključi ikonu za navigaciju</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Prikaži avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Prikaži ikonu za nazad</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Sakrij ikonu</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Prikaži ikonu</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Uključi/isključi stil rasporeda na traci za pretragu</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Prikaži kao dodatak</string>
    <string name="app_bar_layout_searchbar_action_view_button">Prikaži kao prikaz radnje</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Prebacivanje između tema (ponovo kreira aktivnost)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Uključi/isključi temu</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Stavka</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Sadržaj koji se može dodatno pomerati</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Stil kruga</string>
    <string name="avatar_style_square">Stil kvadrata</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Veliki</string>
    <string name="avatar_size_medium">Srednji</string>
    <string name="avatar_size_small">Mali</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dvostruko veoma velika</string>
    <string name="avatar_size_xlarge_accessibility">Veoma velika</string>
    <string name="avatar_size_xsmall_accessibility">Veoma mala</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maksimalno prikazani avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Broj pekoračenih avatara</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tip ivice</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Grupa avatara sa skupom OverflowAvatarCount neće biti u skladu sa maksimumom prikazanih avatara.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Komplet lica</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Gomila lica</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Kliknuto je na višak</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Kliknuto je na prikaz avatara indeksa %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Bedž obaveštenja</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Tačka</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Znak</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotografije</string>
    <string name="bottom_navigation_menu_item_news">Vesti</string>
    <string name="bottom_navigation_menu_item_alerts">Obaveštenja</string>
    <string name="bottom_navigation_menu_item_calendar">Kalendar</string>
    <string name="bottom_navigation_menu_item_team">Tim</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Uključi/isključi oznake</string>
    <string name="bottom_navigation_three_menu_items_button">Prikaži tri stavke menija</string>
    <string name="bottom_navigation_four_menu_items_button">Prikaži četiri stavke menija</string>
    <string name="bottom_navigation_five_menu_items_button">Prikaži pet stavki menija</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Oznake su %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Omogući brzo prevlačenje nadole za odbacivanje</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Prikaži sa stavkama u jednom redu</string>
    <string name="bottom_sheet_with_double_line_items">Prikaži sa stavkama dvostruke linije</string>
    <string name="bottom_sheet_with_single_line_header">Prikaži sa zaglavljem u jednom redu</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Prikaži sa zaglavljem u dva reda i razdelnicima</string>
    <string name="bottom_sheet_dialog_button">Prikaži</string>
    <string name="drawer_content_desc_collapse_state">Razvij</string>
    <string name="drawer_content_desc_expand_state">Minimiziraj</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klik %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Dugi klik %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Kliknite na odbaciti</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Umetanje stavke</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Ažuriraj stavku</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Odbaci</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Dodaj</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Pominjanje</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Podebljano</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kurziv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Podvučeno</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Precrtani tekst</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Opozovi radnju</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Ponovi radnju</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Znak za nabrajanje</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Veza</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Ažuriranje stavke</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Razmak</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Odbaci položaj</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">POČNI</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Grupni prostor</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Prostor stavke</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Zastava</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Kliknuto je na stavku zastavice</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Odgovori</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Kliknuto je na stavku odgovora</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Prosledi</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Kliknuto je na stavku za prosleđivanje</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Izbriši</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Kliknuto je na stavku brisanja</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Snimite fotografiju</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kliknuto je na stavku kamere</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerija</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Pogledajte vaše fotografije</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Kliknuto je na stavku galerije</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Video zapisi</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Reprodukujte video zapise</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Kliknuto je na stavku video zapisa</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Upravljanje</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Upravljajte bibliotekom medija</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Kliknuto je na stavku za upravljanje</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Radnje e-pošte</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumenti</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Poslednji put ažurirano 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Deljenje</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Kliknuto je na stavku deljenja</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Premesti</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Kliknuto je na stavku za premeštanje</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Izbriši</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Kliknuto je na stavku brisanja</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informacije</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Kliknuto je na stavku informacija</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Sat</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Kliknuto je na stavku „Sat“</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Kliknuto je na stavku alarma</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Vremenska zona</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Kliknuto je na stavku vremenske zone</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Različiti prikazi dugmeta</string>
    <string name="button">Taster</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Primer dugmeta „Onemogućeno“</string>
    <string name="button_borderless">Primer dugmeta „Bez ivica“</string>
    <string name="button_borderless_disabled">Primer dugmeta „Onemogućeno bez ivica“</string>
    <string name="button_large">Primer velikog dugmeta</string>
    <string name="button_large_disabled">Primer velikog dugmeta „Onemogućena“</string>
    <string name="button_outlined">Primer dugmeta „Oivičeno“</string>
    <string name="button_outlined_disabled">Primer dugmeta „Oivičeno onemogućeno“</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Odaberite datum</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Jedan datum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nije izabran datum</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Prikazati birač datuma</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Prikaži birač datuma i vremena sa izabranom karticom za datum</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Prikaži birač datuma i vremena sa izabranom karticom za vreme</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Prikaži birač datuma i vremena</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Opseg datuma</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Početak:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Kraj:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nije izabran nijedan početak</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Nije izabran kraj</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Izaberite datum početka</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Izaberite datum završetka</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Opseg datuma i vremena</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Izaberite opseg datuma i vremena</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Prikaži dijalog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Prikaži fioku</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Prikaži dijalog za fioke</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Dijalog na dnu bez iščezavanja</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Prikaži gornju fioku</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Dijalog na vrhu bez iščezavanja</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Prikaži gornji dijalog prikaza sidra</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Prikaži dijalog na vrhu bez dijaloga</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Prikaži ispod naslova dijaloga na vrhu</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Prikaži desnu fioku</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Prikaži levu fioku</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Naslov, primarni tekst</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Titl, sekundarni tekst</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Prilagođeni tekst titla</string>
    <!-- Footer -->
    <string name="list_item_footer">Podnožje stranice, tercijarni tekst</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Lista u jednom redu sa sivim tekstom podnaslova</string>
    <string name="list_item_sub_header_two_line">Lista sa dva reda</string>
    <string name="list_item_sub_header_two_line_dense">Lista sa dva reda sa gustim razmakom</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Lista sa dva reda sa prilagođenim sekundarnim prikazom titlova</string>
    <string name="list_item_sub_header_three_line">Lista sa tri reda sa crnim tekstom podnaslova</string>
    <string name="list_item_sub_header_no_custom_views">Stavke liste bez prilagođenih prikaza</string>
    <string name="list_item_sub_header_large_header">Stavke liste sa velikim prilagođenim prikazima</string>
    <string name="list_item_sub_header_wrapped_text">Stavke liste sa prelomljenim tekstom</string>
    <string name="list_item_sub_header_truncated_text">Stavke liste sa skraćenim tekstom</string>
    <string name="list_item_sub_header_custom_accessory_text">Radnja</string>
    <string name="list_item_truncation_middle">Odsecanje u sredini.</string>
    <string name="list_item_truncation_end">Završi skraćivanje.</string>
    <string name="list_item_truncation_start">Započnite odsecanje.</string>
    <string name="list_item_custom_text_view">Vrednost</string>
    <string name="list_item_click">Kliknuli ste na stavku liste.</string>
    <string name="list_item_click_custom_accessory_view">Kliknuli ste na prilagođeni prikaz dodataka.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Kliknuli ste na prilagođeni prikaz dodataka u podnaslovu.</string>
    <string name="list_item_more_options">Još opcija</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Izaberi</string>
    <string name="people_picker_select_deselect_example">IzaberiOpozovi izbor</string>
    <string name="people_picker_none_example">Nijedna</string>
    <string name="people_picker_delete_example">Izbriši</string>
    <string name="people_picker_custom_persona_description">Ovaj primer pokazuje kako se kreira prilagođeni IPersona objekat.</string>
    <string name="people_picker_dialog_title_removed">Uklonili ste personalnost:</string>
    <string name="people_picker_dialog_title_added">Dodali ste personalnost:</string>
    <string name="people_picker_drag_started">Prevlačenje je započeto</string>
    <string name="people_picker_drag_ended">Prevlačenje je završeno</string>
    <string name="people_picker_picked_personas_listener">Slušalac personalnosti</string>
    <string name="people_picker_suggestions_listener">Slušalac predloga</string>
    <string name="people_picker_persona_chip_click">Kliknuli ste na %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s primalac</item>
        <item quantity="few">%1$s primaoca</item>
        <item quantity="other">%1$s primalaca</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Razvij stalni BottomSheet</string>
    <string name="collapse_persistent_sheet_button"> Sakrij postojani BottomSheet</string>
    <string name="show_persistent_sheet_button"> Prikaži postojani BottomSheet</string>
    <string name="new_view">Ovo je novi prikaz</string>
    <string name="toggle_sheet_content">Uključi/isključi sadržaj donjeg lista</string>
    <string name="switch_to_custom_content">Prebaci se na prilagođeni sadržaj</string>
    <string name="one_line_content">Sadržaj donjeg lista u jednom redu</string>
    <string name="toggle_disable_all_items">Uključi/isključi onemogućavanje svih stavki</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Dodaj/ukloni prikaz</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Promeni skupljenu visinu</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nova fascikla</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Kliknuto je stavku „Nova fascikla“</string>
    <string name="persistent_sheet_item_edit_title">Uredi</string>
    <string name="persistent_sheet_item_edit_toast">Kliknuto je na stavku „Urediti“</string>
    <string name="persistent_sheet_item_save_title">Sačuvaj</string>
    <string name="persistent_sheet_item_save_toast">Kliknuto je na stavku „Sačuvaj“</string>
    <string name="persistent_sheet_item_zoom_in_title">Uvećaj</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Kliknuto je na stavku „Zumirati“</string>
    <string name="persistent_sheet_item_zoom_out_title">Umanji</string>
    <string name="persistent_sheet_item_zoom_out_toast">Kliknuto je na stavku "Umanji"</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Dostupan/na</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Alan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brejdi</string>
    <string name="persona_name_ashley_mccarthy">Ešli Mekarti</string>
    <string name="persona_name_carlos_slattery">Karlos Slateri</string>
    <string name="persona_name_carole_poland">Kerol Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Selest Barton</string>
    <string name="persona_name_charlotte_waltson">Šarlot Voltson</string>
    <string name="persona_name_colin_ballinger">Kolin Balidžer</string>
    <string name="persona_name_daisy_phillips">Dejzi Filips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henri Bril</string>
    <string name="persona_name_isaac_fielder">Isak Filder</string>
    <string name="persona_name_johnie_mcconnell">Nikola Branković</string>
    <string name="persona_name_kat_larsson">Katarina Savić</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kosta Stojković</string>
    <string name="persona_name_kristen_patterson">Kristina Petrović</string>
    <string name="persona_name_lydia_bauer">Lidija Bajić</string>
    <string name="persona_name_mauricio_august">Mauricio Avgust</string>
    <string name="persona_name_miguel_garcia">Luka Macura</string>
    <string name="persona_name_mona_kane">Mona Kalinić</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Vanda Hauard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Dizajner</string>
    <string name="persona_subtitle_engineer">Inženjer</string>
    <string name="persona_subtitle_manager">Menadžer</string>
    <string name="persona_subtitle_researcher">Istraživač</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (dugačak primer teksta za testiranje skraćivanja)</string>
    <string name="persona_view_description_xxlarge">XXLarge avatar sa tri reda teksta</string>
    <string name="persona_view_description_large">Veliki avatar sa dva reda teksta</string>
    <string name="persona_view_description_small">Mali avatar sa jednim redom teksta</string>
    <string name="people_picker_hint">Nije prikazano nijedno sa podsetnikom</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Onemogućeni čip za personalnost</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Greška sa čipom personalnosti</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Čip personalnosti bez ikone za zatvaranje</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Osnovni čip personalnosti</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Kliknuli ste na izabrani čip personalnosti.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Deljenje</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Prati</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Pozovi osobe</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Osveži stranicu</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Otvori u pregledaču</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Ovo je iskačući meni sa više redova. Maksimalni broj redova je postavljen na dva, ostatak teksta će biti skraćen.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Sve novosti</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Sačuvane vesti</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Vesti sa sajtova</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Obavesti van radnog vremena</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Obavesti kada nije aktivno na radnoj površini</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Kliknuli ste na stavku:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Jednostavni meni</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Jednostavan meni2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Meni sa jednom stavkom koja se može izabrati i razdelnikom</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Meni sa svim stavkama koje se mogu izabrati, ikonama i dugačkim tekstom</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Prikaži</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Kružni napredak</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Mali</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Srednji</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Veliki</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linearni napredak</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Neodređeno</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Određivanje</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Traka za pretragu</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Povratni poziv mikrofona</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Automatsko ispravljanje</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon je pritisnut</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Pritisnuti desni prikaz</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Pretraga tastature je pritisnuta</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Prikaži Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Odbaci Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Radnja</string>
    <string name="snackbar_action_long">Radnja dugačkog teksta</string>
    <string name="snackbar_single_line">Snackbar sa jednim redom</string>
    <string name="snackbar_multiline">Ovo je Snackbar sa više redova. Maksimalni broj redova je postavljen na dva, ostatak teksta će biti skraćen.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Ovo je Snackbar sa objavama. Koristi se za komunikaciju novih funkcija.</string>
    <string name="snackbar_primary">Ovo je primarni Snackbar.</string>
    <string name="snackbar_light">Ovo je svetli Snackbar.</string>
    <string name="snackbar_warning">Ovo je Snackbar sa upozorenjem.</string>
    <string name="snackbar_danger">Ovo je Snackbar za opasnost.</string>
    <string name="snackbar_description_single_line">Kratko trajanje</string>
    <string name="snackbar_description_single_line_custom_view">Dugo trajanje sa kružnim napretkom kao mali prilagođeni prikaz</string>
    <string name="snackbar_description_single_line_action">Kratko trajanje sa radnjom</string>
    <string name="snackbar_description_single_line_action_custom_view">Kratko trajanje sa radnjom i srednje prilagođenim prikazom</string>
    <string name="snackbar_description_single_line_custom_text_color">Kratko trajanje sa prilagođenom bojom teksta</string>
    <string name="snackbar_description_multiline">Dugo trajanje</string>
    <string name="snackbar_description_multiline_custom_view">Dugo trajanje sa malim prilagođenim prikazom</string>
    <string name="snackbar_description_multiline_action">Neodređeno trajanje sa ispravkama radnji i teksta</string>
    <string name="snackbar_description_multiline_action_custom_view">Kratko trajanje sa radnjom i srednje prilagođenim prikazom</string>
    <string name="snackbar_description_multiline_action_long">Kratko trajanje sa dugačkim tekstom radnje</string>
    <string name="snackbar_description_announcement">Kratko trajanje</string>
    <string name="snackbar_description_updated">Ovaj tekst je ažuriran.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Prikaži Snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Jedan red</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Tekst u više redova</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Stil objave</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primarni stil</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Svetli stil</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Stil upozorenja</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Stil opasnosti</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Početak</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Pošta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Postavke</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Obaveštenje</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Više</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Poravnavanje teksta</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertikalno</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontalno</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Bez teksta</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Stavke kartice</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Naslov</string>
    <string name="cell_sample_description">Opis</string>
    <string name="calculate_cells">Učitaj/izračunaj 100 ćelija</string>
    <string name="calculate_layouts">Učitaj/izračunaj 100 rasporeda</string>
    <string name="template_list">Lista predložaka</string>
    <string name="regular_list">Regularna lista</string>
    <string name="cell_example_title">Naslov: ćelija</string>
    <string name="cell_example_description">Opis: dodirnite da biste promenili položaj</string>
    <string name="vertical_layout">Vertikalni raspored</string>
    <string name="horizontal_layout">Horizontalni raspored</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standardna kartica – 2 segmenta</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardna kartica sa 3 segmenta</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardna kartica sa 4 segmenta</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardna kartica sa pejdžerom</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Promeni karticu</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Kartica sa tabletama </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Dodirnite za opis alatke</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Dodirnite za opis alatke prilagođenog kalendara</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Dodirnite opis alatke prilagođene boje</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Dodirnite za odbacivanje u opisu alatke</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Dodirnite za opis alatke prilagođenog prikaza</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Opis alatke za najpopularnije prilagođene boje</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Opis alatke na vrhu sa pomakom od 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Opis alatke za donji početni ekran</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Opis alatke donjeg kraja sa pomakom od 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Odbaci unutar opisa alatke</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Opis alatke je odbačen</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Naslov je svetao 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Naslov 1 je srednje 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Naslov 2 je regularni 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Naslov je obično 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Podnaslov 1 je običan 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Podnaslov 2 je srednji 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Telo 1 je redovno 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Telo 2 je srednje 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Natpis je običan 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK verzija: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Stavka %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Fascikla</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Kliknuto</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB je proširen</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB je skupljen</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Kliknite da biste osvežili listu</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Otvori fioku</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Stavka menija</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Pomak po X osi (u dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Pomak po Y osi (u dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Tekst sadržaja</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Ponovi tekst sadržaja</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Širina menija će se promeniti u odnosu na tekst sadržaja. Maksimalna
        širina je ograničena na 75% veličine ekrana. Marginom sadržaja sa strane i na dnu upravlja token. Isti tekst sadržaja će se ponoviti da bi se promenila
        visina.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Meni „Otvaranje“</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Osnovna kartica</string>
    <!-- UI Label for Card -->
    <string name="file_card">Kartica „Datoteka“</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Kartica objave</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Nasumični korisnički interfejs</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opcije</string>
    <!-- UI Label for Title -->
    <string name="card_title">Naslov</string>
    <!-- UI Label for text -->
    <string name="card_text">Tekst</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Podtekst</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekundarna kopija za ovaj reklamni natpis može da se prelomi na dva reda, ako je potrebno.</string>
    <!-- UI Label Button -->
    <string name="card_button">Taster</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Prikaži dijalog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Odbaci dijalog prilikom klika van</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Odbaci dijalog pri pritisku na dugme „Nazad“</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dijalog je odbačen</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Otkaži</string>
    <!-- UI Label Ok -->
    <string name="ok">U redu</string>
    <!-- A sample description -->
    <string name="dialog_description">Dijalog je mali prozor koji od korisnika traži da odluči ili unese dodatne informacije.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Otvori panel</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Razvij panel</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Zatvori fioku</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Izbor tipa fioke</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Vrh</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Cela fioka se prikazuje u vidljivoj oblasti.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Dno</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Cela fioka se prikazuje u vidljivoj oblasti. Brzo prevucite nagore za pomeranje sadržaja. Fioka koja se može proširiti razvija se preko regulatora za prevlačenje.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Levi slajd preko</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Fioka klizi ka vidljivoj oblasti sa leve strane.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Desni slajd preko</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Fioka klizi ka vidljivoj oblasti sa desne strane.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Donji slajd preko</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Prevucite fioku do vidljive oblasti sa dna ekrana. Prevlačenjem nagore na proširivoj fioci dovedite njen ostatak u vidljivu oblast a zatim pomerite.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Vidljiv spoj</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Izbor sadržaja fioke</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Sadržaj veličine celog ekrana koji se može pomerati</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Više od polovine sadržaja ekrana</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Manje od pola sadržaja ekrana</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Sadržaj dinamičke veličine</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Sadržaj ugnežđene fioke</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Proširivo</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Preskoči stanje otvaranja</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Spreči odbacivanje klikom van dijaloga</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Prikaži regulator</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Naslov</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Tekst opisa alatke</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Dodirnite za opis alatke prilagođenog sadržaja</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Gornji početni ekran </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Gornji kraj </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Donji Start</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Donji kraj</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Po sredini </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Prilagođeni centar</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Za ispravke za napomene uz izdanje,</string>
    <string name="click_here">kliknite ovde.</string>
    <string name="open_source_cross_platform">Otvorite izvorni sistem dizajna na više platformi.</string>
    <string name="intuitive_and_powerful">Intuitivno i moćno.</string>
    <string name="design_tokens">Tokeni dizajna</string>
    <string name="release_notes">Napomene uz izdanje</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">Veza za GitHub spremište</string>
    <string name="report_issue">Prijavi problem</string>
    <string name="v1_components">V1 komponente</string>
    <string name="v2_components">V2 komponente</string>
    <string name="all_components">Sve</string>
    <string name="fluent_logo">Fluent logotip</string>
    <string name="new_badge">Novo</string>
    <string name="modified_badge">Izmenjeno</string>
    <string name="api_break_badge">API pauza</string>
    <string name="app_bar_more">Više</string>
    <string name="accent">Naglašavanje</string>
    <string name="appearance">Izgled</string>
    <string name="choose_brand_theme">Odaberite temu brenda:</string>
    <string name="fluent_brand_theme">Brend Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Odaberite izgled</string>
    <string name="appearance_system_default">Podrazumevana vrednost sistema</string>
    <string name="appearance_light">Svetlo</string>
    <string name="appearance_dark">Tamno</string>
    <string name="demo_activity_github_link">GitHub veza za aktivnosti demo verzije</string>
    <string name="control_tokens_details">Detalji kontrolnih tokena</string>
    <string name="parameters">Parametri</string>
    <string name="control_tokens">Kontrolni tokeni</string>
    <string name="global_tokens">Globalni tokeni</string>
    <string name="alias_tokens">Tokeni pseudonima</string>
    <string name="sample_text">Tekst</string>
    <string name="sample_icon">Ikona uzorka</string>
    <string name="color">Boja</string>
    <string name="neutral_color_tokens">Neutralni tokeni u boji</string>
    <string name="font_size_tokens">Tokeni veličine fonta</string>
    <string name="line_height_tokens">Tokeni visine linije</string>
    <string name="font_weight_tokens">Tokeni za debljinu fonta</string>
    <string name="icon_size_tokens">Tokeni veličine ikona</string>
    <string name="size_tokens">Tokeni veličine</string>
    <string name="shadow_tokens">Tokeni u senci</string>
    <string name="corner_radius_tokens">Corner RadiusTokens</string>
    <string name="stroke_width_tokens">Tokeni širine poteza</string>
    <string name="brand_color_tokens">Tokeni boje brenda</string>
    <string name="neutral_background_color_tokens">Neutralni tokeni boja pozadine</string>
    <string name="neutral_foreground_color_tokens">Neutralni tokeni boja u prednjem planu</string>
    <string name="neutral_stroke_color_tokens">Neutralni tokeni poteza boja</string>
    <string name="brand_background_color_tokens">Tokeni boje pozadine brenda</string>
    <string name="brand_foreground_color_tokens">Tokeni brenda boja u prednjem planu</string>
    <string name="brand_stroke_color_tokens">Tokeni brenda poteza boje</string>
    <string name="error_and_status_color_tokens">Tokeni za grešku i status u boji</string>
    <string name="presence_tokens">Tokeni boja prisutnosti</string>
    <string name="typography_tokens">Tipografski tokeni</string>
    <string name="unspecified">Neodređeno</string>

</resources>