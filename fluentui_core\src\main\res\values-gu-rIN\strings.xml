<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">પ્રાથમિક</string>
    <string name="fluentui_secondary">દ્વિતીયક</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">કાઢી નાખો</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">પસંદ કરેલ છે</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">પસંદ કરેલ નથી</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">આઇકૉન</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">આઇકૉન</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">અક્ષમ કર્યું</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">ક્રિયા બટન</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">સક્ષમ કરેલ છે</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">પત્રક બંધ કરો</string>
    <!-- Describes close action -->
    <string name="fluentui_close">બંધ કરો</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">રદ કરો</string>
    <!--name of the icon -->
    <string name="fluentui_search">શોધો</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">માઇક્રોફોન</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">ટેક્સ્ટ સાફ કરો</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">પાછા જાઓ</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">સક્રિય કર્યું</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">નિષ્ક્રિય કર્યું</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">તટસ્થ</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">બ્રાન્ડ</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">કૉન્ટ્રાસ્ટ</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">ઍક્સેંટ</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">ચેતવણી</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">ભય</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">એક ભૂલ આવી છે.</string>
    <string name="fluentui_error">ભૂલ</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">સંકેત</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">શેવરોન</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">બાહ્યરેખા</string>

    <string name="fluentui_action_button_icon">ક્રિયા બટન આઇકૉન</string>
    <string name="fluentui_center">ટેક્સ્ટ મધ્યમાં કરો</string>
    <string name="fluentui_accessory_button">એસેસરી બટન્સ</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">રેડિયો બટન</string>
    <string name="fluentui_label">લેબલ</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">વિસ્તૃત</string>
    <string name="fluentui_collapsed">સંક્ષિપ્ત</string>

    <!--types of control -->
    <string name="fluentui_large">મોટું</string>
    <string name="fluentui_medium">મધ્યમ</string>
    <string name="fluentui_small">નાનું</string>
    <string name="fluentui_password_mode">પાસવર્ડ મોડ</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">ઉપશીર્ષક</string>
    <string name="fluentui_assistive_text">સહાયક ટેક્સ્ટ</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">શીર્ષક</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">ટૂંકું</string>"
    <string name="fluentui_long">લાંબું</string>"
    <string name="fluentui_indefinite">અનિશ્ચિત સમય</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">બટન દબાવ્યું</string>
    <string name="fluentui_dismissed">કાઢી નાખ્યું</string>
    <string name="fluentui_timeout">સમય સમાપ્ત</string>
    <string name="fluentui_left_swiped">ડાબી બાજુ સ્વાઇપ કર્યું</string>
    <string name="fluentui_right_swiped">જમણી બાજુ સ્વાઇપ કર્યું</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">ટેક્સ્ટ</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">સંખ્યા</string>
    <string name="fluentui_keyboard_phone">ફોન</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">ઈમેલ</string>
    <string name="fluentui_keyboard_password">પાસવર્ડ</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">દશાંશ</string>
</resources>