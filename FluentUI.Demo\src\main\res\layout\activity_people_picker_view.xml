<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/people_picker_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:divider="@drawable/ms_row_divider"
    android:orientation="vertical"
    android:showDividers="middle">

    <com.microsoft.fluentui.peoplepicker.PeoplePickerView
        android:id="@+id/people_picker_select"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:fluentui_label="@string/people_picker_select_example"
        app:fluentui_personaChipClickStyle="select" />

    <com.microsoft.fluentui.peoplepicker.PeoplePickerView
        android:id="@+id/people_picker_select_deselect"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:fluentui_label="@string/people_picker_select_deselect_example"
        app:fluentui_characterThreshold="3"
        app:fluentui_personaChipClickStyle="select_deselect" />

</LinearLayout>