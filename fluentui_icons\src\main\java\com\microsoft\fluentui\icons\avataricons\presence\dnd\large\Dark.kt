package com.microsoft.fluentui.icons.avataricons.presence.dnd.large

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import com.microsoft.fluentui.icons.avataricons.presence.dnd.LargeGroup

val LargeGroup.Dark: ImageVector
    get() {
        if (_dark != null) {
            return _dark!!
        }
        _dark = Builder(name = "Dark", defaultWidth = 20.0.dp, defaultHeight = 20.0.dp,
                viewportWidth = 20.0f, viewportHeight = 20.0f).apply {
            path(fill = SolidColor(Color(0xFF000000)), stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(10.0f, 10.0f)
                moveToRelative(-9.0f, 0.0f)
                arcToRelative(9.0f, 9.0f, 0.0f, true, true, 18.0f, 0.0f)
                arcToRelative(9.0f, 9.0f, 0.0f, true, true, -18.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0xFFD74553)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(10.0f, 18.0f)
                curveTo(14.4183f, 18.0f, 18.0f, 14.4183f, 18.0f, 10.0f)
                curveTo(18.0f, 5.5817f, 14.4183f, 2.0f, 10.0f, 2.0f)
                curveTo(5.5817f, 2.0f, 2.0f, 5.5817f, 2.0f, 10.0f)
                curveTo(2.0f, 14.4183f, 5.5817f, 18.0f, 10.0f, 18.0f)
                close()
                moveTo(7.249f, 9.0f)
                horizontalLineTo(12.7499f)
                curveTo(13.3022f, 9.0f, 13.7499f, 9.4477f, 13.7499f, 10.0f)
                curveTo(13.7499f, 10.5523f, 13.3022f, 11.0f, 12.7499f, 11.0f)
                horizontalLineTo(7.249f)
                curveTo(6.6967f, 11.0f, 6.249f, 10.5523f, 6.249f, 10.0f)
                curveTo(6.249f, 9.4477f, 6.6967f, 9.0f, 7.249f, 9.0f)
                close()
            }
        }
                .build()
        return _dark!!
    }

private var _dark: ImageVector? = null
