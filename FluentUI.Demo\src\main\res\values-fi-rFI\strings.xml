<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name"><PERSON><PERSON><PERSON> k<PERSON>ym<PERSON>n esittely</string>
    <string name="app_title"><PERSON><PERSON><PERSON> käyttöliittymä</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Valittu %s</string>
    <string name="app_modifiable_parameters">Muokattavat parametrit</string>
    <string name="app_right_accessory_view"><PERSON>ikea lis<PERSON>tenäkym<PERSON></string>

    <string name="app_style">Tyyli</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Kuvake painettu</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button"><PERSON><PERSON><PERSON> esittely</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Kuvake</string>
    <string name="actionbar_basic_radio_label">Perus</string>
    <string name="actionbar_position_bottom_radio_label">Alas</string>
    <string name="actionbar_position_top_radio_label">Ylä</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Toimintopalkin tyyppi</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Toimintopalkin sijainti</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar-tyyli</string>
    <string name="app_bar_subtitle">Alaotsikko</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Alareuna</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Siirtymiskuvaketta napsautetaan.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Merkintä</string>
    <string name="app_bar_layout_menu_settings">Asetukset</string>
    <string name="app_bar_layout_menu_search">Haku</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Vieritystoiminta: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Vaihda vieritystoimintaa</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Vaihda siirtymiskuvaketta</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Näytä avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Näytä takaisin -kuvake</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Piilota kuvake</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Näytä kuvake</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Vaihda hakupalkin asettelutyyliä</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Näytä lisälaitenäkymänä</string>
    <string name="app_bar_layout_searchbar_action_view_button">Näytä toimintonäkymänä</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Vaihda teemojen välillä (luo toiminnon uudelleen)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Vaihda teemaa</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Kohde</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Erittäin vieritettävä sisältö</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Ympyrätyyli</string>
    <string name="avatar_style_square">Neliötyyli</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Suuri</string>
    <string name="avatar_size_medium">Normaali</string>
    <string name="avatar_size_small">Pieni</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Erittäin suuri</string>
    <string name="avatar_size_xlarge_accessibility">Hyvin suuri</string>
    <string name="avatar_size_xsmall_accessibility">Erittäin pieni</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Suurin näytetty avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Ylivuoto-avatarien määrä</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Reunan tyyppi</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatar-ryhmä, jonka OverflowAvatarCount on asetettu, ei noudata näytettyjen avatarien enimmäismäärää.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Kasvopino</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Kasvokasa</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Ylivuotoa napsautetaan</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView’tä indeksissä %d napsautettiin</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Ilmoitusmerkki</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Piste</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Luettelo</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Hahmo</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Valokuvat</string>
    <string name="bottom_navigation_menu_item_news">Uutiset</string>
    <string name="bottom_navigation_menu_item_alerts">Ilmoitukset</string>
    <string name="bottom_navigation_menu_item_calendar">Kalenteri</string>
    <string name="bottom_navigation_menu_item_team">Tiimi</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Näytä tai piilota selitteet</string>
    <string name="bottom_navigation_three_menu_items_button">Näytä kolme valikkovaihtoehtoa</string>
    <string name="bottom_navigation_four_menu_items_button">Näytä neljä valikkokohdetta</string>
    <string name="bottom_navigation_five_menu_items_button">Näytä viisi valikkokohdetta</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Selitteet ovat %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Ota käyttöön hylkääminen sipaisemalla alas</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Näytä yksirivisillä kohteilla</string>
    <string name="bottom_sheet_with_double_line_items">Näytä kahden rivin kohteilla</string>
    <string name="bottom_sheet_with_single_line_header">Näytä yhden rivin otsikolla</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Näytä kaksoisviivaotsikon ja erotinten kanssa</string>
    <string name="bottom_sheet_dialog_button">Näytä</string>
    <string name="drawer_content_desc_collapse_state">Laajenna</string>
    <string name="drawer_content_desc_expand_state">Pienennä</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Valitse %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Pitkä napsautus %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Napsauta Hylkää</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Lisää kohde</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Päivitä kohde</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Hylkää</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Lisää</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Maininta</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Lihavoitu</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursivointi</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Alleviivaus</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Yliviivattu</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Kumoa</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Tee uudelleen</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Luettelomerkki</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Luettelo</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Linkki</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Kohdetta päivitetään</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Välistys</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Hylkää sijainti</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">ALOITA</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Ryhmän tila</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Kohteen tila</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Merkintä</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Merkintäkohdetta napsautetaan</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Vastaus</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Vastauskohdetta napsautetaan</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Lähetä edelleen</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Edelleenlähetyskohdetta napsautetaan</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Poista</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Kohteen poistaminen napsautetaan</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Ota valokuva</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kamerakohdetta napsautetaan</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Valikoima</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Näytä valokuvat</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Valikoimakohdetta napsautetaan</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videot</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Toista videot</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Videokohdetta napsautetaan</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Hallinta</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Mediakirjaston hallinta</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Kohteen hallintaa napsautetaan</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Sähköpostitoiminnot</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Asiakirjat</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Päivitetty viimeksi klo 14.14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Jaa</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Jaa kohde napsautetaan</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Siirrä</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Siirrä kohde napsautetaan</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Poista</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Kohteen poistaminen napsautetaan</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Tiedot</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Tietokohdetta napsautetaan</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Kello</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Kellokohdetta napsautetaan</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Hälytys</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Hälytyskohdetta napsautetaan</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Aikavyöhyke</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Aikavyöhykekohdetta napsautetaan</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Painikkeen eri näkymät</string>
    <string name="button">Painike</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Käytöstä poistettu painike – esimerkki</string>
    <string name="button_borderless">Reunaton painike – esimerkki</string>
    <string name="button_borderless_disabled">Reunaton käytöstä poistettu painike – esimerkki</string>
    <string name="button_large">Suuri painike – esimerkki</string>
    <string name="button_large_disabled">Suuri käytöstä poistettu painike – esimerkki</string>
    <string name="button_outlined">Ääriviivapainike – esimerkki</string>
    <string name="button_outlined_disabled">Käytöstä poistettu ääriviivapainike – esimerkki</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Valitse päivämäärä</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Yksittäinen päivämäärä</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Ei poimittua päivämäärää</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Näytä päivämäärävalitsin</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Näytä päivämäärän ja kellonajan valitsin, jossa päivämäärävälilehti on valittuna</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Näytä päivämäärän ja ajan valitsin, jossa aikavälilehti on valittuna</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Näytä päivämäärän ja kellonajan valitsin</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Päivämääräalue</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Alku:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Lopeta:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Ei valittua alkua</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Ei valittua päätä</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Valitse alkamispäivä</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Valitse päättymispäivä</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Päivämäärän aikaväli</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Valitse päivämäärän aikaväli</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Näytä valintaikkuna</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Näytä valikko</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Näytä valikkovalintaikkuna</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Ei alareunan himmennysvalintaikkunaa</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Näytä ylävalikko</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Ei alkuhäivytystä -valintaikkuna</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Näytä ankkurinäkymän ylävalintaikkuna</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Näytä ei otsikkoa -valintaikkuna</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Näytä otsikon yläreunan valintaikkunan alla</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Näytä oikea valikko</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Näytä vasen valikko</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Otsikko, ensisijainen teksti</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Alaotsikko, toissijainen teksti</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Mukautettu alaotsikon teksti</string>
    <!-- Footer -->
    <string name="list_item_footer">Alatunniste, kolmas teksti</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Yksirivinen luettelo harmaalla aliotsikon tekstillä</string>
    <string name="list_item_sub_header_two_line">Kaksirivinen luettelo</string>
    <string name="list_item_sub_header_two_line_dense">Kaksirivinen luettelo, jossa on tiheä väli</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Kahden rivin luettelo ja mukautettu toissijainen alaotsikkonäkymä</string>
    <string name="list_item_sub_header_three_line">Kolmirivinen luettelo, jossa on musta aliotsikkoteksti</string>
    <string name="list_item_sub_header_no_custom_views">Luettelokohteet, joilla ei ole mukautettuja näkymiä</string>
    <string name="list_item_sub_header_large_header">Luettelokohteet, joissa on suuret mukautetut näkymät</string>
    <string name="list_item_sub_header_wrapped_text">Luettelokohteet, joissa on rivitetty teksti</string>
    <string name="list_item_sub_header_truncated_text">Luettelokohteet, joiden teksti on katkaistu</string>
    <string name="list_item_sub_header_custom_accessory_text">Toiminto</string>
    <string name="list_item_truncation_middle">Keskimmäinen katkaisu.</string>
    <string name="list_item_truncation_end">Lopeta katkaiseminen.</string>
    <string name="list_item_truncation_start">Aloita katkaiseminen.</string>
    <string name="list_item_custom_text_view">Arvo</string>
    <string name="list_item_click">Napsautit luettelokohdetta.</string>
    <string name="list_item_click_custom_accessory_view">Napsautit mukautettua lisälaitenäkymää.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Napsautit aliotsikon mukautettua lisälaitenäkymää.</string>
    <string name="list_item_more_options">Lisää asetuksia</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Valitse</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Ei mitään</string>
    <string name="people_picker_delete_example">Poista</string>
    <string name="people_picker_custom_persona_description">Tässä esimerkissä näytetään, miten voit luoda mukautetun IPersona-objektin.</string>
    <string name="people_picker_dialog_title_removed">Poistit henkilötyypin:</string>
    <string name="people_picker_dialog_title_added">Lisäsit henkilötyypin:</string>
    <string name="people_picker_drag_started">Vetäminen alkoi</string>
    <string name="people_picker_drag_ended">Vetäminen päättyi</string>
    <string name="people_picker_picked_personas_listener">Henkilötyyppien kuuntelutoiminto</string>
    <string name="people_picker_suggestions_listener">Ehdotusten kuuntelutoiminto</string>
    <string name="people_picker_persona_chip_click">Napsautit kohdetta %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s vastaanottaja</item>
        <item quantity="other">%1$s vastaanottajaa</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Laajenna pysyvä alataulukko</string>
    <string name="collapse_persistent_sheet_button"> Piilota pysyvä alataulukko</string>
    <string name="show_persistent_sheet_button"> Näytä pysyvä alataulukko</string>
    <string name="new_view">Tämä on uusi näkymä</string>
    <string name="toggle_sheet_content">Näytä tai piilota alataulukon sisältö</string>
    <string name="switch_to_custom_content">Vaihda mukautettuun sisältöön</string>
    <string name="one_line_content">Yhden rivin alasivun sisältö</string>
    <string name="toggle_disable_all_items">Poista käytöstä kaikki kohteet tai poista ne käytöstä</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Lisää tai poista näkymä</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Muuta kutistettu korkeus</string>
    <string name="persistent_sheet_item_create_new_folder_title">Uusi kansio</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Uusi kansio -kohdetta napsautetaan</string>
    <string name="persistent_sheet_item_edit_title">Muokkaa</string>
    <string name="persistent_sheet_item_edit_toast">Muokattiin kohdetta napsautetaan</string>
    <string name="persistent_sheet_item_save_title">Tallenna</string>
    <string name="persistent_sheet_item_save_toast">Tallennuskohdetta napsautetaan</string>
    <string name="persistent_sheet_item_zoom_in_title">Lähennys</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Lähennä-kohdetta napsautetaan</string>
    <string name="persistent_sheet_item_zoom_out_title">Loitonnus</string>
    <string name="persistent_sheet_item_zoom_out_toast">Loitonna kohde napsautetaan</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Käytettävissä</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Suunnittelija</string>
    <string name="persona_subtitle_engineer">Insinööri</string>
    <string name="persona_subtitle_manager">Esihenkilö</string>
    <string name="persona_subtitle_researcher">Researcher</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (pitkä tekstiesimerkki katkaisun testaamista varten)</string>
    <string name="persona_view_description_xxlarge">XXLarge-avatar, jossa on kolme tekstiriviä</string>
    <string name="persona_view_description_large">Suuri avatar, jossa on kaksi tekstiriviä</string>
    <string name="persona_view_description_small">Pieni avatar, jossa on yksi tekstirivi</string>
    <string name="people_picker_hint">Ei mitään, vihje näkyvissä</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Käytöstä poistettu henkilötyyppisiru</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Virhe-henkilötyyppisiru</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Henkilötyyppisiru ilman sulkemiskuvaketta</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Perustyyppisiru</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Napsautit valittua henkilötyyppisirua.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Jaa</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Seuraa</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Kutsu henkilöitä</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Päivitä sivu</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Avaa selaimessa</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Tämä on monirivinen ponnahdusvalikko. Rivien enimmäismääräksi on määritetty kaksi, loput tekstistä katkaistaan.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Kaikki uutiset</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Tallennetut uutiset</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Uutiset sivustoista</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Ilmoita työajan ulkopuolella</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Ilmoita, kun työpöytä ei ole aktiivinen</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Napsautit kohdetta:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Yksinkertainen valikko</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Yksinkertainen valikko2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Valikko, jossa on yksi valittavissa oleva kohde ja jakaja</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Valikko, jossa on kaikki valittavissa olevat kohteet, kuvakkeet ja pitkä teksti</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Näytä</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Kehämäinen edistyminen</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20 dp</string>
    <string name="circular_progress_small">Pieni</string>
    <string name="circular_progress_small_size">24 dp</string>
    <string name="circular_progress_medium">Normaali</string>
    <string name="circular_progress_medium_size">36 dp</string>
    <string name="circular_progress_large">Suuri</string>
    <string name="circular_progress_large_size">44 dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineaarinen edistyminen</string>
    <string name="linear_progress_size">4 dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Ei selvitettävissä</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Määritetty</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofonin vastakutsu</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Automaattinen korjaus</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofoni painettu</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Oikea näkymä painettuna</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Näppäimistöhaku painettu</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Näytä välipalapalkki</string>
    <string name="fluentui_dismiss_snackbar">Hylkää välipalapalkki</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Toiminto</string>
    <string name="snackbar_action_long">Pitkä tekstitoiminto</string>
    <string name="snackbar_single_line">Yksirivinen välipalapalkki</string>
    <string name="snackbar_multiline">Tämä on monirivinen naposteltava palkki. Rivien enimmäismääräksi on määritetty kaksi, loput tekstistä katkaistaan.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Tämä on ilmoitusten naposteltavan palkki. Sitä käytetään uusien ominaisuuksien viestimiseen.</string>
    <string name="snackbar_primary">Tämä on ensisijainen välipalapatukka.</string>
    <string name="snackbar_light">Tämä on kevyt välipalapalkki.</string>
    <string name="snackbar_warning">Tämä on varoitusvälipalapalkki.</string>
    <string name="snackbar_danger">Tämä on vaarallinen naposteltava.</string>
    <string name="snackbar_description_single_line">Lyhyt kesto</string>
    <string name="snackbar_description_single_line_custom_view">Pitkä kesto ja kehämäinen edistyminen pienenä mukautettuna näkymänä</string>
    <string name="snackbar_description_single_line_action">Lyhyt kesto ja toiminto</string>
    <string name="snackbar_description_single_line_action_custom_view">Lyhyt kesto toiminnon ja normaalin mukautetun näkymän avulla</string>
    <string name="snackbar_description_single_line_custom_text_color">Lyhyt kesto mukautetulla tekstin värillä</string>
    <string name="snackbar_description_multiline">Pitkä kesto</string>
    <string name="snackbar_description_multiline_custom_view">Pitkä kesto ja pieni mukautettu näkymä</string>
    <string name="snackbar_description_multiline_action">Määrittämätön kesto toiminto- ja tekstipäivityksillä</string>
    <string name="snackbar_description_multiline_action_custom_view">Lyhyt kesto toiminnon ja normaalin mukautetun näkymän avulla</string>
    <string name="snackbar_description_multiline_action_long">Lyhyt kesto ja pitkä toimintoteksti</string>
    <string name="snackbar_description_announcement">Lyhyt kesto</string>
    <string name="snackbar_description_updated">Tämä teksti on päivitetty.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Näytä välipalapalkki</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Yksinkertainen viiva</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Moniviivainen</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Ilmoitustyyli</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Ensisijainen tyyli</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Vaalea tyyli</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Varoitustyyli</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Vaaran tyyli</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Aloitus</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Posti</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Asetukset</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Ilmoitus</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Lisää</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Tekstin tasaus</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Pystysuuntainen</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vaakasuuntainen</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Ei tekstiä</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Välilehtikohteet</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Otsikko</string>
    <string name="cell_sample_description">Kuvaus</string>
    <string name="calculate_cells">100 solun lataaminen/laskeminen</string>
    <string name="calculate_layouts">Lataa tai laske 100 asettelua</string>
    <string name="template_list">Malliluettelo</string>
    <string name="regular_list">Tavallinen luettelo</string>
    <string name="cell_example_title">Otsikko: solu</string>
    <string name="cell_example_description">Kuvaus: Muuta suuntaa napauttamalla</string>
    <string name="vertical_layout">Pystysuuntainen asettelu</string>
    <string name="horizontal_layout">Vaakasuuntainen asettelu</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Vakiovälilehti, 2-segmenttiä</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Vakiovälilehti 3-segmentti</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Vakiovälilehti 4-segmentti</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Vakiovälilehti ja Pager</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Vaihda sarkainta</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Pillerit-välilehti </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Napauta työkaluvihjettä varten</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Napauta, jos haluat mukautetun kalenterin työkaluvihjeen</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Napauta Mukautettu väri -työkaluvihjettä</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Hylkää työkaluvihje napauttamalla</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Näytä mukautetun näkymän työkaluvihje napauttamalla</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Suosituimman mukautetun värin työkaluvihje</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Yläpään työkaluvihje ja 10 dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Alakäynnistystyökaluvihje</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Alapään työkaluvihje ja 10 dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Hylkää työkaluvihjeen sisällä</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Työkaluvihje hylätty</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Otsikko on vaalea 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Otsikko 1 on lievästi lihavoitu 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Otsikko 2 on normaali 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Otsikko on tavallinen 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Alaotsikko 1 on tavallinen 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Alaotsikko 2 on lievästi lihavoitu 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Runko 1 on normaali 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Runko 2 on lievästi lihavoitu 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Kuvateksti on normaali 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-versio: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Kohde %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Kansio</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Napsautettu</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Rakennusteline</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB laajennettu</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB tiivistetty</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Päivitä luettelo napsauttamalla</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Avaa valikko</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Valikkokohde</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Siirtymä X (dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Siirtymä Y (dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Sisällön teksti</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Toista sisältöteksti</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Valikon leveys muuttuu sisältötekstin suhteen. Enimmäisarvo
        leveydelle on rajoitettu 75 prosenttiin näytön koosta. Sisällön reunusta sivulta ja alhaalta hallitaan tunnuksen avulla. Sama sisältöteksti toistuu ja vaihtelee
        korkeutta.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Avaa valikko</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Peruskortti</string>
    <!-- UI Label for Card -->
    <string name="file_card">Tiedostokortti</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Ilmoituskortti</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Satunnainen käyttöliittymä</string>
    <!-- UI Label for Options -->
    <string name="card_options">Asetukset</string>
    <!-- UI Label for Title -->
    <string name="card_title">Otsikko</string>
    <!-- UI Label for text -->
    <string name="card_text">Teksti</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Aliteksti</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Tämän palkin toissijainen kopio voidaan rivittää tarvittaessa kahteen riviin.</string>
    <!-- UI Label Button -->
    <string name="card_button">Painike</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Näytä valintaikkuna</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Hylkää valintaikkuna napsauttamalla sen ulkopuolelta</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Hylkää valintaikkuna painamalla Takaisin</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Valintaikkuna hylätty</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Peruuta</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Valintaikkuna on pieni ikkuna, jossa käyttäjää kehotetaan tekemään päätös tai antamaan lisätietoja.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Avaa siirtymisvalikko</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Laajenna siirtymisvalikko</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Sulje siirtymisvalikko</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Valitse valikon tyyppi</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Ylä</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Koko valikko näkyy näkyvällä alueella.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Ala</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Koko valikko näkyy näkyvällä alueella. Sipaisu ylöspäin liike selaa sisältöä. Laajennettava valikko, laajenna vetokahvalla.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Vasen liuku yli</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Valikko liuku näkyvälle alueelle vasemmalta puolelta.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Oikea liuku yli</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Valikko liuku näkyvälle alueelle oikealta puolelta.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Alareuna liuku yli</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Valikko liukuu näkyvällä alueelle näytön alareunasta. Sipaisu ylöspäin liike laajennettavassa valikossa tuo sen muut osat näkyvälle alueelle ja vierittää sitten.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim näkyvissä</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Valitse valikon sisältö</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Koko näytön kokoinen vieritettävä sisältö</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Yli puolet näytön sisällöstä</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Alle puolet näytön sisällöstä</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Sisällön dynaaminen koko</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Sisäkkäinen valikon sisältö</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Laajennettava</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Ohita avaustila</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Estä Scrim-napsautuksen ohittaminen</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Näytä kahva</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Otsikko</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Työkaluvihjeen teksti</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Näytä mukautetun sisällön työkaluvihje napauttamalla</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Ylin aloitus </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Yläosa </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Alareunan alku </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Alaosa </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Keskitä </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Mukautettu keskikohta</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Julkaisutietojen päivityksiä varten </string>
    <string name="click_here">napsauta tätä.</string>
    <string name="open_source_cross_platform">Avoimen lähdekoodin käyttöympäristöjen välinen suunnittelujärjestelmä.</string>
    <string name="intuitive_and_powerful">Intuitiivinen ja Tehokas.</string>
    <string name="design_tokens">Mallitunnukset</string>
    <string name="release_notes">Julkaisutiedot</string>
    <string name="github_repo">GitHub-säilö</string>
    <string name="github_repo_link">GitHub-säilön linkki</string>
    <string name="report_issue">Ilmoita ongelmasta</string>
    <string name="v1_components">V1-osat</string>
    <string name="v2_components">V2-osat</string>
    <string name="all_components">Kaikki</string>
    <string name="fluent_logo">Fluent-logo</string>
    <string name="new_badge">Uusi</string>
    <string name="modified_badge">Muokattu</string>
    <string name="api_break_badge">Ohjelmointirajapinnan tauko</string>
    <string name="app_bar_more">Lisää</string>
    <string name="accent">Korostus</string>
    <string name="appearance">Ulkoasu</string>
    <string name="choose_brand_theme">Valitse bränditeema:</string>
    <string name="fluent_brand_theme">Fluent Brand</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Valitse ulkoasu</string>
    <string name="appearance_system_default">Järjestelmän oletus</string>
    <string name="appearance_light">Vaalea</string>
    <string name="appearance_dark">Tumma</string>
    <string name="demo_activity_github_link">Esittelyaktiviteetin GitHub-linkki</string>
    <string name="control_tokens_details">Hallintatunnusten tiedot</string>
    <string name="parameters">Parametrit</string>
    <string name="control_tokens">Ohjausobjektitunnukset</string>
    <string name="global_tokens">Yleiset tunnukset</string>
    <string name="alias_tokens">Aliastunnukset</string>
    <string name="sample_text">Teksti</string>
    <string name="sample_icon">Mallikuvake</string>
    <string name="color">Väri</string>
    <string name="neutral_color_tokens">Neutraalit väritunnukset</string>
    <string name="font_size_tokens">Fonttikoon tunnukset</string>
    <string name="line_height_tokens">Viivan korkeustunnukset</string>
    <string name="font_weight_tokens">Fontin painotunnukset</string>
    <string name="icon_size_tokens">Kuvakkeen kokotunnukset</string>
    <string name="size_tokens">Kokotunnukset</string>
    <string name="shadow_tokens">Varjotunnukset</string>
    <string name="corner_radius_tokens">Kulman sädetunnukset</string>
    <string name="stroke_width_tokens">Viivanleveyden tunnukset</string>
    <string name="brand_color_tokens">Brändin väritunnukset</string>
    <string name="neutral_background_color_tokens">Neutraalit taustaväritunnukset</string>
    <string name="neutral_foreground_color_tokens">Neutraalit edustaväritunnukset</string>
    <string name="neutral_stroke_color_tokens">Neutraalin viivan väritunnukset</string>
    <string name="brand_background_color_tokens">Brändin taustaväritunnukset</string>
    <string name="brand_foreground_color_tokens">Brändin edustaväritunnukset</string>
    <string name="brand_stroke_color_tokens">Brändinpiirron väritunnukset</string>
    <string name="error_and_status_color_tokens">Virhe- ja tilaväritunnukset</string>
    <string name="presence_tokens">Tavoitettavuusväritunnukset</string>
    <string name="typography_tokens">Typografiatunnukset</string>
    <string name="unspecified">Ei määritetty</string>

</resources>