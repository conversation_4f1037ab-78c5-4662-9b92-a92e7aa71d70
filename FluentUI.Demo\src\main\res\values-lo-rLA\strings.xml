<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI ເດໂມ</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">ເລືອກ %s ແລ້ວ</string>
    <string name="app_modifiable_parameters">ພາຣາມິເຕີທີ່ສາມາດແກ້ໄຂໄດ້</string>
    <string name="app_right_accessory_view">ມຸມມອງອຸປະກອນເສີມດ້ານຂວາ</string>

    <string name="app_style">ຮູບ​ແບບ</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">ໄອຄອນກົດແລ້ວ</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">ເລີ່ມເດໂມ</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">ມ້າໝຸນ</string>
    <string name="actionbar_icon_radio_label">ໄອຄອນ</string>
    <string name="actionbar_basic_radio_label">ພື້ນຖານ</string>
    <string name="actionbar_position_bottom_radio_label">ດ້ານລຸ່ມ</string>
    <string name="actionbar_position_top_radio_label">ເທິງສຸດ</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">ປະເພດ ActionBar</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ຕໍາແຫນ່ງ ActionBar</string>

    <!--AppBar-->
    <string name="app_bar_style">ຮູບແບບ AppBar</string>
    <string name="app_bar_subtitle">ຄຳແປ</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">ເສັ້ນຂອບດ້ານລຸ່ມ</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">ຄລິກໄອຄອນການນຳທາງແລ້ວ</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">ໝາຍ</string>
    <string name="app_bar_layout_menu_settings">ການຕັ້ງຄ່າ</string>
    <string name="app_bar_layout_menu_search">ຊອກຫາ</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">ລັກສະນະການເລື່ອນ: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">ສະຫຼັບພຶດຕິກຳການເລື່ອນ</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">ສະຫຼັບໄອຄອນການນຳທາງ</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">ສະແດງ​ຮູບແທນຕົວ</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">ສະແດງໄອຄອນດ້ານຫຼັງ</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">ເຊື່ອງໄອຄອນ</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">ສະແດງໄອຄອນ</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">ສະຫຼັບຮູບແບບໂຄງຮ່າງ SearchBar</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">ສະແດງເປັນມຸມມອງອຸປະກອນເສີມ</string>
    <string name="app_bar_layout_searchbar_action_view_button">ສະແດງເປັນມຸມມອງການດຳເນີນການ</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">ສະຫຼັບລະຫວ່າງຮູບແບບສີສັນ (ສ້າງກິດຈະກຳຄືນໃໝ່)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">ສະຫຼັບຮູບແບບ</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">ລາຍການ</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">ເນື້ອຫາພິເສດທີ່ສາມາດເລື່ອນໄດ້</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">ຮູບແບບວົງມົນ</string>
    <string name="avatar_style_square">ຮູບແບບສີ່ຫຼ່ຽມ</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">ໃຫຍ່</string>
    <string name="avatar_size_medium">ປານກາງ</string>
    <string name="avatar_size_small">ນ້ອຍ</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">ຂະໜາດໃຫຍ່ພິເສດສອງເທົ່າ</string>
    <string name="avatar_size_xlarge_accessibility">ຂໍ້​ຄວາມ​ໃຫຍ່​ພິ​ເສດ (220%)</string>
    <string name="avatar_size_xsmall_accessibility">ນ້ອຍເປັນພິເສດ</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">ຮູບແທນຕົວທີ່ສະແດງສູງສຸດ</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">ຈຳນວນ​ຮູບແທນຕົວຫຼາຍໂພດ</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">ປະເພດເສັ້ນຂອບ</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">ກຸ່ມ​ຮູບແທນຕົວທີ່ມີຊຸດ OverflowAvatarCount ຈະບໍ່ປະຕິບັດຕາມ​ຮູບແທນຕົວທີ່ສະແດງສູງສຸດ.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">ໜ້າຊ້ອນກັນ</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">ກອງໜ້າ</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">ຄລິກຫຼາຍໂພດ</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView ທີ່ດັດຊະນີ %d ຄລິກແລ້ວ</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">ປ້າຍການແຈ້ງເຕືອນ</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">ຈ້ຳ</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">ລາຍຊື່</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">ຕົວອັກສອນ</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">ຮູບພາບ</string>
    <string name="bottom_navigation_menu_item_news">ຂ່າວ</string>
    <string name="bottom_navigation_menu_item_alerts">ການເຕືອນ</string>
    <string name="bottom_navigation_menu_item_calendar">ປະຕິທິນ</string>
    <string name="bottom_navigation_menu_item_team">ທີມ</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">ສະຫຼັບປ້າຍ</string>
    <string name="bottom_navigation_three_menu_items_button">ສະແດງລາຍການສາມເມນູ</string>
    <string name="bottom_navigation_four_menu_items_button">ສະແດງສີ່ລາຍການເມນູ</string>
    <string name="bottom_navigation_five_menu_items_button">ສະແດງລາຍການເມນູຫ້າລາຍການ</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">ປ້າຍກຳກັບແມ່ນ %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">ເປີດໃຊ້ງານ ປັດລົງ ເພື່ອຍົກເລີກ</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">ສະແດງດ້ວຍລາຍການແຖວດຽວ</string>
    <string name="bottom_sheet_with_double_line_items">ສະແດງດ້ວຍລາຍການແຖວຄູ່</string>
    <string name="bottom_sheet_with_single_line_header">ສະແດງດ້ວຍສ່ວນຫົວແຖວດຽວ</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">ສະ​ແດງດ້ວຍສ່ວນຫົວຂອງແຖວຄູ່ ແລະ ​ຕົວ​ແບ່ງ</string>
    <string name="bottom_sheet_dialog_button">ສະແດງ</string>
    <string name="drawer_content_desc_collapse_state">ຂະຫຍາຍ</string>
    <string name="drawer_content_desc_expand_state">ຫຍໍ້ລົງ</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">ຄ​ລິກ %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">ກົດຄ້າງໄວ້ %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">ຄລິກ ປ່ອຍໄປ</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">ແຊກລາຍການ</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">ອັບເດດລາຍການ</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">ປ່ອຍໄປ</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">ເພີ່ມ</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">ກ່າວເຖິງ</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">ຕົວໜາ</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">ຕົວເນີ້ງ</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">ຂີດກ້ອງ</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">ຂີດຂ້າ</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">ຍົກເລີກ</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">ເຮັດຄືນ</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">ສັນຍາລັກຫົວຂໍ້</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">ລາຍຊື່</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">ລິ້ງ</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">ອັບເດດລາຍການ</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">ໄລຍະຫ່າງ</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">ປິດຕຳແໜ່ງໄວ້</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">ເລີ່ມຕົ້ນ</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">ສິ້ນສຸດ</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">ພື້ນທີ່ກຸ່ມ</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">ພື້ນທີ່ລາຍການ</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">ໝາຍ</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">ຄລິກລາຍການທຸງແລ້ວ</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">ຕອບກັບ</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">ຕອບກັບລາຍການໂມງແລ້ວ</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">ສົ່ງຕໍ່</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">ຄລິກລາຍການສົ່ງຕໍ່</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">ລຶບ</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">ລຶບລາຍການແບ່ງປັນ</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">ຮູບແທນຕົວ</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">ກ້ອງຖ່າຍຮູບ</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">ຖ່າຍຮູບ</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">ຄລິກລາຍການກ້ອງຖ່າຍຮູບແລ້ວ</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">ແກເລີຣີ</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">ເບິ່ງຮູບພາບຂອງທ່ານ</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">ຄລິກລາຍການແກເລີຣີແລ້ວ</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">ວິດີໂອ</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">ຫຼິ້ນວິດີໂອຂອງທ່ານ</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">ຄລິກລາຍການວິດີໂອແລ້ວ</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">ຈັດການ</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">ຈັດ​ການຄັງເກັບມ້ຽນສື່ຂອງທ່ານ</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">ຈັດການລາຍການຄລິກແລ້ວ</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">ຄຳສັ່ງອີເມວ</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">ເອກະສານ</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">ອັບເດດຫຼ້າສຸດ 2:14PM</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">ແຊຣ໌</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">ຄລິກລາຍການແບ່ງປັນແລ້ວ</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">ຍ້າຍ</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">ຍ້າຍລາຍການແບ່ງປັນແລ້ວ</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">ລຶບ</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">ລຶບລາຍການແບ່ງປັນ</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">ຂໍ້ມູນ</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">ຄລິກລາຍການຂໍ້ມູນແລ້ວ</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">ໂມງ</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">ຄລິກລາຍການໂມງແລ້ວ</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">ໂມງປຸກ</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">ຄລິກລາຍການປຸກແລ້ວ</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">ເຂດເວລາ</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">ຄລິກລາຍການເຂດເວລາ</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">ມຸມມອງຕ່າງໆຂອງປຸ່ມ</string>
    <string name="button">ປຸ່ມ</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">ຕົວຢ່າງປຸ່ມທີ່ປິດໃຊ້ງານ</string>
    <string name="button_borderless">ຕົວຢ່າງປຸ່ມແບບບໍ່ມີເສັ້ນຂອບ</string>
    <string name="button_borderless_disabled">ຕົວຢ່າງປຸ່ມທີ່ປິດໃຊ້ງານແບບບໍ່ມີເສັ້ນຂອບ</string>
    <string name="button_large">ຕົວຢ່າງປຸ່ມຂະໜາດໃຫຍ່</string>
    <string name="button_large_disabled">ຕົວຢ່າງປຸ່ມທີ່ປິດໃຊ້ງານຂະໜາດໃຫຍ່</string>
    <string name="button_outlined">ຕົວຢ່າງປຸ່ມທີ່ມີໂຄງຮ່າງ</string>
    <string name="button_outlined_disabled">ຕົວຢ່າງປຸ່ມທີ່ປິດໃຊ້ງານທີ່ມີໂຄງຮ່າງ</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">ເລືອກວັນທີ</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">ວັນດຽວ</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">ບໍ່ໄດ້ເລືອກວັນທີ</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">ສະແດງຕົວເລືອກເອົາວັນທີ</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">ສະແດງຕົວເລືອກວັນທີທີ່ມີແຖບວັນທີທີ່ເລືອກ</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">ສະແດງຕົວເລືອກວັນທີກັບແຖບເວລາທີ່ເລືອກ</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">ສະແດງຕົວເລືອກວັນເວລາ</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">ຊ່ວງວັນທີ</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">ເລີ່​ມຕົ້ນ:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">ສິ້ນສຸດ:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">ບໍ່ມີການເລືອກເລີ່ມຕົ້ນ</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">ບໍ່​​ເລືອກ​ສິ້ນສຸດ</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">ເລືອກວັນທີເລີ່ມຕົ້ນ</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">ເລືອກວັນທີສິ້ນສຸດ</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">ຊ່ວງເວລາວັນທີ</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">ເລືອກຊ່ວງເວລາວັນທີ</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">ສະແດງກ່ອງຂໍ້ຄວາມ</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">ສະແດງລິ້ນຊັກ</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">ສະແດງກ່ອງຂໍ້ຄວາມລິ້ນຊັກ</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">ບໍ່ມີກ່ອງຂໍ້ຄວາມລຸ່ມສຸດຈາງຫາຍໄປ</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">ສະແດງລິ້ນຊັກດ້ານເທິງ</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">ບໍ່ມີກ່ອງຂໍ້ຄວາມດ້ານເທິງຈາງຫາຍໄປ</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> ສະແດງກ່ອງຂໍ້ຄວາມມຸມມອງດ້ານເທິງສະໝໍ</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> ບໍ່ສະແດງກ່ອງຂໍ້ຄວາມເທິງສຸດ</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> ສະແດງກ່ອງຂໍ້ຄວາມຫົວຂໍ້ທາງລຸ່ມ</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">ສະແດງລິ້ນຊັກດ້ານຂວາ</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">ສະແດງລິ້ນຊັກດ້ານຊ້າຍ</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">ຫົວຂໍ້, ຂໍ້ຄວາມຫຼັກ</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">ຄຳແປ, ຂໍ້ຄວາມຮອງ</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">ຂໍ້ຄວາມຄຳແປທີ່ກຳນົດເອງ</string>
    <!-- Footer -->
    <string name="list_item_footer">ສ່ວນທ້າຍ, ຂໍ້​ຄວາມ​ຊັ້ນ​ສູງ​</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">ລາຍການແຖວດຽວທີ່ມີຂໍ້ຄວາມຫົວຂໍ້ຍ່ອຍສີເທົາ</string>
    <string name="list_item_sub_header_two_line">ລາຍການສອງແຖວ</string>
    <string name="list_item_sub_header_two_line_dense">ລາຍການສອງແຖວທີ່ມີຊ່ອງຫວ່າງຫນາແຫນ້ນ</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">ລາຍການສອງແຖວທີ່ມີມຸມມອງຄຳແປສຳຮອງທີ່ກຳນົດເອງ</string>
    <string name="list_item_sub_header_three_line">ລາຍການສາມແຖວທີ່ມີຂໍ້ຄວາມຫົວຂໍ້ຍ່ອຍສີດໍາ</string>
    <string name="list_item_sub_header_no_custom_views">ລາຍການລາຍຊື່ທີ່ບໍ່ມີມຸມມອງແບບກຳນົດເອງ</string>
    <string name="list_item_sub_header_large_header">ລາຍການລາຍຊື່ທີ່ມີມຸມມອງແບບກຳນົດເອງຂະໜາດໃຫຍ່</string>
    <string name="list_item_sub_header_wrapped_text">ລາຍການລາຍຊື່ດ້ວຍຂໍ້ຄວາມທີ່ຫໍ່ແລ້ວ</string>
    <string name="list_item_sub_header_truncated_text">ລາຍການລາຍຊື່ທີ່ມີຂໍ້ຄວາມຖືກຕັດອອກ</string>
    <string name="list_item_sub_header_custom_accessory_text">ການດຳເນີນການ</string>
    <string name="list_item_truncation_middle">ຕັດເຄິ່ງກາງ.</string>
    <string name="list_item_truncation_end">ສິ້ນສຸດການຕັດ.</string>
    <string name="list_item_truncation_start">ເລີ່ມຕົ້ນການຕັດ.</string>
    <string name="list_item_custom_text_view">ຄ່າ</string>
    <string name="list_item_click">ທ່ານຄລິກໃສ່ລາຍການລາຍຊື່.</string>
    <string name="list_item_click_custom_accessory_view">ທ່ານຄລິກໃສ່ມຸມມອງອຸປະກອນທີ່ກໍານົດເອງ.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">ທ່ານຄລິກໃສ່ຫົວຂໍ້ຍ່ອຍມຸມມອງອຸປະກອນທີ່ກໍານົດເອງ.</string>
    <string name="list_item_more_options">ຕົວເລືອກເພີ່ມເຕີມ</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">ເລືອກ</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">ບໍ່ມີຫຍັງ</string>
    <string name="people_picker_delete_example">ລຶບ</string>
    <string name="people_picker_custom_persona_description">ຕົວຢ່າງນີ້ສະແດງໃຫ້ເຫັນວິທີການສ້າງວັດຖຸ IPsona ແບບກໍາຫນົດເອງ.</string>
    <string name="people_picker_dialog_title_removed">ທ່ານໄດ້ລຶບບຸກຄົນອອກ:</string>
    <string name="people_picker_dialog_title_added">ທ່ານເພີ່ມບຸກຄົນ:</string>
    <string name="people_picker_drag_started">ເລີ່ມລາກແລ້ວ</string>
    <string name="people_picker_drag_ended">ການລາກສິ້ນສຸດລົງແລ້ວ</string>
    <string name="people_picker_picked_personas_listener">ຄົນຟັງ</string>
    <string name="people_picker_suggestions_listener">ຜູ້​ຟັງ​ການແນະນໍາ</string>
    <string name="people_picker_persona_chip_click">ທ່ານໄດ້ຄລິກໃສ່ %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="other">ຜູ້ຮັບ %1$s ຄົນ</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">ຂະຫຍາຍ BottomSheet ແບບຖາວອນ</string>
    <string name="collapse_persistent_sheet_button"> ​ເຊື່ອງ BottomSheet ແບບຖາວອນ</string>
    <string name="show_persistent_sheet_button"> ສະແດງ BottomSheet ແບບຖາວອນ</string>
    <string name="new_view">ນີ້ແມ່ນມຸມມອງໃໝ່</string>
    <string name="toggle_sheet_content">ສະຫຼັບເນື້ອຫາ BottomSheet</string>
    <string name="switch_to_custom_content">ປ່ຽນເປັນເນື້ອຫາແບບກຳນົດເອງ</string>
    <string name="one_line_content">ເນື້ອຫາແຖວລຸ່ມສຸດແຖວດຽວ</string>
    <string name="toggle_disable_all_items">ສະຫຼັບປິດໃຊ້ງານລາຍການທັງໝົດ</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">ມຸມມອງ ເພີ່ມ/ລຶບ</string>
    <string name="persistent_sheet_item_change_collapsed_height"> ປ່ຽນຄວາມສູງທີ່ຫຍໍ້ລົງ</string>
    <string name="persistent_sheet_item_create_new_folder_title">ໂຟ​ລ​ເດີໃໝ່</string>
    <string name="persistent_sheet_item_create_new_folder_toast">ຄລິກລາຍການໂຟນເດີໃໝ່ແລ້ວ</string>
    <string name="persistent_sheet_item_edit_title">ແກ້ໄຂ</string>
    <string name="persistent_sheet_item_edit_toast">ຄລິກແກ້ໄຂລາຍການ</string>
    <string name="persistent_sheet_item_save_title">ບັນທຶກ</string>
    <string name="persistent_sheet_item_save_toast">ຄລິກບັນທຶກລາຍການ</string>
    <string name="persistent_sheet_item_zoom_in_title">ຊູມເຂົ້າ</string>
    <string name="persistent_sheet_item_zoom_in_toast"> ຊູມເຂົ້າລາຍການຄລິກແລ້ວ</string>
    <string name="persistent_sheet_item_zoom_out_title">ຊູມອອກ</string>
    <string name="persistent_sheet_item_zoom_out_toast">ຄລິກລາຍການຊູມອອກແລ້ວ</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">ຫວ່າງ</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">ແຄທ ລາສສັນ</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">ເຄວິນ ສະເຕີຈີສ</string>
    <string name="persona_name_kristen_patterson">ຄຮິສເຕົນ ແພດເຕີສັນ</string>
    <string name="persona_name_lydia_bauer">ລີເດຍ ບາວເດີ</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">ໂມນາ ເຄົນ</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">ນັກອອກແບບ</string>
    <string name="persona_subtitle_engineer">ວິສະວະກອນ</string>
    <string name="persona_subtitle_manager">ຜູ້ຈັດການ</string>
    <string name="persona_subtitle_researcher">ຕົວຄົ້ນຄວ້າ</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (ຕົວຢ່າງຂໍ້ຄວາມຍາວເພື່ອທົດສອບການຕັດ)</string>
    <string name="persona_view_description_xxlarge">ຮູບແທນຕົວຂະຫນາດ XXLarge ທີ່ມີສາມແຖວຂອງຂໍ້ຄວາມ</string>
    <string name="persona_view_description_large">ຮູບແທນຕົວຂະຫນາດໃຫຍ່ທີ່ມີສອງແຖວຂອງຂໍ້ຄວາມ</string>
    <string name="persona_view_description_small">ຮູບແທນຕົວຂະຫນາດນ້ອຍໆທີ່ມີຂໍ້ຄວາມແຖວດ່ຽວ</string>
    <string name="people_picker_hint">ບໍ່​ມີ​ຄຳໃບ້ສະ​ແດງ​ໃຫ້​ເຫັນ​</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">ປິດໃຊ້ງານ Persona Chip ໄວ້</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">ຂໍ້ຜິດພາດ Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Persona Chip ທີ່ບໍ່ມີໄອຄອນປິດ</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Persona Chip ພື້ນຖານ</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">ທ່ານໄດ້ຄລິກໃສ່ Persona Chip ທີ່ເລືອກ.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">ແຊຣ໌</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">ຕິດຕາມ</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">ເຊີນ​ຄົນ</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">ໂຫຼດໜ້າຄືນໃໝ່</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">ເປີດໃນບຣາວເຊີ</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">ນີ້ແມ່ນເມນູຍ່ອຍແຖວເສັ້ນ. ແຖວສູງສຸດແມ່ນຕັ້ງເປັນສອງແຖວ, ສ່ວນທີ່ເຫຼືອຂອງຂໍ້ຄວາມຈະຕັດອອກ.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">ຂ່າວທັງຫມົດ</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">ບັນທຶກຂ່າວແລ້ວ</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">ຂ່າວຈາກເວັບໄຊ</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">ແຈ້ງເຕືອນນອກເວລາເຮັດວຽກ</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">ແຈ້ງເຕືອນເມື່ອບໍ່ໃຊ້ການງານຢູ່ໃນເດັສທັອບ</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">ທ່ານຄລິກໃສ່ລາຍການ:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">ເມນູງ່າຍໆ</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">ເມນູ2 ງ່າຍໆ</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">ເມນູທີ່ມີໜຶ່ງລາຍການທີ່ເລືອກໄດ້ ແລະ ຕົວແບ່ງ</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">ເມນູທີ່ມີລາຍການ, ໄອຄອນ ແລະຂໍ້ຄວາມຍາວທີ່ເລືອກໄດ້ທັງໝົດ</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">ສະແດງ</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">ຄວາມຄືບໜ້າວົງມົນ</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">ນ້ອຍ</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">ປານກາງ</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">ໃຫຍ່</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">ຄວາມຄືບໜ້າເສັ້ນຊື່</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">ບໍ່ຊັດເຈນ</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinate</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">ການເອີ້ນກັບໄມໂຄຣໂຟນ</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">ແກ້ໄຂໃຫ້ຖືກຕ້ອງອັດຕະໂນມັດ</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">ກົດໄມໂຄຣໂຟນແລ້ວ</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">ກົດມຸມມອງດ້ານຂວາ</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">ກົດຄົ້ນຫາແປ້ນພິມ</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">ສະແດງ Snackbar</string>
    <string name="fluentui_dismiss_snackbar">ປິດ Snackbar ໄວ້</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">ການດຳເນີນການ</string>
    <string name="snackbar_action_long">ການດຳເນີນການຂໍ້ຄວາມຍາວ</string>
    <string name="snackbar_single_line">Snackbar ແຖວດ່ຽວ</string>
    <string name="snackbar_multiline">ນີ້ແມ່ນ Snackbar ແຖວເສັ້ນ. ແຖວສູງສຸດແມ່ນຕັ້ງເປັນສອງແຖວ, ສ່ວນທີ່ເຫຼືອຂອງຂໍ້ຄວາມຈະຕັດອອກ.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">ນີ້ແມ່ນການປະກາດ Snackbar. ໃຊ້ສໍາລັບສື່ສານຄຸນສົມບັດໃຫມ່.</string>
    <string name="snackbar_primary">ນີ້ແມ່ນ Snackbar ຫຼັກ.</string>
    <string name="snackbar_light">ນີ້ແມ່ນ Snackbar ແບບເບົາ.</string>
    <string name="snackbar_warning">ນີ້ແມ່ນຄຳເຕືອນ Snackbar.</string>
    <string name="snackbar_danger">ນີ້ແມ່ນອັນຕະລາຍ Snackbar.</string>
    <string name="snackbar_description_single_line">ໄລຍະເວລາສັ້ນ</string>
    <string name="snackbar_description_single_line_custom_view">ໄລຍະເວລາຍາວທີ່ມີຄວາມຄືບຫນ້າແບບວົງເປັນມຸມມອງແບບກໍານົດເອງຂະຫນາດນ້ອຍ</string>
    <string name="snackbar_description_single_line_action">ໄລຍະເວລາສັ້ນໆທີ່ມີການດຳເນີນການ</string>
    <string name="snackbar_description_single_line_action_custom_view">ໄລຍະເວລາສັ້ນດ້ວຍການດຳເນີນການ ແລະ ມຸມມອງແບບກຳນົດເອງຂະໜາດກາງ</string>
    <string name="snackbar_description_single_line_custom_text_color">ໄລຍະເວລາສັ້ນທີ່ມີສີຂໍ້ຄວາມທີ່ປັບແຕ່ງ</string>
    <string name="snackbar_description_multiline">ໄລຍະເວລາຍາວ</string>
    <string name="snackbar_description_multiline_custom_view">ໄລຍະເວລາຍາວດ້ວຍມຸມມອງແບບກຳນົດເອງຂະໜາດນ້ອຍ</string>
    <string name="snackbar_description_multiline_action">ໄລຍະເວລາທີ່ບໍ່ມີກໍານົດດ້ວຍການດໍາເນີນການ ແລະ ການອັບເດດຂໍ້ຄວາມ</string>
    <string name="snackbar_description_multiline_action_custom_view">ໄລຍະເວລາສັ້ນດ້ວຍການດຳເນີນການ ແລະ ມຸມມອງແບບກຳນົດເອງຂະໜາດກາງ</string>
    <string name="snackbar_description_multiline_action_long">ໄລຍະເວລາສັ້ນທີ່ມີຂໍ້ຄວາມການດຳເນີນການຍາວ</string>
    <string name="snackbar_description_announcement">ໄລຍະເວລາສັ້ນ</string>
    <string name="snackbar_description_updated">ຂໍ້ຄວາມນີ້ໄດ້ຖືກປັບປຸງແລ້ວ.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">ສະແດງ Snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">ເສັ້ນດຽວ</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">ຫຼາຍແຖວ</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">ຮູບແບບການປະກາດ</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">ຮູບແບບຫຼັກ</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">ປະ​ເພດສີອ່ອນ</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">ຮູບແບບຄຳເຕືອນ</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">ຮູບແບບອັນຕະລາຍ</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">ໜ້າຫຼັກ</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">ອີເມວ</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">ການຕັ້ງຄ່າ</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">ການແຈ້ງເຕືອນ</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">ເພີ່ມເຕີມ</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">ການຈັດຂໍ້ຄວາມ</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">ລວງຕັ້ງ</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">ລວງນອນ</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">ບໍ່ມີຂໍ້ຄວາມ</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">ແຕະລາຍການ</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">ຊື່</string>
    <string name="cell_sample_description">ລາຍລະອຽດ</string>
    <string name="calculate_cells">ໂຫຼດ/ຄຳນວນ 100 ເຊວ</string>
    <string name="calculate_layouts">ໂຫຼດ/ຄຳນວນ 100 ໂຄງຮ່າງ</string>
    <string name="template_list">ລາຍການແມ່ແບບ</string>
    <string name="regular_list">ລາຍການປົກກະຕິ</string>
    <string name="cell_example_title">ຫົວຂໍ້: ເຊລ</string>
    <string name="cell_example_description">ຄຳອະທິບາຍ: ແຕະເພື່ອປ່ຽນທິດທາງ</string>
    <string name="vertical_layout">ໂຄງຮ່າງລວງຕັ້ງ</string>
    <string name="horizontal_layout">ໂຄງຮ່າງລວງນອນ</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">ແຖບມາດຕະຖານ 2-ສ່ວນ</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">ແຖບມາດຕະຖານ 3-ສ່ວນ</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">ແຖບມາດຕະຖານ 4-ສ່ວນ</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">ແຖບມາດຕະຖານທີ່ມີ ເພດ​ເຈີ</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">ສະຫຼັບແຖບ</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">ແຖບ ເມັດຢາ </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">ແຕະສໍາລັບເຄື່ອງມືແນະນຳ</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">ແຕະເພື່ອເຄື່ອງມືແນະນຳປະຕິທິນແບບກຳນົດເອງ</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">ແຕະໃສ່ເຄື່ອງມືແນະນຳສີທີ່ກໍາຫນົດເອງ</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">ແຕະເພື່ອປິດເຄື່ອງມືແນະນຳພາຍໃນ</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">ແຕະສຳລັບເຄື່ອງມືແນະນຳມຸມມອງແບບກຳນົດເອງ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">ເຄື່ອງມືແນະນຳສີທີ່ກໍາຫນົດເອງດ້ານເທິງ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">ແນະນຳເຄື່ອງມືດ້ານເທິງດ້ວຍ 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">ແນະນຳເຄື່ອງມືເລີ່ມ​ຕົ້ນດ້ານລຸ່ມ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">ແນະນຳເຄື່ອງມືດ້ານລຸ່ມດ້ວຍ 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">ປິດເຄື່ອງມືແນະນຳພາຍໃນ</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">ປິດເຄັດລັບເຄື່ອງມືໄວ້</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">ຫົວຂໍ້ຂ່າວແມ່ນ ໄຟ 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">ຫົວຂໍ້ 1 ແມ່ນປານກາງ 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">ຫົວຂໍ້ 2 ແມ່ນປົກກະຕິ 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">ຫົວຂໍ້ແມ່ນປົກກະຕິ 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">ຫົວ​ຂໍ້​ຍ່ອຍ 1 ແມ່ນ Regular 16sp ປົກກະຕິ</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">ຫົວ​ຂໍ້​ຍ່ອຍ 2 ແມ່ນ 16sp ປານກາງ</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">ສ່ວນເນື້ອຫາ 1 ແມ່ນ ທຳມະດາ 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">ສ່ວນເນື້ອຫາ 2 ແມ່ນ ປານກາງ 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">ຄຳ​ອະ​ທິ​ບາຍ​ພາບແມ່ນ 12sp ປົກກະຕິ</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">ເວີຊັນ SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">%d ລາຍການ</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">ໂຟນເດີ</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">ຄລິກແລ້ວ</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">ບ່ອນນັ່ງ</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">ຂະຫຍາຍ FAB ແລ້ວ</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">ຫຍໍ້ FAB ລົງແລ້ວ</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">ຄລິກເພື່ອລາຍການໂຫຼດຄືນໃໝ່</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">ເປີດແຖບ</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">ລາຍ​ການ​ເມ​ນູ</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">ອອບເຊັດ X (in dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">ອອບເຊັດ Y (in dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">ຂໍ້ຄວາມເນື້ອຫາ</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">ເຮັດຊ້ຳຂໍ້ຄວາມເນື້ອຫາ</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">ຄວາມກວ້າງຂອງເມນູຈະມີການປ່ຽນແປງກ່ຽວກັບຂໍ້ຄວາມເນື້ອຫາ. ຄວາມກວ້າງ
        ສູງສຸດຖືກຈຳກັດໄວ້ທີ່ 75% ຂອງຂະໜາດໜ້າຈໍ. ຂອບເນື້ອໃນຈາກດ້ານຂ້າງ ແລະ ດ້ານລຸ່ມແມ່ນຄວບຄຸມໂດຍໂທເຄັນ. ຂໍ້ຄວາມເນື້ອຫາດຽວກັນຈະຊໍ້າຄືນເພື່ອ
        ຄວາມສູງແຕກຕ່າງກັນ.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">ເປີດເມນູ</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">ບັດພື້ນຖານ</string>
    <!-- UI Label for Card -->
    <string name="file_card">ບັດໄຟລ໌</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">ບັດໄອຄອນປະກາດ</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">UI ແບບສຸ່ມ</string>
    <!-- UI Label for Options -->
    <string name="card_options">ທາງເລືອກ</string>
    <!-- UI Label for Title -->
    <string name="card_title">ຊື່</string>
    <!-- UI Label for text -->
    <string name="card_text">ຂໍ້ຄວາມ</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">ຂໍ້ຄວາມຍ່ອຍ</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">ສໍາເນົາຮອງສໍາລັບປ້າຍໂຄສະນານີ້ສາມາດຕັດເປັນສອງແຖວຖ້າຈໍາເປັນ.</string>
    <!-- UI Label Button -->
    <string name="card_button">ປຸ່ມ</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">ສະແດງກ່ອງໂຕ້ຕອບ</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">ຍົກເລີກກ່ອງໂຕ້ຕອບເມື່ອຄລິກຂ້າງນອກ</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">ຍົກເລີກກ່ອງໂຕ້ຕອບເມື່ອກົດກັບຄືນ</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">ຍົກເລີກກ່ອງໂຕ້ຕອບແລ້ວ</string>
    <!-- UI Label Cancel -->
    <string name="cancel">ຍົກເລີກ</string>
    <!-- UI Label Ok -->
    <string name="ok">ຕົກລົງ</string>
    <!-- A sample description -->
    <string name="dialog_description">ກ່ອງໂຕ້ຕອບເປັນໜ້າຕ່າງນ້ອຍໆທີ່ແຈ້ງເຕືອນຜູ້ໃຊ້ເຮັດການຕັດສິນໃຈ ຫຼື ປ້ອນຂໍ້ມູນເພີ່ມເຕີມ.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">ເປີດແຖບ</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">ຂະຫຍາຍແຖບ</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">ປິດແຖບ</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">ເລືອກປະເພດລິ້ນຊັກ</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">ເທິງສຸດ</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">ລິ້ນຊັກທັງໝົດຈະສະແດງຢູ່ໃນພື້ນທີ່ທີ່ເບິ່ງເຫັນໄດ້.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">ດ້ານລຸ່ມ</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">ລິ້ນຊັກທັງໝົດຈະສະແດງຢູ່ໃນພື້ນທີ່ທີ່ເຫັນໄດ້. ປັດເນື້ອຫາເລື່ອນການເຄື່ອນໄຫວຂຶ້ນ. ລີ້ນຊັກທີ່ສາມາດຂະຫຍາຍໄດ້ ຂະຫຍາຍອອກຜ່ານຕົວຈັບລາກ.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">ເລື່ອນໄປດ້ານຊ້າຍ</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">ເລື່ອນລີ້ນຊັກຂ້າມໄປຫາພື້ນທີ່ທີ່ເຫັນໄດ້ຈາກດ້ານຊ້າຍ.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">ເລື່ອນໄປດ້ານຂວາ</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">ເລື່ອນລີ້ນຊັກຂ້າມໄປຫາພື້ນທີ່ທີ່ເຫັນໄດ້ຈາກດ້ານຂວາ.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">ເລື່ອນໄປດ້ານລຸ່ມ</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">ເລື່ອນລີ້ນຊັກຂ້າມໄປຫາພື້ນທີ່ທີ່ເຫັນໄດ້ຈາກລຸ່ມສຸດຂອງໜ້າຈໍ. ເລື່ອນຂຶ້ນເທິງລິ້ນຊັກທີ່ສາມາດຂະຫຍາຍໄດ້ ເອົາສ່ວນທີ່ເຫຼືອຂອງໄປຫາພື້ນທີ່ທີ່ເບິ່ງເຫັນໄດ້ &amp; ຈາກນັ້ນເລື່ອນ.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">ເບິ່ງເຫັນໄດ້ຫນ້ອຍລົງ</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">ເລືອກເນື້ອຫາລີ້ນຊັກ</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">ເນື້ອຫາຂະໜາດເຕັມຈໍສາມາດເລື່ອນໄດ້</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">ເນື້ອຫາຫຼາຍກວ່າເຄິ່ງຫນຶ່ງຂອງຫນ້າຈໍ</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">ເນື້ອຫາໜ້ອຍກວ່າເຄິ່ງໜ້າຈໍ</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">ເນື້ອຫາຂະຫນາດໄດນາມິກ</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">ເນື້ອຫາລີ້ນຊັກທີ່ຊ້ອນກັນ</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">ຂະຫຍາຍໄດ້</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">ຂ້າມສະຖານະເປີດ</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">ປ້ອງກັນການຍົກເລີກໃນ Scrim Click</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">ສະແດງການຈັດການ</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">ຊື່</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">ຂໍ້ຄວາມຄຳແນະນຳເຄື່ອງມື</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">ແຕະສຳລັບຄຳແນະນຳເຄື່ອງມືເນື້ອຫາແບບກຳນົດເອງ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">ເລີ່ມຕົ້ນເທິງສຸດ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">ດ້ານເທິງສຸດ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">ເລີ່ມຕົ້ນດ້ານລຸ່ມ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">ດ້ານລຸ່ມສຸດ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">ເຄິ່ງກາງ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">ສູນແບບກຳນົດເອງ</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">ສຳລັບການອັບເດດກ່ຽວກັບບັນທຶກທີ່ອອກໃຫ້, </string>
    <string name="click_here">ຄລິກບ່ອນນີ້.</string>
    <string name="open_source_cross_platform">ລະບົບການອອກແບບຂ້າມແພລດຟອມແບບໂອເພນຊອດ.</string>
    <string name="intuitive_and_powerful">ໃຊ້ງານງ່າຍ &amp; ມີປະສິດທິພາບ.</string>
    <string name="design_tokens">ໂທເຄນການອອກແບບ</string>
    <string name="release_notes">ບັນທຶກທີ່ອອກໃຫ້</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">ລິ້ງ GitHub Repo</string>
    <string name="report_issue">ລາຍງານບັນຫາ</string>
    <string name="v1_components">ອົງປະກອບ V1</string>
    <string name="v2_components">ອົງປະກອບ V2</string>
    <string name="all_components">ທັງໝົດ</string>
    <string name="fluent_logo">ໂລໂກ້ Fluent</string>
    <string name="new_badge">ໃໝ່</string>
    <string name="modified_badge">ແກ້ໄຂແລ້ວ</string>
    <string name="api_break_badge">ຕົວແບ່ງ API</string>
    <string name="app_bar_more">ເພີ່ມເຕີມ</string>
    <string name="accent">ໂທນສີ</string>
    <string name="appearance">ລັກສະນະທີ່ປະກົດຂຶ້ນ</string>
    <string name="choose_brand_theme">ເລືອກເທມຍີ່ຫໍ້ຂອງທ່ານ:</string>
    <string name="fluent_brand_theme">ຍີ່ຫໍ້ Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">ເລືອກລັກສະນະທີ່ປະກົດຂຶ້ນ</string>
    <string name="appearance_system_default">ຄ່າເລີ່ມຕົ້ນລະບົບ</string>
    <string name="appearance_light">ສີອ່ອນ</string>
    <string name="appearance_dark">ມືດ</string>
    <string name="demo_activity_github_link">ລິ້ງ GitHub ສຳລັບກິດຈະກຳການສາທິດ</string>
    <string name="control_tokens_details">ລາຍລະອຽດໂທເຄນການຄວບຄຸມ</string>
    <string name="parameters">ພາຣາມິເຕີ</string>
    <string name="control_tokens">ໂທເຄນຄວບຄຸມ</string>
    <string name="global_tokens">ໂທເຄນທົ່ວໂລກ</string>
    <string name="alias_tokens">ໂທເຄນນາມແຝງ</string>
    <string name="sample_text">ຂໍ້ຄວາມ</string>
    <string name="sample_icon">ໄອຄອນຕົວຢ່າງ</string>
    <string name="color">ສີ</string>
    <string name="neutral_color_tokens">ໂທເຄນສີປານກາງ</string>
    <string name="font_size_tokens">ໂທເຄນຂະໜາດຕົວໜັງສື</string>
    <string name="line_height_tokens">ໂທເຄນຄວາມສູງຂອງເສັ້ນ</string>
    <string name="font_weight_tokens">ໂທເຄນນ້ຳໜັກຕົວໜັງສື</string>
    <string name="icon_size_tokens">ໂທເຄນຂະໜາດໄອຄອນ</string>
    <string name="size_tokens">ໂທເຄນຂະໜາດ</string>
    <string name="shadow_tokens">ໂທເຄນເງົາ</string>
    <string name="corner_radius_tokens">ໂທເຄນລັດສະໝີມຸມ</string>
    <string name="stroke_width_tokens">ໂທເຄນຄວາມກວ້າງເສັ້ນຂີດ</string>
    <string name="brand_color_tokens">ໂທເຄນສີຂອງຍີ່ຫໍ້</string>
    <string name="neutral_background_color_tokens">ໂທເຄນສີພື້ນຫຼັງປານກາງ</string>
    <string name="neutral_foreground_color_tokens">ໂທເຄນສີພື້ນໜ້າປານກາງ</string>
    <string name="neutral_stroke_color_tokens">ໂທເຄນສີເສັ້ນຂີດປານກາງ</string>
    <string name="brand_background_color_tokens">ໂທເຄນສີພື້ນຫຼັງຂອງຍີ່ຫໍ້</string>
    <string name="brand_foreground_color_tokens">ໂທເຄນສີພື້ນໜ້າຂອງຍີ່ຫໍ້</string>
    <string name="brand_stroke_color_tokens">ໂທເຄນສີເສັ້ນຂີດຂອງຍີ່ຫໍ້</string>
    <string name="error_and_status_color_tokens">ໂທເຄນຂໍ້ຜິດພາດ ແລະ ສີສະຖານະ</string>
    <string name="presence_tokens">ໂທເຄນສີຂອງສະຖານະ</string>
    <string name="typography_tokens">ໂທເຄນຕົວພິມ</string>
    <string name="unspecified">ບໍ່​ໄດ້ລະບຸ</string>

</resources>