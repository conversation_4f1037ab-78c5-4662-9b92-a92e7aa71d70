<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <path
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"
        android:pathData="M11.25,13L11.25,22L7.25,22C5.4551,22 4,20.5449 4,18.75L4,13L11.25,13ZM20,13L20,18.75C20,20.5449 18.5449,22 16.75,22L12.75,22L12.75,13L20,13ZM14.5,2C16.2949,2 17.75,3.4551 17.75,5.25C17.75,5.895 17.5621,6.4961 17.2381,7.0015L19.75,7C20.4404,7 21,7.4664 21,8.0417L21,10.9583C21,11.5336 20.4404,12 19.75,12L12.75,11.999L12.75,7L11.25,7L11.25,11.999L4.25,12C3.5596,12 3,11.5336 3,10.9583L3,8.0417C3,7.4664 3.5596,7 4.25,7L6.7619,7.0015C6.4379,6.4961 6.25,5.895 6.25,5.25C6.25,3.4551 7.7051,2 9.5,2C10.5055,2 11.4044,2.4567 12.0006,3.1739C12.5956,2.4567 13.4945,2 14.5,2ZM9.5,3.5C8.5335,3.5 7.75,4.2835 7.75,5.25C7.75,6.1682 8.4571,6.9212 9.3565,6.9942L9.5,7L11.25,7L11.25,5.25C11.25,4.2835 10.4665,3.5 9.5,3.5ZM14.5,3.5C13.5335,3.5 12.75,4.2835 12.75,5.25L12.75,7L14.5,7C15.4665,7 16.25,6.2165 16.25,5.25C16.25,4.2835 15.4665,3.5 14.5,3.5Z"
        android:strokeWidth="1"
        android:strokeColor="#00000000" />
</vector>
