<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI Demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s tanlandi</string>
    <string name="app_modifiable_parameters">Oʻzgaruvchan parametrlar</string>
    <string name="app_right_accessory_view">Oʻng aksessuar koʻrinishi</string>

    <string name="app_style">Uslub</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Belgi bosildi</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button"><PERSON><PERSON> boshlash</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Ka<PERSON>el</string>
    <string name="actionbar_icon_radio_label">Belgi</string>
    <string name="actionbar_basic_radio_label">Asosiy</string>
    <string name="actionbar_position_bottom_radio_label">Quyi</string>
    <string name="actionbar_position_top_radio_label">Yuqori</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">ActionBar turi</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar joylashuvi</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar uslubi</string>
    <string name="app_bar_subtitle">Taglavha</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Pastki hoshiya</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Navigatsiya belgisi bosildi.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Bayroq</string>
    <string name="app_bar_layout_menu_settings">Sozlamalar</string>
    <string name="app_bar_layout_menu_search">Qidiruv</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Aylantirish xatti-harakati: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Aylantirish xatti-harakatini almashtirish</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Navigatsiya belgisini almashtirish</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Avatarni koʻrsatish</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Orqaga belgisini koʻrsatish</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Belgini berkitish</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Belgini koʻrsatish</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Qidirish panelining maket uslubini almashtirish</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Aksessuar koʻrinishida koʻrsatish</string>
    <string name="app_bar_layout_searchbar_action_view_button">Amal koʻrinishida koʻrsatish</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Mavzular orasida almashtirish (faollik qayta yaratishlar)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Mavzuni almashtirish</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Qoʻshimcha anlanadigan kontent</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Aylana uslubi</string>
    <string name="avatar_style_square">Kvadrat uslubi</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXL</string>
    <string name="avatar_size_xlarge">XL</string>
    <string name="avatar_size_large">Yirik</string>
    <string name="avatar_size_medium">O‘rtacha</string>
    <string name="avatar_size_small">Kichik</string>
    <string name="avatar_size_xsmall">XS</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Ikki barovar juda katta</string>
    <string name="avatar_size_xlarge_accessibility">Juda Katta</string>
    <string name="avatar_size_xsmall_accessibility">Juda kichkina</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maksimal koʻrsatilgan avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Toʻldiruvchi avatarlar soni</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Chegara turi</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">OverflowAvatarCount toʻplamiga ega Avatar guruhi maksimal koʻrsatilgan avatarga amal qilmaydi.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Face Stack</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Face Pile</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Overflow bosildi</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">%d indeksdagi AvatarView bosildi</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Bildirishnoma nishoni</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Nuqta</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Roʻyxat</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Belgi</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Suratlar</string>
    <string name="bottom_navigation_menu_item_news">Yangiliklar</string>
    <string name="bottom_navigation_menu_item_alerts">Signallar</string>
    <string name="bottom_navigation_menu_item_calendar">Taqvim</string>
    <string name="bottom_navigation_menu_item_team">Jamoa</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Yorliqlarni almashtirish</string>
    <string name="bottom_navigation_three_menu_items_button">Uch menyu elementlarini chiqarish</string>
    <string name="bottom_navigation_four_menu_items_button">Toʻrt menyu elementni koʻrsatish</string>
    <string name="bottom_navigation_five_menu_items_button">Besh menyu elementlarini chiqarish</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Yorliqlar %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Oʻtkazib yuborish uchun pastga surishni yoqish</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Bir satrli element bilan koʻrsatish</string>
    <string name="bottom_sheet_with_double_line_items">Ikki satrli elementlar bilan koʻrsatish</string>
    <string name="bottom_sheet_with_single_line_header">Bir satrli sarlavha elementi bilan koʻrsatish</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Ikki satrli sarlavha va boʻluvchi bilan koʻrsatish</string>
    <string name="bottom_sheet_dialog_button">Koʻrsatish</string>
    <string name="drawer_content_desc_collapse_state">Yoyish</string>
    <string name="drawer_content_desc_expand_state">Kichraytirish</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s bosish</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">%s uzoq bosish</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Yopish tugmasini bosish</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Element joylash</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Elementni yangilash</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Yopish</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Qo‘shish</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Murojaat</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Qalin</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Qiya</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Tagiga chizish</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Ustidan chizish</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Bekor qilish</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Takrorlash</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">O‘q</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Roʻyxat</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Havola</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Element yuklanmoqda</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Oraliq</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Joylashuvini yopish</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">BOSHLASH</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Guruh boʻshligʻi</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Boʻshliq elementi</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Bayroq</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Belgi elementi bosildi</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Javob</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Javob elementi bosildi</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Uzatish</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Uzatish elementi bosildi</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">O‘chirish</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Olib tashlash elementi bosildi</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Suratga olish</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kamera elementi bosildi</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galereya</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Suratlaringizni koʻrish</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Galereya elementi bosildi</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videolar</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Videolaringizni ochish</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Videolar elementi bosildi</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Boshqarish</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Media kutubxonangizni boshqarish</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Boshqarish elementi bosildi</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Email amallari</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Hujjatlar</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Oxirgi yangilanish 2:14PM</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Ulashish</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Ulashuv elementi bosildi</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Koʻchirish</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Koʻchirish elementi bosildi</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">O‘chirish</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Olib tashlash elementi bosildi</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Axborot</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Axborot elementi bosildi</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Soat</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Soat elementi bosildi</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Signal</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Signal elementi bosildi</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Vaqt mintaqasi</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Vaqt mintaqasi elementi bosildi</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Tugmaning turli koʻrinishlari</string>
    <string name="button">Tugma</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Oʻchirilgan tugma misoli</string>
    <string name="button_borderless">Chegarasiz tugma misoli</string>
    <string name="button_borderless_disabled">Chegarasiz oʻchirilgan tugma misoli</string>
    <string name="button_large">Katta tugma misoli</string>
    <string name="button_large_disabled">Katta oʻchirilgan tugma misoli</string>
    <string name="button_outlined">Belgilangan tugma misoli</string>
    <string name="button_outlined_disabled">Belgilangan oʻchirilgan tugma misoli</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Sanani tanlash</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">Sana-vaqt tanlagich</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Yagona sana</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Sana tanlanmagan</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Sana tanlagichni chiqarish</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Tanlangan sana yorligʻi bilan sana vaqt tanlagichini koʻrsatish</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Tanlangan vaqt yorligʻi bilan sana vaqt tanlagichini koʻrsatish</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Sana vaqt tanlagichni koʻrsatish</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Sana diapazoni</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Boshlanishi:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Tugashi:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Tanlagich boshlanmagan</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Oxiri tanlanmagan</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Boshlanish sanasini belgilash</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Tugash sanasini tanlash</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Sana vaqt oraligʻi</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Sana vaqt oraligʻini tanlash</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">Sana-vaqt tanlagich muloqot oynasi</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Dialogni koʻrsatish</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Chizgʻichni koʻrsatish</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Chizgichpanelini koʻrsatish</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Pastki dialog panelisiz</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Yuqori chizgʻichni koʻrsatish</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Yuqori dialog panelisiz</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Langar koʻrinishidagi yuqori muloqot panelini koʻrsatish</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Sarlavhasiz yuqori muloqot oynasini koʻrsatish</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Quyidagi sarlavhaning yuqori dialog panelini koʻrsatish</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Oʻng chizgʻichni koʻrsatish</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Chap chizgʻichni koʻrsatish</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Sarlavha, birlamchi matn</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtitr, ikkilamchi matn</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Maxsus subtitr matni</string>
    <!-- Footer -->
    <string name="list_item_footer">Pastki kolontitul, uchinchi darajali matn</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Kul rangdagi tagsarlavhali bir satrli roʻyxat</string>
    <string name="list_item_sub_header_two_line">Ikki qatorli roʻyxat</string>
    <string name="list_item_sub_header_two_line_dense">Yaqin oraliqli ikki satrli roʻyxat</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Maxsus ikkilamchi subtitr koʻrinishida ikki satrli roʻyxat</string>
    <string name="list_item_sub_header_three_line">Qora quyi sarlavhali matnli uch qatorli roʻyxat</string>
    <string name="list_item_sub_header_no_custom_views">Maxsus koʻrinishlari boʻlmagan bilan elementlarni roʻyxatga olish</string>
    <string name="list_item_sub_header_large_header">Katta maxsus koʻrinishlar boʻlgan elementlarni roʻyxatga olish</string>
    <string name="list_item_sub_header_wrapped_text">Ajratilgan matnli elementlarni roʻyxatga olish</string>
    <string name="list_item_sub_header_truncated_text">Kesilgan matnli elementlarni roʻyxatga olish</string>
    <string name="list_item_sub_header_custom_accessory_text">Amal</string>
    <string name="list_item_truncation_middle">Oʻrta kesilishi.</string>
    <string name="list_item_truncation_end">Kesishni tugating.</string>
    <string name="list_item_truncation_start">Kesishni boshlang.</string>
    <string name="list_item_custom_text_view">Qiymat</string>
    <string name="list_item_click">Roʻyxat elementini bosdingiz.</string>
    <string name="list_item_click_custom_accessory_view">Maxsus aksessuar koʻrinishini bosdingiz.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Tagsarlavhaning maxsus aksessuar koʻrinishini bosdingiz.</string>
    <string name="list_item_more_options">Boshqa parametrlar</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Tanlash</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Yo‘q</string>
    <string name="people_picker_delete_example">O‘chirish</string>
    <string name="people_picker_custom_persona_description">Bu misolda maxsus IPersona obyektini qanday yaratish kerakligi koʻrsatilgan.</string>
    <string name="people_picker_dialog_title_removed">Shaxsni olib tashladingiz:</string>
    <string name="people_picker_dialog_title_added">Shaxs qoʻshdingiz:</string>
    <string name="people_picker_drag_started">Surish boshlandi</string>
    <string name="people_picker_drag_ended">Surish tugadi</string>
    <string name="people_picker_picked_personas_listener">Personas tinglovchisi</string>
    <string name="people_picker_suggestions_listener">Takliflar tinglovchisi</string>
    <string name="people_picker_persona_chip_click">%s bosdingiz</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s ta qabul qiluvchi</item>
        <item quantity="other">%1$s ta qabul qiluvchi</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Doimiy BottomSheet xizmatini kengaytirish</string>
    <string name="collapse_persistent_sheet_button"> Doimiy BottomSheet xizmatini berkitish</string>
    <string name="show_persistent_sheet_button"> Doyimiy pastki varaq xizmatini koʻrsatish</string>
    <string name="new_view">Bu yangi koʻrinish</string>
    <string name="toggle_sheet_content">Pastki varaq kontentini almashtirish</string>
    <string name="switch_to_custom_content">Maxsus kontentga almashtirish</string>
    <string name="one_line_content">Bir qatorli Bottomsheet kontenti</string>
    <string name="toggle_disable_all_items">Hamma elementlar faolsizlashtirishni almashtirish</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Koʻrinishni qoʻshish/olib tashlash</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Yigʻilgan balandlikni oʻzgartirish</string>
    <string name="persistent_sheet_item_create_new_folder_title">Yangi jild</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Yangi jild elementi bosildi</string>
    <string name="persistent_sheet_item_edit_title">Tahrirlash</string>
    <string name="persistent_sheet_item_edit_toast">Tahrirlash elementi bosildi</string>
    <string name="persistent_sheet_item_save_title">Saqlash</string>
    <string name="persistent_sheet_item_save_toast">Saqlash elementi bosildi</string>
    <string name="persistent_sheet_item_zoom_in_title">Kattalashtirish</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Kattalashtirish elementi bosildi</string>
    <string name="persistent_sheet_item_zoom_out_title">Kichraytirish</string>
    <string name="persistent_sheet_item_zoom_out_toast">Kattalashtirish elementi bosilgan</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Mavjud</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Jonni Makkonel</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lidia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Migel Garsiya</string>
    <string name="persona_name_mona_kane">Mona Keyn</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Dizayner</string>
    <string name="persona_subtitle_engineer">Muhandis</string>
    <string name="persona_subtitle_manager">Menejer</string>
    <string name="persona_subtitle_researcher">Tadqiqotchi</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (kesmani sinash uchun namunaviy uzun matn)</string>
    <string name="persona_view_description_xxlarge">Matnning uch qatori bilan XXL avatari</string>
    <string name="persona_view_description_large">Ikki qator matnli katta avatar</string>
    <string name="persona_view_description_small">Bir satrli matn bilan kichik avatar</string>
    <string name="people_picker_hint">maslahat bilan korsatilmagan</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Faolsizlashtirilgan Persona Chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Xato Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Yopish belgisiz Persona Chip</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Asosiy Persona Chip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Tanlangan Persona Chipni bosdingiz.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Ulashish</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Kuzatish</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Odamlarni taklif qilish</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Sahifani yangilash</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Brauzerda ochish</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Bu koʻp qatorli qalqib chiquvchi menyu. Maksimal qatorlar ikki qilib sozlandi, qolgan matn kesiladi.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Hamma yangiliklar</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Saqlangan yangiliklar</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Saytlardan yangiliklar</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Ish vaqtidan tashqari eslatish</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Ish stolida faol boʻlmaganda ogohlantirish</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Bosgan elementingiz:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Oddiy menyu</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Oddiy menyu2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Bitta tanlangan element va ajratuvchi bilan menyu</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Barcha tanlanadigan elementlar, belgilarva uzun matnli menyu</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Koʻrsatish</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Aylanma progressiya</string>
    <string name="circular_progress_xsmall">XS</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Kichik</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">O‘rtacha</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Yirik</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Chiziqli progressiya</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Noaniq</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Aniqlash</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Qidiruv paneli</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Qayta chaqiruv mikrofoni</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Avtotuzatish</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon bosilgan</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Oʻng koʻrish bosilgan</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Klaviatura qidiruvi bosildi</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Snekbarni chiqarish</string>
    <string name="fluentui_dismiss_snackbar">Snekkbarni yopish</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Amal</string>
    <string name="snackbar_action_long">Uzun matnli harakat</string>
    <string name="snackbar_single_line">Bir qatorli snackbar</string>
    <string name="snackbar_multiline">Bu koʻp qatorli snackbar. Eng koʻp satr bilan ikkitaga joylashtiriladi, qolgan matn kesiladi.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Bu eʼlon snackbar. U yangi funksiyalarni eʼlon qilish uchun ishlatiladi.</string>
    <string name="snackbar_primary">Bu asosiy snachbar</string>
    <string name="snackbar_light">Bu yorugʻ snackbar.</string>
    <string name="snackbar_warning">Bu ogohlantiruvchi snachbar paneli.</string>
    <string name="snackbar_danger">Bu xavfli snackbar.</string>
    <string name="snackbar_description_single_line">Qisqa muddat</string>
    <string name="snackbar_description_single_line_custom_view">Kichik maxsus koʻrinish sifatida aylanma progressiyaga ega boʻlgan uzoq muddat</string>
    <string name="snackbar_description_single_line_action">Harakat bilan qisqa davomiylik</string>
    <string name="snackbar_description_single_line_action_custom_view">Aksiya va oʻrta maxsus koʻrinishga ega boʻlgan qisqa muddat</string>
    <string name="snackbar_description_single_line_custom_text_color">Moslashtirilgan matn rangi bilan qisqa muddat</string>
    <string name="snackbar_description_multiline">Uzoq davomiylik</string>
    <string name="snackbar_description_multiline_custom_view">Kichik maxsus koʻrinishga ega boʻlgan uzoq muddat</string>
    <string name="snackbar_description_multiline_action">Amal va matn yangilanishlari bilan muddatsiz</string>
    <string name="snackbar_description_multiline_action_custom_view">Aksiya va oʻrta maxsus koʻrinishga ega boʻlgan qisqa muddat</string>
    <string name="snackbar_description_multiline_action_long">Uzun amal matni bilan qisqa muddat</string>
    <string name="snackbar_description_announcement">Qisqa muddat</string>
    <string name="snackbar_description_updated">Bu matn yangilandi.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Snackbarni chiqarish</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Bittali satr</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Bir necha qatorli</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Eʻlon uslubi</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Birlamchi uslub</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Yorugʻlik uslubi</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Ogohlantirish uslubi</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Danger uslubi</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Bosh sahifa</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Pochta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Sozlamalar</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Bildirishnoma</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Yana</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Matnni tekislash</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertikal</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Gorizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Matnsiz</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Tab elementi</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Sarlavha</string>
    <string name="cell_sample_description">Tavsif</string>
    <string name="calculate_cells">100 ta katakni yuklash/hisoblash</string>
    <string name="calculate_layouts">100 ta maketni yuklash/hisoblash</string>
    <string name="template_list">Andoza roʻyxat</string>
    <string name="regular_list">Doimiy roʻyxat</string>
    <string name="cell_example_title">Sarlavha: Katak</string>
    <string name="cell_example_description">Tavsif: Yoʻnalishni oʻzgartirish uchun bosish</string>
    <string name="vertical_layout">Vertikal maket</string>
    <string name="horizontal_layout">Gorizontal maket</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">2-segment standart tab tugmasi</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standart Tab 3-segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standart Tab 4-segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Peyjer bilan standart tab</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Tab tugmasini almashtirish</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Tezlashtirish uchun bosish </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Vositalar paneli uchun bosish</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Vositalar paneli maxsus taqvimi uchun bosing</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Vositalar paneli maxsus rangi uchun bosish</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Vositalar panel ichini yopish uchun bosish</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Vositalar panelini maxsus koʻrinish uchun bosish</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Yuqoridagi vositalar panelini maxsus rangi</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp offsetY bilan yuqoridagi vositalar panelini tugatish</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Quyida vazifalar panelini boshlash</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp offsetY bilan quyidagi vositalar panelini tugatish</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Vositalar panel ichini yopish</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Vazifalar paneli yopildi</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Sarlavha oʻlchami Light 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Sarlavha 1 oʻlchami Medium 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Sarlavha 2 – Regular 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Sarlavha oʻlchami Regular 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Quyi sarlavha 1 – Regular 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">2 boshliqli 16sp oʻrtacha</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Tana 1 oʻlchami Regular 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Tana 2 oʻlchovi Medium 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Taglavha oʻlchami Regular 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK versiyasi: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Element %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Jild</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Bosildi</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB kengaytirildi</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB kichraytirildi</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Roʻyxatni yangilash uchun bosish</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Panelni ochish</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menyu elementi</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">X siljish (dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Y siljish (dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Kontent matni</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Kontent matnni takrorlash</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menyu kengligi kontent matniga nisbatan oʻzgaradi. Maksimal
        kenglik ekran oʻlchamining 75% bilan cheklangan. Yon va pastdan kontent chegarasi token bilan boshqariladi. Xuddi shu kontent matni balandlikni oʻzgartirish uchun
        takrorlanadi.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Ochiq menyu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Asosiy karta</string>
    <!-- UI Label for Card -->
    <string name="file_card">Fayl kartasi</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Eʼlon kartasi</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Tasodifiy UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">Parametrlar</string>
    <!-- UI Label for Title -->
    <string name="card_title">Sarlavha</string>
    <!-- UI Label for text -->
    <string name="card_text">Matn</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Quyi matn</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Agar kerak boʻlsa, bu bannerning ikkilamchi nusxasi ikki qatorga joylanishi mumkin.</string>
    <!-- UI Label Button -->
    <string name="card_button">Tugma</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Muloqot oynasini koʻrsatish</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Tashqariga bosganingizda muloqot oynasini bekor qilish</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Orqaga tugmasini bosganda muloqot oynasini bekor qilish</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">muloqot oynasi bekor qilindi</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Bekor qilish</string>
    <!-- UI Label Ok -->
    <string name="ok">Ha</string>
    <!-- A sample description -->
    <string name="dialog_description">Muloqot oynasi - foydalanuvchini qaror qabul qilish yoki qoʻshimcha axborotni kiritishni taklif qiluvchi kichik oyna.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Tortmani ochish</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Tortmani kengaytirish</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Panelni yopish</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Panel turini tanlang</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Yuqori</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Butun panel koʻrinadigan hududda koʻrsatiladi.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Pastki</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Butun panel koʻrinadigan hududda koʻrinadi. Harakatli aylantirish kontentini yuqoriga suring. Kengaytiriladigan panel tortish deskriptori orqali kengaytiriladi.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Chapga tortib o‘tkazish</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Panelni chap tomondan koʻrinadigan hududga tortib o‘tkazing.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Oʻngga tortib o‘tkazish</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Panelni oʻngdan koʻrinadigan hududga tortib o‘tkazing.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Pastga tortib o‘tkazish</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Panelni ekranning pastki qismidan koʻrinadigan hududga tortib o‘tkazing. Kengaytiriladigan panelda harakatni yuqoriga suring, uning qolgan qismini koʻrinadigan hududga keltiring va keyin aylantiring.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Ko‘rinadigan skrim</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Panel kontentini belgilash</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Toʻliq ekran oʻlchamli aylantiriladigan kontent</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Kontent ekranning yarmidan katta</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Kontent ekranning yarmidan kichik</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dinamik oʻlchamli kontent</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Ichki panel kontenti</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Kengaytiriladigan</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Ochiq holatni oʻtkazib yuborish</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Scrim klik orqali oʻtkazib yuboruvchini oldini olish</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Dastakni chiqarish</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Sarlavha</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Qalqib chiquvchi ko‘rsatma matni</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Maxsus kontent boʻyicha maslahat olish uchun bosing</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Yuqoridan boshlash </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Yuqori uchi </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Pastki boshlanish </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Pastki uchi </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Markaz </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Maxsus markaz</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Nashr qaydlaridagi yangilanishlar uchun,</string>
    <string name="click_here">bu yerga bosing.</string>
    <string name="open_source_cross_platform">Ochiq manbali o‘zaro faoliyat platformalarni loyihalash tizimi.</string>
    <string name="intuitive_and_powerful">Intuitiv &amp; Kuchli.</string>
    <string name="design_tokens">Dizayn tokenlari</string>
    <string name="release_notes">Nashr qaydlari</string>
    <string name="github_repo">GitHub repo</string>
    <string name="github_repo_link">GitHub Repo havolasi</string>
    <string name="report_issue">Muammo haqida xabar berish</string>
    <string name="v1_components">V1 komponentlari</string>
    <string name="v2_components">V2 komponentlari</string>
    <string name="all_components">Barcha</string>
    <string name="fluent_logo">Tekis logotip</string>
    <string name="new_badge">Yangi</string>
    <string name="modified_badge">O‘zgartirilgan</string>
    <string name="api_break_badge">API uzilish</string>
    <string name="app_bar_more">Yana</string>
    <string name="accent">Urg‘u</string>
    <string name="appearance">Ko‘rinish</string>
    <string name="choose_brand_theme">Brendingiz mavzusini tanlang:</string>
    <string name="fluent_brand_theme">Tekis brend</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Tashqi koʻrinishni tanlash</string>
    <string name="appearance_system_default">Tizim sozlamalari</string>
    <string name="appearance_light">Och</string>
    <string name="appearance_dark">To‘q</string>
    <string name="demo_activity_github_link">Demo faoliyati GitHub havolasi</string>
    <string name="control_tokens_details">Tokenlarni boshqarish tafsilotlari</string>
    <string name="parameters">Parametrlar</string>
    <string name="control_tokens">Boshqarish tokenlari</string>
    <string name="global_tokens">Global tokenlar</string>
    <string name="alias_tokens">Taxallus tokenlari</string>
    <string name="sample_text">Matn</string>
    <string name="sample_icon">Namuna ikonkasi</string>
    <string name="color">Rang</string>
    <string name="neutral_color_tokens">Neytral rangli tokenlar</string>
    <string name="font_size_tokens">Shrift oʻlchami tokenlari</string>
    <string name="line_height_tokens">Satr balandligi tokenlari</string>
    <string name="font_weight_tokens">Shrift og‘irligi tokenlari</string>
    <string name="icon_size_tokens">Ikonka oʻlchami tokenlari</string>
    <string name="size_tokens">Oʻlcham tokenlari</string>
    <string name="shadow_tokens">Soya tokenlari</string>
    <string name="corner_radius_tokens">Burchak RadiusTokenlari</string>
    <string name="stroke_width_tokens">Strok kengligi tokenlari</string>
    <string name="brand_color_tokens">Brend rangli tokenlar</string>
    <string name="neutral_background_color_tokens">Neytral fon rangi tokenlari</string>
    <string name="neutral_foreground_color_tokens">Neytral oldingi rangli tokenlar</string>
    <string name="neutral_stroke_color_tokens">Neytral stroke rangi tokenlari</string>
    <string name="brand_background_color_tokens">Brend fon rangi tokenlari</string>
    <string name="brand_foreground_color_tokens">Brendning oldingi rangli tokenlari</string>
    <string name="brand_stroke_color_tokens">Brend stroke rangi tokenlari</string>
    <string name="error_and_status_color_tokens">Xato va holat rangi tokenlari</string>
    <string name="presence_tokens">Mavjudlik rangi tokenlari</string>
    <string name="typography_tokens">Tipografiya tokenlari</string>
    <string name="unspecified">Aniqlanmagan</string>

</resources>