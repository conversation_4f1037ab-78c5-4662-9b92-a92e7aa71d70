<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Predstavitev uporabniškega vmesnika Fluent</string>
    <string name="app_title">Uporabniški vmesnik Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Izbrano %s</string>
    <string name="app_modifiable_parameters">Prilagodljivi parametri</string>
    <string name="app_right_accessory_view">Desni pogled dodatne opreme</string>

    <string name="app_style">Slog</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikona je pritisnjena</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button"><PERSON><PERSON><PERSON><PERSON></string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Vrtiljak</string>
    <string name="actionbar_icon_radio_label">Ikona</string>
    <string name="actionbar_basic_radio_label">Osnovno</string>
    <string name="actionbar_position_bottom_radio_label">Spodaj</string>
    <string name="actionbar_position_top_radio_label">Zgoraj</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Vrsta vrstice z dejanji</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Položaj vrstice z dejanji</string>

    <!--AppBar-->
    <string name="app_bar_style">Slog vrstice z aplikacijami</string>
    <string name="app_bar_subtitle">Podnaslov</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Spodnja obroba</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Kliknili ste na ikono »Navigacija«.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Zastavica</string>
    <string name="app_bar_layout_menu_settings">Nastavitve</string>
    <string name="app_bar_layout_menu_search">Iskanje</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Delovanje drsenja: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Preklopi način delovanja drsenja</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Preklopi ikono krmarjenja</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Pokaži avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Ikona »Pokaži nazaj«</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ikona »Skrij«</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Pokaži ikono</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Preklopi slog postavitve iskalne vrstice</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Pokaži kot pogled dodatne opreme</string>
    <string name="app_bar_layout_searchbar_action_view_button">Pokaži kot pogled dejanja</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Preklapljanje med temami (znova ustvari dejavnost)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Preklopite temo</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Dodatna vsebina, po kateri se je mogoče pomikati</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Slog kroga</string>
    <string name="avatar_style_square">Slog kvadrata</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Veliko</string>
    <string name="avatar_size_medium">Srednje</string>
    <string name="avatar_size_small">Majhno</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dvojno zelo veliko</string>
    <string name="avatar_size_xlarge_accessibility">Zelo veliko</string>
    <string name="avatar_size_xsmall_accessibility">Zelo majhno</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Največji prikazan avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Število avatarjev prekoračitve</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Vrsta obrobe</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Skupina avatarjev z naborom OverflowAvatarCount ne bo v skladu z največjo prikazano skupino avatarjev.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Sklad obrazov</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Kup ploskev</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Kliknili ste na možnost »Prekoračitev«</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView v kazalu %d je bil kliknjen</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Značka za obvestila</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Pika</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Seznam</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Lik</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotografije</string>
    <string name="bottom_navigation_menu_item_news">Novice</string>
    <string name="bottom_navigation_menu_item_alerts">Opozorila</string>
    <string name="bottom_navigation_menu_item_calendar">Koledar</string>
    <string name="bottom_navigation_menu_item_team">Ekipa</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Preklopi oznake</string>
    <string name="bottom_navigation_three_menu_items_button">Pokaži tri elemente menija</string>
    <string name="bottom_navigation_four_menu_items_button">Pokaži štiri elemente menija</string>
    <string name="bottom_navigation_five_menu_items_button">Pokaži pet elementov menija</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Oznake so %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Spodnji list</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Omogočite podrsavanje navzdol za opustitev</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Pokaži z enovrstičnimi elementi</string>
    <string name="bottom_sheet_with_double_line_items">Pokaži z element z dvojno črto</string>
    <string name="bottom_sheet_with_single_line_header">Pokaži z glavo v eni vrstici</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Pokaži z glavo in razdelilniki z dvema črtama</string>
    <string name="bottom_sheet_dialog_button">Pokaži</string>
    <string name="drawer_content_desc_collapse_state">Razširi</string>
    <string name="drawer_content_desc_expand_state">Minimiziraj</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Kliknite %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Dolg klik %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Kliknite možnost »Opusti«</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Vstavi element</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Element »Posodobi«</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Opusti</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Dodaj</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Omemba</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Krepko</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Ležeče</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Podčrtano</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Prečrtano</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Razveljavi</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Ponovi</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Vrstična oznaka</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Seznam</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Povezava</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Posodabljanje elementa</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Razmik</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Opusti položaj</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">ZAČNI</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">KONEC</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Prostor skupine</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Prostor elementa</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Zastavica</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Kliknili ste na element »Zastavica«</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Odgovori</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Kliknili ste na element »Odgovori«</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Naprej</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Kliknili ste na element »Naprej«</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Izbriši</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Kliknili ste na element »Izbriši«</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Posnemite fotografijo</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kliknili ste na element »Kamera«</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerija</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Oglejte si svoje fotografije</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Kliknili ste na element »Galerija«</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videoposnetki</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Predvajajte svoje videoposnetke</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Kliknili ste na element »Videoposnetki«</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Upravljaj</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Upravljanje predstavnostne knjižnice</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Kliknili ste na element »Upravljaj«</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Dejanja e-pošte</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumenti</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Nazadnje posodobljeno 2:14:00</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Daj v skupno rabo</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Element skupne rabe je kliknjen</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Premakni</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Kliknili ste na element »Premakni«.</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Izbriši</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Kliknili ste na element »Izbriši«</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informacije</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Kliknili ste na element »Informacije«</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Ura</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Kliknili ste na element »Ura«</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Kliknili ste na element »Alarm«</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Časovni pas</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Kliknili ste na element »Časovni pas«</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Različni pogledi gumba</string>
    <string name="button">Gumb</string>
    <string name="buttonbar">Gumbna vrstica</string>
    <string name="button_disabled">Primer onemogočenega gumba</string>
    <string name="button_borderless">Primer gumba brez obrobe</string>
    <string name="button_borderless_disabled">Primer onemogočenega gumba brez obrobe</string>
    <string name="button_large">Primer velikega gumba</string>
    <string name="button_large_disabled">Primer velikega onemogočenega gumba</string>
    <string name="button_outlined">Primer gumba z orisom</string>
    <string name="button_outlined_disabled">Primer onemogočenega gumba z orisom</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Izberite datum</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">En datum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Datum ni izbran</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Pokaži izbirnik datuma</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Pokaži izbirnik datuma in ure z izbranim zavihkom »Datum«</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Pokaži izbirnik datuma in ure z izbranim zavihkom »Čas«</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Izbirnik pokaži datum in uro</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datumski obseg</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Začetek:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Konec:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Izbran ni noben začetek</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Noben konec ni izbran</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Izberi začetni datum</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Izberi končni datum</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Obseg datuma in časa</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Izberite obseg datuma in časa</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Pokaži pogovorno okno</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Pokaži predal</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Pokaži pogovorno okno predala</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Pogovorno okno brez pojemanja na dnu</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Pokaži zgornji predal</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Zgornje pogovorno okno brez pojemanja</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button">Pokaži zgornje pogovorno okno sidrnega pogleda</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button">Pokaži pogovorno okno brez naslova na vrhu</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button">Pokaži spodnje zgornje pogovorno okno naslova</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Pokaži desni predal</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Pokaži levi predal</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Naslov, primarno besedilo</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Podnaslov, sekundarno besedilo</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Besedilo podnaslova po meri</string>
    <!-- Footer -->
    <string name="list_item_footer">Noga, terciarno besedilo</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Enovrstični seznam s sivim podglavnim besedilom</string>
    <string name="list_item_sub_header_two_line">Dvosmerni seznam</string>
    <string name="list_item_sub_header_two_line_dense">Dvočrkovni seznam z gostim razmikom</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Seznam dveh vrstic s sekundarnim podnaslovom po meri</string>
    <string name="list_item_sub_header_three_line">Trivrstični seznam s črnim podglavnim besedilom</string>
    <string name="list_item_sub_header_no_custom_views">Prikaži elemente brez pogledov po meri</string>
    <string name="list_item_sub_header_large_header">Prikaži elemente z velikimi pogledi po meri</string>
    <string name="list_item_sub_header_wrapped_text">Prikaži elemente z oblitim besedilom</string>
    <string name="list_item_sub_header_truncated_text">Prikaži elemente s prirezanim besedilom</string>
    <string name="list_item_sub_header_custom_accessory_text">Dejanje</string>
    <string name="list_item_truncation_middle">Srednje prirezovanje.</string>
    <string name="list_item_truncation_end">Končaj prirezovanje.</string>
    <string name="list_item_truncation_start">Začni prirezovanje.</string>
    <string name="list_item_custom_text_view">Vrednost</string>
    <string name="list_item_click">Kliknili ste element seznama.</string>
    <string name="list_item_click_custom_accessory_view">Kliknili ste pogled dodatne opreme po meri.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Kliknili ste pogled dodatne opreme po meri v podglavi.</string>
    <string name="list_item_more_options">Več možnosti</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Izberi</string>
    <string name="people_picker_select_deselect_example">IzberiPrekliči izbor</string>
    <string name="people_picker_none_example">Brez</string>
    <string name="people_picker_delete_example">Izbriši</string>
    <string name="people_picker_custom_persona_description">V tem primeru je prikazano, kako ustvarite predmet IPersona po meri.</string>
    <string name="people_picker_dialog_title_removed">Odstranili ste osebo:</string>
    <string name="people_picker_dialog_title_added">Dodali ste osebo:</string>
    <string name="people_picker_drag_started">Začetek vlečenja</string>
    <string name="people_picker_drag_ended">Vlečenje je končano</string>
    <string name="people_picker_picked_personas_listener">Poslušalec osebe</string>
    <string name="people_picker_suggestions_listener">Poslušalec predlogov</string>
    <string name="people_picker_persona_chip_click">Kliknili ste %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s prejemnik</item>
        <item quantity="two">%1$s prejemnika</item>
        <item quantity="few">%1$s prejemniki</item>
        <item quantity="other">%1$s prejemnikov</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Razširi trajni spodnji list</string>
    <string name="collapse_persistent_sheet_button">Skrij trajni spodnji list</string>
    <string name="show_persistent_sheet_button"> Skrij TrajniSpodnjiList</string>
    <string name="new_view">To je nov pogled</string>
    <string name="toggle_sheet_content">Preklopi vsebino spodnjega lista</string>
    <string name="switch_to_custom_content">Preklopi na vsebino po meri</string>
    <string name="one_line_content">Vsebina spodnjega lista z eno vrstico</string>
    <string name="toggle_disable_all_items">Preklopi možnost »Onemogoči vse elemente«</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Dodaj/odstrani pogled</string>
    <string name="persistent_sheet_item_change_collapsed_height">Spremeni strnjeno višino</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nova mapa</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Kliknili ste na element »Nova mapa«</string>
    <string name="persistent_sheet_item_edit_title">Uredi</string>
    <string name="persistent_sheet_item_edit_toast">Kliknili ste na element »Uredi«</string>
    <string name="persistent_sheet_item_save_title">Shrani</string>
    <string name="persistent_sheet_item_save_toast">Kliknili ste na element »Shrani«</string>
    <string name="persistent_sheet_item_zoom_in_title">Povečava</string>
    <string name="persistent_sheet_item_zoom_in_toast">Kliknili ste na element »Povečaj«</string>
    <string name="persistent_sheet_item_zoom_out_title">Pomanjšava</string>
    <string name="persistent_sheet_item_zoom_out_toast">Kliknili ste na element »Pomanjšaj«</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Na voljo</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Martin Oblak</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio avgust</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Oblikovalnik</string>
    <string name="persona_subtitle_engineer">Inženir</string>
    <string name="persona_subtitle_manager">Upravitelj</string>
    <string name="persona_subtitle_researcher">Raziskovalec</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (dolg primer besedila za preskus prirezovanja)</string>
    <string name="persona_view_description_xxlarge">Avatar XXLa avatar s tremi vrsticami besedila</string>
    <string name="persona_view_description_large">Velik avatar z dvema vrsticama besedila</string>
    <string name="persona_view_description_small">Majhen avatar z eno vrstico besedila</string>
    <string name="people_picker_hint">Brez prikazanega namiga</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Čip za onemogočeno osebo</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Čip osebe z napako</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Čip osebe brez ikone za zapiranje</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Čip osnovne osebe</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Kliknili ste izbrani čip osebe.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Daj v skupno rabo</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Spremljaj</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Povabi osebe</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Osveži stran</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Odpri v brskalniku</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">To je pojavni meni z več vrsticami. Največje število vrstic je nastavljeno na dve. Preostalo besedilo bo prirezano.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Vse novice</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Shranjene novice</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Novice s spletnih mest</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Obvestila izven delovnega časa</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Obvesti, ko je neaktiven na namizju</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Kliknili ste element:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Preprost meni</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Preprost meni2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Meni z enim elementom, ki ga je mogoče izbrati, in razdelilno črto</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Meni z vsemi elementi, ikonami in dolgim besedilom, ki jih je mogoče izbrati</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Pokaži</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Krožni napredek</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Majhno</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Srednje</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Veliko</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linearni napredek</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Nedoločeno</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Določi</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">IskalnaVrstica</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Povratni klic mikrofona</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Samopopravki</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon je pritisnjen</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Pritisnjen desni pogled</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Iskanje s tipkovnico je pritisnjeno</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Pokaži opozorilno vrstico</string>
    <string name="fluentui_dismiss_snackbar">Opusti prigrizek</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Dejanje</string>
    <string name="snackbar_action_long">Dejanje z dolgim besedilom</string>
    <string name="snackbar_single_line">Opozorilna vrstica z eno vrstico</string>
    <string name="snackbar_multiline">To je večvrstično prigrizek. Največje število vrstic je nastavljeno na dve. Preostalo besedilo bo prirezano.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">To je prigrizek za obvestilo. Uporablja se za komunikacijo novih funkcij.</string>
    <string name="snackbar_primary">To je primarna opozorilna vrstica.</string>
    <string name="snackbar_light">To je lahka opozorilna vrstica.</string>
    <string name="snackbar_warning">To je opozorilna vrstica.</string>
    <string name="snackbar_danger">To je vrstica o nevarnosti.</string>
    <string name="snackbar_description_single_line">Kratko trajanje</string>
    <string name="snackbar_description_single_line_custom_view">Dolgo trajanje s krožnim napredkom kot majhen pogled po meri</string>
    <string name="snackbar_description_single_line_action">Kratko trajanje z dejanjem</string>
    <string name="snackbar_description_single_line_action_custom_view">Kratko trajanje z dejanjem in srednjim pogledom po meri</string>
    <string name="snackbar_description_single_line_custom_text_color">Kratko trajanje s prilagojeno barvo besedila</string>
    <string name="snackbar_description_multiline">Dolgo trajanje</string>
    <string name="snackbar_description_multiline_custom_view">Dolgo trajanje z majhnim pogledom po meri</string>
    <string name="snackbar_description_multiline_action">Neomejeno trajanje z dejanjem in posodobitvami besedila</string>
    <string name="snackbar_description_multiline_action_custom_view">Kratko trajanje z dejanjem in srednjim pogledom po meri</string>
    <string name="snackbar_description_multiline_action_long">Kratko trajanje z dolgim besedilom dejanja</string>
    <string name="snackbar_description_announcement">Kratko trajanje</string>
    <string name="snackbar_description_updated">To besedilo je bilo posodobljeno.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Pokaži opozorilno vrstico</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Enojna črta</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Več črt</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Slog obvestila</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primarni slog</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Svetli slog</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Slog opozorila</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Slog nevarnosti</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Domov </string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Pošta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Nastavitve</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Obvestilo</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Več</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Poravnava besedila</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Navpično</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vodoravno</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Brez besedila</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Elementi zavihka</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Naslov</string>
    <string name="cell_sample_description">Opis</string>
    <string name="calculate_cells">Naloži/izračunaj 100 celic</string>
    <string name="calculate_layouts">Naloži/izračunaj 100 postavitev</string>
    <string name="template_list">Seznam predlog</string>
    <string name="regular_list">Navaden seznam</string>
    <string name="cell_example_title">Naslov: celica</string>
    <string name="cell_example_description">Opis: tapnite, če želite spremeniti usmerjenost</string>
    <string name="vertical_layout">Navpična postavitev</string>
    <string name="horizontal_layout">Vodoravna postavitev</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standardni 2-segmentni zavihek</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardni 3-segment zavihka</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardni 4-segmentni zavihek</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardni zavihek s pozivnikom</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Preklopi med zavihki</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Zavihek »Tablete« </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tapnite za opis orodja</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tapnite za opis orodja za koledar po meri</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Opis orodja za barvo po meri zavihka</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tapnite, če želite opustiti notranji opis orodja</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tapnite za opis orodja za pogled po meri</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Opis orodja za barvo po meri zgoraj</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Opis orodja na vrhu konca z odmikom 10 dp na Y osi</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Opis orodja za začetek spodaj</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Opis orodja na dnu konca z odmikom 10 dp na Y osi</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Opusti notranji opis orodja</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Opis orodja je opuščen</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Naslov je svetlo 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Naslov 1 je srednji 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Naslov 2 je navaden 20 sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Naslov je navaden 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Podnaslov 1 je navaden 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Podnaslov 2 je srednji 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Telo 1 je navadno 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Telo 2 je srednje 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Napis je navaden 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Različica SDK- ja: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Element %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mapa</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Kliknjeno</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Oder</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB razširjeno</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB strnjeno</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Kliknite, če želite osvežiti seznam</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Odprt predal</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menijski element</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Odmik X (v dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Odmik Y (v dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Besedilo vsebine</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Ponovi besedilo vsebine</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Širina menija se bo spremenila glede na besedilo vsebine. Največja vrednost
        širina je omejena na 75% velikosti zaslona. Rob vsebine od strani in spodaj ureja žeton. Ista vsebina besedila se bo ponavljala s spreminjanjem
        višino.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Meni »Odpri«</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Osnovna kartica</string>
    <!-- UI Label for Card -->
    <string name="file_card">Kartica datoteke</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Kartica obvestila</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Naključni uporabniški vmesnik</string>
    <!-- UI Label for Options -->
    <string name="card_options">Možnosti</string>
    <!-- UI Label for Title -->
    <string name="card_title">Naslov</string>
    <!-- UI Label for text -->
    <string name="card_text">Besedilo</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Podbesedilo</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekundarna kopija za to pasico se lahko po potrebi prelomi v dve vrstici.</string>
    <!-- UI Label Button -->
    <string name="card_button">Gumb</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Pokaži pogovorno okno</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Opusti pogovorno okno ob kliku zunaj</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Opusti pogovorno okno ob pritisku na gumb Nazaj</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Pogovorno okno je opuščeno</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Prekliči</string>
    <!-- UI Label Ok -->
    <string name="ok">V redu</string>
    <!-- A sample description -->
    <string name="dialog_description">Pogovorno okno je majhno okno, ki pozove uporabnika k odločitvi ali vnosu dodatnih informacij.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Odpri predal</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Razširi predal</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Zapri predal</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Izberite vrsto predala</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Zgoraj</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Celoten predal je prikazan v vidnem območju.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Spodaj</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Celoten predal se prikaže v vidnem območju. Premikanje po vsebini s podrsljajem navzgor. Razširljiv predal se razširi z vlečenjem ročice.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Levo podrsljaj prek</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Predal se pomakne na vidno območje z leve strani.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Desni podrsljaj prek</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Predal se pomakne na vidno območje z desne strani.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Spodnji podrsljaj prek</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Predal se pomakne na vidno območje z dna zaslona. Premikanje navzgor na razširljivem predalu pripelje preostali del predala do vidnega območja &amp; nato se pomaknite.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim vidno</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Izberite vsebino predala</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Vsebina z možnostjo pomikanja po celotnem zaslonu</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Več kot polovica vsebine zaslona</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Manj kot polovica vsebine zaslona</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Vsebina dinamične velikosti</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Vsebina vgnezdenih predalov</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Razširljivo</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Preskoči odprto stanje</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Prepreči zavrnitev ob kliku Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Pokaži ročico</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Naslov</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Besedilo opisa orodja</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tapnite za opis orodja za vsebino po meri</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Zgornji začetek </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Konec zgoraj </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Začetek spodaj </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Konec spodaj </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Na sredino </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Na sredino po meri</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Za posodobitve opomb ob izdaji,</string>
    <string name="click_here">kliknite tukaj.</string>
    <string name="open_source_cross_platform">Odprtokodni sistem oblikovanja na več platformah.</string>
    <string name="intuitive_and_powerful">Intuitivno &amp; zmogljivo.</string>
    <string name="design_tokens">Žetoni načrta</string>
    <string name="release_notes">Opombe ob izdaji</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub Repo Link</string>
    <string name="report_issue">Pošlji sporočilo o napaki</string>
    <string name="v1_components">Komponente V1</string>
    <string name="v2_components">Komponente V2</string>
    <string name="all_components">Vse</string>
    <string name="fluent_logo">Logotip programa Fluent</string>
    <string name="new_badge">Novo</string>
    <string name="modified_badge">Spremenjeno</string>
    <string name="api_break_badge">Prelom vmesnika API</string>
    <string name="app_bar_more">Več</string>
    <string name="accent">Naglas</string>
    <string name="appearance">Videz</string>
    <string name="choose_brand_theme">Izberite temo blagovne znamke:</string>
    <string name="fluent_brand_theme">Blagovna znamka Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Izberite videz</string>
    <string name="appearance_system_default">Privzeto v sistemu</string>
    <string name="appearance_light">Svetlo</string>
    <string name="appearance_dark">Temno</string>
    <string name="demo_activity_github_link">Povezava do predstavitvene dejavnosti povezave GitHub</string>
    <string name="control_tokens_details">Podrobnosti žetonov kontrolnika</string>
    <string name="parameters">Parametri</string>
    <string name="control_tokens">Žetoni kontrolnika</string>
    <string name="global_tokens">Globalni žetoni</string>
    <string name="alias_tokens">Žetoni za vzdevke</string>
    <string name="sample_text">Besedilo</string>
    <string name="sample_icon">Ikona vzorca</string>
    <string name="color">Barva</string>
    <string name="neutral_color_tokens">Žetoni za nevtralno barvo</string>
    <string name="font_size_tokens">Žetoni za velikost pisave</string>
    <string name="line_height_tokens">Žetoni višine črte</string>
    <string name="font_weight_tokens">Žetoni za debelino pisave</string>
    <string name="icon_size_tokens">Žetoni za velikost ikone</string>
    <string name="size_tokens">Žetoni za velikost</string>
    <string name="shadow_tokens">Žetoni senčenja</string>
    <string name="corner_radius_tokens">Corner RadiusTokens</string>
    <string name="stroke_width_tokens">Žetoni širine poteze</string>
    <string name="brand_color_tokens">Žetoni barve blagovne znamke</string>
    <string name="neutral_background_color_tokens">Žetoni nevtralne barve ozadja</string>
    <string name="neutral_foreground_color_tokens">Žetoni nevtralne barve ospredja</string>
    <string name="neutral_stroke_color_tokens">Žetoni nevtralne barve poteze s peresom</string>
    <string name="brand_background_color_tokens">Žetoni barve ozadja blagovne znamke</string>
    <string name="brand_foreground_color_tokens">Žetoni nevtralne barve ospredja</string>
    <string name="brand_stroke_color_tokens">Žetoni barve poteze s peresom znamke</string>
    <string name="error_and_status_color_tokens">Žetoni z napakami in barvami stanja</string>
    <string name="presence_tokens">Žetoni barve prisotnosti</string>
    <string name="typography_tokens">Žetoni za tipografijo</string>
    <string name="unspecified">Nedoločeno</string>

</resources>