<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Примарно</string>
    <string name="fluentui_secondary">Секундарно</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Отфрли</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Избрано</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Не е избрано</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Икона</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Икона</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Исклучено</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Копче за дејство</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Вклучено</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Затвори го листот</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Затвори</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Откажете</string>
    <!--name of the icon -->
    <string name="fluentui_search">Пребарај</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Микрофон</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Исчисти го текстот</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Назад</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Активирано</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Деактивирано</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Неутрално</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Бренд</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Контраст</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Нагласи</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Предупредување</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Опасност</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Се појави грешка</string>
    <string name="fluentui_error">Грешка</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Совет</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Шеврон</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Контура</string>

    <string name="fluentui_action_button_icon">Икона на копче за дејство</string>
    <string name="fluentui_center">Центриран текст</string>
    <string name="fluentui_accessory_button">Копчиња за додатоци</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Радио-копче</string>
    <string name="fluentui_label">Етикета</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Проширено</string>
    <string name="fluentui_collapsed">Сокриено</string>

    <!--types of control -->
    <string name="fluentui_large">Големо</string>
    <string name="fluentui_medium">Средна</string>
    <string name="fluentui_small">Мало</string>
    <string name="fluentui_password_mode">Режим на лозинка</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Поднаслов</string>
    <string name="fluentui_assistive_text">Помошен текст</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Наслов</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Кратко</string>"
    <string name="fluentui_long">Долго</string>"
    <string name="fluentui_indefinite">Неопределено</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Притиснато копче</string>
    <string name="fluentui_dismissed">Отфрлено</string>
    <string name="fluentui_timeout">Изминато</string>
    <string name="fluentui_left_swiped">Повлекување налево</string>
    <string name="fluentui_right_swiped">Повлекување надесно</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Текст</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Број</string>
    <string name="fluentui_keyboard_phone">Телефон</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Е-пошта</string>
    <string name="fluentui_keyboard_password">Лозинка</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Децимален број</string>
</resources>