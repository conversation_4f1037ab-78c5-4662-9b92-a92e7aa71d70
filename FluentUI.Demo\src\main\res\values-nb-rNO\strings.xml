<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI Demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Merket %s</string>
    <string name="app_modifiable_parameters">Modifiserbare parametere</string>
    <string name="app_right_accessory_view">H<PERSON><PERSON> tilbehørsvisning</string>

    <string name="app_style">Stil</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikon trykket på</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Start demonstrasjon</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Ikon</string>
    <string name="actionbar_basic_radio_label">Grunnleggende</string>
    <string name="actionbar_position_bottom_radio_label">Bunn</string>
    <string name="actionbar_position_top_radio_label">Topp</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Handlingsfelttype</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Plassering av handlingsfelt</string>

    <!--AppBar-->
    <string name="app_bar_style">Applinjestil</string>
    <string name="app_bar_subtitle">Undertittel</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Nedre kantlinje</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Navigasjonsikon klikket.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Flagg</string>
    <string name="app_bar_layout_menu_settings">Innstillinger</string>
    <string name="app_bar_layout_menu_search">Søk</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Rullevirkemåte: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Aktiver/deaktiver virkemåte for rulling</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Veksle navigasjonsikon</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Vis avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Vis tilbake-ikon</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Skjul ikon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Vis ikon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Aktiver/deaktiver søkbar oppsettstil</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Vis som tilbehørsvisning</string>
    <string name="app_bar_layout_searchbar_action_view_button">Vis som handlingsvisning</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Veksle mellom temaer (gjenskaper aktivitet)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Aktiver/deaktiver tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Ekstra rullbart innhold</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Sirkelstil</string>
    <string name="avatar_style_square">Firkantstil</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Stor</string>
    <string name="avatar_size_medium">Middels</string>
    <string name="avatar_size_small">Liten</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dobbel ekstra stor</string>
    <string name="avatar_size_xlarge_accessibility">Ekstra stor</string>
    <string name="avatar_size_xsmall_accessibility">Ekstra lite</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maks. vist avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Antall avatarer med overflyt</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Kantlinjetype</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatargruppe med OverflowAvatarCount angitt samsvarer ikke med maks. vist avatar.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Ansiktsstabel</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Ansiktsbunke</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Overflyt klikket</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView ved indeks %d klikket på</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Varselsmerke</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Prikk</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Liste</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Tegn</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotografier</string>
    <string name="bottom_navigation_menu_item_news">Nyheter</string>
    <string name="bottom_navigation_menu_item_alerts">Varsler</string>
    <string name="bottom_navigation_menu_item_calendar">Kalender</string>
    <string name="bottom_navigation_menu_item_team">Gruppe</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Aktiver/deaktiver etiketter</string>
    <string name="bottom_navigation_three_menu_items_button">Vis tre menyelementer</string>
    <string name="bottom_navigation_four_menu_items_button">Vis fire menyelementer</string>
    <string name="bottom_navigation_five_menu_items_button">Vis fem menyelementer</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Etiketter er %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Bunnark</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Aktiver Sveip ned for å lukke</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Vis med enkeltlinjeelementer</string>
    <string name="bottom_sheet_with_double_line_items">Vis med doble linjeelementer</string>
    <string name="bottom_sheet_with_single_line_header">Vis med enkeltlinjeelementer</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Vis med dobbel linjeoverskrift og skillelinjer</string>
    <string name="bottom_sheet_dialog_button">Vis</string>
    <string name="drawer_content_desc_collapse_state">Utvid</string>
    <string name="drawer_content_desc_expand_state">Minimer</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klikk %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Langt klikk %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klikk på avvis</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Sett inn element</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Oppdater element</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Lukk</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Legg til</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Omtale</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Fet</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursiv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Understreking</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Gjennomstreking</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Angre</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Gjør om</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Punkttegn</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Liste</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Kobling</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Elementet oppdateres</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Avstand</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Lukk posisjon</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">START</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Gruppeområde</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Elementområde</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Flagg</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Flagg klikket element</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Svar</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Svarelement klikket</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Videresend</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Fremoverelement klikket</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Slett</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Slett klikket element</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Ta et bilde</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kameraelement klikket</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galleri</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Vis bildene dine</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Gallerielement klikket</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videoer</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Spill av videoer</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Videoelement klikket</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Administrer</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Administrer mediebiblioteket</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Administrer klikket element</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-posthandlinger</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumenter</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Sist oppdatert kl. 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Del</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Del klikket element</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Flytt</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Flytt klikket element</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Slett</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Slett klikket element</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informasjon</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Informasjonselement klikket</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Klokke</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Klokkeelement klikket</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Alarmelement klikket</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Tidssone</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Tidssoneelement klikket</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Ulike visninger av knapp</string>
    <string name="button">Knapp</string>
    <string name="buttonbar">Knapplinje</string>
    <string name="button_disabled">Eksempel på deaktivert knapp</string>
    <string name="button_borderless">Eksempel på knapp uten kantlinje</string>
    <string name="button_borderless_disabled">Eksempel på deaktivert knapp uten kantlinje</string>
    <string name="button_large">Eksempel på stor knapp</string>
    <string name="button_large_disabled">Eksempel på stor deaktivert knapp</string>
    <string name="button_outlined">Eksempel på omrisset knapp</string>
    <string name="button_outlined_disabled">Eksempel på deaktivert knapp med omriss</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Velg en dato</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Enkeltdato</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Ingen dato valgt</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Vis datovelger</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Vis dato-/klokkeslettvelger med datofanen valgt</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Vis dato-/klokkeslettvelger med klokkeslettfanen valgt</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Vis dato-/klokkeslettvelger</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datoområde</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Start:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Slutt:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Ingen start er valgt</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Ingen slutt valgt</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Velg startdato</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Velg sluttdato</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Dato-/tidsintervall</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Velg dato-/tidsintervall</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Vis dialog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Vis skuff</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Vis skuffdialogboks</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Ingen nedtoning av nederste dialogboks</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Vis øverste skuff</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Ingen nedtoning av den øverste dialogboksen</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Vis dialogboksen for ankervisning øverst</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Vis ingen toppdialogboks for tittel</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Vis dialogboksen for tittel øverst nedenfor</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Vis høyre skuff</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Vis venstre skuff</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Tittel, primær tekst</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Undertittel, sekundær tekst</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Egendefinert undertekst</string>
    <!-- Footer -->
    <string name="list_item_footer">Bunntekst, tertiær tekst</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Liste med én linje med grå underoverskriftstekst</string>
    <string name="list_item_sub_header_two_line">Liste med to linjer</string>
    <string name="list_item_sub_header_two_line_dense">Tolinjet liste med tett avstand</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Tolinjet liste med egendefinert sekundær undertittelvisning</string>
    <string name="list_item_sub_header_three_line">Trelinjet liste med svart underoverskriftstekst</string>
    <string name="list_item_sub_header_no_custom_views">Vis elementer uten egendefinerte visninger</string>
    <string name="list_item_sub_header_large_header">Vis elementer uten egendefinerte visninger</string>
    <string name="list_item_sub_header_wrapped_text">Vis elementer med tekstbryting</string>
    <string name="list_item_sub_header_truncated_text">Vis elementer med avkortet tekst</string>
    <string name="list_item_sub_header_custom_accessory_text">Handling</string>
    <string name="list_item_truncation_middle">Midtre avkorting.</string>
    <string name="list_item_truncation_end">Avslutt avkorting.</string>
    <string name="list_item_truncation_start">Start avkorting.</string>
    <string name="list_item_custom_text_view">Verdi</string>
    <string name="list_item_click">Du klikket på listeelementet.</string>
    <string name="list_item_click_custom_accessory_view">Du klikket på den egendefinerte tilbehørsvisningen for underoverskriften.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Du klikket på den egendefinerte tilbehørsvisningen for underoverskriften.</string>
    <string name="list_item_more_options">Flere alternativer</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Velg</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Ingen</string>
    <string name="people_picker_delete_example">Slett</string>
    <string name="people_picker_custom_persona_description">Dette eksemplet viser hvordan du oppretter et egendefinert IPersona-objekt.</string>
    <string name="people_picker_dialog_title_removed">Du fjernet en identitet:</string>
    <string name="people_picker_dialog_title_added">Du la til en identitet:</string>
    <string name="people_picker_drag_started">Trekking startet</string>
    <string name="people_picker_drag_ended">Trekking avsluttet</string>
    <string name="people_picker_picked_personas_listener">Identitetslytter</string>
    <string name="people_picker_suggestions_listener">Forslagslytter</string>
    <string name="people_picker_persona_chip_click">Du klikket på %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s mottaker</item>
        <item quantity="other">%1$s mottakere</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Vis vedvarende bunnark</string>
    <string name="collapse_persistent_sheet_button"> Skjul vedvarende bunnark</string>
    <string name="show_persistent_sheet_button"> Vis vedvarende bunnark</string>
    <string name="new_view">Dette er en ny visning</string>
    <string name="toggle_sheet_content">Aktiver/deaktiver innhold i bunnark</string>
    <string name="switch_to_custom_content">Bytt til egendefinert innhold</string>
    <string name="one_line_content">Bunnarkinnhold med én linje</string>
    <string name="toggle_disable_all_items">Veksle Deaktiver alle elementer</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Legg til/fjern visning</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Endre skjult høyde</string>
    <string name="persistent_sheet_item_create_new_folder_title">Ny mappe</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Nytt mappeelement klikket</string>
    <string name="persistent_sheet_item_edit_title">Rediger</string>
    <string name="persistent_sheet_item_edit_toast">Rediger klikket element</string>
    <string name="persistent_sheet_item_save_title">Lagre</string>
    <string name="persistent_sheet_item_save_toast">Lagre klikket element</string>
    <string name="persistent_sheet_item_zoom_in_title">Zoom inn</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Zoom inn-element klikket</string>
    <string name="persistent_sheet_item_zoom_out_title">Zoom ut</string>
    <string name="persistent_sheet_item_zoom_out_toast">Zoom ut-element klikket</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Tilgjengelig</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Utforming</string>
    <string name="persona_subtitle_engineer">Tekniker</string>
    <string name="persona_subtitle_manager">Leder</string>
    <string name="persona_subtitle_researcher">Forsker</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (langt teksteksempel for å teste avkorting)</string>
    <string name="persona_view_description_xxlarge">XXLarge-avatar med tre linjer med tekst</string>
    <string name="persona_view_description_large">Stor avatar med to linjer med tekst</string>
    <string name="persona_view_description_small">Liten avatar med én linje med tekst</string>
    <string name="people_picker_hint">Ingen med tipset vist</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Deaktivert identitetsbrikke</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Feil med identitetsbrikke</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Identitetsbrikke uten lukkeikon</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Grunnleggende identitetsbrikke</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Du klikket på en valgt identitetsbrikke.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Del</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Følg</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Inviter personer</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Oppdater side</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Åpne i nettleser</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Dette er en hurtigmeny med flere linjer. Maks. antall linjer er satt til to. Resten av teksten trunkeres.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Alle nyheter</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Lagrede nyheter</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Nyheter fra områder</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Varsle utenom arbeidstid</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Varsle ved inaktivitet på skrivebordet</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Du klikket på elementet:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Enkel meny</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Enkel meny2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Meny med ett valgbart element og en delelinje</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Meny med alle valgbare elementer, ikoner og lang tekst</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Vis</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Sirkelfremdrift</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Liten</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Middels</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Stor</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineær fremdrift</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Ubestemmelig</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Bestemmelig</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Tilbakeringing for mikrofon</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autokorrektur</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon trykket</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Høyrevisning trykket</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Tastatursøk trykket</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Vis snackbar</string>
    <string name="fluentui_dismiss_snackbar">Lukk snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Handling</string>
    <string name="snackbar_action_long">Lang teksthandling</string>
    <string name="snackbar_single_line">Snackbar med én linje</string>
    <string name="snackbar_multiline">Dette er en snackbar med flere linjer. Maks. antall linjer er satt til to. Resten av teksten trunkeres.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Dette er en kunngjøringssnackbar. Den brukes til å kommunisere nye funksjoner.</string>
    <string name="snackbar_primary">Dette er en hovedsnackbar.</string>
    <string name="snackbar_light">Dette er en lett snackbar.</string>
    <string name="snackbar_warning">Dette er en advarselssnackbar.</string>
    <string name="snackbar_danger">Dette er en snackbar for fare.</string>
    <string name="snackbar_description_single_line">Kort varighet</string>
    <string name="snackbar_description_single_line_custom_view">Lang varighet med sirkelfremdrift som liten egendefinert visning</string>
    <string name="snackbar_description_single_line_action">Kort varighet med handling</string>
    <string name="snackbar_description_single_line_action_custom_view">Kort varighet med handling og middels egendefinert visning</string>
    <string name="snackbar_description_single_line_custom_text_color">Kort varighet med egendefinert tekstfarge</string>
    <string name="snackbar_description_multiline">Lang varighet</string>
    <string name="snackbar_description_multiline_custom_view">Lang varighet med liten egendefinert visning</string>
    <string name="snackbar_description_multiline_action">Uendelig varighet med handlings- og tekstoppdateringer</string>
    <string name="snackbar_description_multiline_action_custom_view">Kort varighet med handling og middels egendefinert visning</string>
    <string name="snackbar_description_multiline_action_long">Kort varighet med lang handlingstekst</string>
    <string name="snackbar_description_announcement">Kort varighet</string>
    <string name="snackbar_description_updated">Denne teksten er oppdatert.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Vis snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Enkel linje</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Flere linjer</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Kunngjøringsstil</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primærstil</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Lys stil</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Advarselsstil</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Farestil</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Hjem</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">E-post</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Innstillinger</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Varsel</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mer</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Tekstjustering</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Loddrett</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vannrett</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Ingen tekst</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Faneelementer</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Tittel</string>
    <string name="cell_sample_description">Beskrivelse</string>
    <string name="calculate_cells">Last inn/beregn 100 celler</string>
    <string name="calculate_layouts">Last inn / beregn 100 oppsett</string>
    <string name="template_list">Liste over maler</string>
    <string name="regular_list">Vanlig liste</string>
    <string name="cell_example_title">Tittel: celle</string>
    <string name="cell_example_description">Beskrivelse: trykk for å endre retning</string>
    <string name="vertical_layout">Loddrett oppsett</string>
    <string name="horizontal_layout">Vannrett oppsett</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standard fane 2-segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardsegment med 3 faner</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardsegment med 4 faner</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardfane med personsøker</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Bytt fane</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Knappefane </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Trykk for verktøytips</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Trykk for verktøytips om egendefinert kalender</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Trykk på verktøytips for egendefinert farge</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Trykk for å lukke innvendig verktøytips</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Trykk for verktøytips om egendefinert visning</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Verktøytips om mest populære egendefinerte farge</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Verktøytips for øvre ende med 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Verktøytips for nederste start</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Verktøytips for nedre ende med 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Lukk innvendig verktøytips</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Verktøytips er lukket</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Overskriften er lys 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Tittel 1 er middels 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Tittel 2 er vanlig 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Overskriften er vanlig 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Underoverskrift 1 er vanlig 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Underoverskrift 2 er middels 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Brødtekst 1 er vanlig 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Brødtekst 2 er middels 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Underteksten er vanlig 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-versjon: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Element %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mappe</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Klikket</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB utvidet</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB lukket</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klikk for å oppdatere listen</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Åpne skuff</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menyelement</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Forskyvning X (i dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Forskyvning Y (i dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Innholdstekst</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Gjenta innholdstekst</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menybredden endres når det gjelder innholdstekst. Maksimal
        bredde er begrenset til 75 % av skjermstørrelsen. Innholdsmargen fra side og bunn styres av token. Den samme innholdsteksten gjentas for å variere
        høyden.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Åpne meny</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Grunnleggende kort</string>
    <!-- UI Label for Card -->
    <string name="file_card">Filkort</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Kunngjøringskort</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Tilfeldig brukergrensesnitt</string>
    <!-- UI Label for Options -->
    <string name="card_options">Alternativer</string>
    <!-- UI Label for Title -->
    <string name="card_title">Tittel</string>
    <!-- UI Label for text -->
    <string name="card_text">Tekst</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Undertekst</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekundærkopi for dette banneret kan brytes til to linjer om nødvendig.</string>
    <!-- UI Label Button -->
    <string name="card_button">Knapp</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Vis dialog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Lukk dialogboks ved å klikke utenfor</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Lukk dialogboks ved tilbaketrykk</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialogboks lukket</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Avbryt</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">En dialogboks er et lite vindu som ber brukeren om å ta en beslutning eller angi tilleggsinformasjon.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Åpne skuff</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Utvid skuff</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Lukk skuffen</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Velg skufftype</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Topp</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Hele skuffen vises i det synlige området.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Bunn</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Hele skuffen vises i det synlige området. Sveip opp ruller innhold. Utvidbar skuff utvides via drahåndtak.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Venstre lysbilde over</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Skuff skyves over til synlig område fra venstre side.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Høyre lysbilde over</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Skuff skyves over til synlig område fra høyre side.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Nedre lysbilde over</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Skuff skyver over til synlig område fra bunnen av skjermen. Sveip opp på utvidbar skuff flytter resten av delen til synlig område &amp; rull deretter.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim synlig</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Velg skuffinnhold</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Rullbart innhold i fullskjermstørrelse</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mer enn halvt skjerminnhold</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mindre enn halvt skjerminnhold</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dynamisk innholdsstørrelse</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Nestet skuffinnhold</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Utvidbar</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Hopp over åpen tilstand</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Forhindre oppsigelse ved Scrim-klikk</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Vis håndtak</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Tittel</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Verktøytipstekst</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Trykk for å få verktøytips om egendefinert innhold</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Øverste start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Øvre kant </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Start nederst </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Nedre kant </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Midtstill </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Egendefinert midtstilling</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Hvis du vil ha oppdateringer om produktmerknader, </string>
    <string name="click_here">klikk her.</string>
    <string name="open_source_cross_platform">System for utforming på tvers av plattformer med åpen kildekode.</string>
    <string name="intuitive_and_powerful">Intuitiv &amp; kraftig.</string>
    <string name="design_tokens">Tokener for utforminger</string>
    <string name="release_notes">Produktmerknader</string>
    <string name="github_repo">GitHub-repositorium</string>
    <string name="github_repo_link">Kobling til GitHub-repositorium</string>
    <string name="report_issue">Rapporter problem</string>
    <string name="v1_components">V1-komponenter</string>
    <string name="v2_components">V2-komponenter</string>
    <string name="all_components">Alle</string>
    <string name="fluent_logo">Fluent-logo</string>
    <string name="new_badge">Nytt</string>
    <string name="modified_badge">Endret</string>
    <string name="api_break_badge">API-brudd</string>
    <string name="app_bar_more">Flere</string>
    <string name="accent">Utheving</string>
    <string name="appearance">Utseende</string>
    <string name="choose_brand_theme">Velg tema for varemerke:</string>
    <string name="fluent_brand_theme">Fluent-varemerket</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">Microsoft Teams</string>
    <string name="choose_appearance">Velg utseende</string>
    <string name="appearance_system_default">Systemstandard</string>
    <string name="appearance_light">Lyst</string>
    <string name="appearance_dark">Mørk</string>
    <string name="demo_activity_github_link">Kobling til demoaktivitet på GitHub</string>
    <string name="control_tokens_details">Detaljer om tokener for kontrollere</string>
    <string name="parameters">Parametere</string>
    <string name="control_tokens">Tokener for kontrollere</string>
    <string name="global_tokens">Globale tokener</string>
    <string name="alias_tokens">Tokener for aliaser</string>
    <string name="sample_text">Tekst</string>
    <string name="sample_icon">Eksempelikon</string>
    <string name="color">Farge</string>
    <string name="neutral_color_tokens">Tokener for nøytral farge</string>
    <string name="font_size_tokens">Tokener for skriftstørrelse</string>
    <string name="line_height_tokens">Tokener for linjehøyde</string>
    <string name="font_weight_tokens">Tokener for skrifttykkelse</string>
    <string name="icon_size_tokens">Tokener for ikonstørrelse</string>
    <string name="size_tokens">Tokener for størrelse</string>
    <string name="shadow_tokens">Tokener for skygge</string>
    <string name="corner_radius_tokens">Tokener for hjørneradius</string>
    <string name="stroke_width_tokens">Tokener for pennestrøkbredde</string>
    <string name="brand_color_tokens">Tokener for farger for merkevare</string>
    <string name="neutral_background_color_tokens">Tokener for nøytral bakgrunnsfarge</string>
    <string name="neutral_foreground_color_tokens">Tokener for nøytral forgrunnsfarge</string>
    <string name="neutral_stroke_color_tokens">Tokener for nøytral pennestrøkfarge</string>
    <string name="brand_background_color_tokens">Tokener for bakgrunnsfarge for merkevare</string>
    <string name="brand_foreground_color_tokens">Tokener for forgrunnsfarge for merkevare</string>
    <string name="brand_stroke_color_tokens">Tokener for pennestrøkfarge for merkevare</string>
    <string name="error_and_status_color_tokens">Tokener for feil- og statusfarger</string>
    <string name="presence_tokens">Tokener for tilstedeværelsesfarge</string>
    <string name="typography_tokens">Tokener for typografi</string>
    <string name="unspecified">Uspesifisert</string>

</resources>