<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">عرض توضيحي لواجهة مستخدم Fluent</string>
    <string name="app_title">واجهة مستخدم Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">تم تحديد %s</string>
    <string name="app_modifiable_parameters">معلمات قابلة للتعديل</string>
    <string name="app_right_accessory_view">طريقة عرض الملحقات اليمنى</string>

    <string name="app_style">نمط</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">تم الضغط على الأيقونة</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">بدء العرض التوضيحي</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">عرض دوار</string>
    <string name="actionbar_icon_radio_label">أيقونة</string>
    <string name="actionbar_basic_radio_label">بسيط</string>
    <string name="actionbar_position_bottom_radio_label">أسفل</string>
    <string name="actionbar_position_top_radio_label">أعلى</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">نوع شريط الإجراءات</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">موضع شريط الإجراءات</string>

    <!--AppBar-->
    <string name="app_bar_style">نمط شريط التطبيقات</string>
    <string name="app_bar_subtitle">شريط الترجمة</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">الحد السفلي</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">تم النقر فوق أيقونة التنقل.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">وضع علامة</string>
    <string name="app_bar_layout_menu_settings">الإعدادات</string>
    <string name="app_bar_layout_menu_search">بحث</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">سلوك التمرير: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">تبديل سلوك التمرير</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">تبديل أيقونة التنقل</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">إظهار صورة الأفاتار</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">إظهار أيقونة العودة</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">إخفاء الأيقونة</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">إظهار الأيقونة</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">تبديل نمط تخطيط شريط البحث</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">إظهار كطريقة عرض ملحق</string>
    <string name="app_bar_layout_searchbar_action_view_button">إظهار كطريقة عرض الإجراء</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">التبديل بين النسق (إعادة إنشاء النشاط)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">تبديل النسق</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">عنصر</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">محتوى إضافي قابل للتمرير</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">نمط الدائرة</string>
    <string name="avatar_style_square">نمط مربع</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">كبير</string>
    <string name="avatar_size_medium">متوسط</string>
    <string name="avatar_size_small">صغير</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">كبير جداً للغاية</string>
    <string name="avatar_size_xlarge_accessibility">كبير جداً</string>
    <string name="avatar_size_xsmall_accessibility">صغير جداً</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">أقصى صورة أفاتار معروضة</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">تجاوز عدد صور الأفاتار</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">نوع الحد</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">لن تلتزم مجموعة الأفاتار مع مجموعة OverflowAvatarCount بحد أقصى من الأفاتار المعروض.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">مكدس الوجه</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">كومة الأوجه</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">تم النقر فوق تجاوز النص</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">تم النقر فوق AvatarView في الفهرس %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">شارة الإعلامات</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">نقاط</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">القائمة</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">شخصية</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">صور</string>
    <string name="bottom_navigation_menu_item_news">الأخبار</string>
    <string name="bottom_navigation_menu_item_alerts">تنبيهات</string>
    <string name="bottom_navigation_menu_item_calendar">التقويم</string>
    <string name="bottom_navigation_menu_item_team">فريق</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">تبديل التسميات</string>
    <string name="bottom_navigation_three_menu_items_button">إظهار عناصر القائمة الثلاثة</string>
    <string name="bottom_navigation_four_menu_items_button">إظهار أربعة عناصر قائمة</string>
    <string name="bottom_navigation_five_menu_items_button">إظهار خمسة عناصر قائمة</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">التسميات هي %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">ورقة سفلي</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">تمكين السحب لأسفل للتجاهل</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">إظهار بعناصر ذات سطر واحد</string>
    <string name="bottom_sheet_with_double_line_items">إظهار مع عناصر سطر مزدوج</string>
    <string name="bottom_sheet_with_single_line_header">إظهار مع رأس سطر واحد</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">إظهار برأس مزدوج الخطوط وبالفواصل</string>
    <string name="bottom_sheet_dialog_button">إظهار</string>
    <string name="drawer_content_desc_collapse_state">توسيع</string>
    <string name="drawer_content_desc_expand_state">تصغير</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">نقرة فوق %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">نقرة طويلة فوق %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">انقر فوق تجاهل</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">إدراج عنصر</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">تحديث العنصر</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">تجاهل</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">إضافة</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">إشارة</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">غامق</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">مائل</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">تسطير</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">يتوسطه خط</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">تراجع</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">إعادة</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">رمز نقطي</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">القائمة</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">رابط</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">تحديث العنصر</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">التباعد</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">تجاهل الموضع</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">البدء</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">انتهاء</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">مساحة المجموعة</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">مساحة العنصر</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">وضع علامة</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">تم النقر فوق عنصر العلامة</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">الرد</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">تم النقر فوق عنصر الرد</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">إعادة توجيه</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">تم النقر فوق عنصر إعادة التوجيه</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">حذف</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">حذف العنصر الذي تم النقر فوقه</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">أفاتار</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">كاميرا</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">التقاط صورة</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">تم النقر فوق عنصر الكاميرا</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">معرض</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">عرض صورك</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">تم النقر فوق عنصر المعرض</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">مقاطع فيديو</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">تشغيل مقاطع الفيديو الخاصة بك</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">تم النقر فوق عنصر مقاطع الفيديو</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">إدارة</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">إدارة مكتبة الوسائط</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">تم النقر فوق إدارة العنصر</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">إجراءات البريد الإلكتروني</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">مستندات</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">تاريخ آخر تحديث 2:14 م</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">مشاركة</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">تم النقر فوق مشاركة العنصر</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">نقل</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">تم النقر فوق نقل العنصر</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">حذف</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">حذف العنصر الذي تم النقر فوقه</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">المعلومات</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">تم النقر فوق عنصر المعلومات</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">ساعة</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">تم النقر فوق عنصر الساعة</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">إنذار</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">تم النقر فوق عنصر المنبه</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">منطقة زمنية</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">تم النقر فوق عنصر المنطقة الزمنية</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">طرق عرض مختلفة للزر</string>
    <string name="button">زر</string>
    <string name="buttonbar">شريط الأزرار</string>
    <string name="button_disabled">مثال على زر معطّل</string>
    <string name="button_borderless">مثال على زر بدون حدود</string>
    <string name="button_borderless_disabled">مثال على زر بدون حدود معطّل</string>
    <string name="button_large">مثال على زر كبير</string>
    <string name="button_large_disabled">مثال على زر كبير معطّل</string>
    <string name="button_outlined">مثال على زر محدد</string>
    <string name="button_outlined_disabled">مثال على زر معطّل محدد</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">اختيار تاريخ</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">Datetimepicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">تاريخ واحد</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">لم يتم اختيار أي تاريخ</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">إظهار منتقي التاريخ</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">إظهار منتقي وقت التاريخ مع تحديد علامة تبويب التاريخ</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">إظهار منتقي وقت التاريخ مع تحديد علامة تبويب الوقت</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">إظهار منتقي وقت التاريخ</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">نطاق التاريخ</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">تاريخ البدء:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">الانتهاء:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">لم يتم اختيار أي بدء</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">لم يتم انتقاء أي نهاية</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">تحديد تاريخ البدء</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">تحديد تاريخ الانتهاء</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">نطاق وقت التاريخ</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">تحديد نطاق وقت التاريخ</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">إظهار مربع الحوار</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">إظهار الدرج</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">إظهار مربع حوار الدرج</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">لا يوجد مربع حوار تلاشي أسفل</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">إظهار الدرج العلوي</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">لا يوجد مربع حوار تلاشي علوي</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> إظهار مربع الحوار العلوي لعرض الارتساء</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> عدم إظهار مربع الحوار العلوي للعنوان</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> إظهار مربع الحوار العلوي للعنوان السفلي</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">إظهار الدرج الأيمن</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">إظهار الدرج الأيسر</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">العنوان، النص الأساسي</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">العنوان الفرعي، نص ثانوي</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">نص العنوان الفرعي المخصص</string>
    <!-- Footer -->
    <string name="list_item_footer">تذييل الصفحة، نص ثالث</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">قائمة من سطر واحد مع نص رأس فرعي رمادي</string>
    <string name="list_item_sub_header_two_line">قائمة مكونة من سطرين</string>
    <string name="list_item_sub_header_two_line_dense">قائمة ذات خطين مع تباعد كثيف</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">قائمة من سطرين مع طريقة عرض العناوين الفرعية الثانوية المخصصة</string>
    <string name="list_item_sub_header_three_line">قائمة ثلاثية الأسطر مع نص رأس فرعي أسود</string>
    <string name="list_item_sub_header_no_custom_views">سرد العناصر التي لا تحتوي على طرق عرض مخصصة</string>
    <string name="list_item_sub_header_large_header">سرد العناصر ذات طرق العرض المخصصة الكبيرة</string>
    <string name="list_item_sub_header_wrapped_text">سرد العناصر ذات النص الملتف</string>
    <string name="list_item_sub_header_truncated_text">سرد العناصر ذات النص المقتطع</string>
    <string name="list_item_sub_header_custom_accessory_text">الإجراء</string>
    <string name="list_item_truncation_middle">اقتطاع متوسط.</string>
    <string name="list_item_truncation_end">إنهاء الاقتطاع.</string>
    <string name="list_item_truncation_start">بدء الاقتطاع.</string>
    <string name="list_item_custom_text_view">قيمة</string>
    <string name="list_item_click">قمت بالنقر فوق عنصر القائمة.</string>
    <string name="list_item_click_custom_accessory_view">قمت بالنقر فوق طريقة عرض الملحقات المخصصة.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">قمت بالنقر فوق طريقة عرض الملحقات المخصصة للرأس الفرعي.</string>
    <string name="list_item_more_options">خيارات إضافية</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">تحديد</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">بلا</string>
    <string name="people_picker_delete_example">حذف</string>
    <string name="people_picker_custom_persona_description">يوضح هذا المثال كيفية إنشاء عنصر IPersona مخصص.</string>
    <string name="people_picker_dialog_title_removed">لقد قمت بإزالة شخصية:</string>
    <string name="people_picker_dialog_title_added">لقد أضفت شخصية:</string>
    <string name="people_picker_drag_started">بدأ السحب</string>
    <string name="people_picker_drag_ended">انتهى السحب</string>
    <string name="people_picker_picked_personas_listener">مستمع الشخصيات</string>
    <string name="people_picker_suggestions_listener">إصغاء الاقتراحات</string>
    <string name="people_picker_persona_chip_click">قمت بالنقر فوق %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="zero">%1$s من المستلمين</item>
        <item quantity="one">مستلم %1$s</item>
        <item quantity="two">مستلمان %1$s</item>
        <item quantity="few">%1$s مستلمين</item>
        <item quantity="many">%1$s مستلماً</item>
        <item quantity="other">%1$s مستلم</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">توسيع ورقة سفلية ثابتة</string>
    <string name="collapse_persistent_sheet_button"> إخفاء ورقة سفلية ثابتة</string>
    <string name="show_persistent_sheet_button"> إظهار ورقة سفلية ثابتة</string>
    <string name="new_view">هذه طريقة عرض جديدة</string>
    <string name="toggle_sheet_content">تبديل محتوى الورقة السفلية</string>
    <string name="switch_to_custom_content">التبديل إلى محتوى مخصص</string>
    <string name="one_line_content">محتوى ورقة سفلي سطر واحد</string>
    <string name="toggle_disable_all_items">تبديل تعطيل كافة العناصر</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">إضافة/إزالة طريقة العرض</string>
    <string name="persistent_sheet_item_change_collapsed_height"> تغيير الارتفاع المطوي</string>
    <string name="persistent_sheet_item_create_new_folder_title">مجلد جديد</string>
    <string name="persistent_sheet_item_create_new_folder_toast">تم النقر فوق عنصر مجلد جديد</string>
    <string name="persistent_sheet_item_edit_title">تحرير</string>
    <string name="persistent_sheet_item_edit_toast">تم النقر فوق تحرير العنصر</string>
    <string name="persistent_sheet_item_save_title">حفظ</string>
    <string name="persistent_sheet_item_save_toast">تم النقر فوق حفظ العنصر</string>
    <string name="persistent_sheet_item_zoom_in_title">تكبير</string>
    <string name="persistent_sheet_item_zoom_in_toast"> تم النقر فوق عنصر التكبير/التصغير</string>
    <string name="persistent_sheet_item_zoom_out_title">تصغير</string>
    <string name="persistent_sheet_item_zoom_out_toast">تم النقر فوق عنصر التصغير</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">متوفر</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">أماندا برادي</string>
    <string name="persona_name_ashley_mccarthy">أشلي مكارثى</string>
    <string name="persona_name_carlos_slattery">سلام بيطار</string>
    <string name="persona_name_carole_poland">كارول بولند</string>
    <string name="persona_name_cecil_folk">سيسيل فولك</string>
    <string name="persona_name_celeste_burton">سيلست بيرتون</string>
    <string name="persona_name_charlotte_waltson">تشارلو والتسون</string>
    <string name="persona_name_colin_ballinger">كولين بالينجر</string>
    <string name="persona_name_daisy_phillips">دايسي فيليبس</string>
    <string name="persona_name_elliot_woodward">إليوت وودوارد</string>
    <string name="persona_name_elvia_atkins">إليا أتكينس</string>
    <string name="persona_name_erik_nason">إيريك ناسون</string>
    <string name="persona_name_henry_brill">جاكلين بريل</string>
    <string name="persona_name_isaac_fielder">إيزاك فيلدر</string>
    <string name="persona_name_johnie_mcconnell">جوني ماكونيل</string>
    <string name="persona_name_kat_larsson">كات لارسون</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">كيفين ستارجيس</string>
    <string name="persona_name_kristen_patterson">كريستن باتيرسون</string>
    <string name="persona_name_lydia_bauer">ليديا باور</string>
    <string name="persona_name_mauricio_august">ماوريسيو أغسطس</string>
    <string name="persona_name_miguel_garcia">ميغيل غارسيا</string>
    <string name="persona_name_mona_kane">منى كين</string>
    <string name="persona_name_robin_counts">روبين كاونتس</string>
    <string name="persona_name_robert_tolbert">روبيرت تولبيرت</string>
    <string name="persona_name_tim_deboer">تيم ديبور</string>
    <string name="persona_name_wanda_howard">واندا هاورد</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">المصمم</string>
    <string name="persona_subtitle_engineer">مهندس</string>
    <string name="persona_subtitle_manager">مدير</string>
    <string name="persona_subtitle_researcher">الباحث</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (مثال نص طويل لاختبار الاقتطاع)</string>
    <string name="persona_view_description_xxlarge">صورة XXLarge مع ثلاثة أسطر من النص</string>
    <string name="persona_view_description_large">صورة أفاتار كبيرة مع سطرين من النص</string>
    <string name="persona_view_description_small">صورة أفاتار صغيرة مع سطر واحد من النص</string>
    <string name="people_picker_hint">بلا مع ظهور تلميح</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">شريحة شخصية معطلة</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">شريحة شخصية الخطأ</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">شريحة شخصية بدون أيقونة إغلاق</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">شريحة شخصية أساسية</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">قمت بالنقر فوق شريحة شخصية محددة.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">مشاركة</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">متابعة</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">دعوة أشخاص</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">تحديث الصفحة</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">فتح في مستعرض</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">هذه قائمة منبثقة متعددة الأسطر. تم تعيين الحد الأقصى للسطرين، سيتم اقتطاع باقي النص.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">كل الأخبار</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">الأخبار المحفوظة</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">أخبار من المواقع</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">إعلام خارج ساعات العمل</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">الإعلام عندما يكون غير نشط على سطح المكتب</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">قمت بالنقر فوق العنصر:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">قائمة بسيطة</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">قائمة بسيطة2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">قائمة بعنصر واحد قابل للتحديد ومقسم</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">قائمة تتضمن كافة العناصر والأيقونات والنص الطويل القابل للتحديد</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">إظهار</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">تقدم دائري</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">صغير</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">متوسط</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">كبير</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">تقدم خطي</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">غير معروف</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">تحديد</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">شريط البحث</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">رد اتصال الميكروفون</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">التصحيح التلقائي</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">الميكروفون مضغوط</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">تم الضغط على طريقة العرض اليمنى</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">تم الضغط على البحث في لوحة المفاتيح</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">إظهار شريط الإعلامات</string>
    <string name="fluentui_dismiss_snackbar">تجاهل شريط الإعلامات</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">الإجراء</string>
    <string name="snackbar_action_long">إجراء نص طويل</string>
    <string name="snackbar_single_line">شريط الإعلامات بخط واحد</string>
    <string name="snackbar_multiline">هذا شريط وجبات خفيفة متعدد الأسطر. تم تعيين الحد الأقصى للسطرين، سيتم اقتطاع باقي النص.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">هذا شريط إعلامات للإعلان. يتم استخدامه لإبلاغ الميزات الجديدة.</string>
    <string name="snackbar_primary">هذا شريط إعلامات الأساسي.</string>
    <string name="snackbar_light">هذا شريط إعلامات فاتح.</string>
    <string name="snackbar_warning">هذا شريط إعلام للتحذير.</string>
    <string name="snackbar_danger">هذا شريط إعلامات عن الخطر.</string>
    <string name="snackbar_description_single_line">مدة قصيرة</string>
    <string name="snackbar_description_single_line_custom_view">مدة طويلة مع تقدم دائري كطريقة عرض مخصصة صغيرة</string>
    <string name="snackbar_description_single_line_action">مدة قصيرة مع إجراء</string>
    <string name="snackbar_description_single_line_action_custom_view">مدة قصيرة مع الإجراء وعرض مخصص متوسط</string>
    <string name="snackbar_description_single_line_custom_text_color">مدة قصيرة مع لون نص مخصص</string>
    <string name="snackbar_description_multiline">مدة طويلة</string>
    <string name="snackbar_description_multiline_custom_view">مدة طويلة مع طريقة عرض مخصصة صغيرة</string>
    <string name="snackbar_description_multiline_action">مدة غير محددة مع تحديثات الإجراءات والنصوص</string>
    <string name="snackbar_description_multiline_action_custom_view">مدة قصيرة مع الإجراء وعرض مخصص متوسط</string>
    <string name="snackbar_description_multiline_action_long">مدة قصيرة مع نص إجراء طويل</string>
    <string name="snackbar_description_announcement">مدة قصيرة</string>
    <string name="snackbar_description_updated">تم تحديث هذا النص.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">إظهار شريط الإعلامات</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">خط مفرد</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">خطوط متعددة</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">نمط الإعلان</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">النمط الأساسي</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">نمط فاتح</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">نمط التحذير</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">نمط الخطر</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">الصفحة الرئيسية</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">بريد</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">الإعدادات</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">إشعار</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">المزيد</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">محاذاة النص</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">عمودي</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">أفقي</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">لا يوجد نص</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">عناصر علامة التبويب</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">العنوان</string>
    <string name="cell_sample_description">وصف</string>
    <string name="calculate_cells">تحميل/حساب 100 خلية</string>
    <string name="calculate_layouts">تحميل/حساب 100 تخطيط</string>
    <string name="template_list">قائمة القوالب</string>
    <string name="regular_list">قائمة عادية</string>
    <string name="cell_example_title">العنوان: خلية</string>
    <string name="cell_example_description">الوصف: اضغط لتغيير الاتجاه</string>
    <string name="vertical_layout">تخطيط عمودي</string>
    <string name="horizontal_layout">تخطيط أفقي</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">علامة تبويب قياسية مقطع-2</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">علامة تبويب قياسية 3-مقاطع</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">علامة تبويب قياسية 4-مقاطع</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">علامة تبويب قياسية مع جهاز النّداء</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">تبديل علامة التبويب</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">علامة تبويب الأقراص </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">اضغط للحصول على تلميح الأدوات</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">اضغط للحصول على تلميح أدوات التقويم المخصص</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">اضغط على تلميح أدوات الألوان المخصصة</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">اضغط لتجاهل تلميح الأدوات الداخلي</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">اضغط للحصول على تلميح أدوات العرض المخصص</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">تلميح أدوات اللون المخصص العلوي</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">تلميح أداة النهاية العلوية مع إزاحةX 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">تلميح أدوات البدء السفلي</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">تلميح أداة النهاية السفلية مع إزاحةY 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">تجاهل داخل تلميح الأدوات</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">تم تجاهل تعريف الأدوات</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">العنوان فاتح 28 ملعقة</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">العنوان 1 هو متوسط 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">العنوان 2 هو 20sp عادي</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">العنوان هو 18sp عادي</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">العنوان الفرعي 1 هو 16sp عادي</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">العنوان الفرعي 2 هو متوسط 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">النص الأساسي 1 هو 14sp عادي</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">النص الأساسي 2 متوسط 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">التسمية التوضيحية عادية 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">إصدار عدة تطوير البرامج (SDK): %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">العنصر %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">مجلد</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">تم النقر</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">الهيكل</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">زر الإجراء العائم</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">تم توسيع زر الإجراء العائم</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">تم طي زر الإجراء العائم</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">انقر لتحديث القائمة</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">فتح الدرج</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">عنصر القائمة</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">إزاحة X (في dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">إزاحة Y (في dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">نص المحتوى</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">تكرار نص المحتوى</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">سيتغير عرض القائمة فيما يتعلق بنص المحتوى. يقتصر الحد الأقصى
        للعرض على 75% من حجم الشاشة. يتم التحكم في هامش المحتوى من جانب وأسفل برمز مميز. سيتكرر نص المحتوى نفسه لتغيير
        الارتفاع.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">فتح القائمة</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">البطاقة الأساسية</string>
    <!-- UI Label for Card -->
    <string name="file_card">بطاقة الملف</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">بطاقة الإعلان</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">واجهة مستخدم عشوائية</string>
    <!-- UI Label for Options -->
    <string name="card_options">خيارات</string>
    <!-- UI Label for Title -->
    <string name="card_title">العنوان</string>
    <!-- UI Label for text -->
    <string name="card_text">نص</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">نص فرعي</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">يمكن للنسخة الثانوية لهذا الشعار الالتفاف إلى سطرين إذا لزم الأمر.</string>
    <!-- UI Label Button -->
    <string name="card_button">زر</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">إظهار مربع الحوار</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">تجاهل مربع الحوار عند النقر خارج</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">تجاهل مربع الحوار عند الضغط مرة أخرى</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">تم تجاهل مربع الحوار</string>
    <!-- UI Label Cancel -->
    <string name="cancel">إلغاء الأمر</string>
    <!-- UI Label Ok -->
    <string name="ok">موافق</string>
    <!-- A sample description -->
    <string name="dialog_description">مربع الحوار هو نافذة صغيرة تطالب المستخدم لاتخاذ قرار أو إدخال معلومات إضافية.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">فتح الدرج</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">توسيع الدرج</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">إغلاق الدرج</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">تحديد نوع الدرج</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">أعلى</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">يظهر الدرج بالكامل في المنطقة المرئية.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">أسفل</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">يظهر الدرج بالكامل في المنطقة المرئية. اسحب لأعلى لمحتوى تمرير الحركة. يتم توسيع الدرج القابل للتوسيع عبر مقبض السحب.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">شريحة يسرى فوق</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">شريحة الدرج إلى منطقة مرئية من الجانب الأيمن.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">شريحة يمنى فوق</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">تمرير الدرج إلى منطقة مرئية من الجانب الأيسر.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">شريحة سفلية فوق</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">تمرير الدرج إلى منطقة مرئية من أسفل الشاشة. اسحب لأعلى على درج قابل للتوسيع وأحضر الجزء المتبقي منها إلى المنطقة المرئية ثم قم بالتمرير.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">خربشة مرئية</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">تحديد محتوى الدرج</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">محتوى قابل للتمرير بحجم ملء الشاشة</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">أكثر من نصف محتوى الشاشة</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">محتوى أقل من نصف الشاشة</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">محتوى ديناميكي الحجم</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">محتوى الدرج المتداخل</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">قابل للتوسيع</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">تخطي الحالة المفتوحة</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">منع الرفض عند النقر فوق scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">إظهار المقبض</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">العنوان</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">نص تلميحات الشاشة</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">انقر للحصول على تعريف أدوات المحتوى المخصص</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">بداية الجزء العلوي </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">الطرف العلوي </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">البداية من أسفل </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">الطرف السفلي </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">يوسّط </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">توسيط مخصص</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">للحصول على تحديثات حول ملاحظات حول الإصدار، </string>
    <string name="click_here">انقر هنا.</string>
    <string name="open_source_cross_platform">نظام تصميم مفتوح المصدر عبر منصة.</string>
    <string name="intuitive_and_powerful">سهل &amp; قوي.</string>
    <string name="design_tokens">رموز تصميم مميزة</string>
    <string name="release_notes">ملاحظات حول الإصدار</string>
    <string name="github_repo">إعادة تجزئة GitHub</string>
    <string name="github_repo_link">ارتباط GitHub Repo</string>
    <string name="report_issue">الإبلاغ عن مشكلة</string>
    <string name="v1_components">مكونات V1</string>
    <string name="v2_components">مكونات V2</string>
    <string name="all_components">الكل</string>
    <string name="fluent_logo">شعار Fluent</string>
    <string name="new_badge">جديد</string>
    <string name="modified_badge">تاريخ التعديل</string>
    <string name="api_break_badge">فاصل واجهة برمجة التطبيقات</string>
    <string name="app_bar_more">المزيد</string>
    <string name="accent">التمييز</string>
    <string name="appearance">المظهر</string>
    <string name="choose_brand_theme">اختر نسق علامتك التجارية:</string>
    <string name="fluent_brand_theme">علامة Fluent التجارية</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">اختيار المظهر</string>
    <string name="appearance_system_default">الوضع الافتراضي للنظام</string>
    <string name="appearance_light">فاتحة</string>
    <string name="appearance_dark">داكنة</string>
    <string name="demo_activity_github_link">ارتباط GitHub لنشاط العرض التوضيحي</string>
    <string name="control_tokens_details">تفاصيل رموز التحكم المميزة</string>
    <string name="parameters">المعلمات</string>
    <string name="control_tokens">الرموز المميزة لعنصر التحكم</string>
    <string name="global_tokens">الرموز المميزة للتخزين العمومي</string>
    <string name="alias_tokens">رموز الاسم المستعار المميزة</string>
    <string name="sample_text">النص</string>
    <string name="sample_icon">أيقونة العينة</string>
    <string name="color">اللون</string>
    <string name="neutral_color_tokens">الرموز المميزة للألوان المحايدة</string>
    <string name="font_size_tokens">الرموز المميزة لحجم الخط</string>
    <string name="line_height_tokens">رموز مميزة لارتفاع السطر</string>
    <string name="font_weight_tokens">الرموز المميزة لوزن الخط</string>
    <string name="icon_size_tokens">الرموز المميزة لحجم الأيقونة</string>
    <string name="size_tokens">الرموز المميزة للحجم</string>
    <string name="shadow_tokens">رموز ظل مميزة</string>
    <string name="corner_radius_tokens">RadiusTokens للزاوية</string>
    <string name="stroke_width_tokens">الرموز المميزة لعرض الكتابة</string>
    <string name="brand_color_tokens">الرموز المميزة للون العلامة التجارية</string>
    <string name="neutral_background_color_tokens">رموز مميزة للون خلفية محايد</string>
    <string name="neutral_foreground_color_tokens">الرموز المميزة للون الأمامي المحايد</string>
    <string name="neutral_stroke_color_tokens">الرموز المميزة للون الكتابة المحايد</string>
    <string name="brand_background_color_tokens">رموز مميزة للون خلفية العلامة التجارية</string>
    <string name="brand_foreground_color_tokens">رموز مميزة للون الأمامي للماركة</string>
    <string name="brand_stroke_color_tokens">الرموز المميزة للون كتابة العلامة التجارية</string>
    <string name="error_and_status_color_tokens">الرموز المميزة للون الحالة والخطأ</string>
    <string name="presence_tokens">رموز لون الحضور المميزة</string>
    <string name="typography_tokens">رموز أسلوب الطباعة المميزة</string>
    <string name="unspecified">غير محدد</string>

</resources>