<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demostración da interface de usuario Fluent</string>
    <string name="app_title">Interface de usuario Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Seleccionouse %s</string>
    <string name="app_modifiable_parameters">Parámetros modificables</string>
    <string name="app_right_accessory_view">Visualización de accesorios dereita</string>

    <string name="app_style">Estilo</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Icona premida</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Iniciar demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carrusel</string>
    <string name="actionbar_icon_radio_label">Icona</string>
    <string name="actionbar_basic_radio_label">Básico</string>
    <string name="actionbar_position_bottom_radio_label">Inferior</string>
    <string name="actionbar_position_top_radio_label">Superior</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tipo de ActionBar</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Posición de ActionBar</string>

    <!--AppBar-->
    <string name="app_bar_style">Estilo AppBar</string>
    <string name="app_bar_subtitle">Subtítulo</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Bordo inferior</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Premeuse na icona de navegación.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Marcar</string>
    <string name="app_bar_layout_menu_settings">Configuración</string>
    <string name="app_bar_layout_menu_search">Buscar</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Comportamento do desprazamento: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Alternar o comportamento de desprazamento</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Alternar icona de navegación</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Mostrar avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Mostrar a icona de volta</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ocultar icona</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Mostrar icona</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Alternar o estilo de deseño da barra de busca</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Mostrar como visualización de accesorios</string>
    <string name="app_bar_layout_searchbar_action_view_button">Mostrar como visualización de acción</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Alternar entre temas (recrea a actividade)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Alternar tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Elemento</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Contido extra que se pode desprazar</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Estilo do círculo</string>
    <string name="avatar_style_square">Estilo de cadrado</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Grande</string>
    <string name="avatar_size_medium">Medio</string>
    <string name="avatar_size_small">Pequeno</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dobre extra grande</string>
    <string name="avatar_size_xlarge_accessibility">Extra grande</string>
    <string name="avatar_size_xsmall_accessibility">Extra pequeno</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Avatar máximo mostrado</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Desbordamento de conta de avatares</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tipo de bordo</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">O grupo de avatares con OverflowAvatarCount que se definiu non se adherirá ao máximo de avatar mostrado.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Pila de caras</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Pila de cara</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Premeuse o desbordamento</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView no índice %d que se premeu</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Sinal de notificación</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punto</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Carácter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotos</string>
    <string name="bottom_navigation_menu_item_news">Noticias</string>
    <string name="bottom_navigation_menu_item_alerts">Alertas</string>
    <string name="bottom_navigation_menu_item_calendar">Calendario</string>
    <string name="bottom_navigation_menu_item_team">Equipo</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Etiquetas de alternancia</string>
    <string name="bottom_navigation_three_menu_items_button">Mostra tres elementos do menú</string>
    <string name="bottom_navigation_four_menu_items_button">Mostrar catro elementos do menú</string>
    <string name="bottom_navigation_five_menu_items_button">Mostra cinco elementos do menú</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">As etiquetas son %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Activa a acción de pasar o dedo cara abaixo para rexeitar</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Mostrar con liñas individuais</string>
    <string name="bottom_sheet_with_double_line_items">Mostrar con elementos de liñas dobres</string>
    <string name="bottom_sheet_with_single_line_header">Mostrar cunha soa liña de cabeceira</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Mostrar con cabeceira de dobre liña e liñas divisorias</string>
    <string name="bottom_sheet_dialog_button">Mostrar</string>
    <string name="drawer_content_desc_collapse_state">Expandir</string>
    <string name="drawer_content_desc_expand_state">Minimizar</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Premer %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Clic longo %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Premer en descartar</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Inserir elemento</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Actualizar elemento</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Ignorar</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Engadir</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Mencionar</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Negra</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Cursiva</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Subliñar</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Riscado</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Desfacer</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Refacer</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Viñeta</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Ligazón</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Actualización dos elementos</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Espazamento</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Rexeitar cargo</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">INICIAR</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">FINALIZAR</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Espazo dos grupos</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Espazo dos elementos</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Marcar</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Marcar o elemento no que se premeu</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Responder</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Premeuse no elemento de resposta</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Reenviar</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Reenviar elemento que se premeu</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Eliminar</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Eliminar o elemento no que se premeu</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Cámara</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Facer unha foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Premeuse o elemento da cámara</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galería</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Ver as fotos</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Premeuse no elemento da galería</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Vídeos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Reproduce os vídeos</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Elemento de vídeos no que se premeu</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Xestionar</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Xestiona a biblioteca multimedia</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Xestionar o elemento no que se premeu</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Accións de correo electrónico</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documentos</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Última actualización ás 14:14 h.</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Compartir</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Compartir o elemento no que se premeu</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Mover</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Mover o elemento no que se premeu</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Eliminar</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Eliminar o elemento no que se premeu</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Información</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Premeuse o elemento de información</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Reloxo</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Premeuse no elemento do reloxo</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarma</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Premeuse no elemento de alarma</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Fuso horario</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Premeuse no elemento do fuso horario</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Diferentes vistas do botón</string>
    <string name="button">Botón</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Exemplo de botón desactivado</string>
    <string name="button_borderless">Exemplo de botón sen bordos</string>
    <string name="button_borderless_disabled">Exemplo de botón desactivado sen bordos</string>
    <string name="button_large">Exemplo de botón grande</string>
    <string name="button_large_disabled">Exemplo de botón grande desactivado</string>
    <string name="button_outlined">Exemplo de botón delineado</string>
    <string name="button_outlined_disabled">Exemplo de botón desactivado delineado</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Elixir unha data</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Data única</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Non se escolleu data</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Mostrar o selector de data</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Mostrar o selector de data e hora coa pestana de data seleccionada</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Mostrar o selector de data e hora coa pestana de hora seleccionada</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Mostrar o selector de data e hora</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Intervalo de datas</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Inicio:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Fin:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Non se escolleu ningún comezo</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Sen final escollido</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Seleccionar data de inicio</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Seleccionar data de fin</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Intervalo de data e hora</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Seleccionar intervalo de data e hora</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Mostrar caixa de diálogo</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Mostrar caixón</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Mostrar caixa de diálogo do caixón</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Sen caixa de diálogo de fondo fundido</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Mostrar o caixón superior</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Sen diálogo superior fundido</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Mostrar o diálogo superior da visualización de áncora</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Non mostrar caixa de diálogo superior do título</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Mostrar abaixo o diálogo superior do título</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Mostrar caixón dereito</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Mostrar caixón esquerdo</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Título, texto principal</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtítulo, texto secundario</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Texto de subtítulo personalizado</string>
    <!-- Footer -->
    <string name="list_item_footer">Pé de páxina, texto terciario</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Lista dunha soa liña con texto de subtítulo gris</string>
    <string name="list_item_sub_header_two_line">Lista de dúas liñas</string>
    <string name="list_item_sub_header_two_line_dense">Lista de dúas liñas con espazado denso</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Lista de dúas liñas con visualización de subtítulos secundarios personalizados</string>
    <string name="list_item_sub_header_three_line">Lista de tres liñas con texto de subtítulo negro</string>
    <string name="list_item_sub_header_no_custom_views">Lista de elementos sen visualizacións personalizadas</string>
    <string name="list_item_sub_header_large_header">Lista de elementos con visualizacións personalizadas grandes</string>
    <string name="list_item_sub_header_wrapped_text">Lista de elementos con texto envolto</string>
    <string name="list_item_sub_header_truncated_text">Lista de elementos con texto truncado</string>
    <string name="list_item_sub_header_custom_accessory_text">Acción</string>
    <string name="list_item_truncation_middle">Truncamento medio.</string>
    <string name="list_item_truncation_end">Finalizar truncamento.</string>
    <string name="list_item_truncation_start">Comezar o truncamento.</string>
    <string name="list_item_custom_text_view">Valor</string>
    <string name="list_item_click">Premiches no elemento da lista.</string>
    <string name="list_item_click_custom_accessory_view">Premiches na visualización de accesorios personalizados.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Premiches na visualización de accesorios personalizados do subtítulo.</string>
    <string name="list_item_more_options">Máis opcións</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Seleccionar</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Ningún</string>
    <string name="people_picker_delete_example">Eliminar</string>
    <string name="people_picker_custom_persona_description">Este exemplo mostra como crear un obxecto IPersona personalizado.</string>
    <string name="people_picker_dialog_title_removed">Eliminaches unha persoa:</string>
    <string name="people_picker_dialog_title_added">Engadiches a unha persoa:</string>
    <string name="people_picker_drag_started">Comezou o arrastre</string>
    <string name="people_picker_drag_ended">O arrastre rematou</string>
    <string name="people_picker_picked_personas_listener">Oínte de xente</string>
    <string name="people_picker_suggestions_listener">Oínte de suxestións</string>
    <string name="people_picker_persona_chip_click">Premiches en %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s destinatario</item>
        <item quantity="other">%1$s destinatarios</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Expandir BottomSheet persistente</string>
    <string name="collapse_persistent_sheet_button"> Ocultar BottomSheet persistente</string>
    <string name="show_persistent_sheet_button"> Mostrar BottomSheet persistente</string>
    <string name="new_view">Esta é unha nova visualización</string>
    <string name="toggle_sheet_content">Alterna o contido de Bottomsheet</string>
    <string name="switch_to_custom_content">Cambiar a contido personalizado</string>
    <string name="one_line_content">Contido de Bottomsheet dunha liña</string>
    <string name="toggle_disable_all_items">Alternar todos os elementos</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Engadir/eliminar visualización</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Modificar altura colapsada</string>
    <string name="persistent_sheet_item_create_new_folder_title">Novo cartafol</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Premeuse no elemento do cartafol novo</string>
    <string name="persistent_sheet_item_edit_title">Editar</string>
    <string name="persistent_sheet_item_edit_toast">Editar elemento no que se premeu</string>
    <string name="persistent_sheet_item_save_title">Gardar</string>
    <string name="persistent_sheet_item_save_toast">Gardar elemento no que se premeu</string>
    <string name="persistent_sheet_item_zoom_in_title">Ampliar</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Ampliar o elemento no que se premeu</string>
    <string name="persistent_sheet_item_zoom_out_title">Reducir</string>
    <string name="persistent_sheet_item_zoom_out_toast">Reducir o elemento no que se premeu</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Dispoñible</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Bouzas</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel García</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Deseñador</string>
    <string name="persona_subtitle_engineer">Enxeñeiro</string>
    <string name="persona_subtitle_manager">Xefe</string>
    <string name="persona_subtitle_researcher">Investigador</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (exemplo de texto longo para probar o truncamento)</string>
    <string name="persona_view_description_xxlarge">Avatar XXLarge con tres liñas de texto</string>
    <string name="persona_view_description_large">Gran avatar con dúas liñas de texto</string>
    <string name="persona_view_description_small">Pequeno avatar cunha liña de texto</string>
    <string name="people_picker_hint">Ningunha con suxestión mostrada</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Desactivouse Persona Chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Error en Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Chip Persona sen icona de peche</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Persona Chip básico</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Premiches nun Chip Persona seleccionado.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Compartir</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Seguir</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Convidar persoas</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Actualizar páxina</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Abrir no explorador</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Este é un menú emerxente de varias liñas. As liñas máximas están definidas en dúas, o resto do texto truncarase.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Todas as noticias</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Noticias gardadas</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Novidades dos sitios</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Notificar fóra do horario laboral</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notificar cando está inactivo no escritorio</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Premiches no elemento:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menú simple</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menú2 simple</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menú cun elemento seleccionable e unha liña divisoria</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menú con todos os elementos seleccionables, iconas e texto longo</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Mostrar</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Progreso circular</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Pequeno</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Medio</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Grande</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Progreso lineal</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Indeterminado</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinado</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Devolución de chamada de micrófono</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Corrección automática</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Premeuse o micrófono</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Premeuse a visualización dereita</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Premeuse a busca do teclado</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Mostrar barra</string>
    <string name="fluentui_dismiss_snackbar">Rexeitar barra</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Acción</string>
    <string name="snackbar_action_long">Acción de texto longo</string>
    <string name="snackbar_single_line">Barra dunha liña</string>
    <string name="snackbar_multiline">Esta é unha barra de varias liñas. As liñas máximas están definidas en dúas, o resto do texto truncarase.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Esta é unha barra de anuncios. Úsase para comunicar novas funcionalidades.</string>
    <string name="snackbar_primary">Esta é unha barra principal.</string>
    <string name="snackbar_light">Esta é unha barra clara.</string>
    <string name="snackbar_warning">Esta é unha barra de advertencia.</string>
    <string name="snackbar_danger">Esta é uma barra de perigo.</string>
    <string name="snackbar_description_single_line">Curta duración</string>
    <string name="snackbar_description_single_line_custom_view">Longa duración con progreso circular como pequena visualización personalizada</string>
    <string name="snackbar_description_single_line_action">Curta duración con acción</string>
    <string name="snackbar_description_single_line_action_custom_view">Curta duración con acción e visualización personalizada media</string>
    <string name="snackbar_description_single_line_custom_text_color">Duración curta con cor de texto personalizada</string>
    <string name="snackbar_description_multiline">Longa duración</string>
    <string name="snackbar_description_multiline_custom_view">Longa duración con pequena visualización personalizada</string>
    <string name="snackbar_description_multiline_action">Duración indefinida con actualizacións de acción e texto</string>
    <string name="snackbar_description_multiline_action_custom_view">Curta duración con acción e visualización personalizada media</string>
    <string name="snackbar_description_multiline_action_long">Duración curta con texto de acción longa</string>
    <string name="snackbar_description_announcement">Curta duración</string>
    <string name="snackbar_description_updated">Actualizouse este texto.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Mostrar barra</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Liña única</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Varias liñas</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Estilo de anuncio</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Estilo primario</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Estilo claro</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Estilo de advertencia</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Estilo de perigo</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Inicio</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Correo</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Configuración</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notificación</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Máis</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Aliñamento do texto</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertical</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Sen texto</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Elementos do separador</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Título</string>
    <string name="cell_sample_description">Descrición</string>
    <string name="calculate_cells">Cargar/calcular 100 celas</string>
    <string name="calculate_layouts">Cargar/calcular 100 deseños</string>
    <string name="template_list">Lista de modelos</string>
    <string name="regular_list">Lista normal</string>
    <string name="cell_example_title">Título: cela</string>
    <string name="cell_example_description">Descrición: tocar para modificar de orientación</string>
    <string name="vertical_layout">Deseño vertical</string>
    <string name="horizontal_layout">Deseño horizontal</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Separador estándar de 2 segmentos</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Separador estándar de 3 segmentos</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Separador estándar de 4 segmentos</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Separador estándar con busca</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Cambiar o separador</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Separador de pílulas </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Toca para ver a información sobre ferramentas</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Toca para ver a información sobre ferramentas do calendario personalizado</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Toca Información sobre ferramentas de cor personalizada</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Toca para rexeitar dentro da información sobre ferramentas</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Toca para ver a información sobre ferramentas de visualización personalizada</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Información sobre ferramentas de cores personalizadas</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Información sobre ferramentas de extremo superior con 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Información sobre ferramentas de inicio inferior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Información sobre ferramentas do extremo inferior con 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Descartar dentro da información sobre ferramentas</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Rexeitouse a información sobre ferramentas</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">O titular é claro 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">O título 1 é medio 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">O título 2 é regular 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">O título é normal 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">O subtítulo 1 é normal 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">O subtítulo 2 é medio 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Corpo 1 é regular 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Corpo 2 é medio 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">O subtítulo é regular 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versión de SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Elemento %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Cartafol</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Premido</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">BAF</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">BAF expandido</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">BAF contraído</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Premer para actualizar a lista</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Abrir caixón</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Elemento de menú</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Desprazamento X (en dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Desprazamento Y (en dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Texto de contido</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Repetir texto de contido</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">O ancho do menú cambiará con respecto ao texto do contido. O máx.
         ancho está restrinxido ao 75 % do tamaño da pantalla. A marxe de contido desde o lado e abaixo rexerase por token. O mesmo texto de contido repetirase para variar
         a altura.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Abrir menú</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Cartón básico</string>
    <!-- UI Label for Card -->
    <string name="file_card">Cartón de ficheiro</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Cartón de anuncio</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">IU aleatoria</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opcións</string>
    <!-- UI Label for Title -->
    <string name="card_title">Título</string>
    <!-- UI Label for text -->
    <string name="card_text">Texto</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Subtexto</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">A copia secundaria desta faixa pódese envolver en dúas liñas se é necesario.</string>
    <!-- UI Label Button -->
    <string name="card_button">Botón</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Mostrar caixa de diálogo</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Ignorar o diálogo ao premer fóra</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Ignorar o diálogo ao premer atrás</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Ignorouse o diálogo</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Cancelar</string>
    <!-- UI Label Ok -->
    <string name="ok">Aceptar</string>
    <!-- A sample description -->
    <string name="dialog_description">Un diálogo é unha pequena ventá que solicita ao usuario que tome unha decisión ou que introduza información adicional.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Abrir caixón</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Expandir caixón</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Pechar caixón</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Seleccionar tipo de caixón</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Superior</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Móstrase todo o caixón na rexión visible.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Inferior</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Móstrase todo o caixón na rexión visible. Desliza o contido de desprazamento cara arriba. O caixón expandible expándese a través da agarradoira de arrastre.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Deslizar desde a esquerda</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">O caixón deslízase cara á rexión visible desde o lado esquerdo.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Deslizar desde a dereita</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">O caixón deslízase cara á rexión visible desde o lado dereito.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Deslizar desde abaixo</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Desliza o caixón cara á rexión visible desde a parte inferior da pantalla. Deslizar cara arriba sobre o caixón expandible trae a parte restante á rexión visible, logo, desprázate.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim visible</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Seleccionar contido do caixón</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Contido de desprazamento do tamaño da pantalla completa</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Contido superior á metade da pantalla</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Contido inferior á metade da pantalla</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Contido de tamaño dinámico</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Contido aniñado no caixón</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Expandible</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Omitir estado aberto</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Evitar o despedimento en Scrim Click</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Mostrar agarradoira</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Título</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Texto da información sobre ferramentas</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tocar para ver información sobre ferramentas do contido personalizado</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Inicio superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Extremo superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Inicio inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Extremo inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centrar </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Centro personalizado</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Para actualizacións en notas de lanzamento, </string>
    <string name="click_here">preme aquí.</string>
    <string name="open_source_cross_platform">Sistema de deseño multiplataforma de código aberto.</string>
    <string name="intuitive_and_powerful">Intuitivo e poderoso.</string>
    <string name="design_tokens">Tokens de deseño</string>
    <string name="release_notes">Notas de lanzamento</string>
    <string name="github_repo">Repositorio de GitHub</string>
    <string name="github_repo_link">Ligazón do repositorio de GitHub</string>
    <string name="report_issue">Informar de problema</string>
    <string name="v1_components">Compoñentes V1</string>
    <string name="v2_components">Compoñentes V2</string>
    <string name="all_components">Todo</string>
    <string name="fluent_logo">Logotipo de Fluent</string>
    <string name="new_badge">Novo</string>
    <string name="modified_badge">Modificado</string>
    <string name="api_break_badge">Quebra da API</string>
    <string name="app_bar_more">Máis</string>
    <string name="accent">Énfase</string>
    <string name="appearance">Aspecto</string>
    <string name="choose_brand_theme">Escoller o tema da marca:</string>
    <string name="fluent_brand_theme">Marca de Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Escoller aspecto</string>
    <string name="appearance_system_default">Predefinido polo sistema</string>
    <string name="appearance_light">Claro</string>
    <string name="appearance_dark">Escuro</string>
    <string name="demo_activity_github_link">Ligazón de actividade de demostración de GitHub</string>
    <string name="control_tokens_details">Detalles dos tokens de control</string>
    <string name="parameters">Parámetros</string>
    <string name="control_tokens">Tokens de control</string>
    <string name="global_tokens">Tokens globais</string>
    <string name="alias_tokens">Tokens de alias</string>
    <string name="sample_text">Texto</string>
    <string name="sample_icon">Icona de exemplo</string>
    <string name="color">Cor</string>
    <string name="neutral_color_tokens">Tokens de cores neutras</string>
    <string name="font_size_tokens">Tokens de tamaño do tipo de letra</string>
    <string name="line_height_tokens">Tokens de altura da liña</string>
    <string name="font_weight_tokens">Tokens de espesura do tipo de letra</string>
    <string name="icon_size_tokens">Tokens de tamaño da icona</string>
    <string name="size_tokens">Tokens de tamaño</string>
    <string name="shadow_tokens">Tokens de sombra</string>
    <string name="corner_radius_tokens">Tokens do radio do canto</string>
    <string name="stroke_width_tokens">Tokens de largura do trazo</string>
    <string name="brand_color_tokens">Tokens de cores de marca</string>
    <string name="neutral_background_color_tokens">Tokens de cor de fondo neutra</string>
    <string name="neutral_foreground_color_tokens">Tokens de cor de primeiro plano neutra</string>
    <string name="neutral_stroke_color_tokens">Tokens das cores de trazo neutras</string>
    <string name="brand_background_color_tokens">Tokens de cor de fondo da marca</string>
    <string name="brand_foreground_color_tokens">Tokens de cor de primeiro plano da marca</string>
    <string name="brand_stroke_color_tokens">Tokens das cores de trazo da marca</string>
    <string name="error_and_status_color_tokens">Tokens de cor de erro e estado</string>
    <string name="presence_tokens">Tokens de cores de presenza</string>
    <string name="typography_tokens">Tokens de tipografía</string>
    <string name="unspecified">Non especificado</string>

</resources>