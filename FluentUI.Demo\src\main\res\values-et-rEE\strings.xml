<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Kasutajaliidese Fluent demo</string>
    <string name="app_title">Ka<PERSON><PERSON><PERSON><PERSON>ides Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Valitud %s</string>
    <string name="app_modifiable_parameters">Muudetavad parameetrid</string>
    <string name="app_right_accessory_view">Parempoolne tarvikuvaade</string>

    <string name="app_style">Laad</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikoon on vajutatud</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button"><PERSON><PERSON><PERSON><PERSON> demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Ikoon</string>
    <string name="actionbar_basic_radio_label">Lihtne</string>
    <string name="actionbar_position_bottom_radio_label">Alla</string>
    <string name="actionbar_position_top_radio_label">Üles</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Toiminguriba tüüp</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Toiminguriba paigutus</string>

    <!--AppBar-->
    <string name="app_bar_style">Rakenduseriba laad</string>
    <string name="app_bar_subtitle">Alampealkiri</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Allääris</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Klõpsati navigeerimisikooni.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Märgi lipuga</string>
    <string name="app_bar_layout_menu_settings">Sätted</string>
    <string name="app_bar_layout_menu_search">Otsing</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Kerimiskäitumine: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Vaheta kerimiskäitumist</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Lülita navigeerimine sisse/välja</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Kuva avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Kuva ikoon tagasi</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Peida ikoon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Kuva ikoon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Vaheta otsinguriba paigutuslaadi</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Kuva tarvikuvaatena</string>
    <string name="app_bar_layout_searchbar_action_view_button">Kuva toiminguvaatena</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Vaheta kujunduste vahel (loob tegevuse uuesti)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Vaheta kujundust</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Üksus</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Lisa keritav sisu</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Ringilaad</string>
    <string name="avatar_style_square">Kandiline laad</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXL</string>
    <string name="avatar_size_xlarge">XL</string>
    <string name="avatar_size_large">Suur</string>
    <string name="avatar_size_medium">Keskmine</string>
    <string name="avatar_size_small">Väike</string>
    <string name="avatar_size_xsmall">XS</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Eriti suur</string>
    <string name="avatar_size_xlarge_accessibility">Väga suur</string>
    <string name="avatar_size_xsmall_accessibility">Väga väike</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maksimaalne kuvatud avataride arv</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Ületäitumise avataride arv</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Äärise tüüp</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avataride rühm, millel on märgitud ÜletäitumiseAvatarideArv, ei järgi maksimaalseid kuvatavaid avatare.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Näovirn</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Näokuhi</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Klõpsati valikut ületäitumine</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Klõpsati avatarivaadet indeksil %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Teatise märk</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punkt</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Loend</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Tähemärk</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotod</string>
    <string name="bottom_navigation_menu_item_news">Uudised</string>
    <string name="bottom_navigation_menu_item_alerts">Teatised</string>
    <string name="bottom_navigation_menu_item_calendar">Kalender</string>
    <string name="bottom_navigation_menu_item_team">Meeskond</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Lülita sildid sisse/välja</string>
    <string name="bottom_navigation_three_menu_items_button">Kuva kolm menüükäsku</string>
    <string name="bottom_navigation_four_menu_items_button">Kuva neli menüükäsku</string>
    <string name="bottom_navigation_five_menu_items_button">Kuva viis menüükäsku</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Sildid on %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">AlumineLeht</string>
    <string name="bottom_sheet_dialog">AlumiseLeheDialoog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Luba allanipsamisega sulgemine</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Kuva üherealiste üksustega</string>
    <string name="bottom_sheet_with_double_line_items">Kuva kaherealiste üksustega</string>
    <string name="bottom_sheet_with_single_line_header">Kuva üherealise päisega</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Kuva topeltrea päise ja eraldajatega</string>
    <string name="bottom_sheet_dialog_button">Kuva</string>
    <string name="drawer_content_desc_collapse_state">Laienda</string>
    <string name="drawer_content_desc_expand_state">Minimeeri</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klõpsake %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Pikk klõps %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klõpsake sule</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Lisa üksus</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Värskenda üksust</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Sulge</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Lisa</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Mainimine</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Paks</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursiiv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Allakriipsutus</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Läbikriipsutus</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Tagasivõtmine</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Uuestitegemine</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Täpp</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Loend</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Linkige</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Üksuse värskendamine</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Vahed</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Sulgemise paigutus</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">KÄIVITAMINE</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">LÕPETA</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Rühmaruum</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Üksuste ruum</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Märgi lipuga</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Klõpsati üksust märgi lipuga</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Vasta</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Klõpsati üksust vasta</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Edasi</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Klõpsati üksust saada edasi</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Kustuta</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Klõpsati üksust kustuta</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kaamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Pildista</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Klõpsati üksust kaamera</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerii</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Vaadake oma fotosid</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Klõpsati üksust galerii</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videod</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Esita oma videod</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Klõpsati üksust videod</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Halda</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Meediumiteegi haldamine</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Klõpsati üksust halda</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Meilitoimingud</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumendid</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Viimati värskendatud kell 14.14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Jagamine</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Klõpsati jaga üksust</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Teisalda</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Klõpsati üksust teisalda</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Kustuta</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Klõpsati üksust kustuta</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Teave</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Klõpsati üksust teave</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Kell</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Klõpsati üksust kell</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Märguanne</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Klõpsati üksust märguanne</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Ajavöönd</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Klõpsati üksust ajavöönd</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Nupu eri vaated</string>
    <string name="button">Nupp</string>
    <string name="buttonbar">Nupuriba</string>
    <string name="button_disabled">Keelatud nupu näide</string>
    <string name="button_borderless">Ääristeta nupu näide</string>
    <string name="button_borderless_disabled">Ääristeta keelatud nupu näide</string>
    <string name="button_large">Suure nupu näide</string>
    <string name="button_large_disabled">Suure keelatud nupu näide</string>
    <string name="button_outlined">Liigendatud nupu näide</string>
    <string name="button_outlined_disabled">Liigendatud keelatud nupu näide</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Valige kuupäev</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">KuupäevaKellaValija</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Üks kuupäev</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Kuupäeva pole valitud</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Kuva kuupäevavalija</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Kuva kuupäeva- ja kellaajavalija, kus valitud on kuupäevavahekaart</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Kuva kuupäeva- ja kellaajavalija, kus valitud on kellaajavahekaart</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Kuva lõpukuupäeva/-kellaaja valija</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Kuupäevavahemik</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Algus:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Lõpp:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Algust pole valitud</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Lõppu pole valitud</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Valige alguskuupäev</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Valige lõppkuupäev</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Kuupäeva- ja kellaajavahemik</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Valige kuupäeva- ja kellaajavahemik</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">KuupäevaKellaValijaDialoog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Kuva dialoog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Kuva sahtel</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Kuva sahteldialoog</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Hajutuseta alumine dialoog</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Kuva ülemine sahtel</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Hajutuseta ülemine dialoog</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Kuva ankruvaate ülemine dialoog</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Kuva pealkirjata ülemine dialoog</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Kuva pealkirja all ülemine dialoog</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Kuva parempoolne sahtel</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Kuva vasakpoolne sahtel</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Pealkiri, esmane tekst</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Alapealkiri, teisene tekst</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Kohandatud alapealkirja tekst</string>
    <!-- Footer -->
    <string name="list_item_footer">Jalus, kolmanda astme tekst</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Üherealine loend halli alampäise tekstiga</string>
    <string name="list_item_sub_header_two_line">Kaherealine loend</string>
    <string name="list_item_sub_header_two_line_dense">Tiheda vahega kaherealine loend</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Kaherealine loend kohandatud teisese alampealkirja vaatega</string>
    <string name="list_item_sub_header_three_line">Kolmerealine loend musta alampäise tekstiga</string>
    <string name="list_item_sub_header_no_custom_views">Loetle kohandatud vaadeteta üksused</string>
    <string name="list_item_sub_header_large_header">Loetle suurte kohandatud vaadetega üksused</string>
    <string name="list_item_sub_header_wrapped_text">Loetle mähitud tekstiga üksused</string>
    <string name="list_item_sub_header_truncated_text">Loetle kärbitud tekstiga üksused</string>
    <string name="list_item_sub_header_custom_accessory_text">Toiming</string>
    <string name="list_item_truncation_middle">Keskmine kärpimine.</string>
    <string name="list_item_truncation_end">Lõpeta kärpimine.</string>
    <string name="list_item_truncation_start">Alusta kärpimist.</string>
    <string name="list_item_custom_text_view">Väärtus</string>
    <string name="list_item_click">Klõpsasite üksusele loend.</string>
    <string name="list_item_click_custom_accessory_view">Klõpsasite kohandatud tarvikuvaadet.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Klõpsasite alampäise kohandatud tarvikuvaadet.</string>
    <string name="list_item_more_options">Rohkem valikuid</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Valige</string>
    <string name="people_picker_select_deselect_example">ValiTühistaValik</string>
    <string name="people_picker_none_example">Pole</string>
    <string name="people_picker_delete_example">Kustuta</string>
    <string name="people_picker_custom_persona_description">See näide näitab, kuidas luua kohandatud IPersona objekti.</string>
    <string name="people_picker_dialog_title_removed">Eemaldasite persona:</string>
    <string name="people_picker_dialog_title_added">Lisasite persona:</string>
    <string name="people_picker_drag_started">Lohistamine alustatud</string>
    <string name="people_picker_drag_ended">Lohistamine lõpetatud</string>
    <string name="people_picker_picked_personas_listener">Personade kuulaja</string>
    <string name="people_picker_suggestions_listener">Soovituste kuulaja</string>
    <string name="people_picker_persona_chip_click">Klõpsasite %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s saaja</item>
        <item quantity="other">%1$s saajat</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Laienda püsiv AlumineLeht</string>
    <string name="collapse_persistent_sheet_button"> Peida püsiv AlumineLeht</string>
    <string name="show_persistent_sheet_button"> Püsivalt kuvamise alumine leht</string>
    <string name="new_view">See on uus vaade</string>
    <string name="toggle_sheet_content">Alumise lehe sisu tumblernupp</string>
    <string name="switch_to_custom_content">Aktiveerige kohandatud sisu</string>
    <string name="one_line_content">Üherealine alumise lehe sisu</string>
    <string name="toggle_disable_all_items">Tumblernupp kõigi üksuste keelamiseks</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Lisa/Eemalda vaade</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Muuda ahendatud kõrgust</string>
    <string name="persistent_sheet_item_create_new_folder_title">Uus kaust</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Klõpsati üksust uus kaust</string>
    <string name="persistent_sheet_item_edit_title">Redigeeri</string>
    <string name="persistent_sheet_item_edit_toast">Klõpsati üksust redigeeri</string>
    <string name="persistent_sheet_item_save_title">Salvesta</string>
    <string name="persistent_sheet_item_save_toast">Klõpsati üksust salvesta</string>
    <string name="persistent_sheet_item_zoom_in_title">Suumi sisse</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Klõpsati üksust suumi sisse</string>
    <string name="persistent_sheet_item_zoom_out_title">Suumi välja</string>
    <string name="persistent_sheet_item_zoom_out_toast">Klõpsati üksust suumi välja</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Saadaval</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Markus Lill</string>
    <string name="persona_name_kat_larsson">Kristiina Koppel</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Paul Kirsipuu</string>
    <string name="persona_name_kristen_patterson">Kristel Haab</string>
    <string name="persona_name_lydia_bauer">Leida Oks</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Markus Lill</string>
    <string name="persona_name_mona_kane">Miina Kask</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Kujundaja</string>
    <string name="persona_subtitle_engineer">Insener</string>
    <string name="persona_subtitle_manager">Ülemus</string>
    <string name="persona_subtitle_researcher">Uurija</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (pika teksti näide kärpimise testimiseks)</string>
    <string name="persona_view_description_xxlarge">XXL avatar kolme tekstireaga</string>
    <string name="persona_view_description_large">Suur avatar koos kaherealise tekstiga</string>
    <string name="persona_view_description_small">Väike avatar ühe tekstireaga</string>
    <string name="people_picker_hint">Pole kuvatud vihjega</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Keelatud personakiip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Vigane personakiip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Personakiip ilma sulgemisikoonita</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Lihtne personakiip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Klõpsasite valitud personakiibil.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Jagamine</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Jälgi</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Kutsu inimesi</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Värskenda lehte</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Ava brauseris</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">See on mitmerealine hüpikmenüü. Maksimaalseks ridade arvuks on seatud kaks, ülejäänud tekst kärbitakse.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Kõik uudised</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Salvestatud uudised</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Saitidelt pärit uudised</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Teavita väljaspool tööaega</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Teavita, kui on töölaual passiivne</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Klõpsasite üksust:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Lihtne menüü</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Lihtne menüü2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Ühe valitava üksuse ja eraldajaga menüü</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menüü koos kõigi valitavate üksuste, ikoonide ja pika tekstiga</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Kuva</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Ringikujuline edenemine</string>
    <string name="circular_progress_xsmall">XS</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Väike</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Keskmine</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Suur</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineaarne edenemine</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Määramata</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Määratud</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Otsinguriba</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofoni tagasihelistamine</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Automaatkorrektuur</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofoni on vajutatud</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Vajutatud on paremvaadet</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Vajutati klaviatuuri otsingut</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Kuva teavitusriba</string>
    <string name="fluentui_dismiss_snackbar">Sule teavitusriba</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Toiming</string>
    <string name="snackbar_action_long">Toiming pika tekstiga</string>
    <string name="snackbar_single_line">Üherealine teavitusriba</string>
    <string name="snackbar_multiline">See on mitmerealine teavitusriba. Maksimaalseks ridade arvuks on seatud kaks, ülejäänud tekst kärbitakse.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">See on teadaannete teavitusriba. Seda kasutatakse uute funktsioonide kohta teabe edastamiseks.</string>
    <string name="snackbar_primary">See on peamine teavitusriba</string>
    <string name="snackbar_light">See on lihtne teavitusriba.</string>
    <string name="snackbar_warning">See on hoiatuse teavitusriba.</string>
    <string name="snackbar_danger">See on ohu teavitusriba.</string>
    <string name="snackbar_description_single_line">Lühike kestus</string>
    <string name="snackbar_description_single_line_custom_view">Pika kestusega ringikujuline edenemine väikese kohandatud vaatena</string>
    <string name="snackbar_description_single_line_action">Lühike kestus koos toiminguga</string>
    <string name="snackbar_description_single_line_action_custom_view">Lühike kestus koos toimingu ja keskmise kohandatud vaatega</string>
    <string name="snackbar_description_single_line_custom_text_color">Lühike kestus kohandatud tekstivärviga</string>
    <string name="snackbar_description_multiline">Pikk kestus</string>
    <string name="snackbar_description_multiline_custom_view">Pikk kestus väikese kohandatud vaatega</string>
    <string name="snackbar_description_multiline_action">Määramata kestus koos toimingu- ja tekstivärskendustega</string>
    <string name="snackbar_description_multiline_action_custom_view">Lühike kestus koos toimingu ja keskmise kohandatud vaatega</string>
    <string name="snackbar_description_multiline_action_long">Lühike kestus koos pika toimingutekstiga</string>
    <string name="snackbar_description_announcement">Lühike kestus</string>
    <string name="snackbar_description_updated">Seda teksti on värskendatud.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Kuva teavitusriba</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Üherealine</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Mitmerealine</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Teadaande laad</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Esmane laad</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Hele laad</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Hoiatuse laad</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Ohu laad</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Avaleht</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Meil</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Sätted</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Teatis</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Rohkem</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Teksti joondus</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertikaalne</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horisontaalne</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Tekst puudub</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Vahekaardiüksused</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Pealkiri</string>
    <string name="cell_sample_description">Kirjeldus</string>
    <string name="calculate_cells">Laadi/arvuta 100 lahtrit</string>
    <string name="calculate_layouts">Laadi/arvuta 100 paigutust</string>
    <string name="template_list">Mallide loend</string>
    <string name="regular_list">Tavaloend</string>
    <string name="cell_example_title">Pealkiri: Lahter</string>
    <string name="cell_example_description">Kirjeldus: paigutuse muutmiseks puudutage</string>
    <string name="vertical_layout">Vertikaalpaigutus</string>
    <string name="horizontal_layout">Horisontaalpaigutus</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standardne 2-segmendiline vahekaart</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardne 3-segmendiline vahekaart</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardne 4-segmendiline vahekaart</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Tavaline vahekaart koos piipariga</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Vaheta vahekaarti</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Pillide vahekaart </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Puudutage kohtspikri kuvamiseks</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Puudutage kohandatud kalendri kohtspikri jaoks</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Puudutage kohandatud värvi kohtspikri jaoks</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Puudutage sisemise kohtspikri sulgemiseks</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Puudutage kliendivaate tööriistariba jaoks</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Ülemine kohandatud värvi kohtspikri jaoks</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Ülemise lõpu kohtspikker koos 10 dp X-telje nihkega</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Alumise alguse kohtspikker</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Alumise lõpu kohtspikker koos 10 dp Y-telje nihkega</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Sisemuse sulgemise tööriistariba</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Kohtspikker on suletud</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Pealkiri on hele 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Pealkiri 1 on keskmine 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">2. peakiri on tavaline 20 sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Pealkiri on tavaline 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Alapealkiri 1 on tavaline 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Alapealkiri 2 on keskmine 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Sisutekst 1 on tavaline 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Sisutekst 2 on keskmine 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Pealdis on tavaline 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK versioon: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Üksus %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Kaust</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Klõpsatud</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Tellingud</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB on laiendatud</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB on ahendatud</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klõpsake loendi värskendamiseks</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Ava sahtel</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menüüelement</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Nihe X (dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Nihe Y (dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Sisutekst</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Korda sisuteksti</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menüü laius muutub vastavalt sisutekstile. Maksimaalne
        laius on piiratud 75% ekraani suurusest. Sisuveerist küljelt ja alt reguleerib tõend. Kõrguse muutmiseks sama sisutekst
        kordub.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Ava menüü</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Põhikaart</string>
    <!-- UI Label for Card -->
    <string name="file_card">Failikaart</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Teadaande kaart</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Juhuslik kasutajaliides</string>
    <!-- UI Label for Options -->
    <string name="card_options">Valikud</string>
    <!-- UI Label for Title -->
    <string name="card_title">Pealkiri</string>
    <!-- UI Label for text -->
    <string name="card_text">Tekst</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Alamtekst</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Selle teavitusriba teise eksemplari saab vajaduse korral mahutada kahele reale.</string>
    <!-- UI Label Button -->
    <string name="card_button">Nupp</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Kuva dialoog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Sule dialoog väljaspool klõpsamisel</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Sule dialoog tagasi vajutamisel</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialoog on suletud</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Loobu</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Dialoog on väike aken, mis palub kasutajal teha otsus või sisestada lisateavet.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Ava liugpaan</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Laienda liugpaani</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Sule sahtel</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Vali sahtli tüüp</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Ülemine</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Kogu sahtel kuvatakse nähtaval alal.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Alumine</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Kogu sahtel kuvatakse nähtaval alal. Ülesnipsamine kerib sisu edasi. Laiendatava sahtli saab avada lohistamispidemega.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Vasakult libistamine</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Sahtli libistamine vasakult nähtavale alale.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Paremalt libistamine</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Sahtli libistamine paremalt nähtavale alale.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Alt libistamine</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Sahtli libistamine alt nähtavale alale. Laiendataval sahtlil ülesnipsamine toob selle täielikult nähtavale ja annab võimaluse kerida.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Sirm nähtav</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Vali sahtli sisu</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Täisekraanvaates keritav sisu</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Sisu rohkem kui poolel ekraanil</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Sisu vähem kui poolel ekraanil</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dünaamilise suurusega sisu</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Pesastatud sahtli sisu</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Laiendatav</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Jäta avatud olek vahele</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Keela kihi klõpsamisel sulgemine</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Kuva pide</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Pealkiri</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Kohtspikri tekst</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Puudutage kohandatud sisu kohtspikri kuvamiseks</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Pealmine algus </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Ülemine ots </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Alumine algus </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Alumine ots </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Keskkoht </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Kohandatud keskpunkt</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Väljalaskemärkmete värskenduste saamiseks, </string>
    <string name="click_here">klõpsake siin.</string>
    <string name="open_source_cross_platform">Avatud lähtekoodiga platvormiülene kujundussüsteem.</string>
    <string name="intuitive_and_powerful">Intuitiivne ja võimas.</string>
    <string name="design_tokens">Kujunduse tõendid</string>
    <string name="release_notes">Väljalaskemärkmed</string>
    <string name="github_repo">GitHubi hoidla</string>
    <string name="github_repo_link">GitHubi hoidla link</string>
    <string name="report_issue">Teata probleemist</string>
    <string name="v1_components">V1 komponendid</string>
    <string name="v2_components">V2 komponendid</string>
    <string name="all_components">Kõik</string>
    <string name="fluent_logo">Fluenti logo</string>
    <string name="new_badge">Uus</string>
    <string name="modified_badge">Muudetud</string>
    <string name="api_break_badge">API vaheaeg</string>
    <string name="app_bar_more">Rohkem</string>
    <string name="accent">Rõhutus</string>
    <string name="appearance">Välimus</string>
    <string name="choose_brand_theme">Valige oma brändi kujundus:</string>
    <string name="fluent_brand_theme">Fluenti bränd</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Vali välimus</string>
    <string name="appearance_system_default">Süsteemi vaikesäte</string>
    <string name="appearance_light">Hele</string>
    <string name="appearance_dark">Tume</string>
    <string name="demo_activity_github_link">Demo tegevuse GitHubi link</string>
    <string name="control_tokens_details">Juhtelemendi tõendite üksikasjad</string>
    <string name="parameters">Parameetrid</string>
    <string name="control_tokens">Juhtelemendi tõendid</string>
    <string name="global_tokens">Globaalsed tõendid</string>
    <string name="alias_tokens">Pseudonüümi tõendid</string>
    <string name="sample_text">Tekst</string>
    <string name="sample_icon">Näidisikoon</string>
    <string name="color">Värv</string>
    <string name="neutral_color_tokens">Neutraalse värvi tõendid</string>
    <string name="font_size_tokens">Fondisuuruse tõendid</string>
    <string name="line_height_tokens">Rea kõrguse tõendid</string>
    <string name="font_weight_tokens">Fondi paksuse tõendid</string>
    <string name="icon_size_tokens">Ikooni suuruse tõendid</string>
    <string name="size_tokens">Suuruse tõendid</string>
    <string name="shadow_tokens">Varju tõendid</string>
    <string name="corner_radius_tokens">Nurga raadiuse tõendid</string>
    <string name="stroke_width_tokens">Joone laiuse tõendid</string>
    <string name="brand_color_tokens">Brändi värvi tõendid</string>
    <string name="neutral_background_color_tokens">Neutraalse taustavärvi tõendid</string>
    <string name="neutral_foreground_color_tokens">Neutraalsed esiplaani värvi tõendid</string>
    <string name="neutral_stroke_color_tokens">Neutraalse joone värvi tõendid</string>
    <string name="brand_background_color_tokens">Kaubamärgi taustavärvi tõendid</string>
    <string name="brand_foreground_color_tokens">Brändi esiplaani värvi tõendid</string>
    <string name="brand_stroke_color_tokens">Brändi joone värvi tõendid</string>
    <string name="error_and_status_color_tokens">Tõrke ja oleku värvi tõendid</string>
    <string name="presence_tokens">Võrgusolekuteabe värvi tõendid</string>
    <string name="typography_tokens">Tüpograafia tõendid</string>
    <string name="unspecified">Määramata</string>

</resources>