<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Primair</string>
    <string name="fluentui_secondary">Secundair</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Negeren</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Geselecteerd</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Niet geselecteerd</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Pictogram</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Pictogram</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Uitgeschakeld</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Actieknop</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Ingeschakeld</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Blad sluiten</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Sluiten</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Annuleren</string>
    <!--name of the icon -->
    <string name="fluentui_search">Zoeken</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Microfoon</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Tekst wissen</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Terug</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Geactiveerd</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Gedeactiveerd</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutraal</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Merk</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contrast</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Accent</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Waarschuwing</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Gevaar</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Er is een fout opgetreden</string>
    <string name="fluentui_error">Fout</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Tip</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Punthaak</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Overzicht</string>

    <string name="fluentui_action_button_icon">Pictogram actieknop</string>
    <string name="fluentui_center">Tekst centreren</string>
    <string name="fluentui_accessory_button">Accessoireknoppen</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Keuzerondje</string>
    <string name="fluentui_label">Label</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Verbreed</string>
    <string name="fluentui_collapsed">Samengevouwen</string>

    <!--types of control -->
    <string name="fluentui_large">Groot</string>
    <string name="fluentui_medium">Gemiddeld</string>
    <string name="fluentui_small">Klein</string>
    <string name="fluentui_password_mode">Wachtwoordmodus</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Ondertitel</string>
    <string name="fluentui_assistive_text">Ondersteunende tekst</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Titel</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Kort</string>"
    <string name="fluentui_long">Lang</string>"
    <string name="fluentui_indefinite">Onbepaald</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Knop ingedrukt</string>
    <string name="fluentui_dismissed">Gesloten</string>
    <string name="fluentui_timeout">Time-out</string>
    <string name="fluentui_left_swiped">Naar links geveegd</string>
    <string name="fluentui_right_swiped">Naar rechts geveegd</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Tekst</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Nummer</string>
    <string name="fluentui_keyboard_phone">Telefoon</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">E-mailadres</string>
    <string name="fluentui_keyboard_password">Wachtwoord</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimaal</string>
</resources>