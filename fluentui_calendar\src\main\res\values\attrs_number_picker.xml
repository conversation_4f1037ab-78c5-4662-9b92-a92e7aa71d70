<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>
    <attr name="fluentui_numberPickerStyle"/>

    <declare-styleable name="NumberPicker">
        <!-- Color for the solid color background if such for optimized rendering. -->
        <attr name="fluentui_solidColor"/>
        <!-- The divider for making the selection area. -->
        <attr name="fluentui_selectionDivider"/>
        <!-- The height of the selection divider. -->
        <attr name="fluentui_selectionDividerHeight"/>
        <!-- The distance between the two selection dividers. -->
        <attr name="fluentui_selectionDividersDistance"/>
        <!-- The min height of the NumberPicker. -->
        <attr name="fluentui_internalMinHeight"/>
        <!-- The max height of the NumberPicker. -->
        <attr name="fluentui_internalMaxHeight"/>
        <!-- The min width of the NumberPicker. -->
        <attr name="fluentui_internalMinWidth"/>
        <!-- The max width of the NumberPicker. -->
        <attr name="fluentui_internalMaxWidth"/>
        <!-- The layout of the number picker. -->
        <attr name="fluentui_internalLayout"/>
        <!-- The drawable for pressed virtual (increment/decrement) buttons. -->
        <attr name="fluentui_virtualButtonPressedDrawable"/>
        <!-- If true then the selector wheel is hidden until the picker has focus. -->
        <attr name="fluentui_hideWheelUntilFocused"/>
        <attr name="fluentui_numberPickerTextColor"/>
        <attr name="fluentui_numberPickerSelectedTextColor"/>
        <attr name="fluentui_selectorWheelItemCount"/>
        <attr name="fluentui_textAlign"/>
    </declare-styleable>
</resources>