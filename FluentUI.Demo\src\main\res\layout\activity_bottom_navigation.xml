<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/default_layout_margin"
    android:showDividers="middle"
    android:divider="@drawable/demo_divider"
    android:orientation="vertical">

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/toggle_label_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/default_layout_margin"
        android:text="@string/bottom_navigation_toggle_labels_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/three_menu_items_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/default_layout_margin"
        android:text="@string/bottom_navigation_three_menu_items_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/four_menu_items_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/default_layout_margin"
        android:text="@string/bottom_navigation_four_menu_items_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/five_menu_items_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/default_layout_margin"
        android:text="@string/bottom_navigation_five_menu_items_button" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.microsoft.fluentui.widget.BottomNavigationView
            android:id="@+id/bottom_navigation"
            style="@style/Widget.FluentUI.BottomNavigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:menu="@menu/menu_bottom_nav" />

    </FrameLayout>

</LinearLayout>