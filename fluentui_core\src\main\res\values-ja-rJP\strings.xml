<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">プライマリ</string>
    <string name="fluentui_secondary">セカンダリ</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">却下</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">選択済み</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">未選択</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">アイコン</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">アイコン</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">無効</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">動作設定ボタン</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">有効</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">シートを閉じる</string>
    <!-- Describes close action -->
    <string name="fluentui_close">閉じる</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">キャンセル</string>
    <!--name of the icon -->
    <string name="fluentui_search">検索</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">マイク</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">テキストをクリア</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">戻る</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">アクティブ</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">非アクティブ化</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">中立</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">ブランド</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">コントラスト</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">アクセント</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">警告</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">危険</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">エラーが発生しました</string>
    <string name="fluentui_error">エラー</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">ヒント</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">山形の帯図形</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">アウトライン</string>

    <string name="fluentui_action_button_icon">アクション ボタン アイコン</string>
    <string name="fluentui_center">文字列を中央に揃える</string>
    <string name="fluentui_accessory_button">アクセサリ ボタン</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">ラジオ ボタン</string>
    <string name="fluentui_label">ラベル</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">展開済み</string>
    <string name="fluentui_collapsed">折りたたみ済み</string>

    <!--types of control -->
    <string name="fluentui_large">大</string>
    <string name="fluentui_medium">中</string>
    <string name="fluentui_small">小</string>
    <string name="fluentui_password_mode">パスワード モード</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">字幕</string>
    <string name="fluentui_assistive_text">支援テキスト</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">タイトル</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">短い</string>"
    <string name="fluentui_long">長い</string>"
    <string name="fluentui_indefinite">無期限</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">ボタンが押されました</string>
    <string name="fluentui_dismissed">消去済み</string>
    <string name="fluentui_timeout">タイムアウト</string>
    <string name="fluentui_left_swiped">左にスワイプしました</string>
    <string name="fluentui_right_swiped">右にスワイプしました</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">テキスト</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">数値</string>
    <string name="fluentui_keyboard_phone">電話</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">メール</string>
    <string name="fluentui_keyboard_password">パスワード</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">10 進数</string>
</resources>