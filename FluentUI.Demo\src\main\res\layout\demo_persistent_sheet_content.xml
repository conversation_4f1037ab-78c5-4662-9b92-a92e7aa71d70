<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/demo_bottom_sheet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.microsoft.fluentui.persistentbottomsheet.SheetHorizontalItemList
        android:id="@+id/sheet_horizontal_item_list_1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:background="@drawable/ms_row_divider" />

    <com.microsoft.fluentui.persistentbottomsheet.SheetHorizontalItemList
        android:id="@+id/sheet_horizontal_item_list_2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/sheet_horizontal_item_list_3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:background="@drawable/ms_row_divider" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/sheet_vertical_item_list_1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/fluentui_bottom_sheet_bottom_padding"
        android:layout_marginTop="@dimen/default_layout_margin"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
</LinearLayout>