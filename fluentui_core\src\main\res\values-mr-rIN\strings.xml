<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">प्राथमिक</string>
    <string name="fluentui_secondary">द्वितीय</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">डिसमिस करा</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">निवडले</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">निवडले नाही</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">प्रतीक</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">प्रतीक</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">अक्षम केले</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">क्रिया बटण</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">सक्षम केले</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">पत्रक बंद करा</string>
    <!-- Describes close action -->
    <string name="fluentui_close">बंद करा</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">रद्द करा</string>
    <!--name of the icon -->
    <string name="fluentui_search">शोध</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">मायक्रोफोन</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">मजकूर साफ करा</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">मागे</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">सक्रिय केले</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">निष्क्रिय केले</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">तटस्थ</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">ब्रँड</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">कॉन्ट्रास्ट</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">ऍक्सेंट</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">चेतावणी</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">धोका</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">एक त्रुटी आली आहे</string>
    <string name="fluentui_error">त्रुटी</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">संकेत</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">शेव्हरॉन</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">बाह्यरेखा</string>

    <string name="fluentui_action_button_icon">कृती बटण प्रतीक</string>
    <string name="fluentui_center">मध्यभागातील मजकूर</string>
    <string name="fluentui_accessory_button">ऍक्सेसरी बटणे</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">रेडिओ बटण</string>
    <string name="fluentui_label">लेबल</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">विस्तृत केले</string>
    <string name="fluentui_collapsed">संक्षिप्त केलेले</string>

    <!--types of control -->
    <string name="fluentui_large">मोठा</string>
    <string name="fluentui_medium">मध्यम</string>
    <string name="fluentui_small">लहान</string>
    <string name="fluentui_password_mode">पासवर्ड मोड</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">उपशीर्षक</string>
    <string name="fluentui_assistive_text">सहाय्यक मजकूर</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">शीर्षक</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">आखूड</string>"
    <string name="fluentui_long">लांब</string>"
    <string name="fluentui_indefinite">अनिश्चित</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">बटण दाबले</string>
    <string name="fluentui_dismissed">डिसमिस केले</string>
    <string name="fluentui_timeout">कालबाह्य</string>
    <string name="fluentui_left_swiped">डावीकडे स्वाइप केले</string>
    <string name="fluentui_right_swiped">उजवीकडे स्वाइप केले</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">मजकूर</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">संख्या</string>
    <string name="fluentui_keyboard_phone">फोन</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">ईमेल</string>
    <string name="fluentui_keyboard_password">पासवर्ड</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">दशांश</string>
</resources>