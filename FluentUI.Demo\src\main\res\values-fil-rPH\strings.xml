<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI Demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Pinili ang %s</string>
    <string name="app_modifiable_parameters">Mga Nababagong Parameter</string>
    <string name="app_right_accessory_view">Kanang Accessory View</string>

    <string name="app_style">Estilo</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Napindot ang Icon</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Simulan ang Demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carousel</string>
    <string name="actionbar_icon_radio_label">Icon</string>
    <string name="actionbar_basic_radio_label">Karaniwan</string>
    <string name="actionbar_position_bottom_radio_label">Ibaba</string>
    <string name="actionbar_position_top_radio_label">Itaas</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Uri ng ActionBar</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Posisyon ng ActionBar</string>

    <!--AppBar-->
    <string name="app_bar_style">Estilo ng AppBar</string>
    <string name="app_bar_subtitle">Subtitle</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Bottom Border</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Na-click ang icon ng navigation.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Bandila</string>
    <string name="app_bar_layout_menu_settings">Settings</string>
    <string name="app_bar_layout_menu_search">Search</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Galaw ng pag-scroll: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">I-toggle ang behavior ng pag-scroll</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">I-toggle ang navigation pane</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Ipakita ang avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Ipakita ang back icon</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Itago ang Icon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Ipakita ang Icon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">I-toggle ang estilo ng layout ng searchbar</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Ipakita bilang accessory view</string>
    <string name="app_bar_layout_searchbar_action_view_button">Ipakita bilang action view</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Mag-toggle sa pagitan ng mga tema (muling ginagawa ang aktibidad)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">I-toggle ang tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Item</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Dagdag na nai-scroll na nilalaman</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Mga estilo ng bilog</string>
    <string name="avatar_style_square">Parisukat na estilo</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Malaki</string>
    <string name="avatar_size_medium">Katamtaman</string>
    <string name="avatar_size_small">Maliit</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dobleng Extra Large</string>
    <string name="avatar_size_xlarge_accessibility">Napakalaki</string>
    <string name="avatar_size_xsmall_accessibility">Napakaliit</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Max na Ipinapakitang Avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Bilang ng Overflow Avatar</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Uri ng Border</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Ang Grupo ng Avatar na may OverflowAvatarCount set ay hindi susundin sa max na Ipinapakitang Avatar.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Face Stack</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Face Pile</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Na-click ang overflow</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Na-click ang AvatarView sa index %d-click</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Notification Badge</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Tuldok</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Listahan</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Character</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Mga Larawan</string>
    <string name="bottom_navigation_menu_item_news">Balita</string>
    <string name="bottom_navigation_menu_item_alerts">Mga Alerto</string>
    <string name="bottom_navigation_menu_item_calendar">Kalendaryo</string>
    <string name="bottom_navigation_menu_item_team">Team</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">I-toggle ang mga label</string>
    <string name="bottom_navigation_three_menu_items_button">Ipakita ang tatlong item sa menu</string>
    <string name="bottom_navigation_four_menu_items_button">Ipakita ang apat na item sa menu</string>
    <string name="bottom_navigation_five_menu_items_button">Ipakita ang limang item sa menu</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Ang mga label ay %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">IbabangSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Pagahin ang Pag-swipe Paibabba para Alisin</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Ipakita gamit ang mga single line item</string>
    <string name="bottom_sheet_with_double_line_items">Ipakita gamit ang double line item</string>
    <string name="bottom_sheet_with_single_line_header">Ipakita gamit ang single line item</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Ipakita gamit ang double line header at divider</string>
    <string name="bottom_sheet_dialog_button">Ipakita</string>
    <string name="drawer_content_desc_collapse_state">Palawakin</string>
    <string name="drawer_content_desc_expand_state">I-minimize</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">I-click ang %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Mahabang Pag-click sa %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Na-dismiss ang click</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Magsingit ng item</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">I-update ang Item</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Alisin</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Idagdag</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Pagbanggit</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Bold</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Italic</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Salungguhit</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Strikethrough</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">I-undo</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Gawin muli</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Bullet</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Listahan</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Link</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Ina-update ang Item</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Pag-eespasyo</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Alisin ang Posisyon</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">SIMULA</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Espasyo ng grupo</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Item space</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Bandila</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">I-flag ang item na na-click</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Tumugon</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Na-click ang item ng tugon</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">I-forward</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">I-forward ang item na na-click</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Tanggalin</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Tanggalin ang item na na-click</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Camera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Kumuha ng larawan</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Na-click ang item sa camera</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Gallery</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Tingnan ang iyong mga larawan</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Na-click ang item sa gallery</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Mga video</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">I-play ang iyong mga video</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Na-click ang item ng mga video</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Pamahalaan</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Pamahalaan ang iyong media library</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Pamahalaan ang na-click na item</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Mga aksiyon sa email</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Mga Dokumento</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Huling na-update 2:14PM</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Ibahagi</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Ibahagi ang item na na-click</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Ilipat</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Ilipat ang item na na-click</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Tanggalin</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Tanggalin ang item na na-click</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Impormasyon</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Na-click ang item ng info</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Orasan</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Na-click ang clock item</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Ilipat ang item na na-click</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Time zone</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Na-click ang item sa time zone</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Iba\'t ibang view ng button</string>
    <string name="button">Button</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Halimbawa ng Button ng Naka-disable</string>
    <string name="button_borderless">Halimbawa ng Button na Walang Border</string>
    <string name="button_borderless_disabled">Halimbawa ng Button ng Naka-disable ang Walang Border</string>
    <string name="button_large">Halimbawa ng Malaking Button</string>
    <string name="button_large_disabled">Halimbawa ng Button ng Naka-disable ang Malaki</string>
    <string name="button_outlined">Halimbawa ng Buton ng Naka-outline</string>
    <string name="button_outlined_disabled">Halimbawa ng Button ng Naka-disable ang Naka-outline</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Pumili ng petsa</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Isang Petsa</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Walang piniling petsa</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Ipakita ang pampili ng petsa</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Ipakita ang tawag ng oras ng petsa na may napiling tab ng oras</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Ipakita ang tagapili ng oras ng petsa na may napiling tab ng oras</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Ipakita ang tagapili ng oras ng petsa</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Hanay ng Petsa</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Simula:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Pagtatapos:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Walang piniling simula</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Walang piniling pagtatapos</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Piliin ang petsa ng pagsisimula</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Piliin ang petsa ng pagtatapos</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Saklaw ng Oras ng Petsa</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Pumili ng hanay ng oras ng petsa</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Ipakita ang Dialog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Ipakita ang drawer</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Ipakita ang dialog ng drawer</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Walang fade bottom na dialog</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Ipakita ang nangungunang drawer</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Walang fade top dialog</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Ipakita ang dialog ng view ng anchor</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Ipakita ang walang pamagat na nangungunang dialog</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Ipakita ang dialog sa ibaba ng pamagat sa itaas</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Ipakita ang kanang drawer</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Ipakita ang kaliwang drawer</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Pamagat, pangunahing teksto</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtitle, secondary text</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Pasadyang teksto ng subtitle</string>
    <!-- Footer -->
    <string name="list_item_footer">Footer, tertiary text</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Isang linyang listahan na may gray na teksto ng sub header</string>
    <string name="list_item_sub_header_two_line">Dalawang linyang listahan</string>
    <string name="list_item_sub_header_two_line_dense">Dalawang linyang listahan na may siksik na espasyo</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Listahan ng dalawang linya na may custom na pangalawang subtitle na view</string>
    <string name="list_item_sub_header_three_line">Tatlong linyang listahan na may itim na subheader na teksto</string>
    <string name="list_item_sub_header_no_custom_views">Maglista ng mga item na walang custom na view</string>
    <string name="list_item_sub_header_large_header">Maglista ng mga item na may malalaking custom na view</string>
    <string name="list_item_sub_header_wrapped_text">Maglista ng mga item na may naka-wrap na text</string>
    <string name="list_item_sub_header_truncated_text">Maglista ng mga item na may pinutol na teksto</string>
    <string name="list_item_sub_header_custom_accessory_text">Aksyon</string>
    <string name="list_item_truncation_middle">Gitnang pagputol.</string>
    <string name="list_item_truncation_end">Tapusin ang pagputol.</string>
    <string name="list_item_truncation_start">Simulan ang pagputol.</string>
    <string name="list_item_custom_text_view">Halaga</string>
    <string name="list_item_click">Nag-click ka sa item sa listahan.</string>
    <string name="list_item_click_custom_accessory_view">Nag-click ka sa view ng custom na accessory.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Nag-click ka sa view ng custom na accessory ng sub header.</string>
    <string name="list_item_more_options">Higit pang mga opsyon</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Pumili</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Wala</string>
    <string name="people_picker_delete_example">Tanggalin</string>
    <string name="people_picker_custom_persona_description">Ipinapakita ng halimbawang ito kung paano gumawa ng custom na object ng IPersona.</string>
    <string name="people_picker_dialog_title_removed">Inalis mo ang isang persona:</string>
    <string name="people_picker_dialog_title_added">Nagdagdag ka ng persona:</string>
    <string name="people_picker_drag_started">Nagsimula ang pag-drag</string>
    <string name="people_picker_drag_ended">Natapos ang pag-drag</string>
    <string name="people_picker_picked_personas_listener">Personas Listener</string>
    <string name="people_picker_suggestions_listener">Tagapakinig ng mga Mungkahi</string>
    <string name="people_picker_persona_chip_click">Nag-click ka sa %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s na tatanggap</item>
        <item quantity="other">%1$s mga tatanggap</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Palawakin ang Persistent BottomSheet</string>
    <string name="collapse_persistent_sheet_button"> Ipakita ang Persistent BottomSheet</string>
    <string name="show_persistent_sheet_button"> Ipakita ang Persistent BottomSheet</string>
    <string name="new_view">Ito ay Bagong View</string>
    <string name="toggle_sheet_content">I-toggle ang Bottomsheet Content</string>
    <string name="switch_to_custom_content">Lumipat sa custom na Content</string>
    <string name="one_line_content">Isang Linya sa Bottomsheet na Nilalaman</string>
    <string name="toggle_disable_all_items">I-toggle ang Disable na Lahat ng Item</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Magdagdag/Magtanggal ng View</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Baguhin ang Na-collapse na Taas</string>
    <string name="persistent_sheet_item_create_new_folder_title">Bagong Folder</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Na-click ang bagong item sa Folder</string>
    <string name="persistent_sheet_item_edit_title">I-edit</string>
    <string name="persistent_sheet_item_edit_toast">I-edit ang na-click na item</string>
    <string name="persistent_sheet_item_save_title">I-save</string>
    <string name="persistent_sheet_item_save_toast">I-save ang item na na-click</string>
    <string name="persistent_sheet_item_zoom_in_title">I-zoom In</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Na-click ang Pag-zoom In na item</string>
    <string name="persistent_sheet_item_zoom_out_title">Mag-zoom Out</string>
    <string name="persistent_sheet_item_zoom_out_toast">Na-click ang item na Mag-zoom Out</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Available</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Walang tiyak na tagal na may mga update sa pagkilos at teksto</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Engineer</string>
    <string name="persona_subtitle_manager">Tagapamahala</string>
    <string name="persona_subtitle_researcher">Tagapagsaliksik</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (mahabang halimbawa ng teksto upang subukan ang truncation)</string>
    <string name="persona_view_description_xxlarge">XXMalaking avatar na may tatlong linya ng teksto</string>
    <string name="persona_view_description_large">Malaking avatar na may dalawang linya ng text</string>
    <string name="persona_view_description_small">Maliit na avatar na may isang linya ng text</string>
    <string name="people_picker_hint">Wala sa hint na ipinapakita</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Disabled Persona Chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Error sa Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Persona Chip na walang malapit na icon</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Basic Persona Chip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Nag-click ka sa isang napiling Persona Chip.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Ibahagi</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Subaybayan</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Mag-imbita ng mga tao</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">I-refresh ang pahina</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Buksan sa browser</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Ito ay isang multiline na Popup Menu. Ang mga max na linya ay nakatakda sa dalawa, ang natitirang bahagi ng teksto ay puputulin.
        Lorem ipsum dolor sit amet consectetur adipiscing elit.</string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Lahat ng balita</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Naka-save na balita</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Balita mula sa mga site</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">I-notify sa labas ng oras ng pagtatrabaho</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Abisuhan kapag hindi aktibo sa desktop</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Nag-click ka sa item:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Simpleng menu</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Simpleng menu2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu na may isang mapipiling item at isang divider</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu na may lahat ng mapagpipiliang mga item, icon, at mahabang text</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Ipakita</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Pabilog na Pag-unlad</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Maliit</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Katamtaman</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Malaki</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linear Progress</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Hindi matukoy</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinate</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Callback ng Mikropono</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autocorrect</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Pinindot ang Mikropono</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Napindot ang Kanang View</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Napindot ang Paghahanap sa Keyboard</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Ipakita ang Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Alisin ang Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Aksyon</string>
    <string name="snackbar_action_long">Mahabang Aksyon ng Teksto</string>
    <string name="snackbar_single_line">Isang linyang snackbar</string>
    <string name="snackbar_multiline">Isa itong multiline snackbar. Ang mga max na linya ay nakatakda sa dalawa, ang natitirang bahagi ng teksto ay puputulin.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Ito ay isang anunsyo na snackbar. Ginagamit ito para sa pakikipag-usap ng mga bagong feature.</string>
    <string name="snackbar_primary">Ito ay isang pangunahing snackbar.</string>
    <string name="snackbar_light">Ito ay isang light snackbar.</string>
    <string name="snackbar_warning">Isa itong warning snackbar.</string>
    <string name="snackbar_danger">Isa itong panganib na snackbar.</string>
    <string name="snackbar_description_single_line">Maiksing panahon</string>
    <string name="snackbar_description_single_line_custom_view">Mahabang tagal na may pabilog na pag-unlad bilang maliit na custom na view</string>
    <string name="snackbar_description_single_line_action">Maikling duration na may aksyon</string>
    <string name="snackbar_description_single_line_action_custom_view">Maikling tagal na may aksyon at katamtamang custom na view</string>
    <string name="snackbar_description_single_line_custom_text_color">Maikling tagal na may naka-customize na kulay ng teksto</string>
    <string name="snackbar_description_multiline">Mahabang duration</string>
    <string name="snackbar_description_multiline_custom_view">Mahabang tagal na may maliit na custom na view</string>
    <string name="snackbar_description_multiline_action">Walang tiyak na tagal na may mga update sa pagkilos at teksto</string>
    <string name="snackbar_description_multiline_action_custom_view">Maikling tagal na may aksyon at katamtamang custom na view</string>
    <string name="snackbar_description_multiline_action_long">Maikling tagal na may mahabang teksto ng aksyon</string>
    <string name="snackbar_description_announcement">Maiksing panahon</string>
    <string name="snackbar_description_updated">Ang tekstong ito ay na-update.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Ipakita ang Snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Isang linya</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Multiline</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Estilo ng anunsyo</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Pangunahing istilo</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Banayad na istilo</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Estilo ng babala</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Estilo ng panganib</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Home</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Mail</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Mga Setting</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Abiso</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Higit pa</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Pagpapantay ng Text</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Patayo</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Pahalang</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Walang Text</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Mga Item sa Tab</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Pamagat</string>
    <string name="cell_sample_description">Paglalarawan</string>
    <string name="calculate_cells">Mag-load/magkalkula ng mga 100 cell</string>
    <string name="calculate_layouts">Mag-load/magkalkula ng 100 layout</string>
    <string name="template_list">Listahan ng Template</string>
    <string name="regular_list">Regular na Listahan</string>
    <string name="cell_example_title">Pamagat: Cell</string>
    <string name="cell_example_description">Paglalarawan: I-tap para baguhin ang oryentasyon</string>
    <string name="vertical_layout">Patayong layout</string>
    <string name="horizontal_layout">Pahalang na layout</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Karaniwang Tab 2-Segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Karaniwang Tab 3-Segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Karaniwang Tab 4-Segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standard Tab na may Pager</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Switch Tab</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Pills Tab </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tap para sa Tooltip</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tap para sa Custom Calendar Tooltip</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Tap para sa Custom Color Tooltip</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tap para sa Dismiss Inside Tooltip</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tap para sa Custom View Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Nangungunang Custom Color Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Top End Tooltip na may 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Bottom Start Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Bottom End Tooltip na may 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">I-dismiss sa loob ng Tooltip</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Na-dismiss ang tooltip</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Ang headline ay Light 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Ang Pamagat 1 ay Medium 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Ang Pamagat 2 ay Regular na 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Ang heading ay Regular 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Ang subheading 1 ay Regular na 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Ang subheading 2 ay Medium 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Body 1 ay Regular 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Ang katawan 2 ay Medium 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Regular na 12sp ang Caption</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Bersyon ng SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Item %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Folder</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Na-click</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">Pinalawak ang FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">Pinaliit ang FAB</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">I-click para i-refresh ang listahan</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Buksan ang Drawer</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Item ng Menu</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Offset X (sa dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Offset Y (nasa dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Teksto ng Nilalaman</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Ulitin ang Teksto ng Nilalaman</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Ang lapad ng menu ay magbabago nang may paggalang sa Teksto ng Content. Ang max na
        lapad ay limitado sa 75% ng laki ng screen. Ang margin ng content mula sa gilid at ibaba ay pinamamahalaan ng token. Ang parehong teksto ng nilalaman ay uulitin para mag-iba ng
        taas.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Buksan ang Menu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Basic Card</string>
    <!-- UI Label for Card -->
    <string name="file_card">Card ng file</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Announcement Card</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Random UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">Mga opsyon</string>
    <!-- UI Label for Title -->
    <string name="card_title">Pamagat</string>
    <!-- UI Label for text -->
    <string name="card_text">Teksto</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Sub Text</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Maaaring magbalot ng dalawang linya ang pangalawang kopya para sa banner na ito kung kinakailangan.</string>
    <!-- UI Label Button -->
    <string name="card_button">Button</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Ipakita ang Dialog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">I-dismiss ang dialog sa pag-click sa labas</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">I-dismiss ang dialog sa pagpindot ng pabalik</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Na-dismiss ang dialog</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Kanselahin</string>
    <!-- UI Label Ok -->
    <string name="ok">Ok</string>
    <!-- A sample description -->
    <string name="dialog_description">Ang dialog ay isang maliit na window na nag-uudyok sa user na gumawa ng desisyon o magpasok ng karagdagang impormasyon.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Buksan ang Drawer</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Palawakin ang Drawer</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Isara ang Drawer</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Pumili ng Uri ng Drawer</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Itaas</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Ipinapakita ang buong drawer sa nakikitang rehiyon.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Ibaba</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Ang buong drawer ay nagpapakita sa nakikitang rehiyon. I-swipe pataas ang nilalaman ng pag-iskrol ng paggalaw. Lumalawak ang napapalawak na drawer sa pamamagitan ng hawakan sa pag-drag.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">I-slide Pakaliwa</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">I-slide ang drawer sa nakikitang rehiyon mula sa kaliwang bahagi.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">I-slide Pakanan</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">I-slide ang drawer sa nakikitang rehiyon mula sa kanang bahagi.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">I-slide Pababa</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">I-slide ang drawer sa nakikitang rehiyon mula sa ibaba ng screen. I-swipe pataas ang paggalaw sa napapalawak na drawer na dalhin ang natitirang bahagi nito sa nakikitang rehiyon &amp; pagkatapos ay mag-iskrol.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Nakikita ang Scrim</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Pumili ng Nilalaman ng Drawer</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Laki ng buong screen na naso-scroll na nilalaman</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mahigit sa kalahating nilalaman ng screen</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mas mababa sa nilalaman ng kalahating screen</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dynamic na laki ng nilalaman</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Naka-nest na Nilalaman ng Drawer</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Napapalawak</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Laktawan ang Nakabukas na Katayuan</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Pigilan ang Pag-alis sa Pag-click ng Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Ipakita ang Hawakan</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Pamagat</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Text ng Tooltip</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">I-tap para sa Custom na Content Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Simula sa Itaas </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Itaas sa Dulo </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Start sa ibaba </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Sa bandang huli </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Gitna </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Custom Center</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Para sa mga update sa Mga Release Note, </string>
    <string name="click_here">mag-click dito.</string>
    <string name="open_source_cross_platform">Open source cross platform Design System.</string>
    <string name="intuitive_and_powerful">Intuitive &amp; Makapangyarihan.</string>
    <string name="design_tokens">Mag disenyo ng Tokens</string>
    <string name="release_notes">Mga Release Note</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub Repo Link</string>
    <string name="report_issue">Mag-ulat ng Isyu</string>
    <string name="v1_components">Mga V1 Component</string>
    <string name="v2_components">Mga V2 Component</string>
    <string name="all_components">Lahat</string>
    <string name="fluent_logo">Fluent na Logo</string>
    <string name="new_badge">Bago</string>
    <string name="modified_badge">Binago</string>
    <string name="api_break_badge">API Break</string>
    <string name="app_bar_more">Iba pa</string>
    <string name="accent">Accent</string>
    <string name="appearance">Itsura</string>
    <string name="choose_brand_theme">Piliin ang tema ng iyong brand:</string>
    <string name="fluent_brand_theme">Fluent Brand</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Pumili ng Hitsura</string>
    <string name="appearance_system_default">Default ng System</string>
    <string name="appearance_light">Maliwanag</string>
    <string name="appearance_dark">Madilim</string>
    <string name="demo_activity_github_link">Demo Activity GitHub Link</string>
    <string name="control_tokens_details">Mga detalye ng Mga Control Token</string>
    <string name="parameters">Mga Parameter</string>
    <string name="control_tokens">Mga Control Token</string>
    <string name="global_tokens">Mga Global Token</string>
    <string name="alias_tokens">Mga Token ng Alyas</string>
    <string name="sample_text">Teksto</string>
    <string name="sample_icon">Halimbawang Icon</string>
    <string name="color">Kulay</string>
    <string name="neutral_color_tokens">Mga Neutral na Token ng Kulay</string>
    <string name="font_size_tokens">Mga Token ng Laki ng Font</string>
    <string name="line_height_tokens">Mga Token ng Taas ng Linya</string>
    <string name="font_weight_tokens">Mga Token ng Timbang ng Font</string>
    <string name="icon_size_tokens">Mga Token ng Laki ng Icon</string>
    <string name="size_tokens">Mga Token ng Laki</string>
    <string name="shadow_tokens">Mga Token ng Anino</string>
    <string name="corner_radius_tokens">Mga RadiusTokens ng Corner</string>
    <string name="stroke_width_tokens">Mga Token ng Lapad ng Stroke</string>
    <string name="brand_color_tokens">Mga Token ng Kulay ng Brand</string>
    <string name="neutral_background_color_tokens">Mga Token ng Kulay ng Neutral na Background</string>
    <string name="neutral_foreground_color_tokens">Mga Token ng Neutral na Kulay ng Foreground</string>
    <string name="neutral_stroke_color_tokens">Mga Token ng Kulay ng Neutral Stroke</string>
    <string name="brand_background_color_tokens">Mga Token ng Kulay ng Background ng Brand</string>
    <string name="brand_foreground_color_tokens">Mga Token ng Kulay ng Foreground ng Brand</string>
    <string name="brand_stroke_color_tokens">Mga Token ng Kulay ng Stroke ng Brand</string>
    <string name="error_and_status_color_tokens">Error at Mga Katayuan ng Kulay Token</string>
    <string name="presence_tokens">Mga Token ng Kulay ng Presensya</string>
    <string name="typography_tokens">Mga Token ng Typography</string>
    <string name="unspecified">Hindi tinukoy</string>

</resources>