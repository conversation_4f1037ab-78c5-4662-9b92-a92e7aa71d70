<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Демонстрация на потребителски интерфейс на Fluent</string>
    <string name="app_title">Потребителски интерфейс на Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Избран е %s</string>
    <string name="app_modifiable_parameters">Параметри, които могат да се модифицират</string>
    <string name="app_right_accessory_view">Изглед на десен аксесоар</string>

    <string name="app_style">Стил</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Иконата е натисната</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Стартиране на демонстрацията</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Въртележка</string>
    <string name="actionbar_icon_radio_label">Икона</string>
    <string name="actionbar_basic_radio_label">Основни</string>
    <string name="actionbar_position_bottom_radio_label">Отдолу</string>
    <string name="actionbar_position_top_radio_label">Отгоре</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Тип лента с действия</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Позиция на лентата с действия</string>

    <!--AppBar-->
    <string name="app_bar_style">Стил на лента с приложения</string>
    <string name="app_bar_subtitle">Подзаглавие</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Долна граница</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Иконата за навигация е щракната.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Флаг</string>
    <string name="app_bar_layout_menu_settings">Настройки</string>
    <string name="app_bar_layout_menu_search">Търсене</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Поведение при превъртане: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Превключване на поведението при превъртане</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Превключване на иконата за навигация</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Показване на аватар</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Показване на иконата "Назад"</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Скриване на иконата</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Показване на иконата</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Превключване на стила на оформлението на лентата за търсене</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Показване като изглед на аксесоар</string>
    <string name="app_bar_layout_searchbar_action_view_button">Показване като изглед на действие</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Превключване между темите (пресъздава дейността)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Превключване на тема</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Елемент</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Допълнително съдържание с възможност за превъртане</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Стилове за кръгове</string>
    <string name="avatar_style_square">Квадратен стил</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Голяма</string>
    <string name="avatar_size_medium">Средна</string>
    <string name="avatar_size_small">Малка</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Двойна много голяма</string>
    <string name="avatar_size_xlarge_accessibility">Много голямо</string>
    <string name="avatar_size_xsmall_accessibility">Много малко</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Максимален брой показвани аватари</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Брой аватари при препълване</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Тип граница</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Групата аватари с набор OverflowAvatarCount няма да се придържа към максималния брой показани аватари.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Група на лицето</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Купчина лица</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Щракнато е върху елемент за препълване</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Щракнато е върху AvatarView при индекс %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Значка за известяване</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Точка</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Списък</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Символ</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Снимки</string>
    <string name="bottom_navigation_menu_item_news">Новини</string>
    <string name="bottom_navigation_menu_item_alerts">Известявания</string>
    <string name="bottom_navigation_menu_item_calendar">Календар</string>
    <string name="bottom_navigation_menu_item_team">Екип</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Превключване на етикети</string>
    <string name="bottom_navigation_three_menu_items_button">Показване на три елемента от менюто</string>
    <string name="bottom_navigation_four_menu_items_button">Показване на четири елемента от менюто</string>
    <string name="bottom_navigation_five_menu_items_button">Показване на пет елемента от менюто</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Етикетите са %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">ДолуНаЛиста</string>
    <string name="bottom_sheet_dialog">ДиалоговПрозорецДолуНаЛиста</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Разрешаване на плъзгане надолу за отхвърляне</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Показване с елементи от един ред</string>
    <string name="bottom_sheet_with_double_line_items">Показване с елементи от два реда</string>
    <string name="bottom_sheet_with_single_line_header">Показване с едноредов горен колонтитул</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Показване с двуредова заглавка и разделители</string>
    <string name="bottom_sheet_dialog_button">Показване</string>
    <string name="drawer_content_desc_collapse_state">Разширяване</string>
    <string name="drawer_content_desc_expand_state">Минимизиране</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Щракнете върху %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Дълго щракване %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Отхвърляне при щракване</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Вмъкване на елемент</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Актуализиране на елемент</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Отхвърляне</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Добавяне</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Споменаване</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Получер</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Курсив</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Подчертаване</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Зачеркнато</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Отмяна</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Върни</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Водещ символ</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Списък</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Връзка</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Актуализиране на елемент</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Разстояние</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Отхвърляне на позицията</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">СТАРТ</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">КРАЙ</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Пространство между групи</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Разстояние между елементи</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Флаг</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Елементът с флаг е щракнат</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Отговор</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Щракнато е върху елемент за отговор</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Пренасочване</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Щракнато е върху елемент за препращане</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Изтриване</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Щракнато е върху елемент за изтриване</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Аватар</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Камера</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Правене на снимка</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Щракнат елемент от камерата</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Галерия</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Преглед на вашите снимки</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Щракнато е върху елемент от галерията</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Видеоклипове</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Възпроизвеждане на вашите видеоклипове</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Щракнато е върху елемент от видеоклипове</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Управление</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Управление на вашата мултимедийна библиотека</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Щракнато е върху "Управление на елемент"</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Действия с имейл</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Документи</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Последна актуализация: 14:14 ч.</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Споделяне</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Елементът за споделяне е щракнат</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Преместване</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Щракнато е върху елемент за преместване</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Изтриване</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Щракнато е върху елемент за изтриване</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Информация</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Щракнат информационен елемент</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Часовник</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Щракнато е върху елемент от часовника</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Аларма</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Щракнато е върху елемент на аларма</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Часова зона</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Щракнато е върху елемент на часова зона</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Различни изгледи на бутон</string>
    <string name="button">Бутон</string>
    <string name="buttonbar">Лента с бутони</string>
    <string name="button_disabled">Пример за деактивиран бутон</string>
    <string name="button_borderless">Пример за бутон без граници</string>
    <string name="button_borderless_disabled">Пример за деактивиран бутон без граници</string>
    <string name="button_large">Пример за голям бутон</string>
    <string name="button_large_disabled">Пример за голям деактивиран бутон</string>
    <string name="button_outlined">Пример за очертан бутон</string>
    <string name="button_outlined_disabled">Пример за очертан деактивиран бутон</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Избор на дата</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">ИзборНаДатаИЧас</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Единична дата</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Няма избрана дата</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Показване на избор на дата</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Показване на избора на дата и час с избран раздел за дата</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Показване на избора на дата и час с избран раздел за час</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Показване на избора на дата и час</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Интервал от дати</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Начало:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Край:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Няма избран старт</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Няма избран край</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Избор на начална дата</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Избор на крайна дата</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Диапазон на дата и час</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Избор на диапазон от дати и часове</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">ДиалоговПрозорецЗаИзборНаДатаИЧас</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Показване на диалоговия прозорец</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Показване на чекмеджето</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Показване на диалоговия прозорец на чекмеджето</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Без избледняване на долния диалогов прозорец</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Показване на горното чекмедже</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Без избледняване на горния диалогов прозорец</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Показване на горен диалогов прозорец за изглед на котва</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Не показвай горен диалогов прозорец за заглавие</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Показване под горния диалогов прозорец за заглавие</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Показване на дясното чекмедже</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Показване на лявото чекмедже</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Заглавие, основен текст</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Подзаглавие, вторичен текст</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Текст на подзаглавие по избор</string>
    <!-- Footer -->
    <string name="list_item_footer">Долен колонтитул, третичен текст</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Едноредов списък със сив текст на подзаглавие</string>
    <string name="list_item_sub_header_two_line">Списък с два реда</string>
    <string name="list_item_sub_header_two_line_dense">Двуредов списък с плътна разредка</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Двуредов списък с допълнителен изглед по избор за подзаглавие</string>
    <string name="list_item_sub_header_three_line">Триредов списък с черен текст на подзаглавие</string>
    <string name="list_item_sub_header_no_custom_views">Изброяване на елементи без изгледи по избор</string>
    <string name="list_item_sub_header_large_header">Изброяване на елементи с големи изгледи по избор</string>
    <string name="list_item_sub_header_wrapped_text">Елементи от списък с пренесен текст</string>
    <string name="list_item_sub_header_truncated_text">Изброяване на елементи с отрязан текст</string>
    <string name="list_item_sub_header_custom_accessory_text">Действие</string>
    <string name="list_item_truncation_middle">Отрязване по средата.</string>
    <string name="list_item_truncation_end">Край на отрязването.</string>
    <string name="list_item_truncation_start">Начало на отрязването.</string>
    <string name="list_item_custom_text_view">Стойност</string>
    <string name="list_item_click">Щракнали сте върху елемента от списъка.</string>
    <string name="list_item_click_custom_accessory_view">Щракнали сте върху изгледа на аксесоар по избор.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Щракнали сте върху потребителския изглед на аксесоар на подзаглавие.</string>
    <string name="list_item_more_options">Още опции</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Избор</string>
    <string name="people_picker_select_deselect_example">ИзборОтмянаНаИзбор</string>
    <string name="people_picker_none_example">Няма</string>
    <string name="people_picker_delete_example">Изтриване</string>
    <string name="people_picker_custom_persona_description">Този пример показва как да създадете обект на IPersona по избор.</string>
    <string name="people_picker_dialog_title_removed">Премахнахте персона:</string>
    <string name="people_picker_dialog_title_added">Добавихте персона:</string>
    <string name="people_picker_drag_started">Плъзгането започна</string>
    <string name="people_picker_drag_ended">Плъзгането приключи</string>
    <string name="people_picker_picked_personas_listener">Слушател на персони</string>
    <string name="people_picker_suggestions_listener">Слушател на предложения</string>
    <string name="people_picker_persona_chip_click">Щракнахте върху %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s получател</item>
        <item quantity="other">%1$s получатели</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Разгъване на постоянен долен лист</string>
    <string name="collapse_persistent_sheet_button"> Скриване на постоянна ДолнаЧастНаЛист</string>
    <string name="show_persistent_sheet_button"> Показване на постоянна ДолнаЧастНаЛист</string>
    <string name="new_view">Това е нов изглед</string>
    <string name="toggle_sheet_content">Превключване на съдържанието на ДолнияЛист</string>
    <string name="switch_to_custom_content">Превключване към съдържание по избор</string>
    <string name="one_line_content">Съдържание на долен лист с един ред</string>
    <string name="toggle_disable_all_items">Превключване на забраняването на всички елементи</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Добавяне/премахване на изглед</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Промяна на свитата височина</string>
    <string name="persistent_sheet_item_create_new_folder_title">Нова папка</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Щракнато е върху елемент от нова папка</string>
    <string name="persistent_sheet_item_edit_title">Редактиране</string>
    <string name="persistent_sheet_item_edit_toast">Щракнато е върху елемент за редактиране</string>
    <string name="persistent_sheet_item_save_title">Записване</string>
    <string name="persistent_sheet_item_save_toast">Щракнато е върху елемент за записване</string>
    <string name="persistent_sheet_item_zoom_in_title">Увеличи</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Щракнато е върху елемент за увеличаване</string>
    <string name="persistent_sheet_item_zoom_out_title">Намали</string>
    <string name="persistent_sheet_item_zoom_out_toast">Щракнато е върху елемент за намаляване</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Свободно</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Алан Мунгер</string>
    <string name="persona_name_amanda_brady">Аманда Брейди</string>
    <string name="persona_name_ashley_mccarthy">Ашли Маккарти</string>
    <string name="persona_name_carlos_slattery">Карлос Слатъри</string>
    <string name="persona_name_carole_poland">Карол Поланд</string>
    <string name="persona_name_cecil_folk">Сесил Фолк</string>
    <string name="persona_name_celeste_burton">Селесте Бъртън</string>
    <string name="persona_name_charlotte_waltson">Шарлот Уолтсън</string>
    <string name="persona_name_colin_ballinger">Колин Балингер</string>
    <string name="persona_name_daisy_phillips">Дейзи Филипс</string>
    <string name="persona_name_elliot_woodward">Елиът Удуърд</string>
    <string name="persona_name_elvia_atkins">Елвия Аткинс</string>
    <string name="persona_name_erik_nason">Ерик Нейсън</string>
    <string name="persona_name_henry_brill">Хенри Брил</string>
    <string name="persona_name_isaac_fielder">Айзък Фийлдър</string>
    <string name="persona_name_johnie_mcconnell">Джони Макконъл</string>
    <string name="persona_name_kat_larsson">Кат Ларсон</string>
    <string name="persona_name_katri_ahokas">Катри Ахокас</string>
    <string name="persona_name_kevin_sturgis">Кевин Стърджис</string>
    <string name="persona_name_kristen_patterson">Кристен Патерсън</string>
    <string name="persona_name_lydia_bauer">Лидия Бауер</string>
    <string name="persona_name_mauricio_august">Маурисио Огъст</string>
    <string name="persona_name_miguel_garcia">Мигел Гарсия</string>
    <string name="persona_name_mona_kane">Мона Кейн</string>
    <string name="persona_name_robin_counts">Робин Каунтс</string>
    <string name="persona_name_robert_tolbert">Робърт Толбърт</string>
    <string name="persona_name_tim_deboer">Тим Дибоер</string>
    <string name="persona_name_wanda_howard">Уанда Хауърд</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Дизайнер</string>
    <string name="persona_subtitle_engineer">Инженер</string>
    <string name="persona_subtitle_manager">Ръководител</string>
    <string name="persona_subtitle_researcher">Изследване</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (дълъг текстов пример за тестване на отрязването)</string>
    <string name="persona_view_description_xxlarge">Аватар с размер XXLarge с три реда текст</string>
    <string name="persona_view_description_large">Голям аватар с два реда текст</string>
    <string name="persona_view_description_small">Малък аватар с един ред текст</string>
    <string name="people_picker_hint">Няма с показано подсказване</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Дезактивиран чип на персона</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Грешка в чипа за персона</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Икона на чип на персона без затваряне</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Основен чип персона</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Щракнали сте върху избран чип за персона.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Споделяне</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Следване</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Поканване на хора</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Обновяване на страница</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Отвори в браузъра</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Това е изскачащо меню с много редове. Максималният брой редове е зададен на два, а останалата част от текста ще се закръгли.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Всички новини</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Записани новини</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Новини от сайтове</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Уведомяване извън работно време</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Уведомяване при неактивност на работния плот</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Щракнахте върху елемента:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Просто меню</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Просто меню2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Меню с един избираем елемент и разделител</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Меню с всички избираеми елементи, икони и дълъг текст</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Показване</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Кръгов напредък</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Малка</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Средна</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Голяма</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Линеен напредък</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Неопределено</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Определен</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">ЛентаЗаТърсене</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Обратно повикване от микрофона</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Автокоригиране</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Натиснат микрофон</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Натиснат десен изглед</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Натиснато търсене на клавиатурата</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Показване на лентата за предупреждение</string>
    <string name="fluentui_dismiss_snackbar">Изключване на лентата за предупреждение</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Действие</string>
    <string name="snackbar_action_long">Дълго текстово действие</string>
    <string name="snackbar_single_line">Лента за предупреждение с единична линия</string>
    <string name="snackbar_multiline">Това е панел за закуска с много редове. Максималният брой редове е зададен на два, а останалата част от текста ще се закръгли.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Това е лента за закуска за съобщения. Използва се за комуникация на нови функции.</string>
    <string name="snackbar_primary">Това е основно предупреждение.</string>
    <string name="snackbar_light">Това е малко предупреждение.</string>
    <string name="snackbar_warning">Това е лента за предупреждение.</string>
    <string name="snackbar_danger">Това е лента за предупреждение.</string>
    <string name="snackbar_description_single_line">Кратка продължителност</string>
    <string name="snackbar_description_single_line_custom_view">Дълга продължителност с кръгов напредък като малък изглед по избор</string>
    <string name="snackbar_description_single_line_action">Кратка продължителност с действие</string>
    <string name="snackbar_description_single_line_action_custom_view">Кратка продължителност с действие и среден изглед по избор</string>
    <string name="snackbar_description_single_line_custom_text_color">Кратка продължителност с персонализиран цвят на текста</string>
    <string name="snackbar_description_multiline">Дълга продължителност</string>
    <string name="snackbar_description_multiline_custom_view">Дълга продължителност с малък изглед по избор</string>
    <string name="snackbar_description_multiline_action">Неопределена продължителност с актуализации на действието и текста</string>
    <string name="snackbar_description_multiline_action_custom_view">Кратка продължителност с действие и среден изглед по избор</string>
    <string name="snackbar_description_multiline_action_long">Кратка продължителност с текст с дълго действие</string>
    <string name="snackbar_description_announcement">Кратка продължителност</string>
    <string name="snackbar_description_updated">Този текст е актуализиран.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Показване на лентата за предупреждение</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Единична линия</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Многоредова</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Стил на обявления</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Основен стил</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Светъл стил</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Стил на предупреждение</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Стил на опасност</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Начало</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Поща</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Настройки</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Известие</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Още</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Подравняване на текст</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Вертикално</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Хоризонтално</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Няма текст</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Елементи на раздел</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Заглавие</string>
    <string name="cell_sample_description">Описание</string>
    <string name="calculate_cells">Зареждане/изчисляване на 100 клетки</string>
    <string name="calculate_layouts">Зареждане/изчисляване на 100 оформления</string>
    <string name="template_list">Списък на шаблони</string>
    <string name="regular_list">Обикновен списък</string>
    <string name="cell_example_title">Заглавие: клетка</string>
    <string name="cell_example_description">Описание: Докоснете, за да промените ориентацията</string>
    <string name="vertical_layout">Вертикално оформление:</string>
    <string name="horizontal_layout">Хоризонтално оформление</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Стандартен раздел с 2 сегмента</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Стандартен табулатор с 3 сегмента</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Стандартен табулатор с 4 сегмента</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Стандартен раздел с пейджър</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Превключване на раздела</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Раздел "Капсули" </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Докоснете за пояснение</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Докоснете за пояснение за календар по избор</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Докоснете пояснение за цвят по избор</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Докоснете за изключване на вътрешното пояснение</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Докоснете за пояснение за изглед по избор</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Пояснение за цвят по избор</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Пояснение в долния край с 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Пояснение долу за старт</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Пояснение в долния край с 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Изключване на вътрешното пояснение</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Пояснението е отхвърлено</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Заглавието е Light 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Заглавие 1 е Medium 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Заглавие 2 е Regular 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Заглавието е Regular 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Подзаглавие 1 е Regular 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Подзаглавие 2 е Medium 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Основният текст 1 е Regular 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Основният текст 2 е Medium 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Надписът е Regular 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Версия на SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Елемент %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Папка</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Щракнати</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Определяне на структурата</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB е разгънат</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB е свит</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Щракнете, за да обновите списъка</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Отваряне на чекмеджето</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Елемент от меню</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Изместване по X (в dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Изместване по Y (в dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Текст на съдържание</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Повтаряне на текста на съдържанието</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Ширината на менюто ще се промени по отношение на текста на съдържанието. Максимумът
        ширината е ограничена до 75% на размера на екрана. Полето на съдържанието отстрани и отдолу се управлява от маркер. Един и същ текст на съдържание ще се повтаря, за да варира
        височината.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Отваряне на менюто</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Основна карта</string>
    <!-- UI Label for Card -->
    <string name="file_card">Карта на файл</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Карта за обявления</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Случаен потребителски интерфейс</string>
    <!-- UI Label for Options -->
    <string name="card_options">Опции</string>
    <!-- UI Label for Title -->
    <string name="card_title">Заглавие</string>
    <!-- UI Label for text -->
    <string name="card_text">Текст</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Подтекст</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Вторично копие за този банер може да се пренася на два реда, ако е необходимо.</string>
    <!-- UI Label Button -->
    <string name="card_button">Бутон</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Показване на диалоговия прозорец</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Отхвърляне на диалоговия прозорец при щракване извън</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Изключване на диалоговия прозорец при натискане на бутон НАЗАД</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Диалоговият прозорец е отхвърлен</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Отказ</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Диалоговият прозорец е малък прозорец, който подканва потребителя да вземе решение или да въведе допълнителна информация.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Отваряне на чекмеджето</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Разгъване на чекмеджето</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Затваряне на чекмеджето</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Избор на тип чекмедже</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Отгоре</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Цялото чекмедже се показва във видимата област.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Отдолу</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Цялото чекмедже се показва във видимата област. Плъзнете бързо нагоре съдържанието за превъртане при движение. Разширяемо чекмедже за разширяване чрез манипулатор за плъзгане.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Ляв слайд над</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Плъзгане на чекмеджето до видима област от лявата страна.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Плъзнете отляво над</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Плъзгане на чекмеджето до видима област от дясната страна.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">правилността отдолу над</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Плъзнете чекмеджето до видима област от долната част на екрана. Плъзнете нагоре движение върху разширяемото чекмедже, пренесете останалата част от нея във видимата област &amp; след това превъртете.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim е видим</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Избор на съдържание на чекмеджето</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Съдържание, което може да се превърта на цял екран</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Съдържание, заемащо повече от половин екран</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Съдържание, заемащо по-малко от половин екран</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Съдържание с динамичен размер</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Съдържание на вложено чекмедже</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Разширяем</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Пропускане на отвореното състояние</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Предотвратяване на отхвърлянето при щракване върху Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Показване на манипулатора</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Заглавие</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Текст на пояснение</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Докоснете за пояснение за персонализирано съдържание</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Начало отгоре </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Горен край </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Долен старт </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Долен край </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Център </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Център по избор</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">За актуализации на бележките по изданието, </string>
    <string name="click_here">щракнете тук.</string>
    <string name="open_source_cross_platform">Система за проектиране на различни платформи с отворен код.</string>
    <string name="intuitive_and_powerful">Интуитивно и мощно.</string>
    <string name="design_tokens">Маркери за проектиране</string>
    <string name="release_notes">Бележки по изданието</string>
    <string name="github_repo">Хранилище в GitHub</string>
    <string name="github_repo_link">Връзка към хранилище в GitHub</string>
    <string name="report_issue">Съобщаване за проблем</string>
    <string name="v1_components">Компоненти на V1</string>
    <string name="v2_components">Компоненти на V2</string>
    <string name="all_components">Всички</string>
    <string name="fluent_logo">Емблема на Fluent</string>
    <string name="new_badge">Нова</string>
    <string name="modified_badge">Променени</string>
    <string name="api_break_badge">Прекъсване на API</string>
    <string name="app_bar_more">Още</string>
    <string name="accent">Акцент</string>
    <string name="appearance">Облик</string>
    <string name="choose_brand_theme">Изберете темата на вашата марка:</string>
    <string name="fluent_brand_theme">Марка Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Избор на облик</string>
    <string name="appearance_system_default">По подразбиране за системата</string>
    <string name="appearance_light">Светъл</string>
    <string name="appearance_dark">Тъмен</string>
    <string name="demo_activity_github_link">Връзка към GitHub за демонстрационна дейност</string>
    <string name="control_tokens_details">Подробни данни за контролните маркери</string>
    <string name="parameters">Параметри</string>
    <string name="control_tokens">Контролни маркери</string>
    <string name="global_tokens">Глобални маркери</string>
    <string name="alias_tokens">Маркери за псевдоними</string>
    <string name="sample_text">Текст</string>
    <string name="sample_icon">Примерна икона</string>
    <string name="color">Цвят</string>
    <string name="neutral_color_tokens">Маркери за неутрален цвят</string>
    <string name="font_size_tokens">Маркери за размер на шрифта</string>
    <string name="line_height_tokens">Маркери за височина на линия</string>
    <string name="font_weight_tokens">Маркери за тегло на шрифта</string>
    <string name="icon_size_tokens">Маркери за размер на икона</string>
    <string name="size_tokens">Маркери за размер</string>
    <string name="shadow_tokens">Маркери за сянка</string>
    <string name="corner_radius_tokens">Маркери за радиус на ъгъл</string>
    <string name="stroke_width_tokens">Маркери за ширина на ръкописна линия</string>
    <string name="brand_color_tokens">Маркери за цвят на марката</string>
    <string name="neutral_background_color_tokens">Маркери за неутрален фонов цвят</string>
    <string name="neutral_foreground_color_tokens">Маркери за неутрален цвят на преден план</string>
    <string name="neutral_stroke_color_tokens">Маркери за неутрален цвят на ръкописна линия</string>
    <string name="brand_background_color_tokens">Маркери за фонов цвят на марката</string>
    <string name="brand_foreground_color_tokens">Маркери за цвят на преден план на марката</string>
    <string name="brand_stroke_color_tokens">Маркери за цвят на ръкописна линия на марка</string>
    <string name="error_and_status_color_tokens">Маркери за цвят на грешка и състояние</string>
    <string name="presence_tokens">Маркери за цвят на присъствие</string>
    <string name="typography_tokens">Маркери за типография</string>
    <string name="unspecified">Незададено</string>

</resources>