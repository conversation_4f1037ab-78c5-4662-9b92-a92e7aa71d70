<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">M</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">D</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">M</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">D</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">F</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">S</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">S</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Zur nächsten Woche wechseln</string>
    <string name="accessibility_goto_previous_week">Zur vorherigen Woche wechseln</string>
    <string name="accessibility_today">heute</string>
    <string name="accessibility_selected">Ausgewählt</string>

    <!-- *** Shared *** -->
    <string name="done">Fertig</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Heute</string>
    <string name="tomorrow">Morgen</string>
    <string name="yesterday">Gestern</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Startzeit</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Endzeit</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Startdatum</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Enddatum</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Uhrzeit auswählen</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Datum auswählen</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Datums-/Zeitauswahl</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Datumsauswahl</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Datums-/Zeitauswahlbereich</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Datumsauswahlbereich</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Registerkarte "Startzeit"</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Registerkarte "Endzeit"</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Registerkarte "Startdatum"</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Registerkarte "Enddatum"</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Dialogfeld schließen</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Monat erhöhen</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">nächsten Monat auswählen</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Monat verringern</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">vorherigen Monat auswählen</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Tag erhöhen</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">nächsten Tag auswählen</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Tag verringern</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">vorherigen Tag auswählen</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Jahr erhöhen</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">nächstes Jahr auswählen</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Jahr verringern</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">vorheriges Jahr auswählen</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Datum erhöhen</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">nächstes Datum auswählen</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Datum verringern</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">vorheriges Datum auswählen</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Stunde erhöhen</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">nächste Stunde auswählen</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Stunde verringern</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">vorherige Stunde auswählen</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Minute erhöhen</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">nächste Minute auswählen</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Minute verringern</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">vorherige Minute auswählen</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">AM/PM-Zeitraum umschalten</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">AM/PM-Zeitraum umschalten</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s ausgewählt</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">markiert</string>
</resources>
