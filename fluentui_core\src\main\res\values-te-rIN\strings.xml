<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">ప్రాథమిక</string>
    <string name="fluentui_secondary">ద్వితీయం</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">విస్మరించు</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">ఎంచుకోబడింది</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">ఎంచుకోలేదు</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">సూక్ష్మచిత్రం</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">సూక్ష్మచిత్రం</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">నిలిపివేయబడింది</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">చర్యా బటన్</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">ప్రారంభించబడింది</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">షీట్ మూసివేయి</string>
    <!-- Describes close action -->
    <string name="fluentui_close">మూసివేయి</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">రద్దు చేయి</string>
    <!--name of the icon -->
    <string name="fluentui_search">శోధించు</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">మైక్రోఫోన్</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">వచనాన్ని క్లియర్ చేయి</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">వెనుకకు</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">యాక్టివేట్ చేయబడింది</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">డీ-యాక్టివేట్ చేయబడింది</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">తటస్థం</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">బ్రాండ్</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">వర్ణ వ్యత్యాసం</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">విలక్షణత</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">హెచ్చరిక</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">ప్రమాదం</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">దోషం వచ్చింది</string>
    <string name="fluentui_error">దోషం</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">సూచన</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">చెవ్రన్‌</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">సారాంశ రూపం</string>

    <string name="fluentui_action_button_icon">యాక్షన్ బటన్ ఐకాన్</string>
    <string name="fluentui_center">సెంటర్ టెక్స్ట్</string>
    <string name="fluentui_accessory_button">ఉపసాధనం బటన్‌లు</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">రేడియో బటన్</string>
    <string name="fluentui_label">లేబుల్</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">విస్తరింపజేయబడింది</string>
    <string name="fluentui_collapsed">కుదించబడింది</string>

    <!--types of control -->
    <string name="fluentui_large">పెద్దది</string>
    <string name="fluentui_medium">మధ్యస్థం</string>
    <string name="fluentui_small">చిన్నది</string>
    <string name="fluentui_password_mode">పాస్వర్డ్ మోడ్</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">ఉపశీర్షిక</string>
    <string name="fluentui_assistive_text">సహాయక టెక్స్ట్</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">శీర్షిక</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">పొట్టి</string>"
    <string name="fluentui_long">పొడవు</string>"
    <string name="fluentui_indefinite">నిరవధిక</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">బటన్ నొక్కబడింది</string>
    <string name="fluentui_dismissed">విస్మరించబడింది</string>
    <string name="fluentui_timeout">సమయం ముగిసింది</string>
    <string name="fluentui_left_swiped">ఎడమవైపు స్వైప్ చేయబడింది</string>
    <string name="fluentui_right_swiped">కుడివైపు స్వైప్ చేయబడింది</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">టెక్స్ట్</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">సంఖ్య</string>
    <string name="fluentui_keyboard_phone">ఫోన్</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">ఇమెయిల్</string>
    <string name="fluentui_keyboard_password">అనుమతిపదం</string>
    <string name="fluentui_keyboard_number_password">నంబర్ పాస్వర్డ్</string>
    <string name="fluentui_keyboard_decimal">దశాంశం</string>
</resources>