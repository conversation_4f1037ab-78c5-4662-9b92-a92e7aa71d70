<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demonstração do Fluent UI</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Selecionou %s</string>
    <string name="app_modifiable_parameters">Parâmetros Modificáveis</string>
    <string name="app_right_accessory_view">Vista de Acessório à Direita</string>

    <string name="app_style">Estilo</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ícone Premido</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Iniciar Demonstração</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carrossel</string>
    <string name="actionbar_icon_radio_label">Ícone</string>
    <string name="actionbar_basic_radio_label">Básico</string>
    <string name="actionbar_position_bottom_radio_label">Inferior</string>
    <string name="actionbar_position_top_radio_label">Parte Superior</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tipo de Barra de Ação</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Posição da Barra de Ação</string>

    <!--AppBar-->
    <string name="app_bar_style">Estilo da Barra de Aplicações</string>
    <string name="app_bar_subtitle">Subtítulo</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Limite Inferior</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Ícone de navegação clicado.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Sinalizador</string>
    <string name="app_bar_layout_menu_settings">Definições</string>
    <string name="app_bar_layout_menu_search">Procurar</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Comportamento do deslocamento: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Alternar comportamento do deslocamento</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Ativar/Desativar ícone de navegação</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Mostrar avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Mostrar ícone de anterior</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ocultar ícone</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Mostrar ícone</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Alternar estilo de esquema da barra de pesquisa</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Mostrar como vista de acessório</string>
    <string name="app_bar_layout_searchbar_action_view_button">Mostrar como vista de ação</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Alternar entre temas (recria a atividade)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Alternar tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Item</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Conteúdo extra deslocável</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Estilo do círculo</string>
    <string name="avatar_style_square">Estilo quadrado</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">Xlarge</string>
    <string name="avatar_size_large">Grande</string>
    <string name="avatar_size_medium">Médio</string>
    <string name="avatar_size_small">Pequeno</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Extra Grande Duplo</string>
    <string name="avatar_size_xlarge_accessibility">Muito Grande</string>
    <string name="avatar_size_xsmall_accessibility">Muito Pequeno</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Avatar Máximo Apresentado</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Contagem de Avatar com Capacidade Excedida</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tipo do Limite</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">O Grupo de Avatar com OverflowAvatarCount definido não irão aderir ao máximo de Avatares Apresentados.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Pilha de Rostos</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Pilha de Rosto</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Capacidade excedida clicada</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView no índice %d clicado</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Distintivo de Notificação</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Ponto</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Caráter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotografias</string>
    <string name="bottom_navigation_menu_item_news">Notícias</string>
    <string name="bottom_navigation_menu_item_alerts">Alertas</string>
    <string name="bottom_navigation_menu_item_calendar">Calendário</string>
    <string name="bottom_navigation_menu_item_team">Equipa</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Ativar/desativar etiquetas</string>
    <string name="bottom_navigation_three_menu_items_button">Mostrar três itens de menu</string>
    <string name="bottom_navigation_four_menu_items_button">Mostrar quatro itens de menu</string>
    <string name="bottom_navigation_five_menu_items_button">Mostrar cinco itens de menu</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">As etiquetas são %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Ativar Opção Percorrer para Baixo para Dispensar</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Mostrar com itens de linha única</string>
    <string name="bottom_sheet_with_double_line_items">Mostrar com itens de linha dupla</string>
    <string name="bottom_sheet_with_single_line_header">Mostrar com um único cabeçalho de linha</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Mostrar com cabeçalho de linha e divisores duplos</string>
    <string name="bottom_sheet_dialog_button">Mostrar</string>
    <string name="drawer_content_desc_collapse_state">Expandir</string>
    <string name="drawer_content_desc_expand_state">Minimizar</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Clicar em %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Clique Longo %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Clicar em dispensar</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Inserir item</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Atualizar Item</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Dispensar</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Adicionar</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Menção</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Negrito</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Itálico</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Sublinhado</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Rasurado</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Anular</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Refazer</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Marca de lista</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Ligação</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Atualização de Itens</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Espaçamento</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Dispensar Posição</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">COMEÇAR</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">FIM</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Espaço de grupo</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Espaço do item</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Sinalizador</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Item sinalizado clicado</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Resposta</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Item de resposta clicado</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Em frente</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Item a frente clicado</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Eliminar</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Eliminar item clicado</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Câmara</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Tirar uma fotografia</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Item da câmara clicado</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galeria</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Ver as suas fotografias</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Item da galeria clicado</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Vídeos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Reproduzir os seus vídeos</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Item de vídeos clicado</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Gerir</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Gerir a sua biblioteca de media</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Item de gestão clicado</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Ações de E-mail</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documentos</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Última atualização às 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Partilhar</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Partilhar item clicado</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Mover</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Mover item clicado</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Eliminar</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Eliminar item clicado</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informações</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Item de informações clicado</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Relógio</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Item de relógio clicado</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarme</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Item de alarme clicado</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Fuso horário</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Item de fuso horário clicado</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Diferentes vistas do botão</string>
    <string name="button">Botão</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Exemplo de Botão Desativado</string>
    <string name="button_borderless">Exemplo de Botão Sem Margens</string>
    <string name="button_borderless_disabled">Exemplo de Botão Sem Margens Desativado</string>
    <string name="button_large">Exemplo de Botão Grande</string>
    <string name="button_large_disabled">Exemplo de Botão Grande Desativado</string>
    <string name="button_outlined">Exemplo de Botão Delineado</string>
    <string name="button_outlined_disabled">Exemplo de Botão Delineado Desativado</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Escolher uma data</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Data Única</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nenhuma data escolhida</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Mostrar seletor de datas</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Mostrar o seletor de data/hora com o separador de data selecionado</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Mostrar o seletor de data/hora com o separador de hora selecionado</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Mostrar seletor de data e hora</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Intervalo de Datas</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Início:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Fim:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nenhum início escolhido</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Sem fim escolhido</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Selecionar data de início</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Selecionar data de fim</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Intervalo de Data e Hora</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Selecionar intervalo de data e hora</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Mostrar Caixa de Diálogo</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Mostrar gaveta</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Mostrar caixa de diálogo da gaveta</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Caixa de diálogo inferior sem desvanecer</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Mostrar gaveta superior</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Caixa de diálogo superior sem desvanecer</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Mostrar caixa de diálogo superior da vista de âncora</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Mostrar caixa de diálogo sem título superior</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Mostrar diálogo superior abaixo do título</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Mostrar gaveta direita</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Mostrar gaveta esquerda</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Título, texto principal</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtítulo, texto secundário</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Texto do subtítulo personalizado</string>
    <!-- Footer -->
    <string name="list_item_footer">Rodapé, texto terciário</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Lista de linha única com sub-texto de cabeçalho cinzento</string>
    <string name="list_item_sub_header_two_line">Lista de duas linhas</string>
    <string name="list_item_sub_header_two_line_dense">Lista de duas linhas com espaçamento densa</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Lista de duas linhas com vista personalizada do subtítulo secundário</string>
    <string name="list_item_sub_header_three_line">Lista de três linhas com sub-texto de cabeçalho preto</string>
    <string name="list_item_sub_header_no_custom_views">Listar itens sem vistas personalizadas</string>
    <string name="list_item_sub_header_large_header">Listar itens com vistas personalizadas grandes</string>
    <string name="list_item_sub_header_wrapped_text">Listar itens com texto moldado</string>
    <string name="list_item_sub_header_truncated_text">Listar itens com texto truncado</string>
    <string name="list_item_sub_header_custom_accessory_text">Ação</string>
    <string name="list_item_truncation_middle">Truncagem no meio.</string>
    <string name="list_item_truncation_end">Terminar truncagem.</string>
    <string name="list_item_truncation_start">Iniciar truncagem.</string>
    <string name="list_item_custom_text_view">Valor</string>
    <string name="list_item_click">Clicou no item de lista.</string>
    <string name="list_item_click_custom_accessory_view">Clicou na vista de acessório personalizada.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Clicou na vista de acessório personalizado do subcabeçalho.</string>
    <string name="list_item_more_options">Mais opções</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Selecionar</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Nenhum</string>
    <string name="people_picker_delete_example">Eliminar</string>
    <string name="people_picker_custom_persona_description">Este exemplo mostra como criar um objeto IPersona personalizado.</string>
    <string name="people_picker_dialog_title_removed">Removeu uma persona:</string>
    <string name="people_picker_dialog_title_added">Adicionou uma persona:</string>
    <string name="people_picker_drag_started">Arrastar iniciado</string>
    <string name="people_picker_drag_ended">Arrasto terminado</string>
    <string name="people_picker_picked_personas_listener">Ouvinte de Personas</string>
    <string name="people_picker_suggestions_listener">Ouvinte de Sugestões</string>
    <string name="people_picker_persona_chip_click">Clicou em %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s destinatário</item>
        <item quantity="many">%1$s destinatários</item>
        <item quantity="other">%1$s destinatários</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Expandir BottomSheet Persistente</string>
    <string name="collapse_persistent_sheet_button"> Ocultar BottomSheet Persistente</string>
    <string name="show_persistent_sheet_button"> Mostrar BottomSheet Persistente</string>
    <string name="new_view">Esta é a Nova Vista</string>
    <string name="toggle_sheet_content">Ativar/Desativar Conteúdo da BottomSheet</string>
    <string name="switch_to_custom_content">Mudar para Conteúdo personalizado</string>
    <string name="one_line_content">Conteúdo da BottomSheet de Uma Linha</string>
    <string name="toggle_disable_all_items">Alternar para Desativar Todos os Itens</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Adicionar/Remover Vista</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Alterar a Altura Fechada</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nova Pasta</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Item de Nova Pasta clicado</string>
    <string name="persistent_sheet_item_edit_title">Editar</string>
    <string name="persistent_sheet_item_edit_toast">Item de Edição clicado</string>
    <string name="persistent_sheet_item_save_title">Guardar</string>
    <string name="persistent_sheet_item_save_toast">Item de Guardar clicado</string>
    <string name="persistent_sheet_item_zoom_in_title">Ampliar</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Item de Ampliar clicado</string>
    <string name="persistent_sheet_item_zoom_out_title">Reduzir</string>
    <string name="persistent_sheet_item_zoom_out_toast">Item de Reduzir clicado</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Disponível</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio Augusto</string>
    <string name="persona_name_miguel_garcia">Guilherme Sarmento</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Engenheiro</string>
    <string name="persona_subtitle_manager">Gestor</string>
    <string name="persona_subtitle_researcher">Investigador</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (exemplo de texto longo para testar a truncamento)</string>
    <string name="persona_view_description_xxlarge">Avatar XXLarge com três linhas de texto</string>
    <string name="persona_view_description_large">Avatar grande com duas linhas de texto</string>
    <string name="persona_view_description_small">Avatar pequeno com uma linha de texto</string>
    <string name="people_picker_hint">Nenhum com sugestão apresentada</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Persona Chip desativado</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Erro Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Persona Chip sem ícone de fechar</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Persona Chip Básico</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Clicou num Persona Chip selecionado.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Partilhar</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Seguir</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Convidar pessoas</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Atualizar página</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Abrir no browser</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Este é um Menu de Pop-up com várias linhas. As linhas máximas estão definidas como duas, o resto do texto truncará.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Todas as notícias</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Notícias guardadas</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Notícias de sites</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Notificar fora do horário de trabalho</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notificar quando estiver inativo no ambiente de trabalho</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Clicou no item:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menu simples</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menu simples2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu com um item selecionável e um divisor</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu com todos os itens, ícones e texto longo selecionáveis</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Mostrar</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Progresso Circular</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Pequeno</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Médio</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Grande</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Progresso Linear</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Indeterminado</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinado</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Chamada de Retorno do Microfone</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Correção automática...</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Microfone Premido</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Vista Direita Premida</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Pesquisa de Teclado Premida</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Mostrar Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Dispensar Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Ação</string>
    <string name="snackbar_action_long">Ação de Texto Longo</string>
    <string name="snackbar_single_line">Snackbar de linha única</string>
    <string name="snackbar_multiline">Este é um snackbar com várias linhas. As linhas máximas estão definidas como duas, o resto do texto truncará.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Este é um snackbar de anúncios. É utilizado para comunicar novas funcionalidades.</string>
    <string name="snackbar_primary">Esta é uma snackbar primária.</string>
    <string name="snackbar_light">Esta é uma snackbar clara.</string>
    <string name="snackbar_warning">Esta é uma snackbar de aviso.</string>
    <string name="snackbar_danger">Esta é uma snackbar perigosa.</string>
    <string name="snackbar_description_single_line">Duração curta</string>
    <string name="snackbar_description_single_line_custom_view">Duração longa com progresso circular como vista personalizada pequena</string>
    <string name="snackbar_description_single_line_action">Duração curta com ação</string>
    <string name="snackbar_description_single_line_action_custom_view">Duração curta com ação e vista personalizada média</string>
    <string name="snackbar_description_single_line_custom_text_color">Duração curta com cor de texto personalizada</string>
    <string name="snackbar_description_multiline">Duração longa</string>
    <string name="snackbar_description_multiline_custom_view">Duração longa com vista personalizada pequena</string>
    <string name="snackbar_description_multiline_action">Duração indefinida com atualizações de ações e texto</string>
    <string name="snackbar_description_multiline_action_custom_view">Duração curta com ação e vista personalizada média</string>
    <string name="snackbar_description_multiline_action_long">Duração curta com texto de ação longo</string>
    <string name="snackbar_description_announcement">Duração curta</string>
    <string name="snackbar_description_updated">Este texto foi atualizado.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Mostrar Snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Linha única</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Linhas múltiplas</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Estilo de anúncio</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Estilo principal</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Estilo claro</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Estilo de aviso</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Estilo de perigo</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Página Principal</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Correio</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Definições</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notificação</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mais</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Alinhamento do Texto</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertical</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Sem Texto</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Itens do Separador</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Título</string>
    <string name="cell_sample_description">Descrição</string>
    <string name="calculate_cells">Carregar/calcular 100 células</string>
    <string name="calculate_layouts">Carregar/calcular 100 esquemas</string>
    <string name="template_list">Lista de Modelos</string>
    <string name="regular_list">Lista Regular</string>
    <string name="cell_example_title">Título: Célula</string>
    <string name="cell_example_description">Descrição: Toque para alterar a orientação</string>
    <string name="vertical_layout">Esquema Vertical</string>
    <string name="horizontal_layout">Esquema Horizontal</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Separador Padrão de 2 Segmentos</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Separador Padrão 3 Segmentos</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Separador Padrão 4 Segmentos</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Separador Padrão com Pager</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Mudar de Separador</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Separador de Comprimidos </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Toque para Descrição</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Toque para Descrição do Calendário Personalizado</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Toque em Descrição de Cores Personalizadas</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Toque para Dispensar Descrição Interna</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Toque para Descrição de Vista Personalizada</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Descrição de Cores Personalizadas da Parte Superior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Descrição do Fim da Parte Superior com desvio de 10dp em X</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Descrição do Início da Parte Inferior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Descrição do Fim da Parte Inferior com desvio de 10dp em Y</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Dispensar Descrição interna</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Descrição dispensada</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">O Título é Claro 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">O Título 1 é Médio 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">O Título 2 é Normal 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">O Cabeçalho é Normal 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">O Subcabeçalho 1 é Normal 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">O Subcabeçalho 2 é Médio 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">O Corpo 1 é Normal 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">O Corpo 2 é Médio 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">A Legenda é Normal 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versão do SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Item %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Pasta</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Clicada</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB Expandido</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB Fechado</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Clique para atualizar a lista</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Abrir Gaveta</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Item de Menu</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Desvio X (em dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Desvio Y (em dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Texto do Conteúdo</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Repetir Texto de Conteúdo</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">A largura do menu será alterada em função do Texto do Conteúdo. A largura
        máxima está restrita a 75% do tamanho do ecrã. A margem de conteúdo dos lados e da parte inferior é regida por token. O mesmo texto de conteúdo será repetido para variar
        a altura.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Abrir Menu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Cartão Básico</string>
    <!-- UI Label for Card -->
    <string name="file_card">Cartão Ficheiro</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Cartão de Anúncio</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">IU Aleatória</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opções</string>
    <!-- UI Label for Title -->
    <string name="card_title">Título</string>
    <!-- UI Label for text -->
    <string name="card_text">Texto</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Sub-texto</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">A cópia secundária desta faixa pode ser moldada em duas linhas, se necessário.</string>
    <!-- UI Label Button -->
    <string name="card_button">Botão</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Mostrar Caixa de Diálogo</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Dispensar caixa de diálogo ao clicar no exterior</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Dispensar caixa de diálogo ao premir novamente</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Caixa de diálogo dispensada</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Cancelar</string>
    <!-- UI Label Ok -->
    <string name="ok">Ok</string>
    <!-- A sample description -->
    <string name="dialog_description">Uma caixa de diálogo é uma pequena janela que pede ao utilizador para tomar uma decisão ou introduzir informações adicionais.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Abrir Gaveta</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Expandir Gaveta</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Fechar Gaveta</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Selecionar Tipo de Gaveta</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Parte Superior</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">A gaveta inteira é apresentada na região visível.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Parte Inferior</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">A gaveta inteira é apresentada na região visível. O gesto de percorrer para cima desloca o conteúdo. A gaveta expansível é expandida através da alça de arrastar.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Deslizar para a Esquerda</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Deslizar a gaveta para a região visível do lado esquerdo.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Deslizar para a Direita</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Deslizar a gaveta para a região visível do lado direito.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Deslizar para a Parte Inferior</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Deslizar a gaveta para a região visível a partir da parte inferior do ecrã. O gesto de percorrer para cima na gaveta expansível traz a restante parte para a região visível e, em seguida, desloca.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim Visível</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Selecionar Conteúdo da Gaveta</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Conteúdo deslocável de ecrã inteiro</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Conteúdo em mais de metade do ecrã</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Conteúdo em menos de metade do ecrã</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Conteúdo de tamanho dinâmico</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Conteúdo da Gaveta Aninhada</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Expansível</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Ignorar Estado de Abertura</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Impedir Dispensa ao Clicar no Ecrã</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Mostrar Alça</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Título</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Texto de Descrição</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Toque para obter descrições de conteúdo personalizado</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Início Superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Extremidade Superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Início Inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Extremidade Inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centro </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Centro Personalizado</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Para obter atualizações sobre Notas de Versão, </string>
    <string name="click_here">clique aqui.</string>
    <string name="open_source_cross_platform">Sistema de Design de código aberto para todas as plataformas.</string>
    <string name="intuitive_and_powerful">Intuitivo &amp; Poderoso.</string>
    <string name="design_tokens">Tokens de Design</string>
    <string name="release_notes">Notas de Versão</string>
    <string name="github_repo">Repositório do GitHub</string>
    <string name="github_repo_link">Ligação do Repositório do GitHub</string>
    <string name="report_issue">Comunicar Problema</string>
    <string name="v1_components">Componentes V1</string>
    <string name="v2_components">Componentes V2</string>
    <string name="all_components">Tudo</string>
    <string name="fluent_logo">Logótipo Fluent</string>
    <string name="new_badge">Novos</string>
    <string name="modified_badge">Modificado</string>
    <string name="api_break_badge">Interrupção da API</string>
    <string name="app_bar_more">Mais</string>
    <string name="accent">Destaque</string>
    <string name="appearance">Aspeto</string>
    <string name="choose_brand_theme">Escolha o tema da sua marca:</string>
    <string name="fluent_brand_theme">Marca Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Escolher Aspeto</string>
    <string name="appearance_system_default">Predefinição do Sistema</string>
    <string name="appearance_light">Claro</string>
    <string name="appearance_dark">Escuro</string>
    <string name="demo_activity_github_link">Ligação do GitHub da Atividade de Demonstração</string>
    <string name="control_tokens_details">Detalhes dos Tokens de Controlo</string>
    <string name="parameters">Parâmetros</string>
    <string name="control_tokens">Tokens de Controlo</string>
    <string name="global_tokens">Tokens Globais</string>
    <string name="alias_tokens">Tokens Alias</string>
    <string name="sample_text">Texto</string>
    <string name="sample_icon">Ícone de Exemplo</string>
    <string name="color">Cor</string>
    <string name="neutral_color_tokens">Tokens de Cor Neutros</string>
    <string name="font_size_tokens">Tokens de Tamanho do Tipo de Letra</string>
    <string name="line_height_tokens">Tokens de Altura da Linha</string>
    <string name="font_weight_tokens">Tokens de Peso do Tipo de Letra</string>
    <string name="icon_size_tokens">Tokens de Tamanho do Ícone</string>
    <string name="size_tokens">Tokens de Tamanho</string>
    <string name="shadow_tokens">Tokens de Sombra</string>
    <string name="corner_radius_tokens">RadiusTokens de Canto</string>
    <string name="stroke_width_tokens">Tokens de Largura do Traço</string>
    <string name="brand_color_tokens">Tokens de Cores da Marca</string>
    <string name="neutral_background_color_tokens">Tokens de Cor de Fundo Neutros</string>
    <string name="neutral_foreground_color_tokens">Tokens de Cor de Primeiro Plano Neutros</string>
    <string name="neutral_stroke_color_tokens">Tokens de Cor do Traço Neutro</string>
    <string name="brand_background_color_tokens">Tokens de Cor de Fundo da Marca</string>
    <string name="brand_foreground_color_tokens">Tokens de Cor de Primeiro Plano da Marca</string>
    <string name="brand_stroke_color_tokens">Tokens de Cor do Traço Da Marca</string>
    <string name="error_and_status_color_tokens">Tokens de Cores de Erro e Estado</string>
    <string name="presence_tokens">Tokens de Cor de Presença</string>
    <string name="typography_tokens">Tokens de Tipografia</string>
    <string name="unspecified">Não Especificado</string>

</resources>