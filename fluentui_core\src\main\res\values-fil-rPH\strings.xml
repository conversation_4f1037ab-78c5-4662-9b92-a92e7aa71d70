<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Primary</string>
    <string name="fluentui_secondary">Sekundaryo</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss"><PERSON><PERSON></string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Napili</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Hindi Napili</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Icon</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Icon</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Hindi pinagana</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Button ng Aksyon</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Pinagana</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Isara ang Sheet</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Isara</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Kanselahin</string>
    <!--name of the icon -->
    <string name="fluentui_search">Search</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Mikropono</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">I-clear ang Teksto</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Bumalik</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Na-activate</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Na-de-Activate</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutral</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Brand</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Kontrast</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Accent</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Babala</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Panganib</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Nagkaroon ng error</string>
    <string name="fluentui_error">Error</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Hint</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Chevron</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Outline</string>

    <string name="fluentui_action_button_icon">Icon ng action button</string>
    <string name="fluentui_center">Igitna ang teksto.</string>
    <string name="fluentui_accessory_button">Mga Button ng Accessory</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Radio Button</string>
    <string name="fluentui_label">Label</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Pinalawak</string>
    <string name="fluentui_collapsed">Pinaliit</string>

    <!--types of control -->
    <string name="fluentui_large">Malaki</string>
    <string name="fluentui_medium">Katamtaman</string>
    <string name="fluentui_small">Maliit</string>
    <string name="fluentui_password_mode">Password Mode</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Subtitle</string>
    <string name="fluentui_assistive_text">Assistive na Teksto</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Pamagat</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Short</string>"
    <string name="fluentui_long">Mahaba</string>"
    <string name="fluentui_indefinite">Indefinite</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Napindot ang Button</string>
    <string name="fluentui_dismissed">Inalis</string>
    <string name="fluentui_timeout">Nag-time Out</string>
    <string name="fluentui_left_swiped">Na-swipe Pakaliwa</string>
    <string name="fluentui_right_swiped">Na-swipe Pakanan</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Teksto</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Numero</string>
    <string name="fluentui_keyboard_phone">Telepono</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Email</string>
    <string name="fluentui_keyboard_password">Pasword</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimal</string>
</resources>