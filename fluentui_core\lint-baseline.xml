<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 7.4.2" type="baseline" client="gradle" dependencies="false" name="AGP (7.4.2)" variant="all" version="7.4.2">

    <issue
        id="ObsoleteLintCustomCheck"
        message="Lin<PERSON> found an issue registry (`androidx.compose.runtime.lint.RuntimeIssueRegistry`) which requires a newer API level. That means that the custom lint checks are intended for a newer lint version; please upgrade.">
        <location
            file="C:/Users/<USER>/.gradle/caches/transforms-3/5a95d437489af7b64c794ecfcc0144a2/transformed/jetified-runtime-release/jars/lint.jar"/>
    </issue>

    <issue
        id="LocaleFolder"
        message="The locale folder &quot;`he`&quot; should be called &quot;`iw`&quot; instead; see the `java.util.Locale` documentation">
        <location
            file="src/main/res/values-he-rIL"/>
    </issue>

    <issue
        id="LocaleFolder"
        message="The locale folder &quot;`id`&quot; should be called &quot;`in`&quot; instead; see the `java.util.Locale` documentation">
        <location
            file="src/main/res/values-id-rID"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0"
        errorLine1="    implementation &quot;androidx.appcompat:appcompat:$appCompatVersion&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="66"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.exifinterface:exifinterface than 1.3.6 is available: 1.3.7"
        errorLine1="    implementation &quot;androidx.exifinterface:exifinterface:$exifInterfaceVersion&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="67"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.recyclerview:recyclerview than 1.3.0 is available: 1.3.2"
        errorLine1="    implementation &quot;androidx.recyclerview:recyclerview:$recyclerViewVersion&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="68"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.9.0 is available: 1.12.0"
        errorLine1="    implementation &quot;com.google.android.material:material:$materialVersion&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="70"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of org.jetbrains.kotlin:kotlin-stdlib-jdk7 than 1.8.21 is available: 1.9.20"
        errorLine1="    implementation &quot;org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="71"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of junit:junit than 4.12 is available: 4.13.2"
        errorLine1="    testImplementation &quot;junit:junit:$junitVersion&quot;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="77"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        errorLine1="    androidTestImplementation &quot;androidx.test.ext:junit:$extJunitVersion&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="80"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        errorLine1="    androidTestImplementation &quot;androidx.test.espresso:espresso-core:$espressoVersion&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="81"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.6.1 is available: 2.8.6"
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="92"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-core-ktx than 2.6.1 is available: 2.8.6"
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-livedata-core-ktx:$lifecycleVersion&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="93"
            column="20"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        errorLine1="    val resourceId = resources.getIdentifier(dimensionId, DIMEN_STRING, ANDROID_STRING)"
        errorLine2="                               ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/microsoft/fluentui/util/DisplayUtils.kt"
            line="54"
            column="32"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (809 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="        android:pathData=&quot;M12,2C17.5228,2 22,6.4772 22,12C22,17.5228 17.5228,22 12,22C6.4772,22 2,17.5228 2,12C2,6.4772 6.4772,2 12,2ZM15.4462,8.3971C15.1526,8.1792 14.7359,8.2034 14.4697,8.4697L14.4697,8.4697L12,10.939L9.5303,8.4697L9.4462,8.3971C9.1526,8.1792 8.7359,8.2034 8.4697,8.4697L8.4697,8.4697L8.3971,8.5538C8.1792,8.8474 8.2034,9.2641 8.4697,9.5303L8.4697,9.5303L10.939,12L8.4697,14.4697L8.3971,14.5538C8.1792,14.8474 8.2034,15.2641 8.4697,15.5303L8.4697,15.5303L8.5538,15.6029C8.8474,15.8208 9.2641,15.7966 9.5303,15.5303L9.5303,15.5303L12,13.061L14.4697,15.5303L14.5538,15.6029C14.8474,15.8208 15.2641,15.7966 15.5303,15.5303L15.5303,15.5303L15.6029,15.4462C15.8208,15.1526 15.7966,14.7359 15.5303,14.4697L15.5303,14.4697L13.061,12L15.5303,9.5303L15.6029,9.4462C15.8208,9.1526 15.7966,8.7359 15.5303,8.4697L15.5303,8.4697Z&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ms_ic_dismiss_circle_24_filled.xml"
            line="15"
            column="27"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_communication_shade_10` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_communication_shade_10&quot;>#106EBE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_communication_tint_30` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_communication_tint_30&quot;>#DEECF9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_950` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_950&quot;>#141414&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_700&quot;>#303030&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="34"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_600&quot;>#404040&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_300&quot;>#ACACAC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="38"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_200&quot;>#C8C8C8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="39"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_56` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_56&quot;>#777777&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="41"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_50&quot;>#F1F1F1&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="42"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_gray_25` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_gray_25&quot;>#F8F8F8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="43"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_transparent` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_transparent&quot;>#00000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="47"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_red` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_red&quot;>#D92C2C&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="50"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.fluentui_yellow` appears to be unused"
        errorLine1="    &lt;color name=&quot;fluentui_yellow&quot;>#FFD335&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="51"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.fluentui_corner_radius_2` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;fluentui_corner_radius_2&quot;>2dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.fluentui_corner_radius_4` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;fluentui_corner_radius_4&quot;>4dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="12"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.fluentui_corner_radius_8` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;fluentui_corner_radius_8&quot;>8dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.fluentui_min_touch_size` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;fluentui_min_touch_size&quot;>48dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.fluentui_content_inset` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;fluentui_content_inset&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="15"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ic_arrow_left_24_filled` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ic_arrow_left_24_filled.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ic_checkmark_24_filled` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ic_checkmark_24_filled.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ic_dismiss_20_filled` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ic_dismiss_20_filled.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ic_dismiss_24_filled` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ic_dismiss_24_filled.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ic_dismiss_circle_24_filled` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ic_dismiss_circle_24_filled.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ic_next_icon_12_filled` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ic_next_icon_12_filled.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ic_search_24_filled` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ic_search_24_filled.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ripple_transparent_background` appears to be unused"
        errorLine1="&lt;ripple xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ripple_transparent_background.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_ripple_transparent_background_borderless` appears to be unused"
        errorLine1="&lt;ripple xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_ripple_transparent_background_borderless.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ms_row_divider` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ms_row_divider.xml"
            line="7"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_primary` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_primary&quot;>Primary&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_secondary` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_secondary&quot;>Secondary&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="5"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_dismiss` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_dismiss&quot;>Dismiss&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_selected` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_selected&quot;>Selected&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_not_selected` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_not_selected&quot;>Not Selected&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_icon` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_icon&quot;>Icon&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_accent_icon` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_accent_icon&quot;>Icon&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_disabled` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_disabled&quot;>Disabled&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_action_button` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_action_button&quot;>Action Button&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_dismiss_button` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_dismiss_button&quot;>Dismiss&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_enabled` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_enabled&quot;>Enabled&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_close_sheet` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_close_sheet&quot;>Close Sheet&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_close` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_close&quot;>Close&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_cancel` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_cancel&quot;>Cancel&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_search` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_search&quot;>Search&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_microphone` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_microphone&quot;>Microphone&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_clear_text` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_clear_text&quot;>Clear Text&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_back` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_back&quot;>Back&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_activated` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_activated&quot;>Activated&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_deactivated` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_deactivated&quot;>De-Activated&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_neutral` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_neutral&quot;>Neutral&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_brand` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_brand&quot;>Brand&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_contrast` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_contrast&quot;>Contrast&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_accent` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_accent&quot;>Accent&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_warning` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_warning&quot;>Warning&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="51"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_danger` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_danger&quot;>Danger&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_error_string` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_error_string&quot;>There has been an error&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_error` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_error&quot;>Error&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_hint` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_hint&quot;>Hint&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_chevron` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_chevron&quot;>Chevron&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_outline` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_outline&quot;>Outline&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="62"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_action_button_icon` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_action_button_icon&quot;>Action button icon&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="64"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_center` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_center&quot;>Center text&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="65"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_accessory_button` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_accessory_button&quot;>Accessory Buttons&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_radio_button` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_radio_button&quot;>Radio Button&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_label` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_label&quot;>Label&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_expanded` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_expanded&quot;>Expanded&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="73"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_collapsed` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_collapsed&quot;>Collapsed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="74"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_large` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_large&quot;>Large&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="77"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_medium` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_medium&quot;>Medium&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_small` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_small&quot;>Small&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="79"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_password_mode` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_password_mode&quot;>Password Mode&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="80"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_subtitle` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_subtitle&quot;>Subtitle&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_assistive_text` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_assistive_text&quot;>Assistive Text&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="84"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_title&quot;>Title&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="87"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_short` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_short&quot;>Short&lt;/string>&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="90"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_long` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_long&quot;>Long&lt;/string>&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="91"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_indefinite` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_indefinite&quot;>Indefinite&lt;/string>&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="92"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_button_pressed` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_button_pressed&quot;>Button Pressed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="95"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_dismissed` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_dismissed&quot;>Dismissed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_timeout` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_timeout&quot;>Timed Out&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="97"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_left_swiped` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_left_swiped&quot;>Left Swiped&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="98"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_right_swiped` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_right_swiped&quot;>Right Swiped&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="99"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_text` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_text&quot;>Text&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="102"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_ascii` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_ascii&quot;>ASCII&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="103"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_number` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_number&quot;>Number&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="104"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_phone` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_phone&quot;>Phone&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="105"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_uri` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_uri&quot;>URI&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="106"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_email` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_email&quot;>Email&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="107"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_password` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_password&quot;>Password&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_number_password` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_number_password&quot;>NumberPassword&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="109"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fluentui_keyboard_decimal` appears to be unused"
        errorLine1="    &lt;string name=&quot;fluentui_keyboard_decimal&quot;>Decimal&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="110"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Widget_FluentUI` appears to be unused"
        errorLine1="    &lt;style name=&quot;Widget.FluentUI&quot; parent=&quot;@android:style/Widget&quot;/>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI&quot; parent=&quot;TextAppearance.AppCompat&quot; />"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_Headline` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.Headline&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_Title1` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.Title1&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="16"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_Title2` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.Title2&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_Heading` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.Heading&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_SubHeading1` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.SubHeading1&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_SubHeading2` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.SubHeading2&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="36"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_Body1` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.Body1&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="41"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_Body2` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.Body2&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="46"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_FluentUI_Caption` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.FluentUI.Caption&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_font.xml"
            line="51"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.ThemeOverlay_FluentUI_NeutralAppBar` appears to be unused"
        errorLine1="    &lt;style name=&quot;ThemeOverlay.FluentUI.NeutralAppBar&quot; parent=&quot;&quot;/>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="56"
            column="12"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `ModalWindow` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="private class ModalWindow("
        errorLine2="              ~~~~~~~~~~~">
        <location
            file="src/main/java/com/microsoft/fluentui/compose/ModalPopup.kt"
            line="123"
            column="15"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view `MSRecyclerView` overrides `onTouchEvent` but not `performClick`"
        errorLine1="    override fun onTouchEvent(motionEvent: MotionEvent): Boolean {"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/microsoft/fluentui/view/MSRecyclerView.kt"
            line="151"
            column="18"/>
    </issue>

</issues>
