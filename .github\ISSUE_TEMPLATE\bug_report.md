---
name: Bug report
about: Found a bug in Fluent UI Android? Please let us know.
---

<!--
Thanks for contacting us! We're here to help.

Before you report an issue, check if it's been reported before:

  * Search: https://github.com/microsoft/fluentui-android/search?type=Issues

Note that if you do not provide enough information to reproduce the issue, we may not be able to take action on your report.
-->

### Environment Information

- **Package version(s)**: (fill this out, include which package manager you're using)
- **Android SDK version**: (fill this out if relevant)

### Please provide a reproduction of the bug:

<!--
Providing an isolated reproduction of the bug makes it much easier for us to help you. A reproduction in the demo application is preferred.
-->

#### Actual behavior:

<!-- fill this out -->

#### Expected behavior:

<!-- fill this out -->

### Priorities and help requested:

Are you willing to submit a PR to fix? (Yes, No)

Requested priority: (Blocking, High, Normal, Low)

Products/applications affected: (if applicable)