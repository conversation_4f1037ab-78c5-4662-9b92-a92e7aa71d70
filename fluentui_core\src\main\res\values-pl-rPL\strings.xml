<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Główna</string>
    <string name="fluentui_secondary">Pomocniczy</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss"><PERSON><PERSON><PERSON><PERSON></string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">W<PERSON><PERSON><PERSON></string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Ni<PERSON> wy<PERSON><PERSON></string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Ikona</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Ikona</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">W<PERSON><PERSON><PERSON><PERSON><PERSON></string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Przycisk akcji</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">W<PERSON><PERSON><PERSON><PERSON></string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Zamknij arkusz</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Zamknij</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Anuluj</string>
    <!--name of the icon -->
    <string name="fluentui_search">Wyszukaj</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Mikrofon</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Wyczyść tekst</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Wstecz</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Aktywowano</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Dezaktywowano</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutralny</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marka</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Kontrast</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Akcent</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Ostrzeżenie</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Niebezpieczeństwo</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Wystąpił błąd</string>
    <string name="fluentui_error">Błąd</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Wskazówka</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Cudzysłów ostrokątny</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Konspekt</string>

    <string name="fluentui_action_button_icon">Ikona przycisku akcji</string>
    <string name="fluentui_center">Wyśrodkuj tekst</string>
    <string name="fluentui_accessory_button">Przyciski akcesoriów</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Przycisk radiowy</string>
    <string name="fluentui_label">Etykieta</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Rozwinięta</string>
    <string name="fluentui_collapsed">Zwinięta</string>

    <!--types of control -->
    <string name="fluentui_large">Duża</string>
    <string name="fluentui_medium">Średnia</string>
    <string name="fluentui_small">Mała</string>
    <string name="fluentui_password_mode">Tryb hasła</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Napisy</string>
    <string name="fluentui_assistive_text">Tekst pomocniczy</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Tytuł</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Krótko</string>"
    <string name="fluentui_long">Długo</string>"
    <string name="fluentui_indefinite">Nieokreślony</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Naciśnięto przycisk</string>
    <string name="fluentui_dismissed">Odrzucone</string>
    <string name="fluentui_timeout">Przekroczony limit czasu</string>
    <string name="fluentui_left_swiped">Szybko przesunięte w lewo</string>
    <string name="fluentui_right_swiped">Szybko przesunięte w prawo</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Tekstowa</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Liczba</string>
    <string name="fluentui_keyboard_phone">Telefon</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Adres e-mail</string>
    <string name="fluentui_keyboard_password">Hasło</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Dziesiętna</string>
</resources>