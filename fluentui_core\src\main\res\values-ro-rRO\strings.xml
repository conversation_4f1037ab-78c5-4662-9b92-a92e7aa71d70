<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Principal</string>
    <string name="fluentui_secondary">Secundară</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">I<PERSON><PERSON><PERSON>i</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Selectată</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Neselectată</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Pictogramă</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Pictogramă</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Dezac<PERSON>vată</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">But<PERSON>ul Acțiune</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Activată</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Închideți foaia</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Închideți</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Anulați</string>
    <!--name of the icon -->
    <string name="fluentui_search">Căutare</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Microfon</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Goliți textul</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Înapoi</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Activată</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Dezactivată</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutru</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marcă</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Contrast</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Accent</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Avertisment</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Pericol</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">A apărut o eroare.</string>
    <string name="fluentui_error">Eroare</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Sugestie</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Chevron</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Schiță</string>

    <string name="fluentui_action_button_icon">Pictograma butonului Acțiune</string>
    <string name="fluentui_center">Centrați textul</string>
    <string name="fluentui_accessory_button">Butoane accesoriu</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Butonul Radio</string>
    <string name="fluentui_label">Etichetă</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Extins</string>
    <string name="fluentui_collapsed">Restrâns</string>

    <!--types of control -->
    <string name="fluentui_large">Mare</string>
    <string name="fluentui_medium">Mediu</string>
    <string name="fluentui_small">Mic</string>
    <string name="fluentui_password_mode">Mod parolă</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Subtitlu</string>
    <string name="fluentui_assistive_text">Text de asistență</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Titlu</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Scurte</string>"
    <string name="fluentui_long">Lungi</string>"
    <string name="fluentui_indefinite">Nedeterminate</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">S-a apăsat butonul</string>
    <string name="fluentui_dismissed">S-a ignorat</string>
    <string name="fluentui_timeout">Expirate</string>
    <string name="fluentui_left_swiped">S-a tras cu degetul spre stânga</string>
    <string name="fluentui_right_swiped">S-a tras cu degetul spre dreapta</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Text</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Număr</string>
    <string name="fluentui_keyboard_phone">Număr de telefon</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">E-mail</string>
    <string name="fluentui_keyboard_password">Parolă</string>
    <string name="fluentui_keyboard_number_password">Parolă numerică</string>
    <string name="fluentui_keyboard_decimal">Zecimal</string>
</resources>