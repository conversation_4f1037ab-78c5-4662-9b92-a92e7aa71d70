<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI डेमो</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s चयनित</string>
    <string name="app_modifiable_parameters">संशोधित करने योग्य पैरामीटर्स</string>
    <string name="app_right_accessory_view">दायाँ एक्सेसरी दृश्य</string>

    <string name="app_style">शैली</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">आइकन दबाया गया</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">डेमो शुरू करें</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">कैरूज़ेल</string>
    <string name="actionbar_icon_radio_label">आइकन</string>
    <string name="actionbar_basic_radio_label">मूलभूत</string>
    <string name="actionbar_position_bottom_radio_label">नीचे</string>
    <string name="actionbar_position_top_radio_label">शीर्ष</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">क्रिया पट्टी प्रकार</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar की स्थिति</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar शैली</string>
    <string name="app_bar_subtitle">उपशीर्षक</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">निचली बॉर्डर</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">नेविगेशन आइकन क्लिक किया गया.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">ध्वज</string>
    <string name="app_bar_layout_menu_settings">सेटिंग</string>
    <string name="app_bar_layout_menu_search">खोजें</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">स्क्रॉल व्यवहार: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">स्क्रॉल व्यवहार टॉगल करें</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">नेविगेशन आइकन टॉगल करें</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">अवतार दिखाएँ</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">वापस आइकन दिखाएँ</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">आइकन छुपाएँ</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">आइकन दिखाएँ</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">खोज पट्टी लेआउट शैली टॉगल करें</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">एक्सेसरी दृश्य के रूप में दिखाएँ</string>
    <string name="app_bar_layout_searchbar_action_view_button">क्रिया दृश्य के रूप में दिखाएँ</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">थीम्स के बीच टॉगल करें (गतिविधि फिर से बनाएँ)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">टॉगल थीम</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">आइटम</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">अतिरिक्त स्क्रॉल करने योग्य सामग्री</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">वृत्त शैली</string>
    <string name="avatar_style_square">वर्ग शैली</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">बड़ा</string>
    <string name="avatar_size_medium">मध्यम</string>
    <string name="avatar_size_small">छोटा</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">बहुत ज़्यादा बड़ा</string>
    <string name="avatar_size_xlarge_accessibility">बहुत बड़ा</string>
    <string name="avatar_size_xsmall_accessibility">बहुत छोटा</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">अधिकतम प्रदर्शित अवतार</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">ओवरफ़्लो अवतार गणना</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">बॉर्डर प्रकार</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">OverflowAvatarCount सेट वाला अवतार समूह अधिकतम प्रदर्शित अवतार का पालन नहीं करेगा.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">चेहरा स्टैक</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">चेहरे पर ढेर</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">ओवरफ़्लो क्लिक किया गया</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">%d इंडेक्स पर AvatarView क्लिक किया गया</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">सूचना बैज</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">डॉट</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">सूची</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">वर्ण</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">फ़ोटोज़</string>
    <string name="bottom_navigation_menu_item_news">समाचार</string>
    <string name="bottom_navigation_menu_item_alerts">सूचनाएँ</string>
    <string name="bottom_navigation_menu_item_calendar">कैलेंडर</string>
    <string name="bottom_navigation_menu_item_team">टीम</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">लेबल टॉगल करें</string>
    <string name="bottom_navigation_three_menu_items_button">तीन मेनू आइटम दिखाएँ</string>
    <string name="bottom_navigation_four_menu_items_button">चार मेनू आइटम दिखाएँ</string>
    <string name="bottom_navigation_five_menu_items_button">पाँच मेनू आइटम दिखाएँ</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">लेबल %s हैं</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">ख़ारिज करने के लिए ‘नीचे की ओर स्वाइप करें’ को सक्षम करें</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">एकल पंक्ति आइटम्स के साथ दिखाएँ</string>
    <string name="bottom_sheet_with_double_line_items">दोहरी रेखा वाले आइटम दिखाएँ</string>
    <string name="bottom_sheet_with_single_line_header">एकल पंक्ति शीर्ष लेख के साथ दिखाएँ</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">दोहरी रेखा वाले शीर्ष लेख और विभाजकों के साथ दिखाएँ</string>
    <string name="bottom_sheet_dialog_button">दिखाएँ</string>
    <string name="drawer_content_desc_collapse_state">विस्तृत करें</string>
    <string name="drawer_content_desc_expand_state">छोटा करें</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s क्लिक करें</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">%s लंबा क्लिक करें</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">ख़ारिज करें क्लिक करें</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">आइटम सम्मिलित करें</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">आइटम अपडेट करें</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">ख़ारिज करें</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">जोड़ें</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">उल्लेख करें</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">बोल्ड करें</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">इटैलिक</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">रेखांकित करें</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">स्ट्राइकथ्रू</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">पहले जैसा करें</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">फिर से करें</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">बुलेट</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">सूची</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">लिंक</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">आइटम अपडेट हो रहा है</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">स्पेसिंग</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">स्थिति ख़ारिज करें</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">प्रारंभ करें</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">समूह स्थान</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">आइटम स्थान</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">ध्वज</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">ध्वज आइटम क्लिक किया गया</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">उत्तर दें</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">आइटम रीप्ले क्लिक किया गया</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">फ़ॉरवर्ड करें</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">आइटम फ़ॉरवर्ड करें क्लिक किया गया</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">हटाएँ</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">आइटम हटाएँ क्लिक किया गया</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">अवतार</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">कैमरा</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">एक फ़ोटो लें</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">कैमरा आइटम क्लिक किया गया</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">गैलरी</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">अपने फ़ोटो देखें</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">गैलरी आइटम क्लिक किया गया</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">वीडियो</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">अपने वीडियो चलाएँ</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">वीडियो आइटम क्लिक किया गया</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">प्रबंधित करें</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">अपनी मीडिया लाइब्रेरी प्रबंधित करें</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">आइटम प्रबंधित करें क्लिक किया गया</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">ईमेल क्रियाएँ</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">दस्तावेज़</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">पिछली बार दोपहर 2:14 बजे अपडेट किया गया</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">साझा करें</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">साझा आइटम क्लिक किया गया</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">ले जाएँ</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">आइटम ले जाएँ क्लिक किया गया</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">हटाएँ</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">आइटम हटाएँ क्लिक किया गया</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">जानकारी</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">जानकारी आइटम क्लिक किया गया</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">घड़ी</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">घड़ी आइटम क्लिक किया गया</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">अलार्म</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">अलार्म आइटम क्लिक किया गया</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">समय क्षेत्र</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">समय क्षेत्र आइटम क्लिक किया गया</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">बटन के विभिन्न दृश्य</string>
    <string name="button">बटन</string>
    <string name="buttonbar">बटन पट्टी</string>
    <string name="button_disabled">‘आउटलाइन अक्षम करने वाले’ बटन का उदाहरण</string>
    <string name="button_borderless">‘बॉर्डर रहित करने वाले’ बटन उदाहरण</string>
    <string name="button_borderless_disabled">‘बॉर्डर रहित को अक्षम करने वाले’ बटन का उदाहरण</string>
    <string name="button_large">‘बड़ा करने वाले’ बटन का उदाहरण</string>
    <string name="button_large_disabled">‘बड़ा करने को अक्षम करने वाला’ बटन</string>
    <string name="button_outlined">‘आउटलाइन करने वाले’ बटन का उदाहरण</string>
    <string name="button_outlined_disabled">‘आउटलाइन को अक्षम करने वाले’ बटन</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">कोई दिनांक चुनें</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">एकल दिनांक</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">कोई दिनांक नहीं चुनी गई</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">दिनांक पिकर दिखाएँ</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">चयनित दिनांक टैब के साथ दिनांक समय पिकर दिखाएँ</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">चयनित समय टैब के साथ दिनांक समय पिकर दिखाएँ</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">दिनांक समय पिकर दिखाएँ</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">दिनांक श्रेणी</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">शुरू करें:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">समाप्त:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">कोई प्रारंभ नहीं चुना गया</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">कोई अंत नहीं चुना गया</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">प्रारंभ दिनांक का चयन करें</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">समाप्ति दिनांक का चयन करें</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">दिनांक समय सीमा</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">दिनांक समय सीमा चुनें</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">संवाद दिखाएँ</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">ड्रॉवर दिखाएँ</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">ड्रॉवर संवाद दिखाएँ</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">कोई फ़ेड बॉटम संवाद नहीं</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">शीर्ष ड्रॉवर दिखाएँ</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">शीर्ष फ़ेड संवाद नहीं</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> एंकर दृश्य शीर्ष संवाद दिखाएँ</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> कोई शीर्षक शीर्ष संवाद नहीं दिखाएँ</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> शीर्षक शीर्ष संवाद के नीचे दिखाएँ</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">दायाँ ड्रॉवर दिखाएँ</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">बायाँ ड्रॉवर दिखाएँ</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">शीर्षक, प्राथमिक टेक्स्ट</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">उपशीर्षक, द्वितीयक पाठ</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">कस्टम उपशीर्षक पाठ</string>
    <!-- Footer -->
    <string name="list_item_footer">पाद लेख, तृतीयक पाठ</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">धूसर उप शीर्ष लेख पाठ के साथ एकल-पंक्ति सूची</string>
    <string name="list_item_sub_header_two_line">दो-पंक्ति सूची</string>
    <string name="list_item_sub_header_two_line_dense">घनी रिक्ति वाली दो-पंक्ति सूची</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">कस्टम द्वितीयक उपशीर्षक दृश्य के साथ दो पंक्ति सूची</string>
    <string name="list_item_sub_header_three_line">काले उप शीर्ष लेख पाठ के साथ तीन-पंक्ति वाली सूची</string>
    <string name="list_item_sub_header_no_custom_views">बिना कस्टम दृश्य वाले आइटम सूचीबद्ध करें</string>
    <string name="list_item_sub_header_large_header">बड़े कस्टम दृश्य वाले आइटम सूचीबद्ध करें</string>
    <string name="list_item_sub_header_wrapped_text">लपेटे गए पाठ के साथ आइटम सूचीबद्ध करें</string>
    <string name="list_item_sub_header_truncated_text">कटे हुुए टेक्स्ट वाले आइटम सूचीबद्ध करें</string>
    <string name="list_item_sub_header_custom_accessory_text">कार्रवाई</string>
    <string name="list_item_truncation_middle">मध्य काट-छाँट.</string>
    <string name="list_item_truncation_end">काट-छाँट समाप्त.</string>
    <string name="list_item_truncation_start">काट-छाँट प्रारंभ करें.</string>
    <string name="list_item_custom_text_view">मान</string>
    <string name="list_item_click">आपने सूची आइटम पर क्लिक किया.</string>
    <string name="list_item_click_custom_accessory_view">आपने कस्टम एक्सेसरी दृश्य पर क्लिक किया.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">आपने उप शीर्ष लेख कस्टम सहायक उपकरण दृश्य पर क्लिक किया.</string>
    <string name="list_item_more_options">अधिक विकल्प</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">चुनें</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">कोई नहीं</string>
    <string name="people_picker_delete_example">हटाएँ</string>
    <string name="people_picker_custom_persona_description">यह उदाहरण कस्टम IPersona ऑब्जेक्ट बनाने का तरीका दिखाता है.</string>
    <string name="people_picker_dialog_title_removed">आपने एक परसोना को निकाल दिया:</string>
    <string name="people_picker_dialog_title_added">आपने एक परसोना जोड़ा:</string>
    <string name="people_picker_drag_started">खींचना प्रारंभ किया गया</string>
    <string name="people_picker_drag_ended">खींचना समाप्त</string>
    <string name="people_picker_picked_personas_listener">परसोना श्रोता</string>
    <string name="people_picker_suggestions_listener">सुझाव श्रोता</string>
    <string name="people_picker_persona_chip_click">आपने %s पर क्लिक किया</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s प्राप्तकर्ता</item>
        <item quantity="other">%1$s प्राप्त करने वाले</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">निर्बाध BottomSheet विस्तृत करें</string>
    <string name="collapse_persistent_sheet_button"> निर्बाध BottomSheet छुपाएँ</string>
    <string name="show_persistent_sheet_button"> निर्बाध निचला पत्रक दिखाएँ</string>
    <string name="new_view">यह नया दृश्य है</string>
    <string name="toggle_sheet_content">निचला पत्रक सामग्री टॉगल करें</string>
    <string name="switch_to_custom_content">कस्टम सामग्री पर स्विच करें</string>
    <string name="one_line_content">एक पंक्ति निचली सामग्री</string>
    <string name="toggle_disable_all_items">सभी आइटम्स अक्षम करें टॉगल करें</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">दृश्य जोड़ें/निकालें</string>
    <string name="persistent_sheet_item_change_collapsed_height"> संक्षिप्त ऊँचाई बदलें</string>
    <string name="persistent_sheet_item_create_new_folder_title">नया फ़ोल्डर</string>
    <string name="persistent_sheet_item_create_new_folder_toast">नया फ़ोल्डर आइटम क्लिक किया गया</string>
    <string name="persistent_sheet_item_edit_title">संपादित करें</string>
    <string name="persistent_sheet_item_edit_toast">आइटम संपादित करें क्लिक किया गया</string>
    <string name="persistent_sheet_item_save_title">सहेजें</string>
    <string name="persistent_sheet_item_save_toast">आइटम सहेजें क्लिक किया गया</string>
    <string name="persistent_sheet_item_zoom_in_title">ज़ूम इन करें</string>
    <string name="persistent_sheet_item_zoom_in_toast"> ज़ूम इन आइटम क्लिक किया गया</string>
    <string name="persistent_sheet_item_zoom_out_title">ज़ूम आउट करें</string>
    <string name="persistent_sheet_item_zoom_out_toast">ज़ूम आउट आइटम क्लिक किया गया</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">उपलब्ध</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">एलन मंगर</string>
    <string name="persona_name_amanda_brady">अमांडा ब्रैडी</string>
    <string name="persona_name_ashley_mccarthy">ऐश्ले मैकार्थी</string>
    <string name="persona_name_carlos_slattery">कार्लोस स्लेटरी</string>
    <string name="persona_name_carole_poland">कैरोल पोलैंड</string>
    <string name="persona_name_cecil_folk">सेसिल फ़ोक</string>
    <string name="persona_name_celeste_burton">सेलेस्ट बर्टन</string>
    <string name="persona_name_charlotte_waltson">शार्लोट वाल्टसन</string>
    <string name="persona_name_colin_ballinger">कॉलिन बॉलिंगर</string>
    <string name="persona_name_daisy_phillips">डेज़ी फ़िलिप्स</string>
    <string name="persona_name_elliot_woodward">इलियट वुडवर्ड</string>
    <string name="persona_name_elvia_atkins">एल्विया एटकिन</string>
    <string name="persona_name_erik_nason">एरिक नेसन</string>
    <string name="persona_name_henry_brill">हेनरी ब्रिल</string>
    <string name="persona_name_isaac_fielder">आइज़ैक फ़ील्डर</string>
    <string name="persona_name_johnie_mcconnell">जॉनी मैककॉनेल</string>
    <string name="persona_name_kat_larsson">कैट लार्सन</string>
    <string name="persona_name_katri_ahokas">कट्री अहोकास</string>
    <string name="persona_name_kevin_sturgis">केविन स्टर्गिस</string>
    <string name="persona_name_kristen_patterson">क्रिस्टन पैटरसन</string>
    <string name="persona_name_lydia_bauer">लिडिया बेउर</string>
    <string name="persona_name_mauricio_august">मॉरिशियो अगस्त</string>
    <string name="persona_name_miguel_garcia">मिगुएल गार्सिया</string>
    <string name="persona_name_mona_kane">मोना केन</string>
    <string name="persona_name_robin_counts">रॉबिन काउंट्स</string>
    <string name="persona_name_robert_tolbert">रॉबर्ट टॉलबर्ट</string>
    <string name="persona_name_tim_deboer">टिम डेबोर</string>
    <string name="persona_name_wanda_howard">वांडा हावर्ड</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">डिज़ाइनर</string>
    <string name="persona_subtitle_engineer">इंजीनियर</string>
    <string name="persona_subtitle_manager">प्रबंधक</string>
    <string name="persona_subtitle_researcher">शोधकर्ता</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (काट-छाँट का परीक्षण करने के लिए लंबा पाठ उदाहरण)</string>
    <string name="persona_view_description_xxlarge">पाठ की तीन पंक्तियों वाला XXLarge अवतार</string>
    <string name="persona_view_description_large">पाठ की दो पंक्तियों वाला बड़ा अवतार</string>
    <string name="persona_view_description_small">पाठ की एक पंक्ति वाला छोटा अवतार</string>
    <string name="people_picker_hint">संकेत के साथ कोई नहीं दिखाया गया</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">अक्षम किया गया परसोना चिप</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">त्रुटि परसोना चिप</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">बिना बंद आइकन के साथ परसोना चिप</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">मूल परसोना चिप</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">आपने किसी चयनित परसोना चिप पर क्लिक किया है.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">साझा करें</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">फ़ॉलो करें</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">लोगों को आमंत्रित करें</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">पेज रीफ़्रेश करें</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">ब्राउज़र में खोलें</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">यह एकाधिक पंक्ति पॉपअप मेनू है. अधिकतम पंक्तियाँ दो पर सेट हैं, शेष पाठ छोटा हो जाएगा.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">सभी समाचार</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">सहेजा गया समाचार</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">साइट्स से समाचार</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">कार्य के घंटों के बाद सूचित करें</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">डेस्कटॉप पर निष्क्रिय होने पर सूचित करें</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">आपने आइटम पर क्लिक किया:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">साधारण मेनू</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">साधारण मेनू2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">एक चयन योग्य आइटम और विभाजक वाला मेनू</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">सभी चयन योग्य आइटम्स, आइकन और लंबे पाठ वाला मेनू</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">दिखाएँ</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">वृत्ताकार प्रगति</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">छोटा</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">मध्यम</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">बड़ा</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">रेखीय प्रगति</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">अस्पष्ट</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">निर्धारित करें</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">खोज पट्टी</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">माइक्रोफ़ोन कॉलबैक</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">स्वत: सुधारें</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">माइक्रोफ़ोन दबाया गया</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">दाईं ओर का दृश्य दबाया गया</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">कुंजीपटल खोज दबाया गया</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">स्नैकबार दिखाएँ</string>
    <string name="fluentui_dismiss_snackbar">स्नैकबार खारिज करें</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">कार्रवाई</string>
    <string name="snackbar_action_long">लंबी पाठ कार्रवाई</string>
    <string name="snackbar_single_line">एकल लाइन स्नैकबार</string>
    <string name="snackbar_multiline">यह एक मल्टीलाइन स्नैकबार है. अधिकतम पंक्तियाँ दो पर सेट हैं, शेष पाठ छोटा हो जाएगा.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">यह एक घोषणा स्नैकबार है. इसका उपयोग नई सुविधाओं का उपयोग करने के लिए किया जाता है.</string>
    <string name="snackbar_primary">यह एक प्राथमिक स्नैकबार है.</string>
    <string name="snackbar_light">यह एक हल्का स्नैकबार है.</string>
    <string name="snackbar_warning">यह एक चेतावनी स्नैकबार है.</string>
    <string name="snackbar_danger">यह एक खतरा स्नैकबार है.</string>
    <string name="snackbar_description_single_line">छोटी अवधि</string>
    <string name="snackbar_description_single_line_custom_view">छोटे कस्टम दृश्य के रूप में वृत्ताकार प्रगति के साथ लंबी अवधि</string>
    <string name="snackbar_description_single_line_action">कार्रवाई के साथ छोटी अवधि</string>
    <string name="snackbar_description_single_line_action_custom_view">क्रिया और मध्यम कस्टम दृश्य के साथ छोटी अवधि</string>
    <string name="snackbar_description_single_line_custom_text_color">अनुकूलित पाठ रंग के साथ छोटी अवधि</string>
    <string name="snackbar_description_multiline">लंबी अवधि</string>
    <string name="snackbar_description_multiline_custom_view">छोटे कस्टम दृश्य के साथ लंबी अवधि</string>
    <string name="snackbar_description_multiline_action">क्रिया और पाठ अद्यतनों के साथ अनिश्चित अवधि</string>
    <string name="snackbar_description_multiline_action_custom_view">क्रिया और मध्यम कस्टम दृश्य के साथ छोटी अवधि</string>
    <string name="snackbar_description_multiline_action_long">लंबे क्रिया पाठ के साथ छोटी अवधि</string>
    <string name="snackbar_description_announcement">छोटी अवधि</string>
    <string name="snackbar_description_updated">यह टेक्स्ट अपडेट किया गया है.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">स्नैकबार दिखाएँ</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">एकल रेखा</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">बहुरेखीय</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">घोषणा शैली</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">प्राथमिक शैली</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">हल्की शैली</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">चेतावनी शैली</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">जोख़िम शैली</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">होम पेज</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">मेल</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">सेटिंग</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">सूचना</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">अधिक</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">पाठ संरेखण</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">अनुलंब</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">क्षैतिज</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">कोई पाठ नहीं</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">टैब आइटम्स</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">शीर्षक</string>
    <string name="cell_sample_description">वर्णन</string>
    <string name="calculate_cells">100 कक्ष लोड/परिकलित करें</string>
    <string name="calculate_layouts">100 लेआउट लोड/परिकलित करें</string>
    <string name="template_list">टेम्पलेट सूची</string>
    <string name="regular_list">नियमित सूची</string>
    <string name="cell_example_title">शीर्षक: कक्ष</string>
    <string name="cell_example_description">वर्णन: ओरिएंटेशन बदलने के लिए टैप करें</string>
    <string name="vertical_layout">लंबवत लेआउट</string>
    <string name="horizontal_layout">क्षैतिज लेआउट</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">मानक टैब 2-खंड</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">मानक टैब 3-खंड</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">मानक टैब 4-खंड</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">पेजर के साथ मानक टैब</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">टैब स्विच करें</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">पिल्स टैब </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">टूलटिप के लिए टैप करें</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">कस्टम कैलेंडर टूलटिप के लिए टैप करें</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">कस्टम रंग टूलटिप टैप करें</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">टूलटिप के अंदर ख़ारिज करने के लिए टैप करें</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">कस्टम दृश्य टूलटिप के लिए टैप करें</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">शीर्ष कस्टम रंग टूलटिप</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp ऑफ़सेटX के साथ शीर्ष समाप्ति टूलटिप</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">निचला प्रारंभ टूलटिप</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp ऑफसेटY के साथ निचला समाप्ति टूलटिप</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">टूलटिप के अंदर ख़ारिज करें</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">टूलटिप ख़ारिज किया गया</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">हेडलाइन हल्की 28sp है</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">शीर्षक 1 मध्यम 20sp है</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">शीर्षक 2 नियमित 20sp है</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">शीर्षक नियमित 18sp है</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">उपशीर्षक 1 नियमित 16sp है</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">उपशीर्षक 2 मध्यम 16sp है</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">मुख्य भाग 1 नियमित 14sp है</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">मुख्य भाग 2 मध्यम 14sp है</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">कैप्शन नियमित 12sp है</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK संस्करण: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">आइटम %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">फ़ोल्डर</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">क्लिक किया गया</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">स्कैफ़ोल्ड</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB विस्तृत किया गया</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB संक्षिप्त किया गया</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">रीफ़्रेश करने के लिए क्लिक करें</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">ड्रॉवर खोलें</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">मेनू आइटम</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">ऑफ़सेट X (dp में)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">ऑफ़सेट Y (dp में)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">कंटेंट टेक्स्ट</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">कंटेंट टेक्स्ट दोहराएँ</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">सामग्री पाठ के संदर्भ में मेनू की चौड़ाई परिवर्तित हो जाएगी. अधिकतम
        स्क्रीन आकार के 75% तक चौड़ाई प्रतिबंधित है. पार्श्व और नीचे की सामग्री मार्जिन टोकन द्वारा नियंत्रित होती है. समान सामग्री पाठ भिन्न होने के लिए दोहराया जाएगा
        ऊँचाई.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">मेनू खोलें</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">मूल कार्ड</string>
    <!-- UI Label for Card -->
    <string name="file_card">फ़ाइल कार्ड</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">घोषणा कार्ड</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">यादृच्छिक UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">विकल्प</string>
    <!-- UI Label for Title -->
    <string name="card_title">शीर्षक</string>
    <!-- UI Label for text -->
    <string name="card_text">टेक्स्ट</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">सब टेक्स्ट</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">यदि आवश्यक हो तो इस बैनर की सेकंडरी कॉपी को दो पंक्तियों में समेटा जा सकता है.</string>
    <!-- UI Label Button -->
    <string name="card_button">बटन</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">संवाद दिखाएँ</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">बाहर क्लिक करने पर संवाद ख़ारिज करें</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">संवाद को वापस दबाएँ पर ख़ारिज करें</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">संवाद ख़ारिज किया गया</string>
    <!-- UI Label Cancel -->
    <string name="cancel">रद्द करें</string>
    <!-- UI Label Ok -->
    <string name="ok">ठीक है</string>
    <!-- A sample description -->
    <string name="dialog_description">संवाद एक छोटी विंडो है जो उपयोगकर्ता को कोई निर्णय लेने या अतिरिक्त जानकारी दर्ज करने का संकेत देता है.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">ड्रॉवर खोलें</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">ड्रॉवर को बड़ा करें</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">ड्रॉअर बंद करें</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">ड्रॉवर प्रकार का चयन करें</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">मुख्य</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">पूरा ड्रॉवर दृश्यमान क्षेत्र में दिखाई देता है.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">नीचे</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">पूरे ड्रॉवर दृश्यमान जगह पर दिखाई देता है. गति स्क्रॉल सामग्री को ऊपर की ओर स्वाइप करें. ड्रैग हैंडल के माध्यम से विस्तार करने योग्य ड्रॉवर विस्तृत करें.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">बाईं ओर स्लाइड</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">बाईं ओर से दृश्यमान क्षेत्र पर आरेखक स्लाइड.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">दाईं ओर स्लाइड</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">दाईं ओर से दृश्यमान क्षेत्र पर ड्रॉवर स्लाइड.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">निचली ओर स्लाइड</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">स्क्रीन के निचले भाग से दृश्यमान जगह पर ड्रॉवर स्लाइड. विस्तार करने योग्य ड्रॉवर पर ऊपर की ओर स्वाइप करें. इसके शेष भाग को दृश्यमान क्षेत्र में लाएं और फिर स्क्रॉल करें.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">स्क्रिम दृश्यमान</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">ड्रॉवर सामग्री का चयन करें</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">पूरी स्क्रीन आकार स्क्रॉल करने योग्य सामग्री</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">आधी स्क्रीन सामग्री से ज़्यादा</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">आधी स्क्रीन सामग्री से कम</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">डायनेमिक आकार की सामग्री</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">नेस्टेड ड्रॉवर सामग्री</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">विस्तार करने योग्य</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">‘ओपन स्टेट’ विकल्प को छोड़ें</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Scrim क्लिक पर ख़ारिज करना रोकें</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">हैंडल दिखाएँ</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">शीर्षक</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">टूलटिप पाठ</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">कस्टम सामग्री टूलटिप के लिए टैप करें</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">शीर्ष प्रारंभ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">शीर्ष अंत </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">निचला प्रारंभ </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">निचला अंत </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">मध्य </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">कस्टम केंद्र</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">रिलीज़ नोट्स पर अद्यतनों के लिए, </string>
    <string name="click_here">यहाँ क्लिक करें.</string>
    <string name="open_source_cross_platform">स्रोत क्रॉस प्लेटफ़ॉर्म डिज़ाइन सिस्टम खोलें.</string>
    <string name="intuitive_and_powerful">सहज और सशक्त.</string>
    <string name="design_tokens">डिज़ाइन टोकन</string>
    <string name="release_notes">रिलीज़ नोट्स</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub रीपो लिंक</string>
    <string name="report_issue">समस्या रिपोर्ट करें</string>
    <string name="v1_components">V1 घटक</string>
    <string name="v2_components">V2 घटक</string>
    <string name="all_components">सभी</string>
    <string name="fluent_logo">Fluent लोगो</string>
    <string name="new_badge">नया</string>
    <string name="modified_badge">संशोधित</string>
    <string name="api_break_badge">API विराम</string>
    <string name="app_bar_more">अधिक</string>
    <string name="accent">एक्सेंट</string>
    <string name="appearance">अपीयरेंस</string>
    <string name="choose_brand_theme">अपनी ब्रांड थीम चुनें:</string>
    <string name="fluent_brand_theme">Fluent ब्रांड</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">अपीयरेंस चुनें</string>
    <string name="appearance_system_default">सिस्टम डिफ़ॉल्ट</string>
    <string name="appearance_light">हल्का</string>
    <string name="appearance_dark">गहरा</string>
    <string name="demo_activity_github_link">डेमो गतिविधि GitHub लिंक</string>
    <string name="control_tokens_details">नियंत्रण टोकन विवरण</string>
    <string name="parameters">पैरामीटर्स</string>
    <string name="control_tokens">नियंत्रण टोकन</string>
    <string name="global_tokens">वैश्विक टोकन</string>
    <string name="alias_tokens">उपनाम टोकन</string>
    <string name="sample_text">टेक्स्ट</string>
    <string name="sample_icon">नमूना चिह्न</string>
    <string name="color">रंग</string>
    <string name="neutral_color_tokens">न्यूट्रल रंग टोकन</string>
    <string name="font_size_tokens">फ़ॉन्ट आकार टोकन</string>
    <string name="line_height_tokens">पंक्ति ऊँचाई टोकन</string>
    <string name="font_weight_tokens">फ़ॉन्ट भार टोकन</string>
    <string name="icon_size_tokens">आइकन आकार टोकन</string>
    <string name="size_tokens">आकार टोकन</string>
    <string name="shadow_tokens">छाया टोकन</string>
    <string name="corner_radius_tokens">कॉर्नर रेडियस टोकन</string>
    <string name="stroke_width_tokens">स्ट्रोक चौड़ाई टोकन</string>
    <string name="brand_color_tokens">ब्रांड रंग टोकन</string>
    <string name="neutral_background_color_tokens">न्यूट्रल पृष्ठभूमि रंग टोकन</string>
    <string name="neutral_foreground_color_tokens">न्यूट्रल अग्रभूमि रंग टोकन</string>
    <string name="neutral_stroke_color_tokens">न्यूट्रल स्ट्रोक रंग टोकन</string>
    <string name="brand_background_color_tokens">ब्रांड पृष्ठभूमि रंग टोकन</string>
    <string name="brand_foreground_color_tokens">ब्रांड अग्रभूमि रंग टोकन</string>
    <string name="brand_stroke_color_tokens">ब्रांड स्ट्रोक रंग टोकन</string>
    <string name="error_and_status_color_tokens">त्रुटि और स्थिति रंग टोकन</string>
    <string name="presence_tokens">उपस्थिति रंग टोकन</string>
    <string name="typography_tokens">टाइपोग्राफ़ी टोकन</string>
    <string name="unspecified">अनिर्दिष्ट</string>

</resources>