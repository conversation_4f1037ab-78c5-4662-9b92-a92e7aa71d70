<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI 데모</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s 선택됨</string>
    <string name="app_modifiable_parameters">수정 가능한 매개 변수</string>
    <string name="app_right_accessory_view">오른쪽 액세서리 보기</string>

    <string name="app_style">스타일</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">아이콘 누름</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">데모 시작</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">캐러셀</string>
    <string name="actionbar_icon_radio_label">아이콘</string>
    <string name="actionbar_basic_radio_label">기본</string>
    <string name="actionbar_position_bottom_radio_label">아래쪽</string>
    <string name="actionbar_position_top_radio_label">위쪽</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">작업 표시줄 유형</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar 위치</string>

    <!--AppBar-->
    <string name="app_bar_style">앱 표시줄 스타일</string>
    <string name="app_bar_subtitle">자막</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">하단 테두리</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">탐색 아이콘을 클릭했습니다.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">플래그</string>
    <string name="app_bar_layout_menu_settings">설정</string>
    <string name="app_bar_layout_menu_search">검색</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">스크롤 동작: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">스크롤 동작 설정/해제</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">탐색 아이콘 설정/해제</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">아바타 표시</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">뒤로 표시 아이콘</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">아이콘 숨기기</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">아이콘 표시</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">검색 표시줄 레이아웃 스타일 설정/해제</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">액세서리 보기로 표시</string>
    <string name="app_bar_layout_searchbar_action_view_button">작업 보기로 표시</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">테마 간 전환(활동 다시 만들기)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">토글 테마</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">항목</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">추가 스크롤 가능한 콘텐츠</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">원 스타일</string>
    <string name="avatar_style_square">정사각형 스타일</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">크게</string>
    <string name="avatar_size_medium">중간</string>
    <string name="avatar_size_small">작게</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">아주 아주 크게</string>
    <string name="avatar_size_xlarge_accessibility">아주 크게</string>
    <string name="avatar_size_xsmall_accessibility">아주 작게</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">표시된 최대 아바타</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">오버플로 아바타 수</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">테두리 유형</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">OverflowAvatarCount가 설정된 아바타 그룹은 표시된 최대 아바타를 따르지 않습니다.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">얼굴 스택</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">얼굴 더미</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">오버플로 클릭됨</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">인덱스 %d의 AvatarView가 클릭됨</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">알림 배지</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">점</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">목록</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">문자</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">사진</string>
    <string name="bottom_navigation_menu_item_news">뉴스</string>
    <string name="bottom_navigation_menu_item_alerts">경고</string>
    <string name="bottom_navigation_menu_item_calendar">일정</string>
    <string name="bottom_navigation_menu_item_team">팀</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">레이블 설정/해제</string>
    <string name="bottom_navigation_three_menu_items_button">세 개의 메뉴 항목 표시</string>
    <string name="bottom_navigation_four_menu_items_button">네 개의 메뉴 항목 표시</string>
    <string name="bottom_navigation_five_menu_items_button">다섯 개의 메뉴 항목 표시</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">레이블은 %s입니다.</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">사용 아래로 살짝 밀어 해제</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">한 줄 항목으로 표시</string>
    <string name="bottom_sheet_with_double_line_items">이중 줄 항목으로 표시</string>
    <string name="bottom_sheet_with_single_line_header">한 줄 머리글로 표시</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">이중 줄 머리글 및 구분선으로 표시</string>
    <string name="bottom_sheet_dialog_button">표시</string>
    <string name="drawer_content_desc_collapse_state">확장</string>
    <string name="drawer_content_desc_expand_state">최소화</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s 클릭</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">%s을(를) 길게 클릭</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">해제 클릭</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">항목 삽입</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">항목 업데이트</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">해제</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">추가</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">멘션</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">굵게</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">기울임꼴</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">밑줄</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">취소선</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">실행 취소</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">다시 실행</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">글머리 기호</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">목록</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">링크</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">항목 업데이트 중</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">간격</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">위치 해제</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">시작</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">종료</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">그룹 공간</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">항목 공간</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">플래그</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">플래그 항목 클릭됨</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">회신</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">회신 항목 클릭됨</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">전달</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">항목 전달 클릭됨</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">삭제</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">항목 삭제 클릭됨</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">아바타</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">카메라</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">사진 찍기</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">카메라 항목을 클릭함</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">갤러리</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">사진 보기</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">갤러리 항목을 클릭함</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">비디오</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">비디오 재생</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">비디오 항목 클릭됨</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">관리</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">미디어 라이브러리 관리</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">항목 관리 클릭됨</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">전자 메일 작업</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">문서</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">마지막 업데이트 날짜: 오후 2시 14분</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">공유</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">항목 공유 클릭됨</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">이동</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">항목 이동 클릭됨</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">삭제</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">항목 삭제 클릭됨</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">정보</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">정보 항목을 클릭했습니다.</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">시계</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">클록 항목 클릭됨</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">경보</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">알람 항목을 클릭했습니다.</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">표준 시간대</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">표준 시간대 항목을 클릭함</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">단추의 다양한 보기</string>
    <string name="button">단추</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">사용 안 함 단추의 예</string>
    <string name="button_borderless">테두리 없는 단추의 예</string>
    <string name="button_borderless_disabled">테두리 없는 사용 안 함 단추의 예</string>
    <string name="button_large">큰 단추의 예</string>
    <string name="button_large_disabled">큰 사용 안 함 단추의 예</string>
    <string name="button_outlined">윤곽선이 있는 단추의 예</string>
    <string name="button_outlined_disabled">윤곽선이 있는 사용 안 함 단추의 예</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">날짜 선택</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">단일 날짜</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">선택한 날짜 없음</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">날짜 선택기 표시</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">날짜 탭이 선택된 날짜 시간 선택기 표시</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">시간 탭이 선택된 날짜 시간 선택기 표시</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">날짜 시간 선택기 표시</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">날짜 범위</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">시작:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">끝:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">시작이 선택되지 않음</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">끝 선택 안 됨</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">시작 날짜 선택</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">종료 날짜 선택</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">날짜 시간 범위</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">날짜 시간 범위 선택</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">대화 상자 표시</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">서랍 표시</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">서랍 대화 상자 표시</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">아래쪽 페이드 대화 상자 없음</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">위쪽 서랍 표시</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">페이드 상단 대화 상자 없음</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> 앵커 보기 위쪽 대화 상자 표시</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> 제목 위쪽 대화 상자 표시 안 함</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> 제목 위쪽 아래 대화 상자 표시</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">오른쪽 서랍 표시</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">왼쪽 서랍 표시</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">제목, 기본 텍스트</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">자막, 보조 텍스트</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">사용자 지정 자막 텍스트</string>
    <!-- Footer -->
    <string name="list_item_footer">바닥글, 3차 텍스트</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">회색 하위 머리글 텍스트가 있는 한 줄 목록</string>
    <string name="list_item_sub_header_two_line">두 줄 목록</string>
    <string name="list_item_sub_header_two_line_dense">간격이 조밀한 두 줄 목록</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">사용자 지정 보조 부제목 보기가 있는 두 줄 목록</string>
    <string name="list_item_sub_header_three_line">검은색 하위 머리글 텍스트가 있는 세 줄 목록</string>
    <string name="list_item_sub_header_no_custom_views">사용자 지정 보기가 없는 항목 나열</string>
    <string name="list_item_sub_header_large_header">큰 사용자 지정 보기가 있는 항목 나열</string>
    <string name="list_item_sub_header_wrapped_text">래핑된 텍스트가 있는 항목 나열</string>
    <string name="list_item_sub_header_truncated_text">잘린 텍스트가 있는 항목 나열</string>
    <string name="list_item_sub_header_custom_accessory_text">작업</string>
    <string name="list_item_truncation_middle">중간 잘림입니다.</string>
    <string name="list_item_truncation_end">잘림을 종료합니다.</string>
    <string name="list_item_truncation_start">잘림을 시작합니다.</string>
    <string name="list_item_custom_text_view">값</string>
    <string name="list_item_click">목록 항목을 클릭했습니다.</string>
    <string name="list_item_click_custom_accessory_view">사용자 지정 액세서리 보기를 클릭했습니다.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">하위 헤더 사용자 지정 액세서리 보기를 클릭했습니다.</string>
    <string name="list_item_more_options">기타 옵션</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">선택</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">없음</string>
    <string name="people_picker_delete_example">삭제</string>
    <string name="people_picker_custom_persona_description">이 예에서는 사용자 지정 IPersona 개체를 만드는 방법을 보여  제공합니다.</string>
    <string name="people_picker_dialog_title_removed">가상 사용자를 제거함:</string>
    <string name="people_picker_dialog_title_added">가상 사용자를 추가함:</string>
    <string name="people_picker_drag_started">끌기 시작됨</string>
    <string name="people_picker_drag_ended">끌기 종료</string>
    <string name="people_picker_picked_personas_listener">가상 사용자 수신기</string>
    <string name="people_picker_suggestions_listener">추천 수신기</string>
    <string name="people_picker_persona_chip_click">%s을(를) 클릭했습니다.</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="other">수신자 %1$s명</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">영구 하위 시트 확장</string>
    <string name="collapse_persistent_sheet_button"> 영구 하위시트 숨기기</string>
    <string name="show_persistent_sheet_button"> 영구 하위시트 표시</string>
    <string name="new_view">새 보기입니다.</string>
    <string name="toggle_sheet_content">하위시트 콘텐츠 설정/해제</string>
    <string name="switch_to_custom_content">사용자 지정 콘텐츠로 전환</string>
    <string name="one_line_content">한 줄 하단시트 콘텐츠</string>
    <string name="toggle_disable_all_items">모든 항목 사용 안 함 설정/해제</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">보기 추가/제거</string>
    <string name="persistent_sheet_item_change_collapsed_height"> 축소된 높이 변경</string>
    <string name="persistent_sheet_item_create_new_folder_title">새 폴더</string>
    <string name="persistent_sheet_item_create_new_folder_toast">새 폴더 항목을 클릭함</string>
    <string name="persistent_sheet_item_edit_title">편집</string>
    <string name="persistent_sheet_item_edit_toast">항목 편집 클릭됨</string>
    <string name="persistent_sheet_item_save_title">저장</string>
    <string name="persistent_sheet_item_save_toast">항목 저장 클릭됨</string>
    <string name="persistent_sheet_item_zoom_in_title">확대</string>
    <string name="persistent_sheet_item_zoom_in_toast"> 항목 확대 클릭됨</string>
    <string name="persistent_sheet_item_zoom_out_title">축소</string>
    <string name="persistent_sheet_item_zoom_out_toast">항목 축소 클릭됨</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">사용 가능</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">디자이너</string>
    <string name="persona_subtitle_engineer">엔지니어</string>
    <string name="persona_subtitle_manager">관리자</string>
    <string name="persona_subtitle_researcher">리서치 도구</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (잘림을 테스트하는 긴 텍스트 예제)</string>
    <string name="persona_view_description_xxlarge">세 줄 텍스트가 있는 XXLarge 아바타</string>
    <string name="persona_view_description_large">두 줄 텍스트가 있는 큰 아바타</string>
    <string name="persona_view_description_small">텍스트 한 줄이 있는 작은 아바타</string>
    <string name="people_picker_hint">힌트가 표시된 없음</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">가상 사용자 칩 사용 안 함</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">오류 가상 사용자 칩</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">닫는 아이콘이 없는 가상 사용자 칩</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">기본 가상 사용자 칩</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">선택한 가상 사용자 칩을 클릭했습니다.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">공유</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">팔로우</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">사람 초대</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">페이지 새로 고침</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">브라우저에서 열기</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">여러 줄 팝업 메뉴입니다. 최대 줄이 2로 설정되어 있으며 나머지 텍스트는 잘립니다.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">모든 뉴스</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">저장된 뉴스</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">사이트의 뉴스</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">업무 시간 외에 알림</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">데스크톱에서 비활성 상태일 때 알림</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">항목을 클릭했습니다.</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">간단한 메뉴</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">간단한 메뉴2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">선택 가능한 항목 하나와 구분선이 있는 메뉴</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">모든 선택 가능한 항목, 아이콘 및 긴 텍스트가 있는 메뉴</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">표시</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">순환 진행률</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">작게</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">중간</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">크게</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">선형 진행률</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">미정</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">확정</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">검색 표시줄</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Microphone Callback</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">자동 고침</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">마이크 누름</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">오른쪽 보기 누름</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">키보드 검색 누름</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">스낵바 표시</string>
    <string name="fluentui_dismiss_snackbar">스낵바 해제</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">작업</string>
    <string name="snackbar_action_long">긴 텍스트 작업</string>
    <string name="snackbar_single_line">단일 선 스낵바</string>
    <string name="snackbar_multiline">여러 줄 스레드바입니다. 최대 줄이 2로 설정되어 있으며 나머지 텍스트는 잘립니다.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">공지 사항 스나일바입니다. 새 기능을 전달하는 데 사용됩니다.</string>
    <string name="snackbar_primary">기본 스낵바입니다.</string>
    <string name="snackbar_light">간단한 스낵바입니다.</string>
    <string name="snackbar_warning">경고 스낵바입니다.</string>
    <string name="snackbar_danger">위험 스낵바입니다.</string>
    <string name="snackbar_description_single_line">짧은 기간</string>
    <string name="snackbar_description_single_line_custom_view">작은 사용자 지정 보기로 순환 진행이 있는 긴 기간</string>
    <string name="snackbar_description_single_line_action">작업이 포함된 짧은 기간</string>
    <string name="snackbar_description_single_line_action_custom_view">작업 및 중간 사용자 지정 보기가 있는 짧은 기간</string>
    <string name="snackbar_description_single_line_custom_text_color">사용자 지정된 텍스트 색의 짧은 기간</string>
    <string name="snackbar_description_multiline">긴 기간</string>
    <string name="snackbar_description_multiline_custom_view">사용자 지정 보기가 작은 긴 기간</string>
    <string name="snackbar_description_multiline_action">작업 및 텍스트 업데이트가 포함된 무기한 기간</string>
    <string name="snackbar_description_multiline_action_custom_view">작업 및 중간 사용자 지정 보기가 있는 짧은 기간</string>
    <string name="snackbar_description_multiline_action_long">긴 작업 텍스트가 있는 짧은 기간</string>
    <string name="snackbar_description_announcement">짧은 기간</string>
    <string name="snackbar_description_updated">이 텍스트는 업데이트되었습니다.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">스낵바 표시</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">단일 줄</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">여러 줄</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">알림 스타일</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">기본 스타일</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">밝은 스타일</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">경고 스타일</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">위험 스타일</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">홈</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">메일</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">설정</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">알림</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">자세히</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">텍스트 맞춤</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">세로로 확대/축소</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">가로</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">텍스트 없음</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">탭 항목</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">제목</string>
    <string name="cell_sample_description">설명</string>
    <string name="calculate_cells">100 셀 로드/계산</string>
    <string name="calculate_layouts">100개의 레이아웃 로드/계산</string>
    <string name="template_list">서식 파일 목록</string>
    <string name="regular_list">일반 목록</string>
    <string name="cell_example_title">제목 셀</string>
    <string name="cell_example_description">설명: 방향을 변경하려면 탭하세요.</string>
    <string name="vertical_layout">세로 레이아웃</string>
    <string name="horizontal_layout">가로 레이아웃</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">표준 탭 2-세그먼트</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">표준 탭 3-세그먼트</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">표준 탭 4 세그먼트</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">호출기가 있는 표준 탭</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">스위치 탭</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">알약 모양 탭 </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">도구 설명을 보려면 탭하기</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">사용자 지정 일정 도구 설명 탭하기</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">사용자 지정 색 도구 설명 탭하기</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">내부 도구 설명 해제를 보려면 탭하기</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">사용자 지정 보기 도구 설명을 보려면 탭하기</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">상위 사용자 지정 색 도구 설명</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp 오프셋X가 있는 위쪽 끝 도구 설명</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">아래쪽 시작 도구 설명</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp 오프셋Y가 있는 아래쪽 끝 도구 설명</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">도구 설명 내부 해제</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">도구 설명 해제</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">헤드라인은 밝은 28sp입니다.</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">타이틀 1은 중간 20sp입니다.</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">타이틀 2는 일반 20sp입니다.</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">제목은 레귤러 18sp입니다.</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">부제목 1은 레귤러 16sp입니다.</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">부제목 2는 중간 16sp입니다.</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">본문 1은 레귤러 14sp입니다.</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">본문 2는 중간 14sp입니다.</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">자막은 레귤러 12sp입니다.</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK 버전: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">항목 %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">폴더</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">클릭한 목록</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">비계</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB 확장됨</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB 축소됨</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">클릭하여 목록 새로 고침</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">서랍 열기</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">메뉴 항목</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">오프셋 X(dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">오프셋 Y(dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">콘텐츠 텍스트</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">콘텐츠 텍스트 반복</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">콘텐츠 텍스트에 따라 메뉴 너비가 변경됩니다. 최대값
        너비는 화면 크기의 75% 제한됩니다. 측면과 아래쪽의 콘텐츠 여백은 토큰에 의해 제어됩니다. 동일한 콘텐츠 텍스트가 다르게 반복됩니다.
        높이를 지정합니다.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">메뉴 열기</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">기본 카드</string>
    <!-- UI Label for Card -->
    <string name="file_card">파일 카드</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">알림 카드</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">임의 UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">옵션</string>
    <!-- UI Label for Title -->
    <string name="card_title">제목</string>
    <!-- UI Label for text -->
    <string name="card_text">텍스트</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">하위 텍스트</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">필요한 경우 이 배너의 보조 복사본을 두 줄로 래핑할 수 있습니다.</string>
    <!-- UI Label Button -->
    <string name="card_button">단추</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">대화 상자 표시</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">외부를 클릭하여 대화 상자 닫기</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">뒤로를 눌러 대화 상자 닫기</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">대화 상자가 닫힘</string>
    <!-- UI Label Cancel -->
    <string name="cancel">취소</string>
    <!-- UI Label Ok -->
    <string name="ok">확인</string>
    <!-- A sample description -->
    <string name="dialog_description">대화 상자는 사용자에게 의사 결정을 내리거나 추가 정보를 입력하라는 메시지를 표시하는 작은 창입니다.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">서랍 열기</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">서랍 확장</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">서랍 닫기</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">서랍 유형 선택</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">위쪽</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">표시 영역에 전체 서랍이 표시됩니다.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">아래쪽</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">표시 영역에 전체 서랍이 표시됩니다. 위로 살짝 밀면 콘텐츠가 스크롤됩니다. 확장 가능한 서랍은 서랍 핸들을 통해 확장됩니다.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">왼쪽으로 밀기</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">서랍이 왼쪽에서 표시 영역으로 밀립니다.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">오른쪽으로 밀기</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">서랍이 오른쪽에서 표시 영역으로 밀립니다.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">아래쪽으로 밀기</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">서랍이 화면 아래쪽에서 표시 영역으로 밀립니다. 확장 가능한 서랍에서 위로 살짝 밀면 나머지 부분이 표시 영역으로 이동한 다음 스크롤됩니다.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim 표시</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">서랍 콘텐츠 선택</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">전체 화면 크기 스크롤 가능 콘텐츠</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">절반 이상의 화면 콘텐츠</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">절반 미만의 화면 콘텐츠</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">동적 크기 콘텐츠</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">중첩 서랍 콘텐츠</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">확장 가능</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">열린 상태 건너뛰기</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">스크림 클릭 시 해제 방지</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">핸들 표시</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">타이틀</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">도구 설명 텍스트</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">사용자 지정 콘텐츠 도구 설명을 보려면 탭하세요.</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">상위 시작 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">위쪽 끝 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">아래쪽 시작 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">아래쪽 끝 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">센터 </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">사용자 지정 센터</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">릴리스 정보에 대한 업데이트는</string>
    <string name="click_here">여기를 클릭하세요.</string>
    <string name="open_source_cross_platform">오픈 소스 크로스 플랫폼 디자인 시스템.</string>
    <string name="intuitive_and_powerful">직관적이고 강력합니다.</string>
    <string name="design_tokens">디자인 토큰</string>
    <string name="release_notes">릴리즈 정보</string>
    <string name="github_repo">GitHub 리포지토리</string>
    <string name="github_repo_link">GitHub 리포지토리 링크</string>
    <string name="report_issue">문제 신고</string>
    <string name="v1_components">V1 구성 요소</string>
    <string name="v2_components">V2 구성 요소</string>
    <string name="all_components">모두</string>
    <string name="fluent_logo">Fluent 로고</string>
    <string name="new_badge">새로운</string>
    <string name="modified_badge">수정됨</string>
    <string name="api_break_badge">API 브레이크</string>
    <string name="app_bar_more">추가</string>
    <string name="accent">악센트</string>
    <string name="appearance">모양</string>
    <string name="choose_brand_theme">브랜드 테마 선택:</string>
    <string name="fluent_brand_theme">Fluent 브랜드</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">모양 선택</string>
    <string name="appearance_system_default">시스템 기본값</string>
    <string name="appearance_light">빛</string>
    <string name="appearance_dark">어두운</string>
    <string name="demo_activity_github_link">데모 활동 GitHub 링크</string>
    <string name="control_tokens_details">제어 토큰 세부 정보</string>
    <string name="parameters">매개 변수</string>
    <string name="control_tokens">제어 토큰</string>
    <string name="global_tokens">글로벌 토큰</string>
    <string name="alias_tokens">별칭 토큰</string>
    <string name="sample_text">텍스트</string>
    <string name="sample_icon">샘플 아이콘</string>
    <string name="color">색상</string>
    <string name="neutral_color_tokens">중립 색상 토큰</string>
    <string name="font_size_tokens">글꼴 크기 토큰</string>
    <string name="line_height_tokens">선 높이 토큰</string>
    <string name="font_weight_tokens">글꼴 두께 토큰</string>
    <string name="icon_size_tokens">아이콘 크기 토큰</string>
    <string name="size_tokens">크기 토큰</string>
    <string name="shadow_tokens">섀도 토큰</string>
    <string name="corner_radius_tokens">코너 반경토큰</string>
    <string name="stroke_width_tokens">스트로크 폭 토큰</string>
    <string name="brand_color_tokens">브랜드 컬러 토큰</string>
    <string name="neutral_background_color_tokens">중립 배경 색상 토큰</string>
    <string name="neutral_foreground_color_tokens">중립 전경 색상 토큰</string>
    <string name="neutral_stroke_color_tokens">중립 스트로크 색상 토큰</string>
    <string name="brand_background_color_tokens">브랜드 배경 색상 토큰</string>
    <string name="brand_foreground_color_tokens">브랜드 전경 색상 토큰</string>
    <string name="brand_stroke_color_tokens">브랜드 스트로크 색상 토큰</string>
    <string name="error_and_status_color_tokens">오류 및 상태 색상 토큰</string>
    <string name="presence_tokens">현재 상태 색상 토큰</string>
    <string name="typography_tokens">타이포그래피 토큰</string>
    <string name="unspecified">미지정</string>

</resources>