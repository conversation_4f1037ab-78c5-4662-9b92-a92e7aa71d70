<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tooltip_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingTop="@dimen/fluentui_tooltip_padding_vertical"
        android:paddingBottom="@dimen/fluentui_tooltip_padding_vertical"
        android:paddingEnd="@dimen/fluentui_tooltip_padding_horizontal"
        android:paddingStart="@dimen/fluentui_tooltip_padding_horizontal"
        android:textAppearance="@style/TextAppearance.FluentUI.Tooltip"
        android:text="Press Yes Or No" />

    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/fluentui_tooltip_padding_vertical"
        android:paddingBottom="@dimen/fluentui_tooltip_padding_vertical"
        android:paddingEnd="@dimen/fluentui_tooltip_padding_horizontal"
        android:paddingStart="@dimen/fluentui_tooltip_padding_horizontal"
        android:text="Yes" />

    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/fluentui_tooltip_padding_vertical"
        android:paddingBottom="@dimen/fluentui_tooltip_padding_vertical"
        android:paddingEnd="@dimen/fluentui_tooltip_padding_horizontal"
        android:paddingStart="@dimen/fluentui_tooltip_padding_horizontal"
        android:text="No" />
</LinearLayout>