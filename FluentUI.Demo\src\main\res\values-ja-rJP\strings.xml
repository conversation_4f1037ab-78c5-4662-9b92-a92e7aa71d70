<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI のデモ</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s を選択しました</string>
    <string name="app_modifiable_parameters">変更可能なパラメータ</string>
    <string name="app_right_accessory_view">右側のアクセサリ ビュー</string>

    <string name="app_style">スタイル</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">押されたアイコン</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">デモの開始</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">カルーセル</string>
    <string name="actionbar_icon_radio_label">アイコン</string>
    <string name="actionbar_basic_radio_label">基本</string>
    <string name="actionbar_position_bottom_radio_label">下</string>
    <string name="actionbar_position_top_radio_label">上</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">ActionBar の種類</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar の位置</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar スタイル</string>
    <string name="app_bar_subtitle">字幕</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">下罫線</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">ナビゲーション アイコンがクリックされました。</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">フラグ</string>
    <string name="app_bar_layout_menu_settings">設定</string>
    <string name="app_bar_layout_menu_search">検索</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">スクロール動作: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">スクロール動作の切り替え</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">ナビゲーション アイコンの切り替え</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">アバターを表示</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">戻るアイコンを表示</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">非表示のアイコン</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">アイコンを表示</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">検索バーのレイアウト スタイルを切り替える</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">アクセサリ ビューとして表示</string>
    <string name="app_bar_layout_searchbar_action_view_button">アクション ビューとして表示</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">テーマを切り替えます (アクティビティを再現します)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">テーマの色を切り替え</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">アイテム</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">追加のスクロール可能なコンテンツ</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">円のスタイル</string>
    <string name="avatar_style_square">スクエア スタイル</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">大</string>
    <string name="avatar_size_medium">中</string>
    <string name="avatar_size_small">小</string>
    <string name="avatar_size_xsmall">非表示のアイコン</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">2 倍の特大</string>
    <string name="avatar_size_xlarge_accessibility">特大</string>
    <string name="avatar_size_xsmall_accessibility">極小</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">最大表示アバター</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">オーバーフロー アバター数</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">枠の種類</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">OverflowAvatarCount が設定されたアバター グループは、最大表示アバターに準拠しません。</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">フェイス スタック</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">フェイスパイル</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">オーバーフローがクリックされました</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">インデックス %d の AvatarView がクリックされました</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">通知バッジ</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">ドット</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">リスト</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">キャラクター</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">写真</string>
    <string name="bottom_navigation_menu_item_news">ニュース</string>
    <string name="bottom_navigation_menu_item_alerts">アラート</string>
    <string name="bottom_navigation_menu_item_calendar">予定表</string>
    <string name="bottom_navigation_menu_item_team">チーム</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">ラベルの切り替え</string>
    <string name="bottom_navigation_three_menu_items_button">3 つのメニュー項目を表示</string>
    <string name="bottom_navigation_four_menu_items_button">4 つのメニュー項目を表示</string>
    <string name="bottom_navigation_five_menu_items_button">5 つのメニュー項目を表示</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">ラベルは %s です</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">ボトム シート</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">下へスワイプして閉じるを有効にする</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">単一の項目で表示</string>
    <string name="bottom_sheet_with_double_line_items">二重線の項目を表示</string>
    <string name="bottom_sheet_with_single_line_header">1 行のヘッダーで表示</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">二重線のヘッダーと仕切りで表示</string>
    <string name="bottom_sheet_dialog_button">表示</string>
    <string name="drawer_content_desc_collapse_state">展開</string>
    <string name="drawer_content_desc_expand_state">最小化</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s をクリック</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">長いクリックの %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">[閉じる] をクリックします</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">アイテムの挿入</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">アイテムを更新する</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">却下</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">追加</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">メンション</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">太字</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">斜体</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">下線</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">取り消し線</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">元に戻す</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">やり直し</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">行頭文字</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">リスト</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">リンク</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">アイテムを更新しています</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">間隔</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">ポジションを却下</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">開始</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">グループ スペース</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">アイテム スペース</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">フラグ</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">クリックされたアイテムにフラグを立てる</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">返信</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">返信項目がクリックされました</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">転送</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">転送項目がクリックされました</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">削除</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">クリックした項目を削除</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">アバター</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">カメラ</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">写真を撮影する</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">カメラ アイテムがクリックされました</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">ギャラリー</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">写真を表示する</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">クリックされたギャラリー アイテム</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">ビデオ</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">ビデオの再生</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">動画アイテムがクリックされました</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">管理</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">メディア ライブラリを管理する</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">クリックした項目の管理</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">メール アクション</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">文書</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">最終更新日時: 午後 2:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">共有</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">クリックしたアイテムを共有</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">移動する</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">クリックした項目を移動</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">削除</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">クリックした項目を削除</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">情報</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">情報項目がクリックされました</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">時計</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">クリックされた時計アイテム</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">警報</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">アラーム項目がクリックされました</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">タイム ゾーン</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">タイム ゾーン項目がクリックされました</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">ボタンのさまざまなビュー</string>
    <string name="button">ボタン</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">大きなボタンの例</string>
    <string name="button_borderless">罫線のないボタンの例</string>
    <string name="button_borderless_disabled">フチ無し無効ボタンの例</string>
    <string name="button_large">大きいボタンの例</string>
    <string name="button_large_disabled">大きな無効化ボタンの例</string>
    <string name="button_outlined">アウトライン化されたボタンの例</string>
    <string name="button_outlined_disabled">アウトライン化された無効なボタンの例</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">日付の選択</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">日時ピッカー</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">単一の日付</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">日付が選択されていません</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">日付選択カレンダーの表示</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">日付タブが選択された日時ピッカーを表示する</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">時間タブが選択された日時ピッカーを表示する</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">日時ピッカーを表示</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">期間の設定</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">開始:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">終了:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">開始が選択されていません</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">エンドピックなし</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">開始日を選択</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">終了日を選択</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">日付時間範囲</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">日時範囲を選択</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">ダイアログを表示</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">引き出しを表示</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">引き出しダイアログを表示</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">フェード ボトム ダイアログなし</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">一番上の引き出しを表示</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">フェード トップ ダイアログなし</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button">  アンカー ビューのトップ ダイアログを表示</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> タイトル トップ ダイアログを表示しない</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> タイトルトップダイアログの下に表示</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">右の引き出しを表示</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">左の引き出しを表示</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">タイトル、プライマリ テキスト</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">サブタイトル、サブテキスト</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">カスタム字幕テキスト</string>
    <!-- Footer -->
    <string name="list_item_footer">フッター、第 3 テキスト</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">グレーのサブヘッダー テキストを含む 1 行のリスト</string>
    <string name="list_item_sub_header_two_line">2 行のリスト</string>
    <string name="list_item_sub_header_two_line_dense">間隔が密な 2 行のリスト</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">カスタム副字幕ビューを備えた 2 行のリスト</string>
    <string name="list_item_sub_header_three_line">黒のサブヘッダー テキストを含む 3 行のリスト</string>
    <string name="list_item_sub_header_no_custom_views">カスタム ビューのないリスト アイテム</string>
    <string name="list_item_sub_header_large_header">大きなカスタム ビューを持つリスト アイテム</string>
    <string name="list_item_sub_header_wrapped_text">テキストが折り返されたリスト アイテム</string>
    <string name="list_item_sub_header_truncated_text">テキストが切り捨てられたリスト項目</string>
    <string name="list_item_sub_header_custom_accessory_text">アクション</string>
    <string name="list_item_truncation_middle">中間切り捨て。</string>
    <string name="list_item_truncation_end">切り捨てを終了します。</string>
    <string name="list_item_truncation_start">切り捨てを開始します。</string>
    <string name="list_item_custom_text_view">値</string>
    <string name="list_item_click">リスト項目をクリックしました。</string>
    <string name="list_item_click_custom_accessory_view">カスタム アクセサリ ビューをクリックしました。</string>
    <string name="list_item_click_sub_header_custom_accessory_view">サブヘッダーのカスタム アクセサリ ビューをクリックしました。</string>
    <string name="list_item_more_options">その他のオプション</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">選択</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">なし</string>
    <string name="people_picker_delete_example">削除</string>
    <string name="people_picker_custom_persona_description">この例では、カスタム IPersona オブジェクトを作成する方法を示します。</string>
    <string name="people_picker_dialog_title_removed">ペルソナを削除しました:</string>
    <string name="people_picker_dialog_title_added">ペルソナを追加しました:</string>
    <string name="people_picker_drag_started">ドラッグ開始</string>
    <string name="people_picker_drag_ended">ドラッグ終了</string>
    <string name="people_picker_picked_personas_listener">ペルソナ リスナー</string>
    <string name="people_picker_suggestions_listener">提案リスナー</string>
    <string name="people_picker_persona_chip_click">%s をクリックしました</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="other">%1$s 人の受信者</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">永続的なボトムシートを展開</string>
    <string name="collapse_persistent_sheet_button"> 永続的なボトム シートを非表示にする</string>
    <string name="show_persistent_sheet_button"> 永続的なボトム シートを表示</string>
    <string name="new_view">これは新しいビューです</string>
    <string name="toggle_sheet_content">ボトム シートのコンテンツを切り替え</string>
    <string name="switch_to_custom_content">カスタム コンテンツに切り替える</string>
    <string name="one_line_content">1 行のボトムシート コンテンツ</string>
    <string name="toggle_disable_all_items">Toggle すべてのアイテムを無効にする</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">ビューの追加/削除</string>
    <string name="persistent_sheet_item_change_collapsed_height"> 折りたたんだ高さを変更</string>
    <string name="persistent_sheet_item_create_new_folder_title">新しいフォルダー</string>
    <string name="persistent_sheet_item_create_new_folder_toast">新しいフォルダ項目がクリックされました</string>
    <string name="persistent_sheet_item_edit_title">編集</string>
    <string name="persistent_sheet_item_edit_toast">クリックした項目を編集</string>
    <string name="persistent_sheet_item_save_title">保存</string>
    <string name="persistent_sheet_item_save_toast">クリックしたアイテムを保存</string>
    <string name="persistent_sheet_item_zoom_in_title">拡大</string>
    <string name="persistent_sheet_item_zoom_in_toast">  拡大アイテムがクリックされました</string>
    <string name="persistent_sheet_item_zoom_out_title">縮小</string>
    <string name="persistent_sheet_item_zoom_out_toast">アイテムの縮小がクリックされました</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia">カスタム アクセサリ ビューをクリックしました。</string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">使用可能</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">デザイナー</string>
    <string name="persona_subtitle_engineer">エンジニア</string>
    <string name="persona_subtitle_manager">マネージャー</string>
    <string name="persona_subtitle_researcher">リサーチ ツール</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (切り捨てをテストするための長いテキストの例)</string>
    <string name="persona_view_description_xxlarge">XXLarge アバターと 3 行のテキスト</string>
    <string name="persona_view_description_large">2 行のテキストを含む大きなアバター</string>
    <string name="persona_view_description_small">テキストが 1 行の小さなアバター</string>
    <string name="people_picker_hint">ヒントつきの表示なし</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">無効化されたペルソナ  チップ</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">エラー ペルソナ チップ</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">閉じるアイコンのないペルソナ チップ</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">基本ペルソナ チップ</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">選択したペルソナ チップをクリックしました。</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">共有</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">フォロー</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">ユーザーの招待</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">ページを更新</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">ブラウザーで開く </string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">複数行のポップアップ メニューです。最大行数が 2 に設定されています。残りのテキストは切り捨てられます。
        Lorem ipsum dolor sit amet consectetur adipiscing elit.</string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">すべてのニュース</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">保存済みのニュース</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">サイトからのニュース</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">営業時間外に通知する</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">デスクトップで非アクティブなときに通知する</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">アイテムをクリックしました:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">シンプルメニュー</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">シンプル メニュー 2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">選択可能な項目が 1 つと区切り線があるメニュー</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">選択可能なすべてのアイテム、アイコン、および長いテキストを含むメニュー</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">表示</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">サーキュラー プログレス</string>
    <string name="circular_progress_xsmall">非表示のアイコン</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">小</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">中</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">大</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">リニア プログレス</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">不明</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">決定する</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">検索バー</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">マイク コールバック</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">自動修正</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">マイクが押されました</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">右ビューが押されました</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">キーボード検索が押されました</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">スナックバーを表示</string>
    <string name="fluentui_dismiss_snackbar">スナックバーを閉じる</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">アクション</string>
    <string name="snackbar_action_long">長いテキスト アクション</string>
    <string name="snackbar_single_line">シングル ライン スナックバー</string>
    <string name="snackbar_multiline">複数行のスナックバーです。最大行数が 2 に設定されています。残りのテキストは切り捨てられます。
        Lorem ipsum dolor sit amet consectetur adipiscing elit.</string>
    <string name="snackbar_announcement">お知らせのスナック バーです。 新しい機能を伝えるために使用されます。</string>
    <string name="snackbar_primary">これは主なスナックバーです。</string>
    <string name="snackbar_light">軽いスナックバーです。</string>
    <string name="snackbar_warning">これは警告のスナックバーです。</string>
    <string name="snackbar_danger">これは危険なスナック バーです。</string>
    <string name="snackbar_description_single_line">短期間</string>
    <string name="snackbar_description_single_line_custom_view">小さなカスタム ビューとしての循環進行の長時間</string>
    <string name="snackbar_description_single_line_action">アクション付きの短い持続時間</string>
    <string name="snackbar_description_single_line_action_custom_view">アクションとミディアムカスタムビューの短い期間</string>
    <string name="snackbar_description_single_line_custom_text_color">カスタマイズされたテキストの色による短い期間</string>
    <string name="snackbar_description_multiline">長期間</string>
    <string name="snackbar_description_multiline_custom_view">小さなカスタムビューで長時間</string>
    <string name="snackbar_description_multiline_action">アクションとテキストの更新による無期限の期間</string>
    <string name="snackbar_description_multiline_action_custom_view">アクションとミディアムカスタムビューの短い期間</string>
    <string name="snackbar_description_multiline_action_long">短いアクション テキスト付きの短い期間</string>
    <string name="snackbar_description_announcement">短期間</string>
    <string name="snackbar_description_updated">このテキストは更新されました。</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">スナックバーを表示</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">一重線</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">2 本線</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">発表スタイル</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">プライマリ スタイル</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">ライトスタイル</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">警告スタイル</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">デンジャースタイル</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">ホーム</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">郵便物</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">設定</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">通知</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">表示数を増やす</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">文字の配置</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">垂直方向</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">水平方向</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">テキストなし</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">タブ項目</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">タイトル</string>
    <string name="cell_sample_description">説明</string>
    <string name="calculate_cells">100 個のセルを読み込み/計算する</string>
    <string name="calculate_layouts">100 個のレイアウトを読み込み/計算する</string>
    <string name="template_list">テンプレート リスト</string>
    <string name="regular_list">レギュラー リスト</string>
    <string name="cell_example_title">タイトル: セル</string>
    <string name="cell_example_description">説明: タップして向きを変更します</string>
    <string name="vertical_layout">縦レイアウト</string>
    <string name="horizontal_layout">横レイアウト</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">標準タブ 2 セグメント</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">標準タブ 3 セグメント</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">標準タブ 4 セグメント</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">ページャー付きの標準タブ</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">タブの切り替え</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">丸薬タブ</string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">タップしてツールチップを表示</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">タップしてカスタム カレンダーのツールチップを表示</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">[カスタム カラー ツールチップ] をタップします</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">タップしてツールチップ内を閉じる</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">タップしてカスタム ビューのツールチップを表示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">上部のカスタム カラー ツールチップ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">10dp の offsetX を使用した上端のツールチップ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">ボトム スタートのツールチップ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">10dp の offsetY を使用した下端のツールチップ</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">ツールチップ内で閉じる</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">ツールチップを閉じました</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">標題はライト 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">タイトル 1 はミディアム 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">タイトル 2 はレギュラー 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">見出しは標準 18sp です</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">小見出し 1 は通常の 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">小見出し 2 は中 16sp です</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">ボディ 1 はレギュラー 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">ボディ 2 はミディアム 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">キャプションは標準 12sp です</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK バージョン: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">アイテムの %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">フォルダー</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">クリック数</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">足場</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB 展開済み</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB 折りたたみ済み</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">クリックして最新の情報に更新</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">引き出しを開ける</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">メニュー アイテム</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">オフセット X (in dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">オフセット Y (in dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">コンテンツ テキスト</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">コンテンツ テキストの繰り返し</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">メニューの幅はコンテンツ テキストに対して変更されます。最大値
        幅は画面サイズの75%に制限されています。コンテンツの左右の余白はトークンによって管理されます。同じコンテンツ テキストが繰り返し異なります
        高さ。</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">メニューを開く</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">ベーシックカード</string>
    <!-- UI Label for Card -->
    <string name="file_card">ファイルのカード</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">お知らせカード</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">ランダム UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">オプション</string>
    <!-- UI Label for Title -->
    <string name="card_title">タイトル</string>
    <!-- UI Label for text -->
    <string name="card_text">テキスト</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">サブ テキスト</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">このバナーのセカンダリ コピーは、必要に応じて 2 行に折り返すことができます。</string>
    <!-- UI Label Button -->
    <string name="card_button">ボタン</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">ダイアログを表示</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">外側をクリックしてダイアログを閉じる</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">バック プレス時にダイアログを閉じる</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">ダイアログを閉じました</string>
    <!-- UI Label Cancel -->
    <string name="cancel">キャンセル</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">ダイアログは、ユーザーに決定を求めたり、追加情報を入力したりするための小さなウィンドウです。</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">引き出しを開ける</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">引き出しを拡張する</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">ドロワーを閉じる</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">ドロワーの種類の選択</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">上</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">ドロワー全体が表示可能な領域に表示されます。</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">下</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">ドロワー全体が表示可能な領域に表示されます。モーション スクロール コンテンツを上にスワイプします。 拡張可能なドロワーは、ドラッグ ハンドルを使用して拡張します。</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">左スライド オーバー</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">ドロワーは左側から表示可能な領域にスライドします。</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">右スライド オーバー</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">ドロワーは右側から表示可能な領域にスライドします。</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">下スライド オーバー</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">ドロワーは、画面の下部から表示可能な領域にスライドします。拡張可能なドロワーを上にスワイプすると、残りの部分が表示領域に移動してスクロールします。</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim Visible</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">ドロワー コンテンツの選択</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">全画面表示サイズのスクロール可能なコンテンツ</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">画面の半分を超えるコンテンツ</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">画面の半分未満のコンテンツ</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">動的サイズのコンテンツ</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">入れ子になったドロワーのコンテンツ</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">拡張可能</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">開いている状態をスキップする</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Scrim のクリック時の無視を防止する</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">ハンドルの表示</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">タイトル</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">ツールチップのテキスト</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">タップしてカスタム コンテンツ ツールチップを表示</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">トップスタート</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">最上部</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">ボトム スタート</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">最下部</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">中央揃え </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">カスタム センター</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">リリースノートの最新情報については、</string>
    <string name="click_here">ここをクリックしてください。</string>
    <string name="open_source_cross_platform">オープンソースのクロスプラットフォーム設計システム。</string>
    <string name="intuitive_and_powerful">直感的 &amp; パワフル。</string>
    <string name="design_tokens">デザイン トークン</string>
    <string name="release_notes">リリース ノート</string>
    <string name="github_repo">GitHub リポジトリ</string>
    <string name="github_repo_link">GitHub リポジトリ リンク</string>
    <string name="report_issue">問題の報告</string>
    <string name="v1_components">V1 コンポーネント</string>
    <string name="v2_components">V2 コンポーネント</string>
    <string name="all_components">すべて</string>
    <string name="fluent_logo">Fluentロゴ</string>
    <string name="new_badge">新規</string>
    <string name="modified_badge">更新日時</string>
    <string name="api_break_badge">API ブレイク</string>
    <string name="app_bar_more">表示数を増やす</string>
    <string name="accent">アクセント</string>
    <string name="appearance">外観</string>
    <string name="choose_brand_theme">ブランドのテーマを選択してください:</string>
    <string name="fluent_brand_theme">Fluentブランド</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">外観の選択</string>
    <string name="appearance_system_default">既定のシステム設定</string>
    <string name="appearance_light">淡色</string>
    <string name="appearance_dark">濃色</string>
    <string name="demo_activity_github_link">デモアクティビティ GitHub リンク</string>
    <string name="control_tokens_details">コントロール トークンの詳細</string>
    <string name="parameters">パラメーター</string>
    <string name="control_tokens">コントロール トークン</string>
    <string name="global_tokens">グローバル トークン</string>
    <string name="alias_tokens">エイリアス トークン</string>
    <string name="sample_text">テキスト</string>
    <string name="sample_icon">サンプル アイコン</string>
    <string name="color">色</string>
    <string name="neutral_color_tokens">ニュートラル カラー トークン</string>
    <string name="font_size_tokens">フォントサイズトークン</string>
    <string name="line_height_tokens">行の高さのトークン</string>
    <string name="font_weight_tokens">フォント ウェイト トークン</string>
    <string name="icon_size_tokens">アイコンサイズトークン</string>
    <string name="size_tokens">サイズ トークン</string>
    <string name="shadow_tokens">シャドウトークン</string>
    <string name="corner_radius_tokens">コーナー半径トークン</string>
    <string name="stroke_width_tokens">ストローク幅トークン</string>
    <string name="brand_color_tokens">ブランド カラー トークン</string>
    <string name="neutral_background_color_tokens">ニュートラル 背景色 トークン</string>
    <string name="neutral_foreground_color_tokens">ニュートラル前景色トークン</string>
    <string name="neutral_stroke_color_tokens">ニュートラル ストローク カラートークン</string>
    <string name="brand_background_color_tokens">ブランド背景色のトークン</string>
    <string name="brand_foreground_color_tokens">ブランド 前景色 トークン</string>
    <string name="brand_stroke_color_tokens">ブランド ストローク カラー トークン</string>
    <string name="error_and_status_color_tokens">エラーとステータスの色のトークン</string>
    <string name="presence_tokens">プレゼンス カラー トークン</string>
    <string name="typography_tokens">タイポグラフィー トークン</string>
    <string name="unspecified">指定なし</string>

</resources>