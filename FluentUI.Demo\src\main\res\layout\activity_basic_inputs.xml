<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:divider="@drawable/demo_divider"
    android:orientation="vertical"
    android:padding="@dimen/default_layout_margin"
    android:showDividers="middle">

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:accessibilityHeading="true"
        android:text="@string/basic_input_activity_heading" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:layout_marginBottom="@dimen/default_layout_margin"
        android:background="@drawable/ms_row_divider" />

    <!--For the basic button, style can be set in the app theme.-->

    <com.microsoft.fluentui.widget.Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/button" />

    <com.microsoft.fluentui.widget.Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:enabled="false"
        android:text="@string/button_disabled"  />

    <!--For other button variants, style can be set in the widget.-->

    <com.microsoft.fluentui.widget.Button
        style="@style/Widget.FluentUI.Button.Borderless"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/button_borderless" />

    <com.microsoft.fluentui.widget.Button
        style="@style/Widget.FluentUI.Button.Borderless"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:enabled="false"
        android:text="@string/button_borderless_disabled" />

    <com.microsoft.fluentui.widget.Button
        style="@style/Widget.FluentUI.Button.Large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/button_large" />

    <com.microsoft.fluentui.widget.Button
        style="@style/Widget.FluentUI.Button.Large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:enabled="false"
        android:text="@string/button_large_disabled" />

    <com.microsoft.fluentui.widget.Button
        style="@style/Widget.FluentUI.Button.Outlined"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/button_outlined" />

    <com.microsoft.fluentui.widget.Button
        style="@style/Widget.FluentUI.Button.Outlined"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:enabled="false"
        android:text="@string/button_outlined_disabled" />
</LinearLayout>