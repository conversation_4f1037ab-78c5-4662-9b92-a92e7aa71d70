<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Основная</string>
    <string name="fluentui_secondary">Дополнительная</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Закрыть</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Выбрано</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Не выбрано</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Значок</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Значок</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Отключено</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Управляющая кнопка</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Включено</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Закрыть лист</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Закрыть</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Отмена</string>
    <!--name of the icon -->
    <string name="fluentui_search">Поиск</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Микрофон</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Удалить текст</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Назад</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Активно</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Отключено</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Нейтрально</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Торговая марка</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Контрастность</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Акцент</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Предупреждение</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Опасно</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Произошла ошибка</string>
    <string name="fluentui_error">Ошибка</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Подсказка</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Chevron</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Структура</string>

    <string name="fluentui_action_button_icon">Значок управляющей кнопки</string>
    <string name="fluentui_center">Выравнивание текста по центру</string>
    <string name="fluentui_accessory_button">Кнопки периферийных устройств</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Переключатель</string>
    <string name="fluentui_label">Метка</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Развернуто</string>
    <string name="fluentui_collapsed">Свернуто</string>

    <!--types of control -->
    <string name="fluentui_large">Большой</string>
    <string name="fluentui_medium">Средний</string>
    <string name="fluentui_small">Небольшой</string>
    <string name="fluentui_password_mode">Режим пароля</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Подзаголовок</string>
    <string name="fluentui_assistive_text">Вспомогательный текст</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Название</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Короткое</string>"
    <string name="fluentui_long">Длинное</string>"
    <string name="fluentui_indefinite">Бессрочно</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Нажата кнопка</string>
    <string name="fluentui_dismissed">Закрыто</string>
    <string name="fluentui_timeout">Время ожидания истекло</string>
    <string name="fluentui_left_swiped">Прокручено влево</string>
    <string name="fluentui_right_swiped">Прокручено вправо</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Текст</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Число</string>
    <string name="fluentui_keyboard_phone">Телефон</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Адрес электронной почты</string>
    <string name="fluentui_keyboard_password">Пароль</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Десятичное число</string>
</resources>