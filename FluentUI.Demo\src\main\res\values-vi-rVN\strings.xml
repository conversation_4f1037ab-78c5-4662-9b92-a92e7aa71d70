<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Bản demo giao diện người dùng Fluent</string>
    <string name="app_title">Giao diện người dùng Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Đã chọn %s</string>
    <string name="app_modifiable_parameters">Tham số có thể sửa đổi</string>
    <string name="app_right_accessory_view">Dạng xem ngoại vi bên phải</string>

    <string name="app_style">Kiểu</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Đã nhấn vào biểu tượng</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Bắt đ<PERSON>u bản demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Quay vòng</string>
    <string name="actionbar_icon_radio_label">Biểu tượng</string>
    <string name="actionbar_basic_radio_label">Cơ bản</string>
    <string name="actionbar_position_bottom_radio_label">Đáy</string>
    <string name="actionbar_position_top_radio_label">Cao nhất</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Loại Thanh_hành_động</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Vị trí Thanh_hành_động</string>

    <!--AppBar-->
    <string name="app_bar_style">Kiểu Thanh_ứng_dụng</string>
    <string name="app_bar_subtitle">Phụ đề</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Đường viền dưới cùng</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Đã bấm vào biểu tượng điều hướng.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Cờ</string>
    <string name="app_bar_layout_menu_settings">Cài đặt</string>
    <string name="app_bar_layout_menu_search">Tìm kiếm</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Hành vi cuộn: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Bật/tắt hành vi cuộn</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Bật/tắt biểu tượng điều hướng</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Hiển thị hình đại diện</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Hiển thị biểu tượng Trở về</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ẩn biểu tượng</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Hiển thị biểu tượng</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Bật/tắt kiểu bố trí thanh tìm kiếm</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Hiển thị dưới dạng xem ngoại vi</string>
    <string name="app_bar_layout_searchbar_action_view_button">Hiển thị theo dạng xem hành động</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Bật/tắt giữa các chủ đề (tái tạo hoạt động)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Bật/tắt chủ đề</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Mục</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Nội dung có thể cuộn thêm</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Kiểu vòng tròn</string>
    <string name="avatar_style_square">Kiểu hình vuông</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Lớn</string>
    <string name="avatar_size_medium">Trung bình</string>
    <string name="avatar_size_small">Nhỏ</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Cực cực lớn</string>
    <string name="avatar_size_xlarge_accessibility">Cực lớn</string>
    <string name="avatar_size_xsmall_accessibility">Cực nhỏ</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Hình đại diện được hiển thị tối đa</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Số lượng hình đại diện tràn</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Loại viền</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Nhóm hình đại diện có bộ OverflowAvatarCount sẽ không tuân thủ Hình đại diện được hiển thị tối đa.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Xếp chồng mặt</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Chất đống mặt</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Đã bấm vào tràn</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Đã bấm vào AvatarView ở chỉ mục %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Biểu tượng thông báo</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Chấm</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Danh sách</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Ký tự</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Ảnh</string>
    <string name="bottom_navigation_menu_item_news">Tin tức</string>
    <string name="bottom_navigation_menu_item_alerts">Cảnh báo</string>
    <string name="bottom_navigation_menu_item_calendar">Lịch</string>
    <string name="bottom_navigation_menu_item_team">Nhóm</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Bật/tắt nhãn</string>
    <string name="bottom_navigation_three_menu_items_button">Hiển thị ba mục menu</string>
    <string name="bottom_navigation_four_menu_items_button">Hiển thị bốn mục menu</string>
    <string name="bottom_navigation_five_menu_items_button">Hiển thị năm mục menu</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Nhãn %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Chân_trang_tính</string>
    <string name="bottom_sheet_dialog">Hộp_thoại_chân_trang_tính</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Bật Trượt nhanh xuống để bỏ</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Hiển thị với các mục dòng đơn</string>
    <string name="bottom_sheet_with_double_line_items">Hiển thị với các mục dòng kép</string>
    <string name="bottom_sheet_with_single_line_header">Hiển thị với tiêu đề dòng đơn</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Hiển thị với tiêu đề dòng kép và thanh chia</string>
    <string name="bottom_sheet_dialog_button">Hiển thị</string>
    <string name="drawer_content_desc_collapse_state">Bung rộng</string>
    <string name="drawer_content_desc_expand_state">Thu nhỏ</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Bấm vào %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Bấm và giữ %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Bấm vào Bỏ qua</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Chèn mục</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Cập nhật mục</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Bỏ qua</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Thêm</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Đề cập đến</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Đậm</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Nghiêng</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Gạch dưới</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Gạch ngang chữ</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Hoàn tác</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Làm lại</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Dấu đầu dòng</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Danh sách</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Liên kết</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Đang cập nhật mục</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Giãn cách</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Bỏ qua vị trí</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">BẮT ĐẦU</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">KẾT THÚC</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Không gian nhóm</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Không gian mục</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Cờ</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Đã bấm vào mục Gắn cờ</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Trả lời</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Đã bấm vào mục Trả lời</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Chuyển tiếp</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Đã bấm vào mục Chuyển tiếp</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Xóa</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Đã bấm vào mục Xóa</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Hình đại diện</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Camera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Chụp ảnh</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Đã bấm vào mục Camera</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Bộ sưu tập</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Xem ảnh của bạn</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Đã bấm vào mục Bộ sưu tập</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Video</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Phát video của bạn</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Đã bấm vào mục Video</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Quản lý</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Quản lý thư viện phương tiện của bạn</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Đã bấm vào mục Quản lý</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Thao tác trên email</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Tài liệu</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Cập nhật gần nhất lúc 2:14 CH</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Chia sẻ</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Đã bấm vào mục Chia sẻ</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Di chuyển</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Đã bấm vào mục Di chuyển</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Xóa</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Đã bấm vào mục Xóa</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Thông tin</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Đã bấm vào mục Thông tin</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Đồng hồ</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Đã bấm vào mục Đồng hồ</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Báo thức</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Đã bấm vào mục Báo thức</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Múi giờ</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Đã bấm vào mục Múi giờ</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Các dạng xem khác nhau của nút</string>
    <string name="button">Nút</string>
    <string name="buttonbar">Thanh nút</string>
    <string name="button_disabled">Ví dụ về nút Đã tắt</string>
    <string name="button_borderless">Ví dụ về nút không viền</string>
    <string name="button_borderless_disabled">Ví dụ về nút Đã tắt không viền</string>
    <string name="button_large">Ví dụ về nút lớn</string>
    <string name="button_large_disabled">Ví dụ về nút Đã tắt lớn</string>
    <string name="button_outlined">Ví dụ về nút có viền</string>
    <string name="button_outlined_disabled">Ví dụ về nút Đã tắt có viền</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Chọn ngày tháng</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">Trình_chọn_ngày_tháng</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Ngày đơn</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Chưa chọn ngày tháng nào</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Hiển thị trình chọn ngày tháng</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Hiển thị bộ chọn ngày giờ với tab ngày đã chọn</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Hiển thị bộ chọn ngày giờ với tab thời gian đã chọn</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Hiển thị bộ chọn ngày giờ</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Phạm vi ngày</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Bắt đầu:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Kết thúc:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Chưa chọn bắt đầu nào</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Chưa chọn kết thúc nào</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Chọn ngày bắt đầu</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Chọn ngày kết thúc</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Phạm vi ngày giờ</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Chọn phạm vi ngày giờ</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">Hộp_thoại_trình_chọn_ngày_tháng</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Hiển thị hộp thoại</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Hiển thị ngăn</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Hiển thị hộp thoại ngăn</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Không có hộp thoại ở đáy mờ dần</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Hiển thị ngăn trên cùng</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Không có hộp thoại trên cùng mờ dần</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Hiển thị hộp thoại trên cùng dạng xem neo</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Không hiển thị hộp thoại trên cùng của tiêu đề</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button">Hiển thị dưới hộp thoại trên cùng của tiêu đề</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Hiển thị ngăn bên phải</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Hiển thị ngăn bên trái</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Tiêu đề, văn bản chính</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Phụ đề, văn bản cấp hai</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Văn bản phụ đề tùy chỉnh</string>
    <!-- Footer -->
    <string name="list_item_footer">Chân trang, văn bản cấp ba</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Danh sách dòng đơn với văn bản tiêu đề phụ màu xám</string>
    <string name="list_item_sub_header_two_line">Danh sách hai dòng</string>
    <string name="list_item_sub_header_two_line_dense">Danh sách hai dòng có giãn cách dày đặc</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Danh sách hai dòng với dạng xem phụ đề phụ tùy chỉnh</string>
    <string name="list_item_sub_header_three_line">Danh sách ba dòng với văn bản tiêu đề phụ màu đen</string>
    <string name="list_item_sub_header_no_custom_views">Mục danh sách không có dạng xem tùy chỉnh</string>
    <string name="list_item_sub_header_large_header">Mục danh sách với các dạng xem tùy chỉnh lớn</string>
    <string name="list_item_sub_header_wrapped_text">Mục danh sách có văn bản được xuống dòng tự động</string>
    <string name="list_item_sub_header_truncated_text">Mục danh sách với văn bản bị cắt bớt</string>
    <string name="list_item_sub_header_custom_accessory_text">Hành động</string>
    <string name="list_item_truncation_middle">Cắt bớt ở giữa.</string>
    <string name="list_item_truncation_end">Kết thúc quá trình cắt bớt.</string>
    <string name="list_item_truncation_start">Bắt đầu cắt bớt.</string>
    <string name="list_item_custom_text_view">Giá trị</string>
    <string name="list_item_click">Bạn đã bấm vào mục danh sách.</string>
    <string name="list_item_click_custom_accessory_view">Bạn đã bấm vào dạng xem ngoại vi tùy chỉnh.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Bạn đã bấm vào dạng xem ngoại vi tùy chỉnh tiêu đề phụ.</string>
    <string name="list_item_more_options">Tùy chọn khác</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Chọn</string>
    <string name="people_picker_select_deselect_example">Chọn Bỏ chọn</string>
    <string name="people_picker_none_example">Không có</string>
    <string name="people_picker_delete_example">Xóa</string>
    <string name="people_picker_custom_persona_description">Ví dụ này hiển thị cách tạo đối tượng IPersona tùy chỉnh.</string>
    <string name="people_picker_dialog_title_removed">Bạn đã loại bỏ một nhân vật:</string>
    <string name="people_picker_dialog_title_added">Bạn đã thêm một nhân vật:</string>
    <string name="people_picker_drag_started">Đã bắt đầu kéo</string>
    <string name="people_picker_drag_ended">Đã kết thúc kéo</string>
    <string name="people_picker_picked_personas_listener">Người nghe nhân vật</string>
    <string name="people_picker_suggestions_listener">Người nghe đề xuất</string>
    <string name="people_picker_persona_chip_click">Bạn đã bấm vào %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="other">%1$s người nhận</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Bung rộng chân trang tính liên tục</string>
    <string name="collapse_persistent_sheet_button">Ẩn chân trang tính liên tục</string>
    <string name="show_persistent_sheet_button"> Hiển thị BottomSheet liên tục</string>
    <string name="new_view">Đây là dạng xem mới</string>
    <string name="toggle_sheet_content">Chuyển đổi nội dung Bottomsheet</string>
    <string name="switch_to_custom_content">Chuyển sang Nội dung tùy chỉnh</string>
    <string name="one_line_content">Nội dung chân trang tính một dòng</string>
    <string name="toggle_disable_all_items">Chuyển đổi Tắt tất cả các mục</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Thêm/Loại bỏ dạng xem</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Thay đổi chiều cao thu gọn</string>
    <string name="persistent_sheet_item_create_new_folder_title">Thư mục mới</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Đã bấm vào mục Thư mục mới</string>
    <string name="persistent_sheet_item_edit_title">Chỉnh sửa</string>
    <string name="persistent_sheet_item_edit_toast">Đã bấm vào mục Chỉnh sửa</string>
    <string name="persistent_sheet_item_save_title">Lưu</string>
    <string name="persistent_sheet_item_save_toast">Đã bấm vào mục Lưu</string>
    <string name="persistent_sheet_item_zoom_in_title">Phóng to</string>
    <string name="persistent_sheet_item_zoom_in_toast">Đã bấm vào mục Phóng to</string>
    <string name="persistent_sheet_item_zoom_out_title">Thu nhỏ</string>
    <string name="persistent_sheet_item_zoom_out_toast">Đã bấm vào mục Thu nhỏ</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Sẵn sàng</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Trình thiết kế</string>
    <string name="persona_subtitle_engineer">Kỹ sư</string>
    <string name="persona_subtitle_manager">Người quản lý</string>
    <string name="persona_subtitle_researcher">Trình nghiên cứu</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (văn bản dài ví dụ để kiểm tra tính năng cắt bớt)</string>
    <string name="persona_view_description_xxlarge">Hình đại diện XXLarge với ba dòng văn bản</string>
    <string name="persona_view_description_large">Hình đại diện lớn với hai dòng văn bản</string>
    <string name="persona_view_description_small">Hình đại diện nhỏ với một dòng văn bản</string>
    <string name="people_picker_hint">Không có gợi ý nào hiển thị</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Chip nhân vật bị vô hiệu hóa</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Chip nhân vật lỗi</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Chip nhân vật không có biểu tượng đóng</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Chip nhân vật cơ bản</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Bạn đã bấm vào Chip nhân vật được chọn.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Chia sẻ</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Theo dõi</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Mời mọi người</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Làm mới trang</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Mở trong trình duyệt</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Đây là Menu Bật lên nhiều dòng. Các đường tối đa được đặt thành hai, phần còn lại của văn bản sẽ cắt bớt.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Mọi tin tức</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Tin tức đã lưu</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Tin tức từ các trang</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Thông báo ngoài giờ làm việc</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Thông báo khi không hoạt động trên máy để bàn</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Bạn đã bấm vào mục:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menu đơn giản</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menu đơn giản2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu với một mục có thể chọn và một thanh chia</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu với tất cả các mục, biểu tượng và văn bản dài có thể chọn</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Hiển thị</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Tiến độ vòng tròn</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Nhỏ</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Trung bình</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Lớn</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Tiến độ tuyến tính</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Vô hạn định</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Có hạn định</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Gọi lại micrô</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Tự động sửa</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Đã nhấn micrô</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Đã nhấn Dạng xem bên phải</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Đã nhấn Tìm kiếm bàn phím</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Hiển thị thanh thông báo</string>
    <string name="fluentui_dismiss_snackbar">Bỏ qua thanh thông báo</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Hành động</string>
    <string name="snackbar_action_long">Thao tác với văn bản dài</string>
    <string name="snackbar_single_line">Thanh thông báo một dòng</string>
    <string name="snackbar_multiline">Đây là thanh đồ ăn vặt nhiều dòng. Các đường tối đa được đặt thành hai, phần còn lại của văn bản sẽ cắt bớt.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Đây là thanh thông báo. Nó được sử dụng để thông báo các tính năng mới.</string>
    <string name="snackbar_primary">Đây là thanh thông báo chính.</string>
    <string name="snackbar_light">Đây là thanh thông báo nhẹ.</string>
    <string name="snackbar_warning">Đây là thanh đồ ăn vặt cảnh báo.</string>
    <string name="snackbar_danger">Đây là một thanh ăn vặt nguy hiểm.</string>
    <string name="snackbar_description_single_line">Thời lượng ngắn</string>
    <string name="snackbar_description_single_line_custom_view">Khoảng thời gian dài với tiến độ vòng tròn ở dạng xem tùy chỉnh nhỏ</string>
    <string name="snackbar_description_single_line_action">Thời lượng ngắn kèm hành động</string>
    <string name="snackbar_description_single_line_action_custom_view">Thời lượng ngắn với hành động và dạng xem tùy chỉnh trung bình</string>
    <string name="snackbar_description_single_line_custom_text_color">Thời lượng ngắn với màu văn bản tùy chỉnh</string>
    <string name="snackbar_description_multiline">Thời lượng dài</string>
    <string name="snackbar_description_multiline_custom_view">Thời lượng dài với dạng xem tùy chỉnh nhỏ</string>
    <string name="snackbar_description_multiline_action">Khoảng thời gian vô hạn định với cập nhật hành động và văn bản</string>
    <string name="snackbar_description_multiline_action_custom_view">Thời lượng ngắn với hành động và dạng xem tùy chỉnh trung bình</string>
    <string name="snackbar_description_multiline_action_long">Thời lượng ngắn với văn bản hành động dài</string>
    <string name="snackbar_description_announcement">Thời lượng ngắn</string>
    <string name="snackbar_description_updated">Văn bản này đã được cập nhật.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Hiển thị thanh thông báo</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Dòng đơn</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Nhiều dòng</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Kiểu thông báo</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Kiểu chính</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Kiểu sáng</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Kiểu cảnh báo</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Kiểu nguy hiểm</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Trang chủ</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Thư</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Cài đặt</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Thông báo</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Xem thêm</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Căn chỉnh văn bản</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Dọc</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Ngang</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Không có văn bản</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Mục Tab</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Tiêu đề</string>
    <string name="cell_sample_description">Mô tả</string>
    <string name="calculate_cells">Tải/tính toán 100 ô</string>
    <string name="calculate_layouts">Tải/tính toán 100 bố cục</string>
    <string name="template_list">Danh sách mẫu</string>
    <string name="regular_list">Danh sách thông thường</string>
    <string name="cell_example_title">Tiêu đề: Ô</string>
    <string name="cell_example_description">Mô tả: Nhấn để thay đổi hướng</string>
    <string name="vertical_layout">Bố trí dọc</string>
    <string name="horizontal_layout">Bố trí ngang</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Tab Tiêu chuẩn 2-Phân đoạn</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Tab chuẩn 3 phân đoạn</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Tab Chuẩn 4 phân đoạn</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Tab Tiêu chuẩn với Máy nhắn tin</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Chuyển tab</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Tab Viên thuốc </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Nhấn để xem mách nước</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Nhấn để xem mách nước lịch tùy chỉnh</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Nhấn vào mách nước màu tùy chỉnh</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Nhấn để bỏ qua mách nước bên trong</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Nhấn để xem mách nước dạng xem tùy chỉnh</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Mách nước màu tùy chỉnh trên cùng</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Mách nước kết thúc trên cùng với 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Mách nước bắt đầu dưới cùng</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Mách nước kết thúc dưới cùng với 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Bỏ qua mách nước bên trong</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Đã bỏ qua mách nước</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Tiêu đề là chữ nhạt 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Tiêu đề 1 là chữ trung bình 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Tiêu đề 2 là 20sp thông thường</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Đầu đề là chữ thường 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Tiêu đề phụ 1 là chữ thường 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Tiêu đề phụ 2 là chữ trung bình 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Phần nội dung 1 là chữ thường 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Phần nội dung 2 là chữ trung bình 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Chú thích là chữ thường 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Phiên bản SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Mục %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Thư mục</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Đã bấm</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Khung</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">Nút hành động nổi</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">Đã mở rộng nút hành động nổi</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">Đã thu gọn nút hành động nổi</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Bấm để làm mới danh sách</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Mở ngăn</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Mục menu</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Độ lệch X (tính bằng dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Độ lệch Y (tính bằng dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Văn bản nội dung</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Văn bản nội dung lặp lại</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Độ rộng menu sẽ thay đổi đối với Văn bản Nội dung. Tối đa
        chiều rộng bị giới hạn 75% kích cỡ màn hình. Lề nội dung từ bên cạnh và dưới cùng được quản lý bởi mã thông báo. Cùng một văn bản nội dung sẽ lặp lại thành khác nhau
        chiều cao.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Mở menu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Thẻ cơ bản</string>
    <!-- UI Label for Card -->
    <string name="file_card">Thẻ tệp</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Thẻ thông báo</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Giao diện người dùng ngẫu nhiên</string>
    <!-- UI Label for Options -->
    <string name="card_options">Tùy chọn</string>
    <!-- UI Label for Title -->
    <string name="card_title">Tiêu đề</string>
    <!-- UI Label for text -->
    <string name="card_text">Văn bản</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Văn bản phụ</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Bản sao phụ cho biểu ngữ này có thể ngắt dòng thành hai dòng nếu cần.</string>
    <!-- UI Label Button -->
    <string name="card_button">Nút</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Hiển thị Hộp thoại</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Bỏ qua hộp thoại khi nhấp vào bên ngoài</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Bỏ qua hộp thoại khi nhấn lại</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Hộp thoại bị loại bỏ</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Hủy bỏ</string>
    <!-- UI Label Ok -->
    <string name="ok">Ok</string>
    <!-- A sample description -->
    <string name="dialog_description">Hộp thoại là một cửa sổ nhỏ nhắc người dùng đưa ra quyết định hoặc nhập thông tin bổ sung.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Mở ngăn</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Mở rộng ngăn</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Đóng ngăn</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Chọn loại ngăn</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Trên cùng</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Toàn bộ ngăn sẽ hiển thị trong khu vực hiển thị.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Dưới cùng</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Toàn bộ ngăn sẽ hiển thị trong khu vực hiển thị. Chuyển động trượt nhanh sẽ cuộn nội dung. Mở rộng ngăn có thể mở rộng thông qua núm điều khiển kéo.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Trượt qua từ bên trái</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Ngăn trượt qua khu vực hiển thị từ bên trái.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Trượt qua từ bên phải</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Ngăn trượt qua khu vực hiển thị từ bên phải.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Trượt qua từ đáy</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Ngăn trượt qua đến khu vực hiển thị từ phía dưới cùng màn hình. Chuyển động trượt nhanh trên ngăn có thể mở rộng kéo phần còn lại của ngăn đến khu vực hiển thị &amp; rồi cuộn.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Hiện lưới chặn</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Chọn nội dung ngăn</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Nội dung có thể cuộn chiếm toàn màn hình</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Nội dung chiếm hơn một nửa màn hình</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Nội dung chiếm dưới một nửa màn hình</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Nội dung có kích cỡ động</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Nội dung ngăn xếp chồng nhau</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Có thể mở rộng</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Bỏ qua trạng thái mở</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Ngăn Bỏ qua khi Bấm Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Hiện núm điều khiển</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Tiêu đề</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Văn bản mách nước</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Bấm để xem mách nước nội dung tùy chỉnh</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Bắt đầu trên cùng </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Kết thúc trên cùng </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Bắt đầu dưới cùng </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Kết thúc dưới cùng </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Giữa </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Trung tâm tùy chỉnh</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Đối với các bản cập nhật về Ghi chú phát hành, </string>
    <string name="click_here">bấm vào đây.</string>
    <string name="open_source_cross_platform">Hệ thống thiết kế đa nền tảng nguồn mở.</string>
    <string name="intuitive_and_powerful">Trực quan &amp; Mạnh mẽ.</string>
    <string name="design_tokens">Token thiết kế</string>
    <string name="release_notes">Ghi chú phát hành</string>
    <string name="github_repo">Kho lưu trữ của GitHub</string>
    <string name="github_repo_link">Liên kết Kho lưu trữ của GitHub</string>
    <string name="report_issue">Báo cáo sự cố</string>
    <string name="v1_components">Cấu phần V1</string>
    <string name="v2_components">Cấu phần V2</string>
    <string name="all_components">Tất cả</string>
    <string name="fluent_logo">Logo Fluent</string>
    <string name="new_badge">Mới</string>
    <string name="modified_badge">Đã sửa đổi</string>
    <string name="api_break_badge">Ngắt API</string>
    <string name="app_bar_more">Xem thêm</string>
    <string name="accent">Điểm nhấn</string>
    <string name="appearance">Giao diện</string>
    <string name="choose_brand_theme">Chọn chủ đề thương hiệu của bạn:</string>
    <string name="fluent_brand_theme">Thương hiệu Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Chọn giao diện</string>
    <string name="appearance_system_default">Mặc định của hệ thống</string>
    <string name="appearance_light">Nhạt</string>
    <string name="appearance_dark">Đậm</string>
    <string name="demo_activity_github_link">Liên kết GitHub hoạt động demo</string>
    <string name="control_tokens_details">Chi tiết token điều khiển</string>
    <string name="parameters">Tham số</string>
    <string name="control_tokens">Token điều khiển</string>
    <string name="global_tokens">Token chung</string>
    <string name="alias_tokens">Token bí danh</string>
    <string name="sample_text">Văn bản</string>
    <string name="sample_icon">Biểu tượng mẫu</string>
    <string name="color">Màu</string>
    <string name="neutral_color_tokens">Token màu trung tính</string>
    <string name="font_size_tokens">Token cỡ phông</string>
    <string name="line_height_tokens">Token chiều cao dòng</string>
    <string name="font_weight_tokens">Token độ đậm của phong chữ</string>
    <string name="icon_size_tokens">Token kích cỡ biểu tượng</string>
    <string name="size_tokens">Token kích cỡ</string>
    <string name="shadow_tokens">Token đổ bóng</string>
    <string name="corner_radius_tokens">Token bán kính góc</string>
    <string name="stroke_width_tokens">Token độ rộng nét vẽ</string>
    <string name="brand_color_tokens">Token màu thương hiệu</string>
    <string name="neutral_background_color_tokens">Token màu nền trung tính</string>
    <string name="neutral_foreground_color_tokens">Token màu tiền cảnh trung tính</string>
    <string name="neutral_stroke_color_tokens">Token màu nét vẽ trung tính</string>
    <string name="brand_background_color_tokens">Token màu nền thương hiệu</string>
    <string name="brand_foreground_color_tokens">Token màu tiền cảnh thương hiệu</string>
    <string name="brand_stroke_color_tokens">Token màu nét vẽ thương hiệu</string>
    <string name="error_and_status_color_tokens">Token màu trạng thái và lỗi</string>
    <string name="presence_tokens">Token màu hiện diện</string>
    <string name="typography_tokens">Token sắp kiểu chữ</string>
    <string name="unspecified">Không xác định</string>

</resources>