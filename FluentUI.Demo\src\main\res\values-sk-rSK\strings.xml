<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Ukážka používateľského rozhrania Fluent</string>
    <string name="app_title">Používateľské rozhranie Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Vybratý používateľ %s</string>
    <string name="app_modifiable_parameters">Upravovateľné parametre</string>
    <string name="app_right_accessory_view">Pravé zobrazenie príslušenstva</string>

    <string name="app_style">Štýl</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Stlačila sa ikona</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Spustiť ukážku</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Karusel</string>
    <string name="actionbar_icon_radio_label">Ikona</string>
    <string name="actionbar_basic_radio_label">Základné</string>
    <string name="actionbar_position_bottom_radio_label">Dole</string>
    <string name="actionbar_position_top_radio_label">Hore</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Typ panela akcií</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Pozícia panela akcií</string>

    <!--AppBar-->
    <string name="app_bar_style">Štýl panela aplikácií</string>
    <string name="app_bar_subtitle">Podtitul</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Dolné orámovanie</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Kliklo sa na ikonu Navigácia.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Príznak</string>
    <string name="app_bar_layout_menu_settings">Nastavenia</string>
    <string name="app_bar_layout_menu_search">Hľadať</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Správanie posúvania: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Prepnúť správanie posúvania</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Prepnúť ikonu navigácie</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Zobraziť avatara</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Zobraziť ikonu Späť</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Skryť ikonu</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Zobraziť ikonu</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Prepnúť štýl rozloženia vyhľadávacieho panela</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Zobraziť ako zobrazenie príslušenstva</string>
    <string name="app_bar_layout_searchbar_action_view_button">Zobraziť ako zobrazenie akcie</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Prepínať medzi motívmi (znova vytvorí aktivitu)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Prepnúť motív</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Položka</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Extra posúvateľný obsah</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Štýl kruhu</string>
    <string name="avatar_style_square">Štýl štvorca</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXL</string>
    <string name="avatar_size_xlarge">XL</string>
    <string name="avatar_size_large">Veľké</string>
    <string name="avatar_size_medium">Stredné</string>
    <string name="avatar_size_small">Malé</string>
    <string name="avatar_size_xsmall">XS</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dvojité veľmi veľké</string>
    <string name="avatar_size_xlarge_accessibility">Veľmi veľké</string>
    <string name="avatar_size_xsmall_accessibility">Veľmi malé</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Max. zobrazený avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Počet avatarov pretečenia</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Typ orámovania</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Skupina avatarov so skupinou overflowAvatarCount nebude dodržiavať maximálny zobrazený avatar.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Vrstva tváre</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Kôpka tváre</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Kliklo sa na pretečenie</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Kliklo sa na AvatarView na indexe %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Štítok s oznámením</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Bodka</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Zoznam</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Postava</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotografie</string>
    <string name="bottom_navigation_menu_item_news">Správy</string>
    <string name="bottom_navigation_menu_item_alerts">Upozornenia</string>
    <string name="bottom_navigation_menu_item_calendar">Kalendár</string>
    <string name="bottom_navigation_menu_item_team">Tím</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Prepnúť označenia</string>
    <string name="bottom_navigation_three_menu_items_button">Zobraziť tri položky ponuky</string>
    <string name="bottom_navigation_four_menu_items_button">Zobraziť štyri položky ponuky</string>
    <string name="bottom_navigation_five_menu_items_button">Zobraziť päť položiek ponuky</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Označenia sú %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Povoliť zrušenie potiahnutím prstom</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Zobraziť s jednoriadkovými položkami</string>
    <string name="bottom_sheet_with_double_line_items">Zobraziť s dvojriadkovými položkami</string>
    <string name="bottom_sheet_with_single_line_header">Zobraziť s jednoriadkovou hlavičkou</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Zobraziť s dvojriadkovou hlavičkou a rozdeľovačmi</string>
    <string name="bottom_sheet_dialog_button">Zobraziť</string>
    <string name="drawer_content_desc_collapse_state">Rozbaliť</string>
    <string name="drawer_content_desc_expand_state">Minimalizovať</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Kliknutie %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Dlhé kliknutie %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Kliknutie na tlačidlo Zrušiť</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Vložiť položku</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Aktualizovať položku</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Zrušiť</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Pridať</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Zmienka</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Tučné</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kurzíva</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Podčiarknuté</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Prečiarknuté</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Zrušiť akciu</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Opakovať</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Odrážka</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Zoznam</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Prepojiť</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Aktualizácia položky</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Rozstup</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Zrušiť pozíciu</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">ZAČIATOK</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">KONIEC</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Priestor medzi skupinami</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Priestor medzi položkami</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Príznak</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Kliklo sa na položku Označiť príznakom</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Odpovedať</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Kliklo sa na položku Odpovedať</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Preposlať</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Kliklo sa na položku Preposlať</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Odstrániť</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Kliklo sa na položku Odstrániť</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Fotoaparát</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Odfotiť</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kliklo sa na položku Fotoaparát</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galéria</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Zobrazujte svoje fotografie</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Kliklo sa na položku Galéria</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videá</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Prehrávajte svoje videá</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Kliklo sa na položku Videá</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Spravovať</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Spravovať knižnicu médií</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Kliklo sa na položku Spravovať</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-mailové akcie</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumenty</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Naposledy aktualizované 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Zdieľať</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Kliklo sa na položku Zdieľať</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Premiestniť</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Kliklo sa na položku Presunúť</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Odstrániť</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Kliklo sa na položku Odstrániť</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informácie</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Kliklo sa na položku Informácie</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Hodiny</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Kliklo sa na položku Hodiny</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Budík</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Kliklo sa na položku Budík</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Časové pásmo</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Kliklo sa na položku Časové pásmo</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Rôzne zobrazenia tlačidla</string>
    <string name="button">Tlačidlo</string>
    <string name="buttonbar">Panel tlačidiel</string>
    <string name="button_disabled">Tlačidlo Zakázané, príklad</string>
    <string name="button_borderless">Tlačidlo Bez orámovania, príklad</string>
    <string name="button_borderless_disabled">Tlačidlo Bez orámovania Zakázané, príklad</string>
    <string name="button_large">Tlačidlo Veľké, príklad</string>
    <string name="button_large_disabled">Tlačidlo Veľké Zakázané, príklad</string>
    <string name="button_outlined">Tlačidlo S obrysom, príklad</string>
    <string name="button_outlined_disabled">Tlačidlo S obrysom Zakázané, príklad</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Vyberte dátum</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">Výber dátumu a času</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Jeden dátum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nie je vybratý žiadny dátum</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Zobraziť výber dátumu</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Zobraziť výber dátumu a času s vybratou kartou dátumu</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Zobraziť výber dátumu a času s vybratou kartou času</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Zobraziť výber dátumu a času</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Rozsah dátumov</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Začiatok:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Koniec:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nie je vybratý žiadny začiatok</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Nie je vybratý žiadny koniec</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Vybrať dátum začiatku</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Vybrať dátum ukončenia</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Rozsah dátumu a času</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Vybrať rozsah dátumu a času</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Zobraziť dialógové okno</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Zobraziť zásuvku</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Zobraziť dialógové okno zásuvky</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Dolné dialógové okno Žiadne postupné zoslabnutie</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Zobraziť hornú zásuvku</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Horné dialógové okno Žiadne postupné zoslabnutie</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Zobraziť horné dialógové okno Ukotviť zobrazenie</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Zobraziť horné dialógové okno Žiaden titul</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Zobraziť horné dialógové okno Pod titulom</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Zobraziť pravú zásuvku</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Zobraziť ľavú zásuvku</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Titul, primárny text</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Podtitul, sekundárny text</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Vlastný text podnadpisu</string>
    <!-- Footer -->
    <string name="list_item_footer">Päta, terciárny text</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Jednoriadkový zoznam so sivým textom vedľajšej hlavičky</string>
    <string name="list_item_sub_header_two_line">Dvojriadkový zoznam</string>
    <string name="list_item_sub_header_two_line_dense">Dvojriadkový zoznam s rozstupom nahusto</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Dvojriadkový zoznam s vlastným zobrazením sekundárneho podtitulu</string>
    <string name="list_item_sub_header_three_line">Trojriadkový zoznam s čiernym textom vedľajšej hlavičky</string>
    <string name="list_item_sub_header_no_custom_views">Položky zoznamu bez vlastných zobrazení</string>
    <string name="list_item_sub_header_large_header">Zoznam položiek s veľkými vlastnými zobrazeniami</string>
    <string name="list_item_sub_header_wrapped_text">Položky zoznamu s obtekaným textom</string>
    <string name="list_item_sub_header_truncated_text">Položky zoznamu so skráteným textom</string>
    <string name="list_item_sub_header_custom_accessory_text">Akcia</string>
    <string name="list_item_truncation_middle">Skrátenie v strede.</string>
    <string name="list_item_truncation_end">Skrátenie na konci.</string>
    <string name="list_item_truncation_start">Skrátenie na začiatku.</string>
    <string name="list_item_custom_text_view">Hodnota</string>
    <string name="list_item_click">Klikli ste na položku zoznamu.</string>
    <string name="list_item_click_custom_accessory_view">Klikli ste na vlastné zobrazenie príslušenstva.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Klikli ste na vedľajšiu hlavičku vlastné zobrazenie príslušenstva.</string>
    <string name="list_item_more_options">Ďalšie možnosti</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Vybrať</string>
    <string name="people_picker_select_deselect_example">VybraťZrušiť výber</string>
    <string name="people_picker_none_example">Žiadne</string>
    <string name="people_picker_delete_example">Odstrániť</string>
    <string name="people_picker_custom_persona_description">V tomto príklade je uvedené, ako vytvoriť vlastný objekt IPersona.</string>
    <string name="people_picker_dialog_title_removed">Odstránili ste persónu:</string>
    <string name="people_picker_dialog_title_added">Pridali ste persónu:</string>
    <string name="people_picker_drag_started">Presunutie sa začalo</string>
    <string name="people_picker_drag_ended">Presunutie sa skončilo</string>
    <string name="people_picker_picked_personas_listener">Poslucháč persón</string>
    <string name="people_picker_suggestions_listener">Poslucháč návrhov</string>
    <string name="people_picker_persona_chip_click">Klikli ste na %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s príjemca</item>
        <item quantity="few">%1$s príjemcovia</item>
        <item quantity="many">%1$s príjemcov</item>
        <item quantity="other">%1$s príjemcov</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Rozbaliť trvalý spodný hárok</string>
    <string name="collapse_persistent_sheet_button"> Skryť trvalý spodný hárok</string>
    <string name="show_persistent_sheet_button"> Zobraziť trvalý spodný hárok</string>
    <string name="new_view">Toto je nové zobrazenie</string>
    <string name="toggle_sheet_content">Prepnúť obsah dolného hárka</string>
    <string name="switch_to_custom_content">Prepnúť na vlastný obsah</string>
    <string name="one_line_content">Obsah jednoriadkového dolného hárka</string>
    <string name="toggle_disable_all_items">Prepnúť vypnutie všetkých položiek</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Pridať alebo odstrániť zobrazenie</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Zmeniť zbalenú výšku</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nový priečinok</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Kliklo sa na položku Nový priečinok</string>
    <string name="persistent_sheet_item_edit_title">Upraviť</string>
    <string name="persistent_sheet_item_edit_toast">Kliklo sa na položku Upraviť</string>
    <string name="persistent_sheet_item_save_title">Uložiť</string>
    <string name="persistent_sheet_item_save_toast">Kliklo sa na položku Uložiť</string>
    <string name="persistent_sheet_item_zoom_in_title">Priblížiť</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Kliklo sa na položku Priblížiť</string>
    <string name="persistent_sheet_item_zoom_out_title">Oddialiť</string>
    <string name="persistent_sheet_item_zoom_out_toast">Kliklo sa na položku Oddialiť</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">K dispozícii</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Návrhár</string>
    <string name="persona_subtitle_engineer">Technik</string>
    <string name="persona_subtitle_manager">Manažér</string>
    <string name="persona_subtitle_researcher">Vyhľadávač</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (príklad dlhého textu na testovanie skrátenia)</string>
    <string name="persona_view_description_xxlarge">XXL avatar s tromi riadkami textu</string>
    <string name="persona_view_description_large">Veľký avatar s dvoma riadkami textu</string>
    <string name="persona_view_description_small">Malý avatar s jedným riadkom textu</string>
    <string name="people_picker_hint">Žiadne so zobrazeným tipom</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Zakázaný čip persóny</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Chyba čipu persóny</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Čip persóny s ikonou nezavretia</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Základný čip persóny</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Klikli ste na vybratý čip persóny.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Zdieľať</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Sledovať</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Pozvať ľudí</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Obnoviť stránku</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Otvoriť v prehliadači</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Toto je kontextová ponuka s viacerými riadkami. Maximálny počet riadkov je nastavený na dva, zvyšné riadky sa skrátia.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Všetky novinky</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Uložené správy</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Správy z lokalít</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Upozorňovať mimo pracovného času</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Upozorniť, keď neaktívny/-a v počítači</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Klikli ste na položku:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Jednoduchá ponuka</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Jednoduchá ponuka2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Ponuka s jednou vybrateľnou položkou a oddeľovačom</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Ponuka so všetkými vybrateľnými položkami, ikonami a dlhým textom</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Zobraziť</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Kruhový priebeh</string>
    <string name="circular_progress_xsmall">XS</string>
    <string name="circular_progress_xsmall_size">20 dp</string>
    <string name="circular_progress_small">Malé</string>
    <string name="circular_progress_small_size">24 dp</string>
    <string name="circular_progress_medium">Stredné</string>
    <string name="circular_progress_medium_size">36 dp</string>
    <string name="circular_progress_large">Veľké</string>
    <string name="circular_progress_large_size">44 dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineárny priebeh</string>
    <string name="linear_progress_size">4 dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Neurčitý</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Určitý</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Vyhľadávací panel</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofón Callback</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Automatické opravy</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Ikona Mikrofón sa stlačila</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Stlačené zobrazenie sprava</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Vyhľadávanie na klávesnici sa stlačilo</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Zobraziť panel</string>
    <string name="fluentui_dismiss_snackbar">Zrušiť panel</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Akcia</string>
    <string name="snackbar_action_long">Akcia dlhého textu</string>
    <string name="snackbar_single_line">Panel s jedným riadkom</string>
    <string name="snackbar_multiline">Toto je viacriadkový snackbar. Maximálny počet riadkov je nastavený na dva, zvyšné riadky sa skrátia.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Toto je denný panel oznámení. Používa sa na komunikáciu nových funkcií.</string>
    <string name="snackbar_primary">Toto je primárny panel.</string>
    <string name="snackbar_light">Toto je svetlý panel.</string>
    <string name="snackbar_warning">Toto je panel výstrahy.</string>
    <string name="snackbar_danger">Toto je panel nebezpečenstva.</string>
    <string name="snackbar_description_single_line">Krátke trvanie</string>
    <string name="snackbar_description_single_line_custom_view">Dlhé trvanie s cyklickým priebehom ako malé vlastné zobrazenie</string>
    <string name="snackbar_description_single_line_action">Krátke trvanie s akciou</string>
    <string name="snackbar_description_single_line_action_custom_view">Krátke trvanie s akciou a stredne veľkým vlastným zobrazením</string>
    <string name="snackbar_description_single_line_custom_text_color">Krátke trvanie s prispôsobenou farbou textu</string>
    <string name="snackbar_description_multiline">Dlhé trvanie</string>
    <string name="snackbar_description_multiline_custom_view">Dlhé trvanie s malým vlastným zobrazením</string>
    <string name="snackbar_description_multiline_action">Neurčité trvanie s aktualizáciami akcií a textu</string>
    <string name="snackbar_description_multiline_action_custom_view">Krátke trvanie s akciou a stredne veľkým vlastným zobrazením</string>
    <string name="snackbar_description_multiline_action_long">Krátke trvanie s textom dlhej akcie</string>
    <string name="snackbar_description_announcement">Krátke trvanie</string>
    <string name="snackbar_description_updated">Tento text bol aktualizovaný.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Zobraziť panel</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Jeden riadok</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Viacriadkové</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Štýl oznámenia</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primárny štýl</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Svetlý štýl</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Štýl výstrahy</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Štýl nebezpečenstva</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Domov</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Pošta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Nastavenia</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Oznámenie</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Viac</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Zarovnanie textu</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Zvislo</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vodorovne</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Žiadny text</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Položky na karte</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Titul</string>
    <string name="cell_sample_description">Popis</string>
    <string name="calculate_cells">Načítať alebo vypočítať 100 buniek</string>
    <string name="calculate_layouts">Načítať alebo vypočítať 100 rozložení</string>
    <string name="template_list">Zoznam šablón</string>
    <string name="regular_list">Bežný zoznam</string>
    <string name="cell_example_title">Titul: Bunka</string>
    <string name="cell_example_description">Popis: Ťuknutím zmeníte orientáciu</string>
    <string name="vertical_layout">Zvislé rozloženie</string>
    <string name="horizontal_layout">Vodorovné rozloženie</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Štandardná karta 2 – segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Štandardná karta 3 – segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Štandardná karta 4 – segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Štandardná karta so stránkovačom</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Prepnúť kartu</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Karta Tabletky</string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Ťuknutím zobrazíte popis</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Ťuknutím zobrazíte popis vlastného kalendára</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Ťuknutím zobrazíte popis vlastnej farby</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Ťuknutím zobrazíte popis zrušenia vnútri</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Ťuknutím zobrazíte popis vlastného zobrazenia</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Popis vlastnej farby hore</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Popis na konci hore s odsadením 10 dpX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Popis na začiatku dolu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Popis na konci dolu s odsadením 10 dpY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Popis zrušenia vnútri</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Popis bol zrušený</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Písmo pre Titulok je Ľahké 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Písmo pre Titul 1 je Stredné 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Písmo pre Titul 2 je Normálne 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Písmo pre Nadpis je Normálne 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Písmo pre Podnadpis 1 je Normálne 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Písmo pre Podnadpis 2 je Stredné 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Písmo pre Telo správy 1 je Pravidelné 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Písmo pre Telo správy 2 je Stredné 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Písmo pre Popis je Pravidelné 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Verzia súpravy SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Položka %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Priečinok</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Kliknuté</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Lešenie</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB – rozbalené</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB – zbalené</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Kliknutím obnovíte zoznam</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Otvoriť zásuvku</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Položka ponuky</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Odchýlka X (v dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Odchýlka Y (v dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Text obsahu</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Opakovať text obsahu</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Šírka ponuky sa zmení vzhľadom na text obsahu. Maximálna
        šírka je obmedzená na 75 % veľkosti obrazovky. Okraj obsahu zo strany a zdola sa riadi tokenom. Rovnaký text obsahu sa bude opakovať, aby sa menila
        výška.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Otvoriť ponuku</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Základná karta</string>
    <!-- UI Label for Card -->
    <string name="file_card">Karta súboru</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Karta oznámenia</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Náhodné používateľské rozhranie</string>
    <!-- UI Label for Options -->
    <string name="card_options">Možnosti</string>
    <!-- UI Label for Title -->
    <string name="card_title">Názov</string>
    <!-- UI Label for text -->
    <string name="card_text">Text</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Podtext</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekundárna kópia tohto banera sa môže v prípade potreby zalomiť na dva riadky.</string>
    <!-- UI Label Button -->
    <string name="card_button">Tlačidlo</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Zobraziť dialógové okno</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Zrušiť dialógové okno pri kliknutí mimo</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Zrušiť dialógové okno pri opätovnom stlačení</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialógové okno bolo zrušené</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Zrušiť</string>
    <!-- UI Label Ok -->
    <string name="ok">Ok</string>
    <!-- A sample description -->
    <string name="dialog_description">Dialógové okno je malé okno, ktoré zobrazí používateľovi výzvu na rozhodnutie alebo zadanie ďalších informácií.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Otvoriť zásuvku</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Rozbaliť zásuvku</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Zavrieť zásuvku</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Vybrať typ zásuvky</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Hore</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Celá zásuvka sa zobrazuje vo viditeľnej oblasti.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Dole</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Celá zásuvka sa zobrazuje vo viditeľnej oblasti. Potiahnutím prstom nahor posúvajte obsah. Rozbaliteľnú zásuvku rozbaľte pomocou rukoväte na presunutie.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Posunúť doľava cez</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Posunutie zásuvky z ľavej strany do viditeľnej oblasti.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Posunúť doprava cez</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Posunutie zásuvky z pravej strany do viditeľnej oblasti.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Posunúť dole cez</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Posunutie zásuvky do viditeľnej oblasti z dolnej časti obrazovky. Potiahnutím prstom nahor po rozbaliteľnej zásuvke presuňte zvyšok časti do viditeľnej oblasti a potom ho posuňte.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Viditeľný scrim</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Vybrať obsah zásuvky</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Obsah s možnosťou posúvania na celú obrazovku</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Viac ako polovica obsahu obrazovky</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Menej ako polovica obsahu obrazovky</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Obsah s dynamickou veľkosťou</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Vnorený obsah zásuvky</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Rozbaliteľné</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Vynechať stav otvorenia</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Zabrániť zrýchľovaniu pri kliknutí na Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Zobraziť rukoväť</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Názov</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Text názvu tlačidla</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Ťuknutím zobrazíte popis vlastného obsahu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Začiatok hore </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Horný koniec </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Dolný začiatok </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Dolný koniec </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Na stred </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Vlastný stred</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Ak chcete aktualizovať poznámky k vydaniu, </string>
    <string name="click_here">kliknite sem.</string>
    <string name="open_source_cross_platform">Open source návrhový systém pre rôzne platformy.</string>
    <string name="intuitive_and_powerful">Intuitívne a výkonné.</string>
    <string name="design_tokens">Tokeny návrhu</string>
    <string name="release_notes">Poznámky k vydaniu</string>
    <string name="github_repo">Odkladací priestor GitHub</string>
    <string name="github_repo_link">Prepojenie na odkladací priestor GitHub</string>
    <string name="report_issue">Nahlásiť problém</string>
    <string name="v1_components">Súčasti V1</string>
    <string name="v2_components">Súčasti V2</string>
    <string name="all_components">Všetky</string>
    <string name="fluent_logo">Logo Fluent</string>
    <string name="new_badge">Nové</string>
    <string name="modified_badge">Upravené</string>
    <string name="api_break_badge">Prerušenie rozhrania API</string>
    <string name="app_bar_more">Viac</string>
    <string name="accent">Zvýraznenie</string>
    <string name="appearance">Vzhľad</string>
    <string name="choose_brand_theme">Vyberte motív svojej značky:</string>
    <string name="fluent_brand_theme">Značka Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Vybrať vzhľad</string>
    <string name="appearance_system_default">Predvolené systémové nastavenie</string>
    <string name="appearance_light">Svetlý</string>
    <string name="appearance_dark">Tmavý</string>
    <string name="demo_activity_github_link">Prepojenie na ukážkovú aktivitu v GitHube</string>
    <string name="control_tokens_details">Podrobnosti o tokenoch ovládacieho prvku</string>
    <string name="parameters">Parametre</string>
    <string name="control_tokens">Tokeny ovládacieho prvku</string>
    <string name="global_tokens">Globálne tokeny</string>
    <string name="alias_tokens">Tokeny aliasu</string>
    <string name="sample_text">Text</string>
    <string name="sample_icon">Ikona ukážky</string>
    <string name="color">Farba</string>
    <string name="neutral_color_tokens">Tokeny neutrálnej farby</string>
    <string name="font_size_tokens">Tokeny veľkosti písma</string>
    <string name="line_height_tokens">Tokeny výšky riadka</string>
    <string name="font_weight_tokens">Tokeny hrúbky písma</string>
    <string name="icon_size_tokens">Tokeny veľkosti ikon</string>
    <string name="size_tokens">Tokeny veľkosti</string>
    <string name="shadow_tokens">Tieňové tokeny</string>
    <string name="corner_radius_tokens">Tokeny polomeru rohu</string>
    <string name="stroke_width_tokens">Tokeny šírky ťahu</string>
    <string name="brand_color_tokens">Tokeny farby značky</string>
    <string name="neutral_background_color_tokens">Tokeny neutrálnej farby pozadia</string>
    <string name="neutral_foreground_color_tokens">Tokeny neutrálnej farby popredia</string>
    <string name="neutral_stroke_color_tokens">Tokeny neutrálnej farby ťahu</string>
    <string name="brand_background_color_tokens">Tokeny farby pozadia značky</string>
    <string name="brand_foreground_color_tokens">Tokeny farby popredia značky</string>
    <string name="brand_stroke_color_tokens">Tokeny farby ťahu značky</string>
    <string name="error_and_status_color_tokens">Tokeny chýb a stavu farieb</string>
    <string name="presence_tokens">Tokeny farby prítomnosti</string>
    <string name="typography_tokens">Tokeny typografie</string>
    <string name="unspecified">Neurčené</string>

</resources>