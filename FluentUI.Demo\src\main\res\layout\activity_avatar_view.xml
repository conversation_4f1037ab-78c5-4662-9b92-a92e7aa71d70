<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/default_layout_margin"
    tools:context=".demos.AvatarViewActivity">

    <!--Circle-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/avatar_style_circle"
        android:accessibilityHeading="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:divider="@drawable/demo_divider"
        android:showDividers="middle">

        <!--XXLarge-->

        <TableRow android:id="@+id/avatar_circle_example_xxlarge">

            <TextView
                android:id="@+id/avatar_example_xxlarge_size"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_xxlarge"
                android:contentDescription="@string/avatar_size_xxlarge_accessibility"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xxlarge_photo"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_robert_tolbert"
                app:fluentui_avatarSize="xxlarge" />

        </TableRow>

        <!--XLarge-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_xlarge_size"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_xlarge"
                android:contentDescription="@string/avatar_size_xlarge_accessibility"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xlarge_photo"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarSize="xlarge" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xlarge_initials"
                android:layout_gravity="start"
                app:fluentui_avatarSize="xlarge"
                app:fluentui_name="@string/persona_name_allan_munger" />

        </TableRow>

        <!--Large-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_large_size"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_large"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_large_photo"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarSize="large" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_large_initials"
                android:layout_gravity="start"
                app:fluentui_avatarSize="large"
                app:fluentui_name="@string/persona_email_colin_ballinger" />

        </TableRow>

        <!--Medium-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_medium_size"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_medium"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_medium_photo"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarSize="medium" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_medium_initials"
                android:layout_gravity="start"
                app:fluentui_avatarSize="medium"
                app:fluentui_name="@string/persona_name_wanda_howard" />

        </TableRow>

        <!--Small-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_small_size"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_small"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_small_photo"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarSize="small" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_small_initials"
                android:layout_gravity="start"
                app:fluentui_avatarSize="small"
                app:fluentui_name="@string/persona_name_kristen_patterson" />

        </TableRow>

        <!--XSmall-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_xsmall_size"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_xsmall"
                android:contentDescription="@string/avatar_size_xsmall_accessibility"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xsmall_photo"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_ashley_mccarthy"
                app:fluentui_avatarSize="xsmall" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xsmall_initials"
                android:layout_gravity="start"
                app:fluentui_avatarSize="xsmall"
                app:fluentui_name="@string/persona_name_isaac_fielder" />

        </TableRow>

    </TableLayout>

    <!--Square-->

    <TextView
        style="@style/TextAppearance.FluentUI.Headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:paddingBottom="@dimen/demo_headline_padding_bottom"
        android:text="@string/avatar_style_square"
        android:accessibilityHeading="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/demo_headline_divider_height"
        android:background="@drawable/ms_row_divider" />

    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/default_layout_margin"
        android:divider="@drawable/demo_divider"
        android:showDividers="middle">

        <!--XXLarge-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_xxlarge_size_square"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_xxlarge"
                android:contentDescription="@string/avatar_size_xxlarge_accessibility"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xxlarge_photo_square"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_carlos_slattery"
                app:fluentui_avatarSize="xxlarge"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_carlos_slattery" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xxlarge_initials_square"
                android:layout_gravity="start"
                app:fluentui_avatarSize="xxlarge"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_carole_poland" />

        </TableRow>

        <!--XLarge-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_xlarge_size_square"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_xlarge"
                android:contentDescription="@string/avatar_size_xlarge_accessibility"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xlarge_photo_square"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_daisy_phillips"
                app:fluentui_avatarSize="xlarge"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_daisy_phillips" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xlarge_initials_square"
                android:layout_gravity="start"
                app:fluentui_avatarSize="xlarge"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_daisy_phillips" />

        </TableRow>

        <!--Large-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_large_size_square"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_large"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_large_photo_square"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_robin_counts"
                app:fluentui_avatarSize="large"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_robin_counts" />

            <!--This one sets shape and initials programmatically-->
            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_large_initials_square"
                android:layout_gravity="start"
                app:fluentui_avatarSize="large" />

        </TableRow>

        <!--Medium-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_medium_size_square"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_medium"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_medium_photo_square"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_cecil_folk"
                app:fluentui_avatarSize="medium"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_cecil_folk" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_medium_initials_square"
                android:layout_gravity="start"
                app:fluentui_avatarSize="medium"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_celeste_burton" />

        </TableRow>

        <!--Small-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_small_size_square"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_small"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_small_photo_square"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_elliot_woodward"
                app:fluentui_avatarSize="small"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_elliot_woodward" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_small_initials_square"
                android:layout_gravity="start"
                app:fluentui_avatarSize="small"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_erik_nason"
                app:fluentui_avatarBackgroundColor="@android:color/holo_blue_dark" />

        </TableRow>

        <!--XSmall-->

        <TableRow>

            <TextView
                android:id="@+id/avatar_example_xsmall_size_square"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/avatar_size_text_margin_end"
                android:text="@string/avatar_size_xsmall"
                android:contentDescription="@string/avatar_size_xsmall_accessibility"
                android:textAppearance="@style/TextAppearance.DemoDescription" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xsmall_photo_square"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/default_layout_margin"
                app:fluentui_avatarImageDrawable="@drawable/avatar_henry_brill"
                app:fluentui_avatarSize="xsmall"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_henry_brill" />

            <com.microsoft.fluentui.persona.AvatarView
                android:id="@+id/avatar_example_xsmall_initials_square"
                android:layout_gravity="start"
                app:fluentui_avatarSize="xsmall"
                app:fluentui_avatarStyle="square"
                app:fluentui_name="@string/persona_name_lydia_bauer" />

        </TableRow>

    </TableLayout>

</LinearLayout>