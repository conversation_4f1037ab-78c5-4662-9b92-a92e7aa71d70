<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/activity_tooltip"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/default_layout_margin">

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/tooltip_anchor_top_start"
        style="@style/Demo.Tooltip.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tooltip_custom_button"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/tooltip_anchor_top_end"
        style="@style/Demo.Tooltip.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tooltip_button"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/tooltip_anchor_bottom_start"
        style="@style/Demo.Tooltip.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tooltip_button"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/tooltip_anchor_bottom_end"
        style="@style/Demo.Tooltip.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tooltip_button"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/tooltip_anchor_center"
        style="@style/Demo.Tooltip.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tooltip_center_button"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/tooltip_anchor_custom_view"
        style="@style/Demo.Tooltip.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tooltip_custom_view"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.755" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/tooltip_anchor_calendar_demo"
        style="@style/Demo.Tooltip.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tooltip_calendar_demo_button"
        app:layout_constraintTop_toBottomOf="@+id/tooltip_anchor_top_start"
        app:layout_constraintBottom_toTopOf="@+id/tooltip_anchor_center"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>