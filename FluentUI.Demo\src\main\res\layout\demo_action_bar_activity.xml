<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="UselessParent">

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager1"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.microsoft.fluentui.actionbar.ActionBarLayout
        android:id="@+id/actionbar1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        app:fluentui_type="basic" />

</FrameLayout>