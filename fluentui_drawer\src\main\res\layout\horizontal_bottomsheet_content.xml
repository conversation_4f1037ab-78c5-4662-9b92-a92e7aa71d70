<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/fluentui_persistent_bottomsheet_content_offset"
    android:paddingEnd="@dimen/fluentui_persistent_bottomsheet_content_offset"
    android:orientation="vertical">

    <TextView
        android:id="@+id/header_text"
        style="@style/TextAppearance.FluentUI.PersistentBottomSheetHeading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:accessibilityHeading="true"/>

    <com.microsoft.fluentui.persistentbottomsheet.SheetHorizontalItemList
        android:id="@+id/horizontal_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>
