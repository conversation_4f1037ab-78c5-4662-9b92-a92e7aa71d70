<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI-demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Markerede %s</string>
    <string name="app_modifiable_parameters">Redigerbare parametre</string>
    <string name="app_right_accessory_view">Højre tilbehørsvisning</string>

    <string name="app_style">Typografi</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Der er trykket på ikonet</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Start Demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Karrusel</string>
    <string name="actionbar_icon_radio_label">Ikon</string>
    <string name="actionbar_basic_radio_label">Grundlæggende</string>
    <string name="actionbar_position_bottom_radio_label">Nederst</string>
    <string name="actionbar_position_top_radio_label">Top</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Handlingslinjetype</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Placering af handlingslinje</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar Style</string>
    <string name="app_bar_subtitle">Undertekst</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Nederste kant</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Der blev klikket på et navigationsikon.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Flag</string>
    <string name="app_bar_layout_menu_settings">Indstillinger</string>
    <string name="app_bar_layout_menu_search">Søg</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Rullefunktion: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Slå rullefunktionsmåde til/fra</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Slå navigationsikon til/fra</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Vis avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Vis ikonet Tilbage</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Skjul ikon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Vis ikon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Slå layouttypen for søgepanelet til/fra</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Vis som tilbehørsvisning</string>
    <string name="app_bar_layout_searchbar_action_view_button">Vis som handlingsvisning</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Skift mellem temaer (genopretter aktivitet)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Slå tema til/fra</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Ekstra indhold, der kan rulles i</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Cirkeltypografi</string>
    <string name="avatar_style_square">Firkantet typografi</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Stor</string>
    <string name="avatar_size_medium">Mellem</string>
    <string name="avatar_size_small">Lille</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dobbelt ekstra stor</string>
    <string name="avatar_size_xlarge_accessibility">Ekstra stor</string>
    <string name="avatar_size_xsmall_accessibility">Ekstra lille</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maks. vist avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Antal overløbsavatarer</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Kanttype</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatargruppen med OverflowAvatarCount indstillet vil ikke overholde det maksimale antal viste avatarer.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Ansigtsstak</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Ansigtsstak</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Der blev klikket på overløb</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Der blev klikket på AvatarView ved indeks %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Meddelelsesbadge</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punkt</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Liste</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Tegn</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Billeder</string>
    <string name="bottom_navigation_menu_item_news">Nyheder</string>
    <string name="bottom_navigation_menu_item_alerts">Underretninger</string>
    <string name="bottom_navigation_menu_item_calendar">Kalender</string>
    <string name="bottom_navigation_menu_item_team">Team</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Slå mærkater til/fra</string>
    <string name="bottom_navigation_three_menu_items_button">Vis tre menupunkter</string>
    <string name="bottom_navigation_four_menu_items_button">Vis fire menupunkter</string>
    <string name="bottom_navigation_five_menu_items_button">Vis fem menupunkter</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Mærkater er %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Nederste ark</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Aktivér stryg ned for at afvise</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Vis med elementer på én linje</string>
    <string name="bottom_sheet_with_double_line_items">Vis med elementer med dobbelt linje</string>
    <string name="bottom_sheet_with_single_line_header">Vis med overskrift med enkelt linje</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Vis med dobbeltlinjeoverskrift og skillelinjer</string>
    <string name="bottom_sheet_dialog_button">Vis</string>
    <string name="drawer_content_desc_collapse_state">Udvid</string>
    <string name="drawer_content_desc_expand_state">Minimer</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klik på %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Langt klik %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klik på Afvis</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Indsæt element</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Opdater element</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Afvis</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Tilføj</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Omtale</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Fed</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursiv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Understregning</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Gennemstreget</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Fortryd</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Annuller Fortryd</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Punkttegn</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Liste</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Link</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Opdatering af element</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Afstand</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Afvis position</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">START</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Gruppeområde</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Elementplads</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Flag</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Der blev klikket på Flagelement</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Besvar</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Der blev klikket på et svarelement</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Videresend</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Der blev klikket på Videresend element</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Slet</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Der blev klikket på Slet element</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Tag et foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Der blev klikket på et kameraelement</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galleri</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Få vist dine fotos</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Der blev klikket på et gallerielement</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videoer</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Afspil dine videoer</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Der blev klikket på et videoelement</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Administrer</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Administrer dit mediebibliotek</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Der blev klikket på Administrer element</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Mailhandlinger</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumenter</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Senest opdateret kl. 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Del</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Der blev klikket på Del element</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Flyt</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Der blev klikket på Flyt element</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Slet</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Der blev klikket på Slet element</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Oplysninger</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Der blev klikket på et oplysningselement</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Ur</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Der blev klikket på et urelement</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Der blev klikket på et alarmelement</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Tidszone</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Der blev klikket på et tidszoneelement</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Forskellige visninger af en knap</string>
    <string name="button">Knap</string>
    <string name="buttonbar">Værktøjslinje</string>
    <string name="button_disabled">Eksempel på deaktiveret knap</string>
    <string name="button_borderless">Eksempel på knap uden kanter</string>
    <string name="button_borderless_disabled">Eksempel på deaktiveret knap uden kanter</string>
    <string name="button_large">Eksempel på stor knap</string>
    <string name="button_large_disabled">Eksempel på stor deaktiveret knap</string>
    <string name="button_outlined">Eksempel på fremhævet knap</string>
    <string name="button_outlined_disabled">Eksempel på fremhævet deaktiveret knap</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Vælg en dato</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Enkelt dato</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Der er ikke valgt nogen dato</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Vis datovælger</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Vis dato- og klokkeslætsvælger med fanen dato valgt</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Vis dato- og klokkeslætsvælger med fanen Klokkeslæt valgt</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Vis dato- og klokkeslætsvælger</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datoområde</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Start:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Slut:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Der er ikke valgt nogen start</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Der er ikke valgt nogen ende</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Vælg startdato</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Vælg slutdato</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Dato- og klokkeslætsinterval</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Vælg dato- og klokkeslætsinterval</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Vis dialogboks</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Vis skuffe</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Vis skuffedialogboks</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Dialogboksen Ingen udtoning nederst</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Vis øverste skuffe</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Dialogboksen Ingen udtoning øverst</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Dialogboksen Vis ankervisning øverst</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Vis dialogboksen Ingen titel øverst</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Vis dialogboksen under titel øverst</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Vis højre skuffe</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Vis venstre skuffe</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Titel, primær tekst</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Undertitel, sekundær tekst</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Brugerdefineret undertiteltekst</string>
    <!-- Footer -->
    <string name="list_item_footer">Sidefod, tertiær tekst</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Liste med en enkelt linje med grå underoverskriftstekst</string>
    <string name="list_item_sub_header_two_line">Liste med to linjer</string>
    <string name="list_item_sub_header_two_line_dense">Tolinjet liste med tæt afstand</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Liste med to linjer med brugerdefineret visning af undertekster</string>
    <string name="list_item_sub_header_three_line">Liste med tre linjer med sort underoverskriftstekst</string>
    <string name="list_item_sub_header_no_custom_views">Listeelementer uden brugerdefinerede visninger</string>
    <string name="list_item_sub_header_large_header">Listeelementer med store brugerdefinerede visninger</string>
    <string name="list_item_sub_header_wrapped_text">Listeelementer med ombrudt tekst</string>
    <string name="list_item_sub_header_truncated_text">Listeelementer med afkortet tekst</string>
    <string name="list_item_sub_header_custom_accessory_text">Handling</string>
    <string name="list_item_truncation_middle">Afkortning i midten.</string>
    <string name="list_item_truncation_end">Afslut afkortning.</string>
    <string name="list_item_truncation_start">Start afkortning.</string>
    <string name="list_item_custom_text_view">Værdi</string>
    <string name="list_item_click">Du har klikket på listeelementet.</string>
    <string name="list_item_click_custom_accessory_view">Du har klikket på visningen brugerdefineret tilbehør.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Du har klikket på visningen brugerdefineret tilbehør i underoverskrift.</string>
    <string name="list_item_more_options">Flere indstillinger</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Markér</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Ingen</string>
    <string name="people_picker_delete_example">Slet</string>
    <string name="people_picker_custom_persona_description">Dette eksempel viser, hvordan du opretter et brugerdefineret IPersona-objekt.</string>
    <string name="people_picker_dialog_title_removed">Du fjernede en karakter:</string>
    <string name="people_picker_dialog_title_added">Du har tilføjet en karakter:</string>
    <string name="people_picker_drag_started">Træk startet</string>
    <string name="people_picker_drag_ended">Træk afsluttet</string>
    <string name="people_picker_picked_personas_listener">Lyttefunktion for karakterer</string>
    <string name="people_picker_suggestions_listener">Lyttefunktion til forslag</string>
    <string name="people_picker_persona_chip_click">Du har klikket på %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s modtager</item>
        <item quantity="other">%1$s modtagere</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Udvid fast bundark</string>
    <string name="collapse_persistent_sheet_button"> Skjul fast bundark</string>
    <string name="show_persistent_sheet_button"> Vis vedvarende nederste ark</string>
    <string name="new_view">Dette er en ny visning</string>
    <string name="toggle_sheet_content">Slå indhold i nederste ark til/fra</string>
    <string name="switch_to_custom_content">Skift til brugerdefineret indhold</string>
    <string name="one_line_content">Indhold i bundark med én linje</string>
    <string name="toggle_disable_all_items">Slå Deaktiver alle elementer til/fra</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Tilføj/fjern visning</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Skift skjult højde</string>
    <string name="persistent_sheet_item_create_new_folder_title">Ny mappe</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Der blev klikket på Nyt mappeelement</string>
    <string name="persistent_sheet_item_edit_title">Rediger</string>
    <string name="persistent_sheet_item_edit_toast">Der blev klikket på Rediger element</string>
    <string name="persistent_sheet_item_save_title">Gem</string>
    <string name="persistent_sheet_item_save_toast">Der blev klikket på Gem element</string>
    <string name="persistent_sheet_item_zoom_in_title">Zoom ind</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Der blev klikket på elementet Zoom ind</string>
    <string name="persistent_sheet_item_zoom_out_title">Zoom ud</string>
    <string name="persistent_sheet_item_zoom_out_toast">Der blev klikket på et zoom ud-element</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Tilgængelig</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Daisys</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Tekniker</string>
    <string name="persona_subtitle_manager">Leder</string>
    <string name="persona_subtitle_researcher">Researcher</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (eksempel på lang tekst for at teste afkortning)</string>
    <string name="persona_view_description_xxlarge">XXLarge-avatar med tre tekstlinjer</string>
    <string name="persona_view_description_large">Stor avatar med to tekstlinjer</string>
    <string name="persona_view_description_small">Lille avatar med én tekstlinje</string>
    <string name="people_picker_hint">Ingen med vist tip</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Deaktiveret Persona Chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Error Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Persona Chip uden lukkeikon</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Basic Persona Chip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Du har klikket på en valgt Persona Chip.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Del</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Følg</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Inviter personer</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Opdater side</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Åbn i browser</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Dette er en pop op-menu med flere linjer. Maks. linjer er angivet til to. Resten af teksten afkortes.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Alle nyheder</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Gemte nyheder</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Nyheder fra websteder</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Giv besked uden for arbejdstiden</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Giv besked, når den er inaktiv på skrivebordet</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Du har klikket på elementet:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Enkel menu</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Enkel menu2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu med ét element, der kan vælges, og en skillelinje</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu med alle elementer, ikoner og lang tekst, der kan markeres</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Vis</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Cirkulær fremgang</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Lille</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Mellem</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Stor</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineær status</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Ikke defineret</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Defineret</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Søgelinje</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Microphone Callback</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autokorrektur</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Der blev trykket på mikrofonen</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Der blev trykket på højre visning</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Der blev trykket på tastatursøgning</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Vis snackbar</string>
    <string name="fluentui_dismiss_snackbar">Afvis Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Handling</string>
    <string name="snackbar_action_long">Lang tekst</string>
    <string name="snackbar_single_line">Snackbar med enkelt linje</string>
    <string name="snackbar_multiline">Dette er en snackbar med flere linjer. Maks. linjer er angivet til to. Resten af teksten afkortes.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Dette er en snackbar med meddelelser. Den bruges til at kommunikere nye funktioner.</string>
    <string name="snackbar_primary">Dette er en primær snackbar.</string>
    <string name="snackbar_light">Dette er en let snackbar.</string>
    <string name="snackbar_warning">Dette er en advarselssnack.</string>
    <string name="snackbar_danger">Dette er en snackbar, der udgør en fare.</string>
    <string name="snackbar_description_single_line">Kort varighed</string>
    <string name="snackbar_description_single_line_custom_view">Lang varighed med cirkulær status som lille brugerdefineret visning</string>
    <string name="snackbar_description_single_line_action">Kort varighed med handling</string>
    <string name="snackbar_description_single_line_action_custom_view">Kort varighed med handling og mellemstor brugerdefineret visning</string>
    <string name="snackbar_description_single_line_custom_text_color">Kort varighed med tilpasset tekstfarve</string>
    <string name="snackbar_description_multiline">Lang varighed</string>
    <string name="snackbar_description_multiline_custom_view">Lang varighed med lille brugerdefineret visning</string>
    <string name="snackbar_description_multiline_action">Varighed på ubestemt tid med handlings- og tekstopdateringer</string>
    <string name="snackbar_description_multiline_action_custom_view">Kort varighed med handling og mellemstor brugerdefineret visning</string>
    <string name="snackbar_description_multiline_action_long">Kort varighed med lang handlingstekst</string>
    <string name="snackbar_description_announcement">Kort varighed</string>
    <string name="snackbar_description_updated">Denne tekst er blevet opdateret.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Vis snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Enkeltstreg</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Flere streger</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Meddelelsestypografi</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primær typografi</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Lys typografi</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Advarselstype</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Faretypografi</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Start</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Post</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Indstillinger</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Meddelelse</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mere</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Tekstjustering</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Lodret</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vandret</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Ingen tekst</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Faneelementer</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Titel</string>
    <string name="cell_sample_description">Beskrivelse</string>
    <string name="calculate_cells">Indlæs/beregn 100 celler</string>
    <string name="calculate_layouts">Indlæs/beregn 100 layout</string>
    <string name="template_list">Skabelonliste</string>
    <string name="regular_list">Almindelig liste</string>
    <string name="cell_example_title">Titel: Celle</string>
    <string name="cell_example_description">Beskrivelse: Tryk for at ændre retning</string>
    <string name="vertical_layout">Lodret layout</string>
    <string name="horizontal_layout">Vandret layout</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Segment på standardfane 2</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardfane 3-segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardfane 4-segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardfane med personsøger</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Skift fane</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Fane til knapper </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tryk for at få værktøjstip</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tryk for at få værktøjstip til brugerdefineret kalender</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Tryk på værktøjstip for brugerdefineret farve</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tryk for at afvise inde i værktøjstip</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tryk for at få værktøjstip til brugerdefineret visning</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Værktøjstip for brugerdefineret farve for oven</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Værktøjstip for at slutte for oven med 10dp-forskydningX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Værktøjstip til at starte for neden</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Værktøjstip for at slutte for neden med 10dp-forskydningY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Afvis inde i værktøjstip</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Værktøjstip er afvist</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Overskriften er Light 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Titel 1 er mellem 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Titel 2 er almindelig 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Overskriften er almindelig 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Underoverskrift 1 er Regular 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Underoverskrift 2 er mellem 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Brødtekst 1 er almindelig 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Brødtekst 2 er mellem 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Billedtekst er almindelig 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-version: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Element %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mappe</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Klikket</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Stillads</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB udvidet</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB skjult</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klik for at opdatere liste</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Åbn skuffe</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menupunkt</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Forskydning X (i dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Forskydning Y (i dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Indholdstekst</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Gentag indholdstekst</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menubredden ændres i forhold til indholdstekst. Maks
        bredden er begrænset til 75 % af skærmstørrelsen. Indholdsmargenen fra side og bund styres efter token. Den samme indholdstekst gentages for at variere
        højden.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Åbn menu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Grundlæggende kort</string>
    <!-- UI Label for Card -->
    <string name="file_card">Filkort</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Bekendtgørelseskort</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Tilfældig brugergrænseflade</string>
    <!-- UI Label for Options -->
    <string name="card_options">Indstillinger</string>
    <!-- UI Label for Title -->
    <string name="card_title">Titel</string>
    <!-- UI Label for text -->
    <string name="card_text">Tekst</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Undertekst</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekundær kopi til dette banner kan ombrydes til to linjer, hvis det er nødvendigt.</string>
    <!-- UI Label Button -->
    <string name="card_button">Knap</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Vis dialogboks</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Afvis dialogboks, når der klikkes udenfor</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Afvis dialogboks ved tryk på tilbage</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialogboksen blev afvist</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Annuller</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">En dialogboks er et lille vindue, hvor brugeren bliver bedt om at tage en beslutning eller angive flere oplysninger.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Åbn skuffe</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Udvid skuffe</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Luk skuffe</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Vælg skuffetype</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Top</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Hele skuffen vises i det synlige område.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Nederst</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Hele skuffen vises i det synlige område. Strygebevægelse opad ruller indholdet. Skuffen kan udvides via trækhåndtag.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Skub til venstre over</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Skuffen glider over til synligt område fra venstre side.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Skub til højre over</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Skuffen glider over til synligt område fra højre side.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Skub mod bunden over</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Skuffe slide over til synligt område fra bunden af skærmen. Strygebevægelse opad på skuffe, der kan udvides bringer resten af delen til synligt område og scroll derefter.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim synlig</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Vælg skuffeindhold</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Indhold i fuld skærmsstørrelse, der kan rulles i</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mere end halv skærmindhold</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mindre end halv skærmindhold</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Indhold i dynamisk størrelse</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Indhold i indlejret skuffe</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Kan udvides</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Spring åben tilstand over</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Undgå afvisning ved Scrim-klik</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Vis håndtag</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Titel</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Værktøjstiptekst</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tryk for værktøjstip for brugerdefineret indhold</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Øverste start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Øverste slut </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Nederste start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Nederste slut </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centreret </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Brugerdefineret center</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">For opdateringer om produktbemærkninger, </string>
    <string name="click_here">klik her.</string>
    <string name="open_source_cross_platform">Designsystem med åben kildekode på tværs af platforme.</string>
    <string name="intuitive_and_powerful">Intuitiv og effektiv.</string>
    <string name="design_tokens">Designtokens</string>
    <string name="release_notes">Produktbemærkninger</string>
    <string name="github_repo">GitHub-lager</string>
    <string name="github_repo_link">Link til GitHub-lager</string>
    <string name="report_issue">Rapportér et problem</string>
    <string name="v1_components">V1-komponenter</string>
    <string name="v2_components">V2-komponenter</string>
    <string name="all_components">Alle</string>
    <string name="fluent_logo">Fluent-logo</string>
    <string name="new_badge">Ny</string>
    <string name="modified_badge">Ændret</string>
    <string name="api_break_badge">API-pause</string>
    <string name="app_bar_more">Mere</string>
    <string name="accent">Markeringsfarve</string>
    <string name="appearance">Udseende</string>
    <string name="choose_brand_theme">Vælg dit brandtema:</string>
    <string name="fluent_brand_theme">Fluent-brand</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Vælg udseende</string>
    <string name="appearance_system_default">Systemstandard</string>
    <string name="appearance_light">Lys</string>
    <string name="appearance_dark">Mørk</string>
    <string name="demo_activity_github_link">Link til demoaktivitet for GitHub</string>
    <string name="control_tokens_details">Oplysninger om kontroltokens</string>
    <string name="parameters">Parametre</string>
    <string name="control_tokens">Kontroltokens</string>
    <string name="global_tokens">Globale tokens</string>
    <string name="alias_tokens">Aliastokens</string>
    <string name="sample_text">Tekst</string>
    <string name="sample_icon">Eksempelikon</string>
    <string name="color">Farve</string>
    <string name="neutral_color_tokens">Tokens for neutral farve</string>
    <string name="font_size_tokens">Tokens for skriftstørrelse</string>
    <string name="line_height_tokens">Tokens for linjehøjde</string>
    <string name="font_weight_tokens">Tokens for skrifttykkelse</string>
    <string name="icon_size_tokens">Tokens for ikonstørrelse</string>
    <string name="size_tokens">Størrelsestokens</string>
    <string name="shadow_tokens">Tokens for baggrundsskygge</string>
    <string name="corner_radius_tokens">Tokens for hjørneradius</string>
    <string name="stroke_width_tokens">Tokens for stregbredde</string>
    <string name="brand_color_tokens">Tokens for mærkefarve</string>
    <string name="neutral_background_color_tokens">Tokens for neutral baggrundsfarve</string>
    <string name="neutral_foreground_color_tokens">Tokens for neutral forgrundsfarve</string>
    <string name="neutral_stroke_color_tokens">Tokens for neutral pennestrøgsfarve</string>
    <string name="brand_background_color_tokens">Tokens for baggrundsfarve for brand</string>
    <string name="brand_foreground_color_tokens">Tokens for forgrundsfarve for brand</string>
    <string name="brand_stroke_color_tokens">Tokens for pennestrøgsfarve for brand</string>
    <string name="error_and_status_color_tokens">Tokens for fejl- og statusfarve</string>
    <string name="presence_tokens">Tokens for tilstedeværelsesfarve</string>
    <string name="typography_tokens">Tokens for typografi</string>
    <string name="unspecified">Ikke angivet</string>

</resources>