package com.microsoft.fluentuidemo.icons.listitemicons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush.Companion.linearGradient
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import com.microsoft.fluentuidemo.icons.ListItemIcons

public val ListItemIcons.Folder40: ImageVector
    get() {
        if (_folder40 != null) {
            return _folder40!!
        }
        _folder40 = Builder(name = "Folder40", defaultWidth = 40.0.dp, defaultHeight = 40.0.dp,
                viewportWidth = 40.0f, viewportHeight = 40.0f).apply {
            path(fill = SolidColor(Color(0xFFFFB900)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(19.0f, 10.0f)
                lineTo(16.88f, 7.88f)
                curveTo(16.3179f, 7.3172f, 15.5554f, 7.0007f, 14.76f, 7.0f)
                horizontalLineTo(4.0f)
                curveTo(3.4696f, 7.0f, 2.9609f, 7.2107f, 2.5858f, 7.5858f)
                curveTo(2.2107f, 7.9609f, 2.0f, 8.4696f, 2.0f, 9.0f)
                verticalLineTo(31.0f)
                curveTo(2.0f, 32.1046f, 2.8954f, 33.0f, 4.0f, 33.0f)
                horizontalLineTo(36.0f)
                curveTo(37.1046f, 33.0f, 38.0f, 32.1046f, 38.0f, 31.0f)
                verticalLineTo(12.0f)
                curveTo(38.0f, 10.8954f, 37.1046f, 10.0f, 36.0f, 10.0f)
                horizontalLineTo(19.0f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFD75E)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(19.0f, 10.0f)
                lineTo(16.88f, 12.12f)
                curveTo(16.3179f, 12.6828f, 15.5554f, 12.9993f, 14.76f, 13.0f)
                horizontalLineTo(2.0f)
                verticalLineTo(31.0f)
                curveTo(2.0f, 32.1046f, 2.8954f, 33.0f, 4.0f, 33.0f)
                horizontalLineTo(36.0f)
                curveTo(37.1046f, 33.0f, 38.0f, 32.1046f, 38.0f, 31.0f)
                verticalLineTo(12.0f)
                curveTo(38.0f, 10.8954f, 37.1046f, 10.0f, 36.0f, 10.0f)
                horizontalLineTo(19.0f)
                close()
            }
            path(fill = linearGradient(0.0f to Color(0x02FFFFFF), 1.0f to Color(0x4CFFD75E), start =
                    Offset(2.0f,10.0f), end = Offset(2.0f,33.0f)), stroke = null, strokeLineWidth =
                    0.0f, strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(19.0f, 10.0f)
                lineTo(16.88f, 12.12f)
                curveTo(16.3179f, 12.6828f, 15.5554f, 12.9993f, 14.76f, 13.0f)
                horizontalLineTo(2.0f)
                verticalLineTo(31.0f)
                curveTo(2.0f, 32.1046f, 2.8954f, 33.0f, 4.0f, 33.0f)
                horizontalLineTo(36.0f)
                curveTo(37.1046f, 33.0f, 38.0f, 32.1046f, 38.0f, 31.0f)
                verticalLineTo(12.0f)
                curveTo(38.0f, 10.8954f, 37.1046f, 10.0f, 36.0f, 10.0f)
                horizontalLineTo(19.0f)
                close()
            }
            path(fill = SolidColor(Color(0xFFffffff)), stroke = null, fillAlpha = 0.4f, strokeAlpha
                    = 0.4f, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(16.88f, 12.12f)
                curveTo(16.3179f, 12.6828f, 15.5554f, 12.9993f, 14.76f, 13.0f)
                horizontalLineTo(2.0f)
                verticalLineTo(14.0f)
                horizontalLineTo(15.05f)
                curveTo(15.9764f, 14.008f, 16.8683f, 13.6484f, 17.53f, 13.0f)
                lineTo(20.53f, 10.0f)
                horizontalLineTo(19.0f)
                lineTo(16.88f, 12.12f)
                close()
            }
            path(fill = SolidColor(Color(0xFFE67628)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(4.0f, 33.0f)
                horizontalLineTo(36.0f)
                curveTo(36.8861f, 33.0013f, 37.6675f, 32.4194f, 37.92f, 31.57f)
                curveTo(37.5695f, 31.8466f, 37.1365f, 31.998f, 36.69f, 32.0f)
                horizontalLineTo(3.32f)
                curveTo(2.8701f, 32.0002f, 2.4332f, 31.8487f, 2.08f, 31.57f)
                curveTo(2.3326f, 32.4194f, 3.1139f, 33.0013f, 4.0f, 33.0f)
                close()
            }
        }
        .build()
        return _folder40!!
    }

private var _folder40: ImageVector? = null
