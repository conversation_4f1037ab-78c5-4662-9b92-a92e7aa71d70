<?xml version="1.0" encoding="utf-8"?>
<resources>
  <string name="monday_initial">M</string>
  <string name="tuesday_initial">T</string>
  <string name="wednesday_initial">O</string>
  <string name="thursday_initial">T</string>
  <string name="friday_initial">F</string>
  <string name="saturday_initial">L</string>
  <string name="sunday_initial">S</string>
  <string name="accessibility_goto_next_week">Gå til neste uke</string>
  <string name="accessibility_goto_previous_week">Gå til forrige uke</string>
  <string name="accessibility_today">i dag</string>
  <string name="accessibility_selected">Merket</string>
  <string name="done">Fullført</string>
  <string name="date_time">%1$s, %2$s</string>
  <string name="today">I dag</string>
  <string name="tomorrow">I morgen</string>
  <string name="yesterday">I går</string>
  <string name="date_time_picker_start_time">Starttidspunkt</string>
  <string name="date_time_picker_end_time">Sluttidspunkt</string>
  <string name="date_time_picker_start_date">Startdato</string>
  <string name="date_time_picker_end_date">Sluttdato</string>
  <string name="date_time_picker_choose_time">Velg tidspunkt</string>
  <string name="date_time_picker_choose_date">Velg dato</string>
  <string name="date_time_picker_accessibility_dialog_title">Dato-/klokkeslettvelger</string>
  <string name="date_picker_accessibility_dialog_title">Datovelger</string>
  <string name="date_time_picker_range_accessibility_dialog_title">Dato-/klokkeslettvelger-intervall</string>
  <string name="date_picker_range_accessibility_dialog_title">Datovelger-intervall</string>
  <string name="date_time_picker_accessiblility_start_time">Fane for starttidspunkt</string>
  <string name="date_time_picker_accessiblility_end_time">Fane for sluttidspunkt</string>
  <string name="date_picker_accessiblility_start_date">Fane for startdato</string>
  <string name="date_picker_accessiblility_end_date">Fane for sluttdato</string>
  <string name="date_time_picker_accessibility_close_dialog_button">Lukk dialogboks</string>
  <string name="date_picker_accessibility_increment_month_button">Øk tallet for måneder</string>
  <string name="date_picker_accessibility_next_month_click_action">velg neste måned</string>
  <string name="date_picker_accessibility_decrement_month_button">Reduser tallet for måneder</string>
  <string name="date_picker_accessibility_previous_month_click_action">velg forrige måned</string>
  <string name="date_picker_accessibility_increment_day_button">Øk tallet for dager</string>
  <string name="date_picker_accessibility_next_day_click_action">velg neste dag</string>
  <string name="date_picker_accessibility_decrement_day_button">Reduser tallet for dager</string>
  <string name="date_picker_accessibility_previous_day_click_action">velg forrige dag</string>
  <string name="date_picker_accessibility_increment_year_button">Øk tallet for år</string>
  <string name="date_picker_accessibility_next_year_click_action">velg neste år</string>
  <string name="date_picker_accessibility_decrement_year_button">Reduser tallet for år</string>
  <string name="date_picker_accessibility_previous_year_click_action">velg forrige år</string>
  <string name="date_time_picker_accessibility_increment_date_button">Øk tallet for dato</string>
  <string name="date_picker_accessibility_next_date_click_action">velg neste dato</string>
  <string name="date_time_picker_accessibility_decrement_date_button">Reduser tallet for dato</string>
  <string name="date_picker_accessibility_previous_date_click_action">velg forrige dato</string>
  <string name="date_time_picker_accessibility_increment_hour_button">Øk tallet for timer</string>
  <string name="date_picker_accessibility_next_hour_click_action">velg neste time</string>
  <string name="date_time_picker_accessibility_decrement_hour_button">Reduser tallet for timer</string>
  <string name="date_picker_accessibility_previous_hour_click_action">velg forrige time</string>
  <string name="date_time_picker_accessibility_increment_minute_button">Øk tallet for minutter</string>
  <string name="date_picker_accessibility_next_minute_click_action">velg neste minutt</string>
  <string name="date_time_picker_accessibility_decrement_minute_button">Reduser tallet for minutter</string>
  <string name="date_picker_accessibility_previous_minute_click_action">velg forrige minutt</string>
  <string name="date_time_picker_accessibility_period_toggle_button">Veksle for AM-/PM-periode</string>
  <string name="date_time_picker_accessibility_period_toggle_click_action">veksle for AM-/PM-periode</string>
  <string name="date_time_picker_accessibility_selected_date">%s er valgt</string>
  <string name="calendar_adapter_accessibility_item_selected">valgt</string>
</resources>