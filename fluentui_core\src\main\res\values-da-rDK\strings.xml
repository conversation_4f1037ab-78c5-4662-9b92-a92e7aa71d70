<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary"><PERSON><PERSON><PERSON>r</string>
    <string name="fluentui_secondary"><PERSON><PERSON><PERSON><PERSON>r</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Afvis</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Markeret</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Ikke markeret</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Ikon</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Ikon</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Deaktiveret</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">Handlingsknap</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Aktiveret</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Luk ark</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Luk</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">Annuller</string>
    <!--name of the icon -->
    <string name="fluentui_search">Søg</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Mikrofon</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Ryd tekst</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Tilbage</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Aktiveret</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Deaktiveret</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Neutral</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Brand</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Kontrast</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Markeringsfarve</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Advarsel</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Fare</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Der opstod en fejl</string>
    <string name="fluentui_error">Fejl</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">Tip</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Vinkel</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Disposition</string>

    <string name="fluentui_action_button_icon">Ikon for handlingsknap</string>
    <string name="fluentui_center">Centrer tekst</string>
    <string name="fluentui_accessory_button">Tilbehørsknapper</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Alternativknap</string>
    <string name="fluentui_label">Navngive</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Udvidet</string>
    <string name="fluentui_collapsed">Skjult</string>

    <!--types of control -->
    <string name="fluentui_large">Stor</string>
    <string name="fluentui_medium">Mellem</string>
    <string name="fluentui_small">Lille</string>
    <string name="fluentui_password_mode">Adgangskodetilstand</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Undertekst</string>
    <string name="fluentui_assistive_text">Hjælpetekst</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Titel</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Kort</string>"
    <string name="fluentui_long">Lang</string>"
    <string name="fluentui_indefinite">Uendelig</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Knap trykket ned</string>
    <string name="fluentui_dismissed">Afvist</string>
    <string name="fluentui_timeout">Afbrudt pga. timeout</string>
    <string name="fluentui_left_swiped">Strøget til venstre</string>
    <string name="fluentui_right_swiped">Strøget til højre</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Tekst</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Tal</string>
    <string name="fluentui_keyboard_phone">Telefon</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">Mail</string>
    <string name="fluentui_keyboard_password">Adgangskode</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Decimal</string>
</resources>