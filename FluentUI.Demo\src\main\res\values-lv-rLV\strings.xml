<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI demonstrācija</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Atlasīts: %s</string>
    <string name="app_modifiable_parameters">Modificējami parametri</string>
    <string name="app_right_accessory_view">Labais piederumu skats</string>

    <string name="app_style">Stils</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikona nospiesta</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Sākt demonstrāciju</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Ikona</string>
    <string name="actionbar_basic_radio_label">Pamata</string>
    <string name="actionbar_position_bottom_radio_label">Apakšā</string>
    <string name="actionbar_position_top_radio_label">Augšā</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Darbību joslas tips</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Darbību joslas pozīcija</string>

    <!--AppBar-->
    <string name="app_bar_style">Programmas joslas stils</string>
    <string name="app_bar_subtitle">Subtitrs</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Apakšējā apmale</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Noklikšķināts uz navigācijas ikonas.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Karogs</string>
    <string name="app_bar_layout_menu_settings">Iestatījumi</string>
    <string name="app_bar_layout_menu_search">Meklēt</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Ritināšanas paradumi: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Pārslēgt ritināšanas darbību</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Pārslēgt navigācijas ikonu</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Radīt avatāru</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Rādīt ikonu Atpakaļ</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Slēpt ikonu</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Rādīt ikonu</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Pārslēgt meklēšanas joslas izkārtojuma stilu</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Rādīt kā piederumu skatu</string>
    <string name="app_bar_layout_searchbar_action_view_button">Rādīt kā darbību skatu</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Pārslēgt dizainus (atkārtoti izveido darbību)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Pārslēgt dizainu</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Vienums</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Papildu ritināmais saturs</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Apļu stili</string>
    <string name="avatar_style_square">Kvadrāta stils</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Liels</string>
    <string name="avatar_size_medium">Vidēja</string>
    <string name="avatar_size_small">Mazs</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Divreiz īpaši liels</string>
    <string name="avatar_size_xlarge_accessibility">Īpaši liels</string>
    <string name="avatar_size_xsmall_accessibility">Īpaši mazs</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maks. parādītais avatārs</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Pārpildes avatāru skaits</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Malas tips</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatāru grupa ar iestatītu pārpildes avatarcount neatbilst maksimālajam parādītā avatāram.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Burtveidola grēda</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Burtveidolu kaudze</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Noklikšķināts uz pārpildes</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">Noklikšķināts uz AvatarView indeksa %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Paziņojumu žetons</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punkts</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Saraksts</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Rakstzīme</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotoattēli</string>
    <string name="bottom_navigation_menu_item_news">Jaunumi</string>
    <string name="bottom_navigation_menu_item_alerts">Brīdinājumi</string>
    <string name="bottom_navigation_menu_item_calendar">Kalendārs</string>
    <string name="bottom_navigation_menu_item_team">Komanda</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Pārslēgt etiķetes</string>
    <string name="bottom_navigation_three_menu_items_button">Rādīt trīs izvēlnes vienumus</string>
    <string name="bottom_navigation_four_menu_items_button">Rādīt četrus izvēlnes vienumus</string>
    <string name="bottom_navigation_five_menu_items_button">Rādīt piecus izvēlnes vienumus</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Etiķetes ir %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Apakšējā lapa</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Iespējot pavilkt uz leju, lai noraidītu</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Rādīt ar vienas rindiņas vienumiem</string>
    <string name="bottom_sheet_with_double_line_items">Rādīt ar dubultiem rindiņas vienumiem</string>
    <string name="bottom_sheet_with_single_line_header">Rādīt ar vienas rindiņas galveni</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Rādīt ar dubulto rindas galveni un atdalītājiem</string>
    <string name="bottom_sheet_dialog_button">Rādīt</string>
    <string name="drawer_content_desc_collapse_state">Izvērst</string>
    <string name="drawer_content_desc_expand_state">Minimizēt</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Noklikšķināt uz %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Ilgs klikšķis %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Noklikšķināt uz noraidīt</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Vienuma ievietošana</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Atjaunināt vienumu</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Nerādīt</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Pievienot</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Kur pieminēts</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Treknraksts</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Slīpraksts</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Pasvītrojums</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Pārsvītrojums</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Atsaukt</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Atcelt atsaukšanu</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Aizzīme</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Saraksts</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Saite</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Vienuma atjaunināšana</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Atstarpe</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Noraidīt pozīciju</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">SĀKT</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">BEIGAS</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Grupu telpa</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Vienumu telpa</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Karogs</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Noklikšķināts uz vienuma atzīmēšanas</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Atbildēt</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Noklikšķināts uz atbildes vienuma</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Uz priekšu</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Noklikšķināts uz pārsūtīta vienuma</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Dzēst</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Noklikšķināts uz vienuma dzēšanas</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatārs</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Fotografēt</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Noklikšķināts uz kameras vienuma</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerija</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Skatīt fotoattēlus</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Noklikšķināts uz galerijas vienuma</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Video</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Atskaņojiet savus videoklipus</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Noklikšķināts uz video vienuma</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Pārvaldīt</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Pārvaldiet savu multivides bibliotēku</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Noklikšķināts uz vienuma pārvaldīšanas</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-pasta darbības</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumenti</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Pēdējoreiz atjaunināts plkst. 14.14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Kopīgot</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Noklikšķināts uz kopīgošanas vienuma</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Pārvietot</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Noklikšķināts uz vienuma pārvietošanas</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Dzēst</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Noklikšķināts uz vienuma dzēšanas</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informācija</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Noklikšķināts uz informācijas vienuma</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Pulkstenis</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Noklikšķināts uz pulksteņa vienuma</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Signāls</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Noklikšķināts uz signāla vienuma</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Laika josla</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Noklikšķināts uz laika joslas vienuma</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Dažādi pogas skati</string>
    <string name="button">Poga</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Atspējotas pogas piemērs</string>
    <string name="button_borderless">Bez apmales pogas piemērs</string>
    <string name="button_borderless_disabled">Bez apmales atspējotās pogas piemērs</string>
    <string name="button_large">Lielas pogas piemērs</string>
    <string name="button_large_disabled">Lielas atspējotas pogas piemērs</string>
    <string name="button_outlined">Izceltas pogas piemērs</string>
    <string name="button_outlined_disabled">Izceltas atspējotas pogas piemērs</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Izvēlēties datumu</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Viens datums</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Datums nav izvēlēts</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Rādīt datumu atlasītāju</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Rādīt datuma un laika atlasītāju ar atlasītu datuma cilni</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Rādīt datuma un laika atlasītāju ar atlasītu laika cilni</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Rādīt datuma un laika atlasītāju</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datumu diapazons</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Sākums:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Beigas:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Sākums nav atlasīts</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Beigas nav atlasītas</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Atlasīt sākuma datumu</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Atlasīt beigu datumu</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Datuma un laika diapazons</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Atlasīt datuma un laika diapazonu</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Radīt dialogu</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Rādīt atvilktni</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Rādīt atvilktnes dialogu</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Bez pārejas apakšējā dialogā</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Rādīt augšējo atvilktni</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Dialogs bez pārejas uz augšu</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button">Rādīt enkura skata augšējo dialogu</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Nerādīt virsraksta augšējo dialogu</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button">Rādīt zem virsraksta augšējā dialoga</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Rādīt atvilktni pa labi</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Rādīt atvilktni pa kreisi</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Virsraksts, pamata teksts</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Apakšvirsraksts, sekundārais teksts</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Pielāgots subtitra teksts</string>
    <!-- Footer -->
    <string name="list_item_footer">Kājene, terciārais teksts</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Vienas rindiņas saraksts ar pelēku apakšvirsraksta tekstu</string>
    <string name="list_item_sub_header_two_line">Divrindu saraksts</string>
    <string name="list_item_sub_header_two_line_dense">Divu rindiņu saraksts ar blīvu atstarpi</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Divu rindiņu saraksts ar pielāgotu sekundāro subtitru skatu</string>
    <string name="list_item_sub_header_three_line">Trīs rindiņu saraksts ar melnu apakšvirsraksta tekstu</string>
    <string name="list_item_sub_header_no_custom_views">Uzskaitīti vienumus bez pielāgotiem skatījumiem</string>
    <string name="list_item_sub_header_large_header">Uzskaitīti vienumus ar lieliem pielāgotiem skatiem</string>
    <string name="list_item_sub_header_wrapped_text">Uzskaitīt vienumus ar aplauztu tekstu</string>
    <string name="list_item_sub_header_truncated_text">Uzrakstīt vienumus ar aprautu tekstu</string>
    <string name="list_item_sub_header_custom_accessory_text">Darbība</string>
    <string name="list_item_truncation_middle">Vidējā apraušana.</string>
    <string name="list_item_truncation_end">Rindas apraušana.</string>
    <string name="list_item_truncation_start">Sākt apraušanu.</string>
    <string name="list_item_custom_text_view">Vērtība</string>
    <string name="list_item_click">Jūs noklikšķinājāt uz saraksta vienuma.</string>
    <string name="list_item_click_custom_accessory_view">Jūs noklikšķinājāt uz pielāgotā piederuma skata.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Jūs noklikšķinājāt uz apakšvirsraksta pielāgotā piederuma skata.</string>
    <string name="list_item_more_options">Vairāk opciju</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Atlasīt</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Nav</string>
    <string name="people_picker_delete_example">Dzēst</string>
    <string name="people_picker_custom_persona_description">Šajā piemērā parādīts, kā izveidot pielāgotu IPersona objektu.</string>
    <string name="people_picker_dialog_title_removed">Jūs noņēmāt personāžu:</string>
    <string name="people_picker_dialog_title_added">Jūs pievienojāt personāžu:</string>
    <string name="people_picker_drag_started">Vilkšana sākta</string>
    <string name="people_picker_drag_ended">Vilkšana beidzās</string>
    <string name="people_picker_picked_personas_listener">Personāžu uztvērējs</string>
    <string name="people_picker_suggestions_listener">Ieteikumu uztvērējs</string>
    <string name="people_picker_persona_chip_click">Jūs noklikšķinājāt uz %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="zero">%1$s saņēmēju</item>
        <item quantity="one">%1$s saņēmējs</item>
        <item quantity="other">%1$s saņēmēji</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Izvērst pastāvīgo apakšējo lapu</string>
    <string name="collapse_persistent_sheet_button"> Paslēpt pastāvīgo apakšējo lapu</string>
    <string name="show_persistent_sheet_button">Paslēpt pastāvīgo apakšējo lapu</string>
    <string name="new_view">Šis ir jauns skats</string>
    <string name="toggle_sheet_content">Pārslēgt apakšējās lapas saturu</string>
    <string name="switch_to_custom_content">Pārslēgties uz pielāgoto saturu</string>
    <string name="one_line_content">Vienas rindiņas apakšējās lapas saturs</string>
    <string name="toggle_disable_all_items">Pārslēgt visu vienumu atspējošanu</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Pievienot/noņemt skatu</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Mainīt sakļauto augstumu</string>
    <string name="persistent_sheet_item_create_new_folder_title">Jauna mape</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Noklikšķināts uz jauna mapes vienuma</string>
    <string name="persistent_sheet_item_edit_title">Rediģēt</string>
    <string name="persistent_sheet_item_edit_toast">Noklikšķināts uz vienuma rediģēšanas</string>
    <string name="persistent_sheet_item_save_title">Saglabāt</string>
    <string name="persistent_sheet_item_save_toast">Noklikšķināts uz vienuma saglabāšanas</string>
    <string name="persistent_sheet_item_zoom_in_title">Tuvināt</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Noklikšķināts uz tuvināšanas vienuma</string>
    <string name="persistent_sheet_item_zoom_out_title">Tālināt</string>
    <string name="persistent_sheet_item_zoom_out_toast">Noklikšķināts uz attālināšanas vienuma</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Pieejams</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Alnis Moricss</string>
    <string name="persona_name_amanda_brady">Amanda Briede</string>
    <string name="persona_name_ashley_mccarthy">Anna Mūrniece</string>
    <string name="persona_name_carlos_slattery">Kārlis Slakteris</string>
    <string name="persona_name_carole_poland">Karīna Pirtniece</string>
    <string name="persona_name_cecil_folk">Santa Folka</string>
    <string name="persona_name_celeste_burton">Sintija Bērziņa</string>
    <string name="persona_name_charlotte_waltson">Šarlote Vatsone</string>
    <string name="persona_name_colin_ballinger">Kārlis Bāliņš</string>
    <string name="persona_name_daisy_phillips">Diāna Filipa</string>
    <string name="persona_name_elliot_woodward">Eliots Vakars</string>
    <string name="persona_name_elvia_atkins">Evija Ābola</string>
    <string name="persona_name_erik_nason">Ēriks Naukšēns</string>
    <string name="persona_name_henry_brill">Harijs Brils</string>
    <string name="persona_name_isaac_fielder">Imants Feldmanis</string>
    <string name="persona_name_johnie_mcconnell">Jānis Māliņš</string>
    <string name="persona_name_kat_larsson">Kate Langste</string>
    <string name="persona_name_katri_ahokas">Katrīna Ahoka</string>
    <string name="persona_name_kevin_sturgis">Kārlis Sturģis</string>
    <string name="persona_name_kristen_patterson">Kristena Patersona</string>
    <string name="persona_name_lydia_bauer">Laidiju Bauere</string>
    <string name="persona_name_mauricio_august">Māris Augusts</string>
    <string name="persona_name_miguel_garcia">Ilgonis Purmals</string>
    <string name="persona_name_mona_kane">Monta Kāne</string>
    <string name="persona_name_robin_counts">Rihards Kants</string>
    <string name="persona_name_robert_tolbert">Roberts Tauriņš</string>
    <string name="persona_name_tim_deboer">Toms Darītājs</string>
    <string name="persona_name_wanda_howard">Vanda Hovarda</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Noformētājs</string>
    <string name="persona_subtitle_engineer">Inženieris</string>
    <string name="persona_subtitle_manager">Vadītājs</string>
    <string name="persona_subtitle_researcher">Pētnieks</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (gara teksta piemērs, lai pārbaudītu apraušanu)</string>
    <string name="persona_view_description_xxlarge">XXLarge avatārs ar trim teksta rindiņām</string>
    <string name="persona_view_description_large">Liels avatārs ar divām teksta rindiņām</string>
    <string name="persona_view_description_small">Mazs avatārs ar vienu teksta rindiņu</string>
    <string name="people_picker_hint">Nav parādītu ar ieteikumu</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Atspējots personāža čips</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Personas čipa kļūda</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Personāža čips bez aizvēršanas ikonas</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Pamata personāža čips</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Jūs noklikšķinājāt uz atlasītās personas čipa.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Kopīgot</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Sekot</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Uzaicināt cilvēkus</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Lapas atsvaidzināšana</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Atvērt pārlūkprogrammā</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Šī ir daudzrindu uznirstošā izvēlne. Maksimālais rindiņu skaits ir iestatīts uz divi; pārējie teksti tiks aprauti.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Visi jaunumi</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Saglabātās ziņas</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Jaunumi no vietnēm</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Informēt ārpus darba laika</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Informēt, ja neaktīvs datorā</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Jūs noklikšķinājāt uz vienuma:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Vienkārša izvēlne</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">2. vienkāršā izvēlne</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Izvēlne ar vienu atlasāmu vienumu un atdalītāju</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Izvēlne ar visiem atlasāmajiem vienumiem, ikonām un garu tekstu</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Rādīt</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Riņķveida norise</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Mazs</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Vidēja</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Liels</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineāra norise</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Nenoteikts</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Noteikts</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Meklēšanas josla</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofona atzvanīšana</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Automātiskā koriģēšana</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofons nospiests</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Nospiests labais skats</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Nospiesta tastatūras meklēšana</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Rādīt Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Nerādīt Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Darbība</string>
    <string name="snackbar_action_long">Ilga teksta darbība</string>
    <string name="snackbar_single_line">Vienrindu uzkodu josla</string>
    <string name="snackbar_multiline">Šī ir daudzrindu uzkodu josla. Maksimālais rindiņu skaits ir iestatīts uz divi; pārējie teksti tiks aprauti.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Šī ir paziņojumu uzkodas josla. Tas tiek izmantots, lai sazinātos ar jauniem līdzekļiem.</string>
    <string name="snackbar_primary">Šī ir primārā uzkodu josla.</string>
    <string name="snackbar_light">Šī ir gaišā uzkodu josla.</string>
    <string name="snackbar_warning">Šī ir brīdinājuma uzkodu bārs.</string>
    <string name="snackbar_danger">Šis ir bīstams uzkodu bārs.</string>
    <string name="snackbar_description_single_line">Īslaicīgs</string>
    <string name="snackbar_description_single_line_custom_view">Ilgs ilgums ar riņķveida norisi kā mazu pielāgoto skatu</string>
    <string name="snackbar_description_single_line_action">Īss ilgums ar darbību</string>
    <string name="snackbar_description_single_line_action_custom_view">Īss ilgums ar darbību un vidēji pielāgotu skatu</string>
    <string name="snackbar_description_single_line_custom_text_color">Īss ilgums ar pielāgotu teksta krāsu</string>
    <string name="snackbar_description_multiline">Ilgs ilgums</string>
    <string name="snackbar_description_multiline_custom_view">Ilgs ilgums ar mazu pielāgoto skatu</string>
    <string name="snackbar_description_multiline_action">Nenoteikts ilgums ar darbību un teksta atjauninājumiem</string>
    <string name="snackbar_description_multiline_action_custom_view">Īss ilgums ar darbību un vidēji pielāgotu skatu</string>
    <string name="snackbar_description_multiline_action_long">Īss ilgums ar garu darbības tekstu</string>
    <string name="snackbar_description_announcement">Īslaicīgs</string>
    <string name="snackbar_description_updated">Šis teksts tika atjaunināts.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Rādīt uzkodu joslu</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Viena rindiņa</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Vairākrindiņu</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Paziņojuma stils</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primārais stils</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Gaišs stils</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Brīdinājuma stils</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Draudu stils</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Sākums</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Pasts</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Iestatījumi</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Paziņojums</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Vairāk</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Teksta līdzinājums</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertikāli</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontāli</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Nav teksta</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Cilnes vienumi</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Virsraksts</string>
    <string name="cell_sample_description">Apraksts</string>
    <string name="calculate_cells">Ielādēt/aprēķināt 100 šūnas</string>
    <string name="calculate_layouts">Ielādēt/aprēķināt 100 izkārtojumus</string>
    <string name="template_list">Veidņu saraksts</string>
    <string name="regular_list">Standarta saraksts</string>
    <string name="cell_example_title">Nosaukums: šūna</string>
    <string name="cell_example_description">Apraksts: pieskarieties, lai mainītu orientāciju</string>
    <string name="vertical_layout">Vertikālais izkārtojums</string>
    <string name="horizontal_layout">Horizontāls izkārtojums</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standarta 2. ciļņu segments</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standarta 3. cilnes segments</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standarta 4. cilnes segments</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standarta cilne ar peidžeri</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Pārslēgt cilni</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Tabulēšanas cilne</string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Pieskarties ekrāna padomam</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Pieskarties, lai atvērtu pielāgota kalendāra ekrāna padomu</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Pieskarties pielāgotas krāsas ekrāna padomam</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Pieskarties, lai noraidītu iekšējo ekrāna padomu</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Pieskaries, lai atvērtu pielāgota skata ekrāna padomu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Pieskarties pielāgotas krāsas ekrāna padomam</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Augšējās beigšanas ekrāna padoms ar 10dp nobīdiX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Apakšējās sākšanas ekrāna padoms</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Apakšējās beigšanas ekrāna padoms ar 10dp nobīdiY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Noraidīt iekšējo ekrāna padomu</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Ekrāna padoms noraidīts</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Virsraksts ir gaišs 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">1. virsraksts ir vidējs 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">2. virsraksts ir standarta 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Virsraksts ir standarta 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">1. apakšvirsraksts ir standarta 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">2. apakšvirsraksts ir vidējs 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">1. pamatteksts ir standarta 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">2. pamatteksts ir vidējs 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Titrs ir standarta 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK versija: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Vienums %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mape</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Noklikšķināts</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Šablons</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB izvērsts</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB sakļauts</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Noklikšķiniet, lai atsvaidzinātu sarakstu</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Atvērt paneli</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Izvēlnes elements</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Nobīde X (dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Nobīde Y (dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Satura teksts</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Atkārtot satura tekstu</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Izvēlnes platums tiks mainīts attiecībā pret satura tekstu. Maksimālais platums
        ir ierobežots līdz 75% no ekrāna lieluma. Satura piemali no sāniem un apakšas nosaka marķieris. Tas pats satura teksts tiks atkārtots, lai mainītos
        augstums.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Atvērt izvēlni</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Pamata kartīte</string>
    <!-- UI Label for Card -->
    <string name="file_card">Faila kartīte</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Paziņojuma kartīte</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Nejaušs lietotāja interfeiss</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opcijas</string>
    <!-- UI Label for Title -->
    <string name="card_title">Virsraksts</string>
    <!-- UI Label for text -->
    <string name="card_text">Teksts</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Apakšteksts</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Ja nepieciešams, šīs reklāmkaroga sekundārās kopijas var aplauzt līdz divām rindiņām.</string>
    <!-- UI Label Button -->
    <string name="card_button">Poga</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Radīt dialoglodziņu</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Noraidīt dialoglodziņu, noklikšķinot ārpusē</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Noraidīt dialoglodziņu, nospiežot atpakaļ</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialoglodziņš noraidīts</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Atcelt</string>
    <!-- UI Label Ok -->
    <string name="ok">Labi</string>
    <!-- A sample description -->
    <string name="dialog_description">Dialoglodziņš ir mazs logs, kurā lietotājam tiek lūgts pieņemt lēmumu vai ievadīt papildinformāciju.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Atvērt paneli</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Izvērst paneli</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Aizvērt paneli</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Atlasīt paneļa tipu</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Augšā</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Redzamajā apgabalā tiek rādīts viss panelis.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Apakšā</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Redzamajā apgabalā tiek rādīts viss panelis. Pavelciet uz augšu kustības ritināšanas saturu. Izvēršamu paneli izvērš, izmantojot vilkšanas turi.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Kreisais slaids virs</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Panelis slīd pāri redzamajam reģionam no kreisās puses.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Labais slaids virs</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Panelis slīd pāri redzamajam reģionam no labās puses.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Apakšējais slaids virs</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Panelis slīd pāri redzamajam reģionam no ekrāna apakšas. Pavelciet uz augšu kustību izvēršamajam panelim, pārējo daļu novietojiet redzamajā apgabalā un pēc tam ritiniet.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Redzams Scrim</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Atlasīt paneļa saturu</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Pilnekrāna izmēra ritināms saturs</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Vairāk nekā puse ekrāna satura</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Mazāk nekā puse ekrāna satura</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dinamiskā lieluma saturs</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Ligzdots paneļa saturs</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Izvēršams</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Izlaist atvērto stāvokli</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Novērst nerādīšanu, noklikšķinot uz Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Rādīt turi</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Virsraksts</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Rīka padoma teksts</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Pieskarieties, lai atvērtu pielāgota satura rīka padomu</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Sākums augšā </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Augšpuses gals</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Apakšējais sākums</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Apakšpuses gals</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centrā </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Pielāgots centrs</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Lai iegūtu informāciju par laidienu piezīmēm,</string>
    <string name="click_here">noklikšķiniet šeit.</string>
    <string name="open_source_cross_platform">Atvērt avota starpplatformu noformēšanas sistēmu.</string>
    <string name="intuitive_and_powerful">Intuitīvs &amp; jaudīgs.</string>
    <string name="design_tokens">Noformējuma marķieri</string>
    <string name="release_notes">Informācija par laidienu</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub Repo saite</string>
    <string name="report_issue">Ziņot par problēmu</string>
    <string name="v1_components">V1 komponenti</string>
    <string name="v2_components">V2 komponenti</string>
    <string name="all_components">Visi</string>
    <string name="fluent_logo">Fluent logotips</string>
    <string name="new_badge">Jauns</string>
    <string name="modified_badge">Modificēts</string>
    <string name="api_break_badge">API pārtraukums</string>
    <string name="app_bar_more">Vairāk</string>
    <string name="accent">Izcēlums</string>
    <string name="appearance">Izskats</string>
    <string name="choose_brand_theme">Izvēlieties zīmola dizainu:</string>
    <string name="fluent_brand_theme">Fluent zīmols</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">Microsoft Teams</string>
    <string name="choose_appearance">Izskata izvēle</string>
    <string name="appearance_system_default">Sistēmas noklusējums</string>
    <string name="appearance_light">Gaiša</string>
    <string name="appearance_dark">Tumša</string>
    <string name="demo_activity_github_link">Demonstrācijas darbību GitHub saite</string>
    <string name="control_tokens_details">Vadīklu marķieru detalizētā informācija</string>
    <string name="parameters">Parametri</string>
    <string name="control_tokens">Vadīklu marķieri</string>
    <string name="global_tokens">Globālie marķieri</string>
    <string name="alias_tokens">Aizstājvārdu marķieri</string>
    <string name="sample_text">Teksts</string>
    <string name="sample_icon">Ikonas paraugs</string>
    <string name="color">Krāsa</string>
    <string name="neutral_color_tokens">Neitrālas krāsas marķieri</string>
    <string name="font_size_tokens">Fonta lieluma marķieri</string>
    <string name="line_height_tokens">Rindiņas augstuma marķieri</string>
    <string name="font_weight_tokens">Fonta biezuma marķieri</string>
    <string name="icon_size_tokens">Ikonas lieluma marķieri</string>
    <string name="size_tokens">Lieluma marķieri</string>
    <string name="shadow_tokens">Ēnas marķieri</string>
    <string name="corner_radius_tokens">Stūra rādiusa marķieri</string>
    <string name="stroke_width_tokens">Vilkuma platuma marķieri</string>
    <string name="brand_color_tokens">Zīmola krāsas marķieri</string>
    <string name="neutral_background_color_tokens">Neitrālā fona krāsas marķieri</string>
    <string name="neutral_foreground_color_tokens">Neitrāla priekšplāna krāsu marķieri</string>
    <string name="neutral_stroke_color_tokens">Neitrālā vilkuma krāsas marķieri</string>
    <string name="brand_background_color_tokens">Zīmola fona krāsas marķieri</string>
    <string name="brand_foreground_color_tokens">Zīmola priekšplāna krāsu marķieri</string>
    <string name="brand_stroke_color_tokens">Zīmola vilkuma krāsas marķieri</string>
    <string name="error_and_status_color_tokens">Kļūdu un statusa krāsu marķieri</string>
    <string name="presence_tokens">Klātbūtnes krāsu marķieri</string>
    <string name="typography_tokens">Salikuma marķieri</string>
    <string name="unspecified">Nenorādīts</string>

</resources>