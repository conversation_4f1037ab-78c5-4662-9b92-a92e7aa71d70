<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demo dell\'interfaccia utente di Fluent</string>
    <string name="app_title">Interfaccia utente Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s selezionato</string>
    <string name="app_modifiable_parameters">Parametri modificabili</string>
    <string name="app_right_accessory_view">Visualizzazione accessori a destra</string>

    <string name="app_style">Stile</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Icona premuta</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Avvia demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Sequenza</string>
    <string name="actionbar_icon_radio_label">Icona</string>
    <string name="actionbar_basic_radio_label">Base</string>
    <string name="actionbar_position_bottom_radio_label">In fondo</string>
    <string name="actionbar_position_top_radio_label">In alto</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tipo barra delle azioni</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Posizione della barra delle azioni</string>

    <!--AppBar-->
    <string name="app_bar_style">Stile AppBar</string>
    <string name="app_bar_subtitle">Sottotitolo</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Bordo inferiore</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Icona di spostamento selezionata.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Bandiera</string>
    <string name="app_bar_layout_menu_settings">Impostazioni</string>
    <string name="app_bar_layout_menu_search">Cerca</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Comportamento di scorrimento: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Attiva/disattiva il comportamento di scorrimento</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Attiva/Disattiva icona di spostamento</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Mostra avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Mostra icona Indietro</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Nascondi icona</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Mostra icona</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Attiva/disattiva stile di layout della barra di ricerca</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Mostra come visualizzazione accessori</string>
    <string name="app_bar_layout_searchbar_action_view_button">Mostra come visualizzazione azione</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Passa da un tema all\'altro (ricrea l\'attività)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Alternanza del tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Elemento</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Contenuto aggiuntivo scorrevole</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Stile cerchio</string>
    <string name="avatar_style_square">Stile quadrato</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">Xlarge</string>
    <string name="avatar_size_large">Grande</string>
    <string name="avatar_size_medium">Medio</string>
    <string name="avatar_size_small">Piccola</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Extra large doppio</string>
    <string name="avatar_size_xlarge_accessibility">Molto grande</string>
    <string name="avatar_size_xsmall_accessibility">Molto piccola</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Avatar massimo visualizzato</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Numero di avatar overflow</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tipo di bordo</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Il gruppo di avatar con OverflowAvatarCount impostato non rispetterà il numero massimo di avatar visualizzati.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Face Stack</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Face Pile</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Overflow selezionato</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView in corrispondenza dell\'indice %d cliccato</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Badge di notifica</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punto</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Elenco</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Personaggio</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Foto</string>
    <string name="bottom_navigation_menu_item_news">Notizie</string>
    <string name="bottom_navigation_menu_item_alerts">Avvisi</string>
    <string name="bottom_navigation_menu_item_calendar">Calendario</string>
    <string name="bottom_navigation_menu_item_team">Team</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Attiva/disattiva etichette</string>
    <string name="bottom_navigation_three_menu_items_button">Mostra tre voci di menu</string>
    <string name="bottom_navigation_four_menu_items_button">Mostra quattro voci di menu</string>
    <string name="bottom_navigation_five_menu_items_button">Mostra cinque voci di menu</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Le etichette sono %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">Foglio inferiore</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Abilita scorrimento rapido verso il basso per ignorare</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Mostra con singole voci di riga</string>
    <string name="bottom_sheet_with_double_line_items">Mostra con voci doppie</string>
    <string name="bottom_sheet_with_single_line_header">Mostra con intestazione a riga singola</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Mostra con intestazione a doppia riga e divisori</string>
    <string name="bottom_sheet_dialog_button">Mostra</string>
    <string name="drawer_content_desc_collapse_state">Espandi</string>
    <string name="drawer_content_desc_expand_state">Riduci a icona</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Fai clic su %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Clic lungo %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Fai clic su Chiudi</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Inserisci elemento</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Aggiorna elemento</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Ignora</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Aggiungi</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Menzione</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Grassetto</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Corsivo</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Sottolinea</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Barrato</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Annulla</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Ripetere</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Punto elenco</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Elenco</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Collegamento</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Aggiornamento elementi</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Spaziatura</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Ignora posizione</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">INIZIA</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">FINE</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Spazio del gruppo</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Spazio elemento</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Bandiera</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Contrassegna elemento selezionato</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Risposta</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Elemento di risposta selezionato</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Trasferisci</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Elemento di inoltro selezionato</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Elimina</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Elimina elemento selezionato</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Webcam</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Scatta una foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Elemento della fotocamera selezionato</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Raccolta</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Visualizza le tue foto</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Elemento della raccolta selezionato</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Video</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Riproduci i tuoi video</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Elemento video selezionato</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Gestisci</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Gestisci il catalogo multimediale</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Gestisci elemento selezionato</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Azioni di posta elettronica</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documenti</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Ultimo aggiornamento alle 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Condividi</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Condividi elemento selezionato</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Sposta</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Sposta elemento selezionato</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Elimina</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Elimina elemento selezionato</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informazioni</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Elemento info selezionato</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Orologio</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Elemento orologio selezionato</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Allarmi</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Elemento allarme selezionato</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Fuso orario</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Elemento del fuso orario selezionato</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Visualizzazioni diverse del pulsante</string>
    <string name="button">Pulsante</string>
    <string name="buttonbar">Barra dei pulsanti</string>
    <string name="button_disabled">Esempio di pulsante disabilitato</string>
    <string name="button_borderless">Esempio di pulsante senza bordi</string>
    <string name="button_borderless_disabled">Esempio di pulsante Disabilitato senza bordi</string>
    <string name="button_large">Esempio di pulsante Grande</string>
    <string name="button_large_disabled">Esempio di pulsante Disabilitato grande</string>
    <string name="button_outlined">Esempio di pulsante con contorno</string>
    <string name="button_outlined_disabled">Esempio di pulsante Disabilitato evidenziato</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Scegli una data</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Data singola</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nessuna data selezionata</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Mostra selezione data</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Mostra selezione data/ora con la scheda Data selezionata</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Mostra selezione data e ora con la scheda Ora selezionata</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Mostra selezione data e ora</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Intervallo di date</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Inizio:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Fine:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nessun inizio selezionato</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Nessuna fine selezionata</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Seleziona la data di inizio</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Seleziona la data di fine</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Intervallo di data e ora</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Seleziona l\'intervallo di data e ora</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Mostra finestra di dialogo</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Mostra cassetto</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Mostra finestra di dialogo del cassetto</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Nessuna finestra di dialogo dissolvenza in basso</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Mostra cassetto superiore</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Nessuna finestra di dialogo dissolvenza in alto</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Mostra la finestra di dialogo superiore della vista di ancoraggio</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Mostra la finestra di dialogo senza titolo in alto</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Mostra la finestra di dialogo in alto sotto il titolo</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Mostra cassetto destro</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Mostra cassetto sinistro</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Titolo, testo principale</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Sottotitolo, testo secondario</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Testo del sottotitolo personalizzato</string>
    <!-- Footer -->
    <string name="list_item_footer">Piè di pagina, testo terziario</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Elenco a riga singola con testo intestazione secondaria grigio</string>
    <string name="list_item_sub_header_two_line">Elenco a due righe</string>
    <string name="list_item_sub_header_two_line_dense">Elenco a due righe con spaziatura fitta</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Elenco a due righe con visualizzazione sottotitoli secondaria personalizzata</string>
    <string name="list_item_sub_header_three_line">Elenco a tre righe con testo di intestazione secondaria nera</string>
    <string name="list_item_sub_header_no_custom_views">Voci di elenco senza visualizzazioni personalizzate</string>
    <string name="list_item_sub_header_large_header">Voci di elenco con visualizzazioni personalizzate di grandi dimensioni</string>
    <string name="list_item_sub_header_wrapped_text">Voci di elenco con testo a capo</string>
    <string name="list_item_sub_header_truncated_text">Voci di elenco con testo troncato</string>
    <string name="list_item_sub_header_custom_accessory_text">Azione</string>
    <string name="list_item_truncation_middle">Troncamento centrale.</string>
    <string name="list_item_truncation_end">Termina troncamento.</string>
    <string name="list_item_truncation_start">Avvia troncamento.</string>
    <string name="list_item_custom_text_view">Valore</string>
    <string name="list_item_click">Hai fatto clic sulla voce di elenco.</string>
    <string name="list_item_click_custom_accessory_view">Hai selezionato la visualizzazione accessori personalizzati.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Hai selezionato la visualizzazione accessori personalizzata dell\'intestazione secondaria.</string>
    <string name="list_item_more_options">Altre opzioni</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Seleziona</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Nessuno</string>
    <string name="people_picker_delete_example">Elimina</string>
    <string name="people_picker_custom_persona_description">Questo esempio mostra come creare un oggetto IPersona personalizzato.</string>
    <string name="people_picker_dialog_title_removed">Hai rimosso un utente tipo:</string>
    <string name="people_picker_dialog_title_added">Hai aggiunto un utente tipo:</string>
    <string name="people_picker_drag_started">Trascinamento avviato</string>
    <string name="people_picker_drag_ended">Trascinamento terminato</string>
    <string name="people_picker_picked_personas_listener">Ascoltatore di utenti tipo</string>
    <string name="people_picker_suggestions_listener">Suggerimenti Ascoltatore</string>
    <string name="people_picker_persona_chip_click">Hai selezionato %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s destinatario</item>
        <item quantity="many">%1$s destinatari</item>
        <item quantity="other">%1$s destinatari</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Espandi foglio inferiore permanente</string>
    <string name="collapse_persistent_sheet_button"> Nascondi foglio inferiore permanente</string>
    <string name="show_persistent_sheet_button"> Mostra foglio inferiore permanente</string>
    <string name="new_view">Questa è la nuova visualizzazione</string>
    <string name="toggle_sheet_content">Attiva/Disattiva contenuto del foglio inferiore</string>
    <string name="switch_to_custom_content">Passa al contenuto personalizzato</string>
    <string name="one_line_content">Contenuto del foglio inferiore di una riga</string>
    <string name="toggle_disable_all_items">Attiva/Disattiva tutti gli elementi</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Aggiungi/rimuovi visualizzazione</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Modifica altezza compressa</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nuova cartella</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Nuovo elemento cartella selezionato</string>
    <string name="persistent_sheet_item_edit_title">Modifica</string>
    <string name="persistent_sheet_item_edit_toast">Modifica elemento selezionato</string>
    <string name="persistent_sheet_item_save_title">Salva</string>
    <string name="persistent_sheet_item_save_toast">Salva elemento selezionato</string>
    <string name="persistent_sheet_item_zoom_in_title">Ingrandisci</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Elemento zoom avanti selezionato</string>
    <string name="persistent_sheet_item_zoom_out_title">Riduci</string>
    <string name="persistent_sheet_item_zoom_out_toast">Fai clic su Zoom indietro</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Disponibile</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Pio Milanesi</string>
    <string name="persona_name_kat_larsson">Claudia Mazzanti</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Gerolamo Iadanza</string>
    <string name="persona_name_kristen_patterson">Martina Milani</string>
    <string name="persona_name_lydia_bauer">Valeria Lo Duca</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Gianni Pisani</string>
    <string name="persona_name_mona_kane">Giorgia Fanucci</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Ingegnere</string>
    <string name="persona_subtitle_manager">Responsabile</string>
    <string name="persona_subtitle_researcher">Ricercatore</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (esempio di testo lungo per testare il troncamento)</string>
    <string name="persona_view_description_xxlarge">Avatar XXLarge con tre righe di testo</string>
    <string name="persona_view_description_large">Avatar grande con due righe di testo</string>
    <string name="persona_view_description_small">Avatar piccolo con una riga di testo</string>
    <string name="people_picker_hint">Nessuno con suggerimento visualizzato</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Chip utente tipo disabilitato</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Errore chip utente tipo</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Chip utente tipo senza icona di chiusura</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Chip utente tipo di base</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Hai fatto clic su un chip utente tipo selezionato.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Condividi</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Segui</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Invita persone</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Aggiorna la pagina</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Apri nel browser</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Questo è un menu popup su più righe. Il numero massimo di righe è impostato su due. Il resto del testo verrà troncato.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Tutte le notizie</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Notizie salvate</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Notizie dai siti</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Inviare notifiche al di fuori dell\'orario di lavoro</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notifica quando è inattivo sul desktop</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Hai selezionato l\'elemento:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menu semplice</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menu semplice2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu con un elemento selezionabile e un divisore</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu con tutti gli elementi selezionabili, le icone e il testo lungo</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Mostra</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Progresso circolare</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Piccola</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Medio</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Grande</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Progresso lineare</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Indeterminato</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinato</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Barra di ricerca</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Richiamata automatica microfono</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Correzione automatica</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Microfono premuto</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Visualizzazione destra premuta</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Ricerca tastiera premuta</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Mostra snackbar</string>
    <string name="fluentui_dismiss_snackbar">Chiudi snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Azione</string>
    <string name="snackbar_action_long">Azione testo lungo</string>
    <string name="snackbar_single_line">Snackbar a linea singola</string>
    <string name="snackbar_multiline">Questo è uno snackbar multilinea. Il numero massimo di righe è impostato su due. Il resto del testo verrà troncato.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Questo è una snackbar di annuncio. Viene usata per comunicare nuove funzionalità.</string>
    <string name="snackbar_primary">Questo è uno snackbar principale.</string>
    <string name="snackbar_light">Questo è uno snackbar leggero.</string>
    <string name="snackbar_warning">Questa è una barra snack di avviso.</string>
    <string name="snackbar_danger">Questa è una barra snack di pericolo.</string>
    <string name="snackbar_description_single_line">Durata breve</string>
    <string name="snackbar_description_single_line_custom_view">Lunga durata con avanzamento circolare come piccola visualizzazione personalizzata</string>
    <string name="snackbar_description_single_line_action">Durata breve con azione</string>
    <string name="snackbar_description_single_line_action_custom_view">Durata breve con azione e visualizzazione media personalizzata</string>
    <string name="snackbar_description_single_line_custom_text_color">Durata breve con colore del testo personalizzato</string>
    <string name="snackbar_description_multiline">Durata estesa</string>
    <string name="snackbar_description_multiline_custom_view">Lunga durata con piccola visualizzazione personalizzata</string>
    <string name="snackbar_description_multiline_action">Durata indefinita con aggiornamenti di azioni e testo</string>
    <string name="snackbar_description_multiline_action_custom_view">Durata breve con azione e visualizzazione media personalizzata</string>
    <string name="snackbar_description_multiline_action_long">Durata breve con testo di azione lungo</string>
    <string name="snackbar_description_announcement">Durata breve</string>
    <string name="snackbar_description_updated">Questo testo è stato aggiornato.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Mostra snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Linea singola</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Multilinea</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Stile annuncio</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Stile principale</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Stile chiaro</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Stile avviso</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Stile di pericolo</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Home</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Posta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Impostazioni</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notifica</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Altro</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Allineamento testo</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Verticale</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Orizzontale</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Nessun testo</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Elementi scheda</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Titolo</string>
    <string name="cell_sample_description">Descrizione</string>
    <string name="calculate_cells">Carica/calcola 100 celle</string>
    <string name="calculate_layouts">Carica/calcola 100 layout</string>
    <string name="template_list">Elenco modelli</string>
    <string name="regular_list">Elenco normale</string>
    <string name="cell_example_title">Titolo: Cella</string>
    <string name="cell_example_description">Descrizione: tocca per cambiare l\'orientamento</string>
    <string name="vertical_layout">Layout verticale</string>
    <string name="horizontal_layout">Layout orizzontale</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Scheda standard a 2 segmenti</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Scheda Standard a 3 segmenti</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Scheda Standard a 4 segmenti</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Scheda standard con cercapersone</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Cambia scheda</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Scheda pillole </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tocca per la descrizione comando</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tocca per descrizione comando calendario personalizzato</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Tocca descrizione comando colore personalizzato</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tocca per ignorare la descrizione comando interna</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tocca per la descrizione comando visualizzazione personalizzata</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Descrizione comando colore personalizzato superiore</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Descrizione comando finale superiore con offsetY 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Descrizione comando iniziale inferiore</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Descrizione comando finale inferiore con offsetY 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Chiudi descrizione comando interna</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Descrizione comando ignorata</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Il titolo è 28sp leggero</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Il titolo 1 è 20sp medio</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Il titolo 2 è normale 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Il titolo è 18sp normale</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Sottotitolo 1: 16sp normale</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Il sottotitolo 2 è 16sp medio</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Il corpo 1 è 14sp normale</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Il corpo 2 è 14sp medio</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Il sottotitolo è 12sp normale</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versione SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">%d elemento</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Cartella</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Selezionati</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB espanso</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB compresso</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Fai clic per aggiornare l’elenco</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Apri cassetto</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Voce di menu</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Offset X (in dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Offset Y (in dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Testo contenuto</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Ripeti testo contenuto</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">La larghezza del menu cambierà rispetto al testo del contenuto. Valore massimo
        la larghezza è limitata a 75% delle dimensioni dello schermo. Il margine del contenuto dal lato e dal basso è regolato dal token. Lo stesso testo del contenuto si ripeterà per variare
        l\'altezza.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Apri menu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Scheda Basic</string>
    <!-- UI Label for Card -->
    <string name="file_card">Scheda file</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Scheda annuncio</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Interfaccia utente casuale</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opzioni</string>
    <!-- UI Label for Title -->
    <string name="card_title">Titolo</string>
    <!-- UI Label for text -->
    <string name="card_text">Testo</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Testo secondario</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">La copia secondaria per questo banner può andare a capo in due righe, se necessario.</string>
    <!-- UI Label Button -->
    <string name="card_button">Pulsante</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Mostra finestra di dialogo</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Chiudere la finestra di dialogo quando si fa clic all\'esterno</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Interrompere la finestra di dialogo quando si preme il tasto indietro</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Chiusura della finestra di dialogo</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Annulla</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Una finestra di dialogo è una piccola finestra che richiede all\'utente di prendere una decisione o di inserire ulteriori informazioni.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Apri cassetto</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Espandi cassetto</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Chiudi pannello</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Seleziona tipo di pannello</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">In alto</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">L\'intero pannello viene visualizzato nell\'area visibile.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">In basso</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">L\'intero pannello viene visualizzato nell\'area visibile. Movimento di scorrimento rapido verso l\'alto per scorrere il contenuto. Il pannello espandibile viene espanso tramite quadratino di trascinamento.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Scorrimento a sinistra su</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Scorri verso l\'area visibile dal lato sinistro.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Scorri a destra sopra</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Scorri verso l\'area visibile dal lato destro.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Scorrimento in basso su</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Consente lo scorrimento del pannello verso l\'area visibile dalla parte inferiore dello schermo. Il movimento di scorrimento rapido verso l\'alto sul pannello espandibile porta la parte restante nell\'area visibile e ne consente quindi lo scorrimento.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Visibile a Scrim</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Seleziona contenuto pannello</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Contenuto scorrevole a schermo intero</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Contenuto a più di metà schermo</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Contenuto a meno di metà schermo</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Contenuto di dimensioni dinamiche</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Contenuto pannello annidato</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Espandibile</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Ignora stato di apertura</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Impedisci chiusura al clic Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Mostra quadratino</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Titolo</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Testo descrizione comando</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tocca per visualizzare la descrizione comando del contenuto personalizzato</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Inizio superiore </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Bordo superiore </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Avvio dal basso </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Bordo inferiore </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centro </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Centro personalizzato</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Per aggiornamenti sulle note sulla versione,</string>
    <string name="click_here">fai clic qui.</string>
    <string name="open_source_cross_platform">Open source multipiattaforma Design System.</string>
    <string name="intuitive_and_powerful">Intuitivo &amp; Potente.</string>
    <string name="design_tokens">Progetta token</string>
    <string name="release_notes">Note sulla versione</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub Repo Link</string>
    <string name="report_issue">Segnala problema</string>
    <string name="v1_components">Componenti V1</string>
    <string name="v2_components">Componenti V2</string>
    <string name="all_components">Tutto</string>
    <string name="fluent_logo">Fluent Logo</string>
    <string name="new_badge">Nuovo</string>
    <string name="modified_badge">Data modifica</string>
    <string name="api_break_badge">Interruzione API</string>
    <string name="app_bar_more">Altro</string>
    <string name="accent">Accento</string>
    <string name="appearance">Aspetto</string>
    <string name="choose_brand_theme">Scegli il tema del tuo marchio:</string>
    <string name="fluent_brand_theme">Marchio Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Scegli aspetto</string>
    <string name="appearance_system_default">Sistema predefinito</string>
    <string name="appearance_light">Chiaro</string>
    <string name="appearance_dark">Scuro</string>
    <string name="demo_activity_github_link">Collegamento attività demo GitHub</string>
    <string name="control_tokens_details">Dettagli token di controllo</string>
    <string name="parameters">Parametri</string>
    <string name="control_tokens">Token di controllo</string>
    <string name="global_tokens">Token globali</string>
    <string name="alias_tokens">Token alias</string>
    <string name="sample_text">Testo</string>
    <string name="sample_icon">Icona di esempio</string>
    <string name="color">Colore</string>
    <string name="neutral_color_tokens">Token colore neutro</string>
    <string name="font_size_tokens">Token dimensioni carattere</string>
    <string name="line_height_tokens">Token dell\'altezza della linea</string>
    <string name="font_weight_tokens">Token spessore carattere</string>
    <string name="icon_size_tokens">Token dimensioni icona</string>
    <string name="size_tokens">Token di dimensioni</string>
    <string name="shadow_tokens">Token shadow</string>
    <string name="corner_radius_tokens">Corner RadiusTokens</string>
    <string name="stroke_width_tokens">Token larghezza tratto</string>
    <string name="brand_color_tokens">Token colore marchio</string>
    <string name="neutral_background_color_tokens">Token colore di sfondo neutro</string>
    <string name="neutral_foreground_color_tokens">Token colore primo piano neutro</string>
    <string name="neutral_stroke_color_tokens">Token colore tratto neutro</string>
    <string name="brand_background_color_tokens">Token colore di sfondo del marchio</string>
    <string name="brand_foreground_color_tokens">Token colore primo piano del marchio</string>
    <string name="brand_stroke_color_tokens">Token colore tratto marchio</string>
    <string name="error_and_status_color_tokens">Token colore errore e stato</string>
    <string name="presence_tokens">Token colore presenza</string>
    <string name="typography_tokens">Token tipografici</string>
    <string name="unspecified">Non specificato</string>

</resources>