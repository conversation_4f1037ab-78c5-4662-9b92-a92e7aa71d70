<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demo der Fluent-Benutzeroberfläche</string>
    <string name="app_title">Fluent-Benutzeroberfläche</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s ausgewählt</string>
    <string name="app_modifiable_parameters">Änderbare Parameter</string>
    <string name="app_right_accessory_view"><PERSON><PERSON><PERSON></string>

    <string name="app_style">Formatvorlage</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Symbol gedrückt</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Demo starten</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label"><PERSON><PERSON><PERSON></string>
    <string name="actionbar_icon_radio_label">Symbol</string>
    <string name="actionbar_basic_radio_label">Standard</string>
    <string name="actionbar_position_bottom_radio_label">Unten</string>
    <string name="actionbar_position_top_radio_label">Oben</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">ActionBar-Typ</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar Position</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar Style</string>
    <string name="app_bar_subtitle">Untertitel</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Rahmenlinie unten</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Auf das Navigationssymbol wurde geklickt.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Kennzeichnen</string>
    <string name="app_bar_layout_menu_settings">Einstellungen</string>
    <string name="app_bar_layout_menu_search">Suchen</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Scrollverhalten: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Scrollverhalten umschalten</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Navigationssymbol umschalten</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Avatar anzeigen</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Symbol "Zurück" anzeigen</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Symbol ausblenden</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Symbol anzeigen</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Layoutstil der Suchleiste umschalten</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Als Zubehöransicht anzeigen</string>
    <string name="app_bar_layout_searchbar_action_view_button">Als Aktionsansicht anzeigen</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Zwischen Designs umschalten (Aktivität neu erstellen)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Design umschalten</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Element</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Zusätzlicher bildlauffähiger Inhalt</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Kreisart</string>
    <string name="avatar_style_square">Quadratisches Format</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Groß</string>
    <string name="avatar_size_medium">Mittel</string>
    <string name="avatar_size_small">Klein</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Doppelt sehr groß</string>
    <string name="avatar_size_xlarge_accessibility">Sehr groß</string>
    <string name="avatar_size_xsmall_accessibility">Sehr klein</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maximal angezeigter Avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Anzahl von Überlauf-Avataren</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Rahmentyp</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Die Avatargruppe, für die OverflowAvatarCount festgelegt ist, entspricht nicht der maximalen Anzahl angezeigter Avatars.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Gesichtsstapel</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Gesichtsstapel</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Überlauf geklickt</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView am Index %d geklickt</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Benachrichtigungs-Badge</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punkt</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Liste</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Charakter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotos</string>
    <string name="bottom_navigation_menu_item_news">Neuigkeiten</string>
    <string name="bottom_navigation_menu_item_alerts">Warnungen</string>
    <string name="bottom_navigation_menu_item_calendar">Kalender</string>
    <string name="bottom_navigation_menu_item_team">Team</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Bezeichnungen umschalten</string>
    <string name="bottom_navigation_three_menu_items_button">Drei Menüelemente anzeigen</string>
    <string name="bottom_navigation_four_menu_items_button">Vier Menüelemente anzeigen</string>
    <string name="bottom_navigation_five_menu_items_button">Fünf Menüelemente anzeigen</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Bezeichnungen sind %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Aktivieren Sie zum Schließen nach unten wischen</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Mit einzeiligen Elementen anzeigen</string>
    <string name="bottom_sheet_with_double_line_items">Mit doppelzeiligem Element anzeigen</string>
    <string name="bottom_sheet_with_single_line_header">Mit einzeiligem Header anzeigen</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Mit doppelzeiligem Header und Trennzeichen anzeigen</string>
    <string name="bottom_sheet_dialog_button">Anzeigen</string>
    <string name="drawer_content_desc_collapse_state">Erweitern</string>
    <string name="drawer_content_desc_expand_state">Minimieren</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">%s klicken</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Langes Klicken %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klicken Sie auf "Schließen"</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Element einfügen</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Element aktualisieren</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Schließen</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Hinzufügen</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Erwähnen</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Fett</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursiv</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Unterstreichen</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Durchgestrichen</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Rückgängig</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Wiederholen</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Aufzählungszeichen</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Liste</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Link</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Element wird aktualisiert</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Abstand</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Position schließen</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">STARTEN</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Gruppenraum</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Elementbereich</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Kennzeichnen</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Auf "Element kennzeichnen" geklickt</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Antworten</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Auf "Element antworten" geklickt</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Weiterleiten</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Auf "Element weiterleiten" geklickt</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Löschen</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Auf Element löschen geklickt</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Foto aufnehmen</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kameraelement angeklickt</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galerie</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Ihre Fotos anzeigen</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Auf Katalogelement geklickt</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Ihre Videos wiedergeben</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Videoelement angeklickt</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Verwalten</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Ihre Medienbibliothek verwalten</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Auf "Element verwalten" geklickt</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-Mail-Aktionen</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumente</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Letzte Aktualisierung: 14:14 Uhr</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Teilen</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Auf "Element freigeben" geklickt</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Verschieben</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Auf "Element verschieben" geklickt</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Löschen</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Auf Element löschen geklickt</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Informationen</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Auf Infoelement geklickt</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Uhr</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Auf Uhrelement geklickt</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Auf Alarmelement geklickt</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Zeitzone</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Auf Zeitzonenelement geklickt</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Verschiedene Ansichten der Schaltfläche</string>
    <string name="button">Schaltfläche</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Beispiel für deaktivierte Schaltfläche</string>
    <string name="button_borderless">Beispiel für eine randlose Schaltfläche</string>
    <string name="button_borderless_disabled">Beispiel für eine randlose deaktivierte Schaltfläche</string>
    <string name="button_large">Beispiel für große Schaltfläche</string>
    <string name="button_large_disabled">Beispiel für eine große deaktivierte Schaltfläche</string>
    <string name="button_outlined">Beispiel für eine umrissene Schaltfläche</string>
    <string name="button_outlined_disabled">Beispiel für eine umrissene deaktivierte Schaltfläche</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Wählen Sie ein Datum aus</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Einzelnes Datum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Kein Datum ausgewählt</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Datumsauswahl anzeigen</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Datums-/Uhrzeitauswahl mit ausgewählter Datumsregisterkarte anzeigen</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Datums-/Uhrzeitauswahl mit ausgewählter Registerkarte "Uhrzeit" anzeigen</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Datums-/Uhrzeitauswahl anzeigen</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Zeitraum</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Start:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Ende:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Kein Start ausgewählt</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Kein Ende ausgewählt</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Startdatum auswählen</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Enddatum auswählen</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Datum/Uhrzeit-Bereich</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Datums-/Uhrzeitbereich auswählen</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Dialogfeld anzeigen</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Drawer anzeigen</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Dialogfeld "Drawer" anzeigen</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Kein Dialogfeld "Unterer Bereich ausblenden"</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Oberen Drawer anzeigen</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Kein Dialogfeld "Oberer Bereich ausblenden"</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Dialogfeld "Ankeransicht oben" anzeigen</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Dialogfeld "Kein Titel oben" anzeigen</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Dialogfeld "Titel oben unten" anzeigen</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Rechter Drawer anzeigen</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Linker Drawer anzeigen</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Titel, Primärtext</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Untertitel, sekundärer Text</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Benutzerdefinierter Untertiteltext</string>
    <!-- Footer -->
    <string name="list_item_footer">Fußzeile, tertiärer Text</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Einzeilige Liste mit grauem Unterheadertext</string>
    <string name="list_item_sub_header_two_line">Zweizeilige Liste</string>
    <string name="list_item_sub_header_two_line_dense">Zweizeilige Liste mit dichtem Abstand</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Zweizeiliger Listenwert mit benutzerdefinierter sekundärer Untertitelansicht</string>
    <string name="list_item_sub_header_three_line">Dreizeilige Liste mit schwarzem Unterheadertext</string>
    <string name="list_item_sub_header_no_custom_views">Elemente ohne benutzerdefinierte Ansichten auflisten</string>
    <string name="list_item_sub_header_large_header">Elementen mit großen benutzerdefinierten Ansichten auflisten</string>
    <string name="list_item_sub_header_wrapped_text">Auflisten von Elementen mit umschlossenem Text</string>
    <string name="list_item_sub_header_truncated_text">Listenelemente mit abgeschnittenem Text</string>
    <string name="list_item_sub_header_custom_accessory_text">Aktion</string>
    <string name="list_item_truncation_middle">Mittlere Kürzung.</string>
    <string name="list_item_truncation_end">Abschneiden beenden.</string>
    <string name="list_item_truncation_start">Abschneiden starten.</string>
    <string name="list_item_custom_text_view">Wert</string>
    <string name="list_item_click">Sie haben auf das Listenelement geklickt.</string>
    <string name="list_item_click_custom_accessory_view">Sie haben auf die benutzerdefinierte Zubehöransicht geklickt.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Sie haben auf die benutzerdefinierte Zubehöransicht der Unterüberschrift geklickt.</string>
    <string name="list_item_more_options">Weitere Optionen</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Auswählen</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Keine</string>
    <string name="people_picker_delete_example">Löschen</string>
    <string name="people_picker_custom_persona_description">Dieses Beispiel zeigt, wie Sie ein benutzerdefiniertes IPersona-Objekt erstellen.</string>
    <string name="people_picker_dialog_title_removed">Sie haben eine Persona entfernt:</string>
    <string name="people_picker_dialog_title_added">Sie haben eine Persona hinzugefügt:</string>
    <string name="people_picker_drag_started">Ziehen gestartet</string>
    <string name="people_picker_drag_ended">Ziehen beendet</string>
    <string name="people_picker_picked_personas_listener">Personas-Zuhörer</string>
    <string name="people_picker_suggestions_listener">Vorschlagszuhörer</string>
    <string name="people_picker_persona_chip_click">Sie haben auf %s geklickt</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s Empfänger</item>
        <item quantity="other">%1$s Empfänger</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Persistentes BottomSheet erweitern</string>
    <string name="collapse_persistent_sheet_button"> Persistentes BottomSheet ausblenden</string>
    <string name="show_persistent_sheet_button"> Persistentes BottomSheet anzeigen</string>
    <string name="new_view">Dies ist eine neue Ansicht</string>
    <string name="toggle_sheet_content">Inhalt des Bottomsheet umschalten</string>
    <string name="switch_to_custom_content">Zu benutzerdefiniertem Inhalt wechseln</string>
    <string name="one_line_content">Einzeilige Inhalte des Bottomsheet</string>
    <string name="toggle_disable_all_items">"Alle Elemente deaktivieren" umschalten</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Ansicht hinzufügen/entfernen</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Reduzierte Höhe ändern</string>
    <string name="persistent_sheet_item_create_new_folder_title">Neuer Ordner</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Auf neues Ordnerelement geklickt</string>
    <string name="persistent_sheet_item_edit_title">Bearbeiten</string>
    <string name="persistent_sheet_item_edit_toast">Auf "Element bearbeiten" geklickt</string>
    <string name="persistent_sheet_item_save_title">Speichern</string>
    <string name="persistent_sheet_item_save_toast">Auf "Element speichern" geklickt</string>
    <string name="persistent_sheet_item_zoom_in_title">Vergrößern</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Element vergrößern angeklickt</string>
    <string name="persistent_sheet_item_zoom_out_title">Verkleinern</string>
    <string name="persistent_sheet_item_zoom_out_toast">Auf "Element verkleinern" geklickt</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Verfügbar</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Konstrukteur(in)</string>
    <string name="persona_subtitle_manager">Manager*in</string>
    <string name="persona_subtitle_researcher">Recherche</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (Beispiel für langen Text zum Testen der Kürzung)</string>
    <string name="persona_view_description_xxlarge">XXLarge Avatar mit drei Textzeilen</string>
    <string name="persona_view_description_large">Großer Avatar mit zwei Textzeilen</string>
    <string name="persona_view_description_small">Kleiner Avatar mit einer Textzeile</string>
    <string name="people_picker_hint">Keine mit angezeigtem Hinweis</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Deaktivierter Persona Chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Fehler Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Symbol für Persona Chip ohne Schließen</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Standard Persona Chip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Sie haben auf einen ausgewählten Persona Chip geklickt.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Teilen</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Folgen</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Personen einladen</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Seite aktualisieren</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Im Browser öffnen</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Dies ist ein mehrzeiliges Popupmenü. Die maximale Zeilenanzahl ist auf zwei festgelegt. Der Rest des Texts wird abgeschnitten.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Alle Neuigkeiten</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Gespeicherte Neuigkeiten</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Neuigkeiten von Websites</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Außerhalb der Arbeitszeit benachrichtigen</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Benachrichtigen, wenn auf dem Desktop inaktiv</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Sie haben auf das Element geklickt:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Einfaches Menü</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Einfaches Menü2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menü mit einem auswählbaren Element und einem Trennzeichen</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menü mit allen auswählbaren Elementen, Symbolen und langem Text</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Anzeigen</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Zirkulärer Fortschritt</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Klein</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Mittel</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Groß</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linearer Fortschritt</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Unbestimmter Status</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Bestimmen</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofonrückruf</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autokorrektur</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon gedrückt</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Rechte Ansicht gedrückt</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Tastatursuche gedrückt</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Snackbar anzeigen</string>
    <string name="fluentui_dismiss_snackbar">Snackbar schließen</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Aktion</string>
    <string name="snackbar_action_long">Lange Textaktion</string>
    <string name="snackbar_single_line">Einzeilige Snackbar</string>
    <string name="snackbar_multiline">Dies ist eine mehrzeilige Snackbar. Die maximale Zeilenanzahl ist auf zwei festgelegt. Der Rest des Texts wird abgeschnitten.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Dies ist eine Ankündigungs-Snackbar. Wird für die Kommunikation mit neuen Features verwendet.</string>
    <string name="snackbar_primary">Dies ist eine primäre Snackbar.</string>
    <string name="snackbar_light">Dies ist eine helle Snackbar.</string>
    <string name="snackbar_warning">Dies ist eine Warnungs-Snackbar.</string>
    <string name="snackbar_danger">Dies ist eine Gefahr-Snackbar.</string>
    <string name="snackbar_description_single_line">Kurze Dauer</string>
    <string name="snackbar_description_single_line_custom_view">Lange Dauer mit zirkulärem Fortschritt als kleine benutzerdefinierte Ansicht</string>
    <string name="snackbar_description_single_line_action">Kurze Dauer mit Aktion</string>
    <string name="snackbar_description_single_line_action_custom_view">Kurze Dauer mit Aktion und mittlerer benutzerdefinierter Ansicht</string>
    <string name="snackbar_description_single_line_custom_text_color">Kurze Dauer mit angepasster Textfarbe</string>
    <string name="snackbar_description_multiline">Lange Dauer</string>
    <string name="snackbar_description_multiline_custom_view">Lange Dauer mit kleiner benutzerdefinierter Ansicht</string>
    <string name="snackbar_description_multiline_action">Unbegrenzte Dauer mit Aktions- und Textaktualisierungen</string>
    <string name="snackbar_description_multiline_action_custom_view">Kurze Dauer mit Aktion und mittlerer benutzerdefinierter Ansicht</string>
    <string name="snackbar_description_multiline_action_long">Kurze Dauer mit langem Aktionstext</string>
    <string name="snackbar_description_announcement">Kurze Dauer</string>
    <string name="snackbar_description_updated">Dieser Text wurde aktualisiert.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Snackbar anzeigen</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Einfache Linie</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Mehrzeilig</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Ankündigungsstil</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primärer Stil</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Helles Format</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Warnungsstil</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Art der Gefahr</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Startseite</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">E-Mail</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Einstellungen</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Benachrichtigung</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Mehr</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Textausrichtung</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertikal</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Kein Text</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Registerkartenelemente</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Titel</string>
    <string name="cell_sample_description">Beschreibung</string>
    <string name="calculate_cells">100 Zellen laden/berechnen</string>
    <string name="calculate_layouts">100 Layouts laden/berechnen</string>
    <string name="template_list">Vorlagenliste</string>
    <string name="regular_list">Reguläre Liste</string>
    <string name="cell_example_title">Titel: Zelle</string>
    <string name="cell_example_description">Beschreibung: Tippen Sie, um die Ausrichtung zu ändern</string>
    <string name="vertical_layout">Vertikales Layout</string>
    <string name="horizontal_layout">Horizontales Layout</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standardregisterkarte 2-Segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standardregisterkarte 3-Segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standardregisterkarte 4-Segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standardregisterkarte mit Pager</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Registerkarte wechseln</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Registerkarte "Pillen" </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Für QuickInfo tippen</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tippen Sie für benutzerdefinierte Kalender-QuickInfo</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Tippen Sie auf die QuickInfo "Benutzerdefinierte Farbe"</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tippen Sie, um die QuickInfo zu schließen</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tippen Sie, um eine QuickInfo zur benutzerdefinierten Ansicht anzuzeigen</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Top-QuickInfo für benutzerdefinierte Farben</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">QuickInfo am oberen Ende mit 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">QuickInfo zum unteren Start</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">QuickInfo am unteren Ende mit 10-dp-OffsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">In QuickInfo schließen</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">QuickInfo geschlossen</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Kopfzeile ist hell 28 s</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Titel 1 ist mittel 20 s</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Titel 2 ist normal 20 s</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Überschrift ist normal 18 s</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Unterüberschrift 1 ist normal 16 s</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Unterüberschrift 2 ist Mittel 16 s</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Text 1 ist normal 14 s</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Text 2 ist mittel 14 s</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Beschriftung ist normal 12 s</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-Version: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Element %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Ordner</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Geklickt</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Gerüst</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB Erweitert</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB Reduziert</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klicken zur Aktualisierung der Liste</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Schublade öffnen</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menüelement</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Abstand X (in DP)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Abstand Y (in DP)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Inhaltstext</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Inhaltstext wiederholen</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Die Menübreite ändert sich in Bezug auf den Inhaltstext. Der Höchstwert
        Die Breite ist auf 75% der Bildschirmgröße beschränkt. Der Inhaltsrand von der Seite und unten wird durch das Token gesteuert. Der gleiche Inhaltstext wird wiederholt, um zu variieren.
        die Höhe.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Menü öffnen</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Einfache Karte</string>
    <!-- UI Label for Card -->
    <string name="file_card">Dateikarte</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Ankündigungskarte</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Zufällige Benutzeroberfläche</string>
    <!-- UI Label for Options -->
    <string name="card_options">Optionen</string>
    <!-- UI Label for Title -->
    <string name="card_title">Titel</string>
    <!-- UI Label for text -->
    <string name="card_text">Text</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Untertext</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Die sekundäre Kopie für dieses Banner kann bei Bedarf in zwei Zeilen umbrochen werden.</string>
    <!-- UI Label Button -->
    <string name="card_button">Schaltfläche</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Dialogfeld anzeigen</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Dialogfeld schließen durch Klicken außerhalb</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Dialogfeld beim Drücken auf der Rückseite schließen</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialogfeld geschlossen</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Abbrechen</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">Ein Dialog ist ein kleines Fenster, das den Benutzer auffordert, eine Entscheidung zu treffen oder zusätzliche Informationen einzugeben.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Schublade öffnen</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Schublade erweitern</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Schublade schließen</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Schubladentyp auswählen</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Oben</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Die gesamte Schublade wird im sichtbaren Bereich angezeigt.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Unten</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Die gesamte Schublade wird im sichtbaren Bereich angezeigt. Eine Wischbewegung nach oben scrollt den Inhalt. Die erweiterbare Schublade wird anhand des Ziehpunktes erweitert.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Überblendung von links</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Schublade von der linken Seite in den sichtbaren Bereich überblenden.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Überblendung von rechts</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Schublade von der rechten Seite in den sichtbaren Bereich überblenden.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Überblendung von unten</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Die Schublade wird vom unteren Bildschirmrand in den sichtbaren Bereich verschoben. Eine Wischbewegung an der erweiterbaren Schublade bringt ihren restlichen Teil in den sichtbaren Bereich &amp; scrollen Sie dann.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Sichtbare Abdunkelung</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Schubladeninhalt auswählen</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Bildlauffähiger Inhalt in Vollbildgröße</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Mehr als halber Bildschirminhalt</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Weniger als halber Bildschirminhalt</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dynamische Größenanpassung von Inhalten</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Inhalt der geschachtelten Schublade</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Erweiterbar</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Offenen Zustand überspringen</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Beenden bei Scrim-Klick verhindern</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Ziehpunkt anzeigen</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Titel</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">QuickInfo-Text</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tippen Sie, um die QuickInfo für benutzerdefinierte Inhalte anzuzeigen.</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Anfang oben </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Oberes Ende </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Unterer Start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Unteres Ende </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Zentriert </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Benutzerdefiniertes Center</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Updates zu Versionshinweisen finden Sie hier: </string>
    <string name="click_here">klicken Sie hier.</string>
    <string name="open_source_cross_platform">Plattformübergreifendes Open-Source-Designsystem.</string>
    <string name="intuitive_and_powerful">Intuitive &amp; leistungsfähig.</string>
    <string name="design_tokens">Designtoken</string>
    <string name="release_notes">Versionshinweise</string>
    <string name="github_repo">GitHub Repository</string>
    <string name="github_repo_link">GitHub-Repository-Link</string>
    <string name="report_issue">Problem melden</string>
    <string name="v1_components">V1-Komponenten</string>
    <string name="v2_components">V2-Komponenten</string>
    <string name="all_components">Alle</string>
    <string name="fluent_logo">Fluent-Logo</string>
    <string name="new_badge">Neu</string>
    <string name="modified_badge">Geändert</string>
    <string name="api_break_badge">API-Unterbrechung</string>
    <string name="app_bar_more">Mehr</string>
    <string name="accent">Akzent</string>
    <string name="appearance">Darstellung</string>
    <string name="choose_brand_theme">Wählen Sie Ihr Markendesign aus:</string>
    <string name="fluent_brand_theme">Marke Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Darstellung auswählen</string>
    <string name="appearance_system_default">Systemstandard</string>
    <string name="appearance_light">Hell</string>
    <string name="appearance_dark">Dunkel</string>
    <string name="demo_activity_github_link">GitHub-Link für Demoaktivität</string>
    <string name="control_tokens_details">Details zu Steuerungstoken</string>
    <string name="parameters">Parameter</string>
    <string name="control_tokens">Steuerungstoken</string>
    <string name="global_tokens">Globale Token</string>
    <string name="alias_tokens">Aliastoken</string>
    <string name="sample_text">Text</string>
    <string name="sample_icon">Beispielsymbol</string>
    <string name="color">Farbe</string>
    <string name="neutral_color_tokens">Neutralfarbtoken</string>
    <string name="font_size_tokens">Schriftgradtoken</string>
    <string name="line_height_tokens">Linienhöhentoken</string>
    <string name="font_weight_tokens">Schriftbreitentoken</string>
    <string name="icon_size_tokens">Symbolgrößentoken</string>
    <string name="size_tokens">Größentoken</string>
    <string name="shadow_tokens">Schattentoken</string>
    <string name="corner_radius_tokens">Eck-RadiusTokens</string>
    <string name="stroke_width_tokens">Token für Strichbreite</string>
    <string name="brand_color_tokens">Markenfarbtoken</string>
    <string name="neutral_background_color_tokens">Neutralhintergrundfarbtoken</string>
    <string name="neutral_foreground_color_tokens">Neutrale Vordergrundfarbtoken</string>
    <string name="neutral_stroke_color_tokens">Neutralstrichfarbtoken</string>
    <string name="brand_background_color_tokens">Markenhintergrundfarbtoken</string>
    <string name="brand_foreground_color_tokens">Neutralvordergrundfarbtoken</string>
    <string name="brand_stroke_color_tokens">Markenstrichfarbtoken</string>
    <string name="error_and_status_color_tokens">Fehler- und Statusfarbtoken</string>
    <string name="presence_tokens">Anwesenheitsfarbtoken</string>
    <string name="typography_tokens">Typografietoken</string>
    <string name="unspecified">Keine Angabe</string>

</resources>