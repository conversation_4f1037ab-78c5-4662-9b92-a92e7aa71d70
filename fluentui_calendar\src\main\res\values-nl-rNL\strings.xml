<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">M</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">D</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">W</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">D</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">V</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">Z</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">Z</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Ga naar volgende week</string>
    <string name="accessibility_goto_previous_week">Ga naar vorige week</string>
    <string name="accessibility_today">vandaag</string>
    <string name="accessibility_selected">Geselecteerd</string>

    <!-- *** Shared *** -->
    <string name="done">Gereed</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Vandaag</string>
    <string name="tomorrow">Morgen</string>
    <string name="yesterday">Gisteren</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Begintijd</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Eindtijd</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Begindatum</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Einddatum</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Tijd kiezen</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Datum kiezen</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Datum- en tijdkiezer</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Datumkiezer</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Bereik voor Datum- en tijdkiezer</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Bereik voor Datumkiezer</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Tabblad Begintijd</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Tabblad Eindtijd</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Tabblad Begindatum</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Tabblad Einddatum</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Dialoogvenster sluiten</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Maand verhogen</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">volgende maand selecteren</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Maand verlagen</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">vorige maand selecteren</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Dag verhogen</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">volgende dag selecteren</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Dag verlagen</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">vorige dag selecteren</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Jaar verhogen</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">volgend jaar selecteren</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Jaar verlagen</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">vorig jaar selecteren</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Datum verhogen</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">volgende datum selecteren</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Datum verlagen</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">vorige datum selecteren</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Uur verhogen</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">volgend uur selecteren</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Uur verlagen</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">vorig uur selecteren</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Minuut verhogen</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">volgende minuut selecteren</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Minuut verlagen</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">vorige minuut selecteren</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">AM/PM-periode in-/uitschakelen</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">AM/PM-periode in-/uitschakelen</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s geselecteerd</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">geselecteerd</string>
</resources>
