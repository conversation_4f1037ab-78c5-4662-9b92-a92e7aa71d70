<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">הדגמה של Fluent UI</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">%s נבחרו</string>
    <string name="app_modifiable_parameters">פרמטרים הניתנים לשינוי</string>
    <string name="app_right_accessory_view">תצוגת עזר ימנית</string>

    <string name="app_style">סגנון</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">סמל לחוץ</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">התחל הדגמה</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">קרוסלה</string>
    <string name="actionbar_icon_radio_label">סמל</string>
    <string name="actionbar_basic_radio_label">בסיסי</string>
    <string name="actionbar_position_bottom_radio_label">תחתון</string>
    <string name="actionbar_position_top_radio_label">עליון</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">סוג שורת הפעולות</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">מיקום שורת הפעולות</string>

    <!--AppBar-->
    <string name="app_bar_style">סגנון סרגל אפליקציות</string>
    <string name="app_bar_subtitle">כותרת משנה</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">גבול תחתון</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">בוצעה לחיצה על סמל ניווט.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">דגל</string>
    <string name="app_bar_layout_menu_settings">הגדרות</string>
    <string name="app_bar_layout_menu_search">חפש</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">אופן פעולת הגלילה: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">החלף את אופן פעולת הגלילה</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">החלפת מצב של סמל הניווט</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">הצג אוואטאר</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">הצג סמל \'הקודם\'</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">הסתר סמל</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">הצג סמל</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">החלף את סגנון הפריסה של סרגל חיפוש</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">הצג כתצוגת עזר</string>
    <string name="app_bar_layout_searchbar_action_view_button">הצג כתצוגת פעולה</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">החלף בין ערכות נושא (צור מחדש פעילות)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">החלף מצב של ערכת נושא</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">פריט</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">ניתן לגלול לתוכן נוסף</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">סגנון העיגול</string>
    <string name="avatar_style_square">סגנון הריבוע</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">גודל XXLarge</string>
    <string name="avatar_size_xlarge">גודל XLarge</string>
    <string name="avatar_size_large">גדול</string>
    <string name="avatar_size_medium">גודל בינוני</string>
    <string name="avatar_size_small">קטן</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">גדול מאוד כפול</string>
    <string name="avatar_size_xlarge_accessibility">גדול מאוד</string>
    <string name="avatar_size_xsmall_accessibility">קטן מאוד</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">המספר המרבי של אוואטארים מוצגים.</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">מונה גלישת אוואטארים</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">סוג גבול</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">קבוצת האוואטארים עם ההגדרה ‘מונה גלישת אוואטרים‘ לא תתאם למספר המרבי של אוואטארים מוצגים.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Face Stack</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">שורה של פנים חופפים חלקית</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">בוצעה לחיצה על גלישה</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">בוצעה לחיצה על תצוגת אוואטאר באינדקס %d</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">תג הודעה</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">נקודה</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">רשימה</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">דמות</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">תמונות</string>
    <string name="bottom_navigation_menu_item_news">חדשות</string>
    <string name="bottom_navigation_menu_item_alerts">התראות</string>
    <string name="bottom_navigation_menu_item_calendar">לוח שנה</string>
    <string name="bottom_navigation_menu_item_team">צוות</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">החלף מצב תוויות</string>
    <string name="bottom_navigation_three_menu_items_button">הצג שלושה פריטי תפריט</string>
    <string name="bottom_navigation_four_menu_items_button">הצג ארבעה פריטי תפריט</string>
    <string name="bottom_navigation_five_menu_items_button">הצג חמישה פריטי תפריט</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">התוויות %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">החלק במהירות כלפי מטה כדי לבטל</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">הצג עם פריטי שורה בודדת</string>
    <string name="bottom_sheet_with_double_line_items">הצג עם פריטי שורה כפולה</string>
    <string name="bottom_sheet_with_single_line_header">הצג עם כותרת שורה בודדת</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">הצג עם כותרת דו-קווית וחוצצים</string>
    <string name="bottom_sheet_dialog_button">הצג</string>
    <string name="drawer_content_desc_collapse_state">הרחב</string>
    <string name="drawer_content_desc_expand_state">מזער</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">לחץ על %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">לחיצה ארוכה של %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">לחץ על ‘התעלם מזה‘</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">הוסף פריט</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">עדכן פריט</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">התעלם מזה</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">הוסף</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">אזכור</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">מודגש</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">נטוי</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">קו תחתון</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">בצע קו חוצה</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">בטל</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">בצע שוב</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">תבליט</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">רשימה</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">קישור</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">עדכון פריט</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">ריווח</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">התעלם ממיקום</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">התחלה</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">סיום</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">מרווח קבוצה</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">מרווח פריט</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">‎%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">דגל</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">בוצעה לחיצה על סימון פריט בדגל</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">תשובה</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">בוצעה לחיצה על הפעל פריט שוב</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">העברה</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">בוצעה לחיצה על העברת פריט</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">מחק</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">בוצעה לחיצה על מחיקת פריט</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">אוואטאר</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">מצלמה</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">צלם תמונה</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">בוצעה לחיצה על פריט מצלמה</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">גלריה</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">הצג את התמונות שלך</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">בוצעה לחיצה על פריט גלריה</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">סרטוני וידאו</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">הפעל את סרטוני הווידאו שלך</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">בוצעה לחיצה על פריטי סרטון וידאו</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">ניהול</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">נהל את ספריית המדיה שלך</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">בוצעה לחיצה על ניהול פריט</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">פעולות דואר אלקטרוני</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">מסמכים</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">עודכן לאחרונה ב- 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">שיתוף</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">בוצעה לחיצה על ‘שתף פריט‘</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">הזזה</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">בוצעה לחיצה על הזזת פריט</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">מחק</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">בוצעה לחיצה על מחיקת פריט</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">מידע</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">בוצעה לחיצה על פריט מידע</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">שעון</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">בוצעה לחיצה על פריט שעון</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">התראה</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">בוצעה לחיצה על פריט התראה</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">אזור זמן</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">בוצעה לחיצה על פריט אזור זמן</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">לחצן במצבי תצוגה שונים</string>
    <string name="button">לחצן</string>
    <string name="buttonbar">סרגל לחצנים</string>
    <string name="button_disabled">דוגמה של לחצן לא זמין</string>
    <string name="button_borderless">דוגמה של לחצן ללא גבולות</string>
    <string name="button_borderless_disabled">דוגמה של לחצן ללא שוליים לא זמין</string>
    <string name="button_large">דוגמה של לחצן גדול</string>
    <string name="button_large_disabled">דוגמה של לחצן גדול לא זמין</string>
    <string name="button_outlined">דוגמה של לחצן עם קו מתאר</string>
    <string name="button_outlined_disabled">דוגמה של לחצן עם קו מתאר במצב לא זמין</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">בחר תאריך</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">תאריך בודד</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">לא נבחר תאריך</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">הצג בורר תאריכים</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">הצג את בורר התאריך והשעה עם לשונית התאריך שנבחר</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">הצג את בורר התאריך והשעה עם לשונית השעה שנבחרה</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">בורר תאריך ושעה</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">טווח תאריכים</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">התחלה:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">סיום:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">לא נבחרה התחלה</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">לא נבחר תאריך סיום</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">בחר תאריך התחלה</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">בחר תאריך סיום</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">טווח תאריך ושעה</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">בחר טווח תאריכים ושעות</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">הצג דו-שיח</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">הצג מגירה</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">הצג דו-שיח של מגירה</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">ללא דו-שיח תחתון עם עמעום</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">הצג מגירה עליונה</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">ללא דו-שיח עליון עם עמעום</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> הצג דו-שיח עליון של תצוגת עוגן</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> אל תציג כותרת דו-שיח עליון</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> הצג מתחת לכותרת הדו-שיח העליון</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">הצג מגירה ימנית</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">הצג מגירה שמאלית</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">כותרת, טקסט ראשי</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">כותרת משנה, טקסט משני</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">טקסט כותרת משנה מותאם אישית</string>
    <!-- Footer -->
    <string name="list_item_footer">כותרת תחתונה, טקסט שלישוני</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">רשימת חד-טורית עם טקסט כותרת משנה אפור</string>
    <string name="list_item_sub_header_two_line">רשימה דו-טורית</string>
    <string name="list_item_sub_header_two_line_dense">רשימה דו-טורית עם ריווח צפוף</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">רשימה של שני טורים עם תצוגת כותרת משנה מותאמת אישית</string>
    <string name="list_item_sub_header_three_line">רשימה תלת-טורית עם טקסט כותרת משנה שחור</string>
    <string name="list_item_sub_header_no_custom_views">פריטי רשימה ללא תצוגה מותאמת אישית</string>
    <string name="list_item_sub_header_large_header">פריטי רשימה עם תצוגות גדולות מותאמות אישית</string>
    <string name="list_item_sub_header_wrapped_text">פריטי רשימה עם גלישת טקסט</string>
    <string name="list_item_sub_header_truncated_text">פריטי רשימה עם טקסט חתוך</string>
    <string name="list_item_sub_header_custom_accessory_text">פעולה</string>
    <string name="list_item_truncation_middle">אמצע החיתוך.</string>
    <string name="list_item_truncation_end">סיום חיתוך.</string>
    <string name="list_item_truncation_start">תחילת חיתוך.</string>
    <string name="list_item_custom_text_view">ערך</string>
    <string name="list_item_click">לחצת על פריט הרשימה.</string>
    <string name="list_item_click_custom_accessory_view">לחצת על תצוגת עזר מותאמת אישית.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">לחצת על תצוגת עזר של כותרת משנה מותאמת אישית.</string>
    <string name="list_item_more_options">אפשרויות נוספות</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">בחר</string>
    <string name="people_picker_select_deselect_example">בחירה ביטול בחירה</string>
    <string name="people_picker_none_example">ללא</string>
    <string name="people_picker_delete_example">מחק</string>
    <string name="people_picker_custom_persona_description">דוגמה זו מציגה כיצד ליצור אובייקט IPersna מותאם אישית.</string>
    <string name="people_picker_dialog_title_removed">הסרת אישיות:</string>
    <string name="people_picker_dialog_title_added">הוספת אישיות:</string>
    <string name="people_picker_drag_started">הגרירה התחילה</string>
    <string name="people_picker_drag_ended">הגרירה הסתיימה</string>
    <string name="people_picker_picked_personas_listener">קשב אישיות</string>
    <string name="people_picker_suggestions_listener">קשב הצעות</string>
    <string name="people_picker_persona_chip_click">לחצת על %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">נמען %1$s</item>
        <item quantity="two">%1$s נמענים</item>
        <item quantity="many">%1$s נמענים</item>
        <item quantity="other">%1$s נמענים</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">הרחב גיליון תחתון עקבי</string>
    <string name="collapse_persistent_sheet_button"> הסתר גיליון תחתון עקבי</string>
    <string name="show_persistent_sheet_button"> הצג גיליון תחתון עקבי</string>
    <string name="new_view">זוהי תצוגה חדשה</string>
    <string name="toggle_sheet_content">החלף מצב תוכן של גיליון תחתון</string>
    <string name="switch_to_custom_content">עבור לתוכן מותאם אישית</string>
    <string name="one_line_content">תוכן של גיליון תחתון חד טורי</string>
    <string name="toggle_disable_all_items">החלף מצב של הפיכת כל הפריטים ללא זמינים</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">הוסף/הסר תצוגה</string>
    <string name="persistent_sheet_item_change_collapsed_height"> שינוי גובה הכיווץ</string>
    <string name="persistent_sheet_item_create_new_folder_title">תיקיה חדשה</string>
    <string name="persistent_sheet_item_create_new_folder_toast">בוצעה לחיצה על פריט תיקיה חדש</string>
    <string name="persistent_sheet_item_edit_title">עריכה</string>
    <string name="persistent_sheet_item_edit_toast">בוצעה לחיצה על ערוך פריט</string>
    <string name="persistent_sheet_item_save_title">שמור</string>
    <string name="persistent_sheet_item_save_toast">בוצעה לחיצה על שמור פריט</string>
    <string name="persistent_sheet_item_zoom_in_title">הגדלת תצוגה</string>
    <string name="persistent_sheet_item_zoom_in_toast"> בוצע לחיצה על פריט הגדלת תצוגה</string>
    <string name="persistent_sheet_item_zoom_out_title">הקטנת תצוגה</string>
    <string name="persistent_sheet_item_zoom_out_toast">בוצעה לחיצה על פריט הקטנת תצוגה</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">זמין</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">אלן מאנגר</string>
    <string name="persona_name_amanda_brady">אמנדה בריידי</string>
    <string name="persona_name_ashley_mccarthy">אשלי מק\'רתי</string>
    <string name="persona_name_carlos_slattery">קרלוס סלאטרי</string>
    <string name="persona_name_carole_poland">קרולה פולנד</string>
    <string name="persona_name_cecil_folk">ססיל פולק</string>
    <string name="persona_name_celeste_burton">סלסט ברטון</string>
    <string name="persona_name_charlotte_waltson">שרלוט וולטסון</string>
    <string name="persona_name_colin_ballinger">קולין בלינגר</string>
    <string name="persona_name_daisy_phillips">דייזי פיליפס</string>
    <string name="persona_name_elliot_woodward">אליוט וודוורד</string>
    <string name="persona_name_elvia_atkins">אלוויה אטקינס</string>
    <string name="persona_name_erik_nason">אריק נסון</string>
    <string name="persona_name_henry_brill">הנרי בריל</string>
    <string name="persona_name_isaac_fielder">אייזיק פילדר</string>
    <string name="persona_name_johnie_mcconnell">גלעד הראל</string>
    <string name="persona_name_kat_larsson">נטע סינגר</string>
    <string name="persona_name_katri_ahokas">קאטרי אהוקה</string>
    <string name="persona_name_kevin_sturgis">חגית גבאי</string>
    <string name="persona_name_kristen_patterson">כלנית ברעם</string>
    <string name="persona_name_lydia_bauer">לילך ברקאי</string>
    <string name="persona_name_mauricio_august">מאוריציו אוגוסט</string>
    <string name="persona_name_miguel_garcia">איתמר אפשטיין</string>
    <string name="persona_name_mona_kane">מונה קובליו</string>
    <string name="persona_name_robin_counts">רובין קאונטס</string>
    <string name="persona_name_robert_tolbert">רוברט טולברט</string>
    <string name="persona_name_tim_deboer">טים דבואר</string>
    <string name="persona_name_wanda_howard">וונדה הווארד</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">מהנדס</string>
    <string name="persona_subtitle_manager">מנהל</string>
    <string name="persona_subtitle_researcher">חוקר</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (דוגמה של טקסט ארוך לבדיקת קיטוע)</string>
    <string name="persona_view_description_xxlarge">אוואטאר בגודל XXLarge עם שלוש שורות טקסט</string>
    <string name="persona_view_description_large">אוואטאר גדול עם שתי שורות טקסט</string>
    <string name="persona_view_description_small">אוואטאר קטן עם שורת טקסט אחת</string>
    <string name="people_picker_hint">לא מוצג אף ‘עם רמז‘</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">שבב אישיות מושבת</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">שגיאת שבב אישיות</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">שבב אישיות ללא סמל סגירה</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">שבב אישיות בסיסי</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">לחצת על שבב האישיות שנבחר.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">שיתוף</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">עקוב</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">הזמן אנשים</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">רענן דף</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">פתח בדפדפן</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">זהו תפריט מוקפץ מרובה קווים. הקווים המרביים מוגדרים לשתיים, שאר הטקסט ייחתך.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">כל החדשות</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">חדשות שמורות</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">חדשות מאתרים</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">יידע מחוץ לשעות העבודה</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">הודע רק כאשר אני לא פעיל במחשב השולחני</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">לחצת על הפריט:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">תפריט פשוט</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">תפריט פשוט2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">תפריט עם פריט ניתן לבחירה אחד וחוצץ</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">תפריט עם כל הפריטים, הסמלים והטקסט הארוך הניתנים לבחירה</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">הצג</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">התקדמות מעגלית</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">קטן</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">גודל בינוני</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">גדול</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">התקדמות לינארית</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">ללא הגבלת אורך</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">מחוון התקדמות</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">‎Lorem ipsum dolor sit amet, consectetur adipiscing elit,
‎        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.‎
‎‪        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">סרגל חיפוש</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">מיקרופון משמיע קול</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">תיקון שגיאות אוטומטי</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">בוצעה לחיצה על המיקרופון</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">תצוגה ימנית נלחצה</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">חיפוש בלוח המקשים נלחץ</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">הצג בר התראות</string>
    <string name="fluentui_dismiss_snackbar">בטל Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">פעולה</string>
    <string name="snackbar_action_long">פעולת טקסט ארוכה</string>
    <string name="snackbar_single_line">בר התראות (snackbar) של שורה יחידה</string>
    <string name="snackbar_multiline">זהו בר התראה (snackbar) מרובה שורות. מספר השורות המרבי לשתיים, שאר הטקסט ייחתך.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">זהו בר התראה (snackbar) להכרזה. הוא משמש לתקשורת עם תכונות חדשות.</string>
    <string name="snackbar_primary">זהו בר התראות (snackbar) ראשי.</string>
    <string name="snackbar_light">זהו בר התראות (snackbar) קל.</string>
    <string name="snackbar_warning">זהו משוב אזהרה.</string>
    <string name="snackbar_danger">זהו משוב סכנה.</string>
    <string name="snackbar_description_single_line">משך זמן קצר</string>
    <string name="snackbar_description_single_line_custom_view">משך זמן ארוך עם התקדמות מעגלית כתצוגה מותאמת אישית קטנה</string>
    <string name="snackbar_description_single_line_action">משך זמן קצר עם פעולה</string>
    <string name="snackbar_description_single_line_action_custom_view">משך זמן קצר עם פעולה ותצוגה בינונית מותאמת אישית</string>
    <string name="snackbar_description_single_line_custom_text_color">משך זמן קצר עם צבע טקסט מותאם אישית</string>
    <string name="snackbar_description_multiline">משך זמן ארוך</string>
    <string name="snackbar_description_multiline_custom_view">משך זמן ארוך עם תצוגה מותאמת אישית קטנה</string>
    <string name="snackbar_description_multiline_action">משך זמן לא מוגדר עם עדכוני פעולה וטקסט</string>
    <string name="snackbar_description_multiline_action_custom_view">משך זמן קצר עם פעולה ותצוגה בינונית מותאמת אישית</string>
    <string name="snackbar_description_multiline_action_long">משך זמן קצר עם טקסט פעולה ארוך</string>
    <string name="snackbar_description_announcement">משך זמן קצר</string>
    <string name="snackbar_description_updated">הטקסט עודכן.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">הצג בר התראות (snackbar)</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">חד טורי</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">מרובה שורות</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">סגנון הכרזה</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">סגנון ראשי</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">סגנון בהיר</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">סגנון אזהרה</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">סגנון סכנה</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">בית</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">דואר</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">הגדרות</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">הודעה</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">יותר</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">יישור טקסט</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">אנכי</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">אופקי</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">ללא טקסט</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">פריטי כרטיסייה</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">כותרת</string>
    <string name="cell_sample_description">תיאור</string>
    <string name="calculate_cells">טען/חשב 100 תאים</string>
    <string name="calculate_layouts">טען/חשב 100 פריסות</string>
    <string name="template_list">רשימת תבניות</string>
    <string name="regular_list">רשימה רגילה</string>
    <string name="cell_example_title">כותרת: תא</string>
    <string name="cell_example_description">תיאור: הקש כדי לשנות כיוון</string>
    <string name="vertical_layout">פריסה אנכית</string>
    <string name="horizontal_layout">פריסה אופקית</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">כרטיסיה רגילה - 2 מקטעים</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">כרטיסיה רגילה עם 3 מקטעים</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">כרטיסיה רגילה עם 4 מקטעים</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">כרטיסיה רגילה עם רכיב הדף</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">החלף כרטיסיה</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">תגית גלולות </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">הקש עבור תיאור כלי</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">הקש לקבלת תיאור כלי של לוח שנה מותאם אישית</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">הקש על תיאור כלי של צבע מותאם אישית</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">הקש כדי לבטל תיאור כלי פנימי</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">הקש לקבלת תיאור כלי של תצוגה מותאמת אישית</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">תיאור כלי עליון של צבע מותאם אישית</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">תיאור כלי קצה עליון עם היסט 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">תיאור כלי התחלה תחתון</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">תיאור כלי קצה תחתון עם היסט של 10dp</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">בטל בתוך תיאור כלי</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">תיאור הכלי נסגר</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">הכותרת היא 28sp בהיר</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">כותרת 1 היא 20sp בינוני</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">כותרת 2 היא 20sp רגיל</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">הכותרת היא 18sp רגיל</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">כותרת משנה 1 היא 16sp רגיל</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">כותרת משנה 2 היא 16spבינוני</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">גוף הודעה 1 הוא 14sp רגיל</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">גוף הודעה 2 הוא 14sp בינוני</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">הכתובית היא 12sp רגיל</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">גירסת SDK: ‏%s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">פריט %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">תיקיה</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">נלחץ</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB מורחב</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB מכווץ</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">לחץ כדי לרענן את הרשימה</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">פתח מגירה</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">פריט תפריט</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">היסט X (ב- dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">היסט Y (ב- dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">טקסט תוכן</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">טקסט תוכן חוזר</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">רוחב התפריט ישתנה בהתאם לטקסט התוכן. הרוחב המרבי
        מוגבל ל- 75% מגודל המסך. שולי התוכן מהצדדים ומלמטה נקבעים על-ידי אסימון. תהיה חזרה על אותו טקסט תוכן כדי לשנות את
        הגובה.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">פתח תפריט</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">כרטיס בסיסי</string>
    <!-- UI Label for Card -->
    <string name="file_card">כרטיס קובץ</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">כרטיס הכרזה</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">ממשק משתמש אקראי</string>
    <!-- UI Label for Options -->
    <string name="card_options">אפשרויות</string>
    <!-- UI Label for Title -->
    <string name="card_title">כותרת</string>
    <!-- UI Label for text -->
    <string name="card_text">טקסט</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">טקסט משנה</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">במקרה הצורך, הטקסט המשני של כרזה זו יכול לגלוש לשתי שורות.</string>
    <!-- UI Label Button -->
    <string name="card_button">לחצן</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">הצג תיבת דו-שיח</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">סגור את תיבת הדו-שיח בעת לחיצה מחוצה לה</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">סגור את תיבת הדו-שיח בעת לחיצה על לחצן \'הקודם\'</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">תיבת הדו-שיח נסגרה</string>
    <!-- UI Label Cancel -->
    <string name="cancel">ביטול</string>
    <!-- UI Label Ok -->
    <string name="ok">אישור</string>
    <!-- A sample description -->
    <string name="dialog_description">תיבת דו-שיח היא חלון קטן המבקש מהמשתמש לקבל החלטה או להזין מידע נוסף.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">פתח מגירה</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">הרחב מגירה</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">סגור מגירה</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">בחר סוג מגירה</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">עליון</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">המגירה כולה מוצגת באזור הגלוי.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">תחתון</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">המגירה כולה מוצגת באזור הגלוי. החלק במהירות כלפי מעלה את תוכן הגלילה. המגירה הניתנת להרחבה מתרחבת באמצעות נקודת אחיזה לגרירה.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">שקופית שמאלית מעל</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">החלק את המגירה לאזור הגלוי מצד שמאל.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">שקופית ימנית מעל</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">החלק את המגירה לאזור הגלוי מצד ימין.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">שקופית תחתונה מעל</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">החלק את המגירה לאזור הגלוי מתחתית המסך. החלק במהירות כלפי מעלה במגירה הניתנת להרחבה, הבא את שאר החלק לאזור הגלוי &amp; לאחר מכן גלול.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim גלוי</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">בחר תוכן מגירה</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">תוכן הניתן לגלילה בגודל מסך מלא</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">יותר מחצי תוכן מסך</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">תוכן של פחות מחצי מסך</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">תוכן גודל דינאמי</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">תוכן מגירה מקונן</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">ניתן להרחבה</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">דלג על המצב ‘פתוח‘</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">מנע ביטול בעת לחיצה על Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">הצג נקודת אחיזה</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">כותרת</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">טקסט של תיאור כלי</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">הקש לקבלת תיאור כלי של תוכן מותאם אישית</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">התחלה עליונה </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">סוף עליון </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">התחלה תחתונה </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">סוף תחתון </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">מרכז </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">מרכז מותאם אישית</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">לקבלת עדכונים לגבי הערות מוצר, </string>
    <string name="click_here">לחץ כאן.</string>
    <string name="open_source_cross_platform">מערכת עיצוב חוצת פלטפורמות של קוד פתוח.</string>
    <string name="intuitive_and_powerful">אינטואיטיבי וחזק.</string>
    <string name="design_tokens">אסימוני עיצוב</string>
    <string name="release_notes">הערות מוצר</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub Repo Link</string>
    <string name="report_issue">דווח על בעיה</string>
    <string name="v1_components">רכיבי V1</string>
    <string name="v2_components">רכיבי V2</string>
    <string name="all_components">הכול</string>
    <string name="fluent_logo">סמל Fluent</string>
    <string name="new_badge">חדש</string>
    <string name="modified_badge">השתנה</string>
    <string name="api_break_badge">מעבר API</string>
    <string name="app_bar_more">עוד</string>
    <string name="accent">הדגשה</string>
    <string name="appearance">מראה</string>
    <string name="choose_brand_theme">בחר את ערכת הנושא של המותג שלך:</string>
    <string name="fluent_brand_theme">מותג Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">בחר מראה</string>
    <string name="appearance_system_default">ברירת המחדל של המערכת</string>
    <string name="appearance_light">בהיר</string>
    <string name="appearance_dark">כהה</string>
    <string name="demo_activity_github_link">Demo Activity GitHub Link</string>
    <string name="control_tokens_details">פרטי אסימוני בקרה</string>
    <string name="parameters">פרמטרים</string>
    <string name="control_tokens">אסימוני פקד</string>
    <string name="global_tokens">אסימונים כלליים</string>
    <string name="alias_tokens">אסימוני כינוי</string>
    <string name="sample_text">טקסט</string>
    <string name="sample_icon">סמל לדוגמה</string>
    <string name="color">צבע</string>
    <string name="neutral_color_tokens">אסימוני צבע ניטרליים</string>
    <string name="font_size_tokens">אסימוני גודל גופן</string>
    <string name="line_height_tokens">אסימוני גובה שורה</string>
    <string name="font_weight_tokens">אסימוני עובי גופן</string>
    <string name="icon_size_tokens">אסימוני גודל סמל</string>
    <string name="size_tokens">אסימוני גודל</string>
    <string name="shadow_tokens">אסימוני הטלת צל</string>
    <string name="corner_radius_tokens">Corner RadiusTokens</string>
    <string name="stroke_width_tokens">אסימוני רוחב משיכה</string>
    <string name="brand_color_tokens">אסימוני צבע של מותג</string>
    <string name="neutral_background_color_tokens">אסימוני צבע של רקע ניטרלי</string>
    <string name="neutral_foreground_color_tokens">אסימוני צבע בחזית ניטרלית</string>
    <string name="neutral_stroke_color_tokens">אסימוני צבע של משיכת דיו ניטרלית</string>
    <string name="brand_background_color_tokens">אסימוני צבע של רקע מותג</string>
    <string name="brand_foreground_color_tokens">אסימוני צבע בחזית של מותג</string>
    <string name="brand_stroke_color_tokens">אסימוני צבע משיכת דיו של מותג</string>
    <string name="error_and_status_color_tokens">אסימוני צבע של שגיאה ומצב</string>
    <string name="presence_tokens">אסימוני צבע של נוכחות</string>
    <string name="typography_tokens">אסימוני טיפוגרפיה</string>
    <string name="unspecified">לא צוין</string>

</resources>