<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">P</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">S</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">Ç</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">P</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">C</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">C</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">P</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Sonraki haftaya git</string>
    <string name="accessibility_goto_previous_week">Önceki haftaya git</string>
    <string name="accessibility_today">bugün</string>
    <string name="accessibility_selected">Seçili</string>

    <!-- *** Shared *** -->
    <string name="done">Bitti</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Bugün</string>
    <string name="tomorrow">Yarın</string>
    <string name="yesterday">Dün</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Başlangıç saati</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Bitiş saati</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Başlangıç tarihi</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Bitiş tarihi</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Saat Seçin</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Tarih Seçin</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Tarih Saat Seçici</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Tarih Seçici</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Tarih Saat Seçici Aralığı</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Tarih Seçici Aralığı</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Başlangıç saati sekmesi</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Bitiş saati sekmesi</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Başlangıç tarihi sekmesi</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Bitiş tarihi sekmesi</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">İletişim kutusunu kapat</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Ayı artır</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">gelecek ayı seç</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Ayı azalt</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">önceki ayı seç</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Günü artır</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">sonraki günü seç</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Günü azalt</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">önceki gün seç</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Yılı artır</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">gelecek yılı seç</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Yılı azalt</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">önceki yılı seç</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Tarihi artır</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">sonraki tarihi seç</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Tarihi azalt</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">önceki tarihi seç</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Saati artır</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">sonraki saati seç</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Saati azalt</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">önceki saati seç</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Dakikayı artır</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">sonraki dakikayı seç</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Dakikayı azalt</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">önceki dakikayı seç</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">ÖÖ/ÖS aralığını değiştir</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">ÖÖ/ÖS aralığını değiştir</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s seçildi</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">seçili</string>
</resources>
