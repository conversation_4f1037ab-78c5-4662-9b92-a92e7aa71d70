<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">Пн</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">Вт</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">Ср</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">Чт</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">Пт</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">Сб</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">Вс</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Перейти к следующей неделе</string>
    <string name="accessibility_goto_previous_week">Перейти к предыдущей неделе</string>
    <string name="accessibility_today">сегодня</string>
    <string name="accessibility_selected">Выбрано</string>

    <!-- *** Shared *** -->
    <string name="done">Готово</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Сегодня</string>
    <string name="tomorrow">Завтра</string>
    <string name="yesterday">Вчера</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Время начала</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Время окончания</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Дата начала</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Дата окончания</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Выбор времени</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Выбор даты</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Выбор даты и времени</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Выбор даты</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Диапазон выбора даты и времени</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Диапазон выбора даты</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Вкладка "Время начала"</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Вкладка "Время окончания"</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Вкладка "Дата начала"</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Вкладка "Дата окончания"</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Закрытие диалогового окна</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Прибавить месяц</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">выберите следующий месяц</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Убавить месяц</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">выберите предыдущий месяц</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Прибавить день</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">выберите следующий день</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Убавить день</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">выберите предыдущий день</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Прибавить год</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">выберите следующий год</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Убавить год</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">выберите предыдущий год</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Увеличить дату</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">выберите следующую дату</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Уменьшить дату</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">выберите предыдущую дату</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Прибавить час</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">выберите следующий час</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Убавить час</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">выберите предыдущий час</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Прибавить минуту</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">выберите следующую минуту</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Убавить минуту</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">выберите предыдущую минуту</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">Переключить AM/PM</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">переключение AM/PM</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">Выбрано: %s</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">выбрано</string>
</resources>
