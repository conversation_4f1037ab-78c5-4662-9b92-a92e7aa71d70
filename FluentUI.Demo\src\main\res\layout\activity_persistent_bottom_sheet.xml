<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".demos.PersistentBottomSheetActivity">

    <LinearLayout
        android:id="@+id/demo_main_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/default_layout_margin"
        android:background="?attr/fluentuiBackgroundColor"
        android:orientation="vertical">

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/show_persistent_bottom_sheet_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/default_layout_margin"
            android:text="@string/expand_bottom_sheet_button" />

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/collapse_persistent_bottom_sheet_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/default_layout_margin"
            android:text="@string/collapse_persistent_sheet_button" />

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/toggle_bottom_sheet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/default_layout_margin"
            android:text="@string/toggle_sheet_content" />

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/set_one_line_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/default_layout_margin"
            android:text="@string/one_line_content" />

        <com.microsoft.fluentui.widget.Button
            android:id="@+id/toggle_disable_all_items"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/default_layout_margin"
            android:text="@string/toggle_disable_all_items" />

    </LinearLayout>

    <ScrollView
        android:id="@+id/scroll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/demo_main_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/large_scrollable_text"
                android:textColor="@color/fluentui_black" />

        </LinearLayout>
    </ScrollView>

    <com.microsoft.fluentui.persistentbottomsheet.PersistentBottomSheet
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/demo_persistent_sheet"
        android:layout_alignParentBottom="true"
        app:fluentui_peekHeight="@dimen/fluentui_persistent_bottom_sheet_peek_height"
        app:fluentui_isDrawerHandleVisible="true"/>

    <com.microsoft.fluentui.persistentbottomsheet.PersistentBottomSheet
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/default_persistent_sheet"
        android:layout_alignParentBottom="true"
        android:visibility="gone"
        app:fluentui_peekHeight="@dimen/fluentui_persistent_bottom_sheet_peek_height"
        app:fluentui_itemsInRow="5"
        app:fluentui_isDrawerHandleVisible="true"/>

</RelativeLayout>