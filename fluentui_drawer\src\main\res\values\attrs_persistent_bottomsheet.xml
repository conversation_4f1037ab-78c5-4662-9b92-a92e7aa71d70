<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>
    <declare-styleable name="PersistentBottomSheet">
        <!-- The min height of the BottomSheet -->
        <attr name="fluentui_peekHeight"/>
        <!--  Determines whether to keep the default drawer handle or not -->
        <attr name="fluentui_isDrawerHandleVisible"/>
        <!--  horizontal Item in a row-->
        <attr name="fluentui_itemsInRow"/>
        <!-- horizontal item text style-->
        <attr name="fluentui_horizontalItemTextAppearance"/>
        <!-- vertical item text style-->
        <attr name="fluentui_verticalItemTextAppearance"/>
        <!-- vertical item subtitle style-->
        <attr name="fluentui_verticalItemSubTextAppearance"/>
        <!--header text style-->
        <attr name="fluentui_headerTextAppearance"/>
    </declare-styleable>
</resources>