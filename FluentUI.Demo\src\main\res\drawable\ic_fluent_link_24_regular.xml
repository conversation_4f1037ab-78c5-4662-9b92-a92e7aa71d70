<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <path
        android:fillColor="@color/fluent_default_icon_tint"
        android:pathData="M9.25 7C9.664 7 10 7.336 10 7.75c0 0.377-0.277 0.688-0.64 0.742L9.25 8.5H7c-1.933 0-3.5 1.567-3.5 3.5 0 1.864 1.457 3.388 3.294 3.494L7 15.5h2.25c0.414 0 0.75 0.336 0.75 0.75 0 0.377-0.277 0.688-0.64 0.742L9.25 17H7c-2.761 0-5-2.239-5-5 0-2.678 2.105-4.864 4.75-4.994L7 7h2.25zM17 7c2.761 0 5 2.239 5 5 0 2.678-2.105 4.864-4.75 4.994L17 17h-2.25C14.336 17 14 16.664 14 16.25c0-0.377 0.277-0.688 0.64-0.742l0.11-0.008H17c1.933 0 3.5-1.567 3.5-3.5 0-1.864-1.457-3.388-3.294-3.494L17 8.5h-2.25C14.336 8.5 14 8.164 14 7.75c0-0.377 0.277-0.688 0.64-0.742L14.75 7H17zM7 11.25h10c0.414 0 0.75 0.336 0.75 0.75 0 0.38-0.282 0.694-0.648 0.743L17 12.75H7c-0.414 0-0.75-0.336-0.75-0.75 0-0.38 0.282-0.694 0.648-0.743L7 11.25h10H7z"
        android:strokeWidth="1" />
</vector>