package com.microsoft.fluentui.icons.avataricons.presence.away.large

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import com.microsoft.fluentui.icons.avataricons.presence.away.LargeGroup

val LargeGroup.Dark: ImageVector
    get() {
        if (_dark != null) {
            return _dark!!
        }
        _dark = Builder(name = "Dark", defaultWidth = 20.0.dp, defaultHeight = 20.0.dp,
                viewportWidth = 20.0f, viewportHeight = 20.0f).apply {
            path(fill = SolidColor(Color(0xFF000000)), stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(10.0f, 10.0f)
                moveToRelative(-9.0f, 0.0f)
                arcToRelative(9.0f, 9.0f, 0.0f, true, true, 18.0f, 0.0f)
                arcToRelative(9.0f, 9.0f, 0.0f, true, true, -18.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0xFFF8D22A)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(10.0f, 18.0f)
                curveTo(14.4183f, 18.0f, 18.0f, 14.4183f, 18.0f, 10.0f)
                curveTo(18.0f, 5.5817f, 14.4183f, 2.0f, 10.0f, 2.0f)
                curveTo(5.5817f, 2.0f, 2.0f, 5.5817f, 2.0f, 10.0f)
                curveTo(2.0f, 14.4183f, 5.5817f, 18.0f, 10.0f, 18.0f)
                close()
                moveTo(10.5f, 6.5f)
                verticalLineTo(9.5194f)
                lineTo(12.6247f, 11.2191f)
                curveTo(13.056f, 11.5641f, 13.1259f, 12.1934f, 12.7809f, 12.6247f)
                curveTo(12.4359f, 13.056f, 11.8066f, 13.1259f, 11.3753f, 12.7809f)
                lineTo(8.8753f, 10.7809f)
                curveTo(8.6381f, 10.5911f, 8.5f, 10.3038f, 8.5f, 10.0f)
                verticalLineTo(6.5f)
                curveTo(8.5f, 5.9477f, 8.9477f, 5.5f, 9.5f, 5.5f)
                curveTo(10.0523f, 5.5f, 10.5f, 5.9477f, 10.5f, 6.5f)
                close()
            }
        }
                .build()
        return _dark!!
    }

private var _dark: ImageVector? = null
