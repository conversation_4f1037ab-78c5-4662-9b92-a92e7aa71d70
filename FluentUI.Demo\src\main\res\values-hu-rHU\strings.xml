<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent felhasználói felület – demó</string>
    <string name="app_title">Fluent felhasználói felület</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Kijelölte ezt: %s</string>
    <string name="app_modifiable_parameters">Módosítható paraméterek</string>
    <string name="app_right_accessory_view">Jobb kiegészítő nézet</string>

    <string name="app_style">Stílus</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikon megnyomva</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button"><PERSON><PERSON><PERSON> ind<PERSON>a</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Forgótár</string>
    <string name="actionbar_icon_radio_label">Ikon</string>
    <string name="actionbar_basic_radio_label">Alapszintű</string>
    <string name="actionbar_position_bottom_radio_label">Lent</string>
    <string name="actionbar_position_top_radio_label">Fent</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Műveletsáv típusa</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Műveletsáv pozíciója</string>

    <!--AppBar-->
    <string name="app_bar_style">Alkalmazássáv stílusa</string>
    <string name="app_bar_subtitle">Felirat</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Alsó szegély</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">A navigációs ikonra kattintott.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Zászló</string>
    <string name="app_bar_layout_menu_settings">Beállítások</string>
    <string name="app_bar_layout_menu_search">Keresés</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Görgetési viselkedés: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Görgetési viselkedés be- és kikapcsolása</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Navigáció be- és kikapcsolása ikon</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Avatar megjelenítése</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Vissza ikon megjelenítése</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ikon elrejtése</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Ikon megjelenítése</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Keresősáv elrendezési stílusának be- és kikapcsolása</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Megjelenítés kiegészítő nézetként</string>
    <string name="app_bar_layout_searchbar_action_view_button">Megjelenítés műveletnézetként</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Váltás a témák között (újra létrehozza a tevékenységet)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Téma váltása</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Elem</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Extra görgethető tartalom</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Kör stílusa</string>
    <string name="avatar_style_square">Négyzetstílus</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Nagy</string>
    <string name="avatar_size_medium">Közepes</string>
    <string name="avatar_size_small">Kicsi</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dupla extra nagy</string>
    <string name="avatar_size_xlarge_accessibility">Extra nagy</string>
    <string name="avatar_size_xsmall_accessibility">Nagyon kicsi</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Megjelenített avatarok maximális száma</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Túlcsordulási avatarok száma</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Szegély típusa</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Az OverflowAvatarCount beállítással rendelkező avatarcsoport nem felel meg a megjelenített avatarok maximális számának.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Archalom</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Archalom</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">A túlcsordulásra kattintott</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">A(z) %d indexnél az avatarnézetre kattintott</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Értesítési jelvény</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Pont</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Szereplő</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fényképek</string>
    <string name="bottom_navigation_menu_item_news">Hírek</string>
    <string name="bottom_navigation_menu_item_alerts">Értesítések</string>
    <string name="bottom_navigation_menu_item_calendar">Naptár</string>
    <string name="bottom_navigation_menu_item_team">Csapat</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Címkék be- és kikapcsolása</string>
    <string name="bottom_navigation_three_menu_items_button">Három menüelem megjelenítése</string>
    <string name="bottom_navigation_four_menu_items_button">Négy menüelem megjelenítése</string>
    <string name="bottom_navigation_five_menu_items_button">Öt menüelem megjelenítése</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">A címkék állapota: %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Lefelé pöccintés engedélyezése a bezáráshoz</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Megjelenítés egysoros elemekkel</string>
    <string name="bottom_sheet_with_double_line_items">Megjelenítés kétsoros elemekkel</string>
    <string name="bottom_sheet_with_single_line_header">Megjelenítés egysoros fejléccel</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Megjelenítés dupla sorfejléccel és elválasztókkal</string>
    <string name="bottom_sheet_dialog_button">Megjelenítés</string>
    <string name="drawer_content_desc_collapse_state">Kibontás</string>
    <string name="drawer_content_desc_expand_state">Kis méret</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Kattintás a következőre: %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Hosszú kattintás %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">A bezárás gombra kattintás</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Elem beszúrása</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Elem frissítése</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Elvetés</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Hozzáadás</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Említés</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Félkövér</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Dőlt</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Aláhúzás</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Áthúzás</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Visszavonás</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Megismétlés</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Felsorolásjel</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Hivatkozás</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Az elem frissül</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Térköz</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Pozíció bezárása</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">INDÍTÁS</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Csoporttér</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Elemtér</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Zászló</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Az elem megjelölésére kattintott</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Válasz</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">A válaszelemre kattintott</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Továbbítás</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Az elem továbbítására kattintott</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Törlés</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Az elem törlésére kattintott</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Fénykép készítése</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">A kameraelemre kattintott</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galéria</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Fényképek megtekintése</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">A katalóguselemre kattintott</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videók</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">A videók lejátszása</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">A videók elemre kattintott</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Kezelés</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">A médiatár kezelése</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Az elem kezelésére kattintott</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-mail műveletek</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumentumok</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Utolsó frissítés: 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Megosztás</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Az elem megosztására kattintott</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Áthelyezés</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Az elem áthelyezésére kattintott</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Törlés</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Az elem törlésére kattintott</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Információ</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Az információs elemre kattintott</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Óra</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Az óraelemre kattintott</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Riasztó</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">A riasztáselemre kattintott</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Időzóna</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Az időzónaelemre kattintott</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">A gomb különböző nézetei</string>
    <string name="button">Gomb</string>
    <string name="buttonbar">Gombsáv</string>
    <string name="button_disabled">Letiltott gomb – példa</string>
    <string name="button_borderless">Szegély nélküli gomb – példa</string>
    <string name="button_borderless_disabled">Szegély nélküli letiltott gomb – példa</string>
    <string name="button_large">Nagy méretű gomb – példa</string>
    <string name="button_large_disabled">Nagy méretű letiltott gomb – példa</string>
    <string name="button_outlined">Körvonalas gomb – példa</string>
    <string name="button_outlined_disabled">Körvonalas letiltott gomb – példa</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Válasszon egy dátumot</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Egyetlen dátum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Nincs kiválasztva dátum</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Dátumválasztó megjelenítése</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Dátum- és időválasztó megjelenítése a kijelölt dátum lappal</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Dátum- és időválasztó megjelenítése a kijelölt idő lappal</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Dátum- és időválasztó megjelenítése</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Dátumtartomány</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Kezdés:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Befejezés:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Nincs kezdés kiválasztva</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Nincs kiválasztva vég</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Kezdés dátumának kiválasztása</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Záró dátum kiválasztása</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Dátum- és időtartomány</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">A dátum- és időtartomány kiválasztása</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Párbeszédpanel megjelenítése</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Fiók megjelenítése</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Fiók párbeszédpanel megjelenítése</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Nincs halványuló alsó párbeszédpanel</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Felső fiók megjelenítése</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Nincs elhalványuló felső párbeszédpanel</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Horgonynézet felső párbeszédpaneljének megjelenítése</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Cím nélküli felső párbeszédpanel megjelenítése</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> A cím alatti felső párbeszédpanel megjelenítése</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Jobb oldali fiók megjelenítése</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Bal oldali fiók megjelenítése</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Cím, elsődleges szöveg</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Alcím, másodlagos szöveg</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Egyéni feliratszöveg</string>
    <!-- Footer -->
    <string name="list_item_footer">Lábléc, harmadlagos szöveg</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Egysoros lista szürke alfejlécszöveggel</string>
    <string name="list_item_sub_header_two_line">Kétsoros lista</string>
    <string name="list_item_sub_header_two_line_dense">Kétsoros lista sűrű térközzel</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Kétsoros lista egyéni másodlagos alcímnézettel</string>
    <string name="list_item_sub_header_three_line">Háromsoros lista fekete alfejlécszöveggel</string>
    <string name="list_item_sub_header_no_custom_views">Egyéni nézetekkel nem rendelkező listaelemek</string>
    <string name="list_item_sub_header_large_header">Nagy egyéni nézetekkel rendelkező listaelemek</string>
    <string name="list_item_sub_header_wrapped_text">Tördelt szöveget tartalmazó listaelemek</string>
    <string name="list_item_sub_header_truncated_text">Csonkolt szöveget tartalmazó listaelemek</string>
    <string name="list_item_sub_header_custom_accessory_text">Művelet</string>
    <string name="list_item_truncation_middle">Középső csonkolás.</string>
    <string name="list_item_truncation_end">Csonkolás befejezése.</string>
    <string name="list_item_truncation_start">Csonkolás indítása.</string>
    <string name="list_item_custom_text_view">Érték</string>
    <string name="list_item_click">A listaelemre kattintott.</string>
    <string name="list_item_click_custom_accessory_view">Az egyéni kiegészítő nézetre kattintott.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Az alfejléc egyéni kiegészítő nézetére kattintott.</string>
    <string name="list_item_more_options">További beállítások</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Kiválasztás</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Nincs</string>
    <string name="people_picker_delete_example">Törlés</string>
    <string name="people_picker_custom_persona_description">Ez a példa bemutatja, hogyan hozható létre egyéni IPersona-objektum.</string>
    <string name="people_picker_dialog_title_removed">Eltávolított egy általános személyt:</string>
    <string name="people_picker_dialog_title_added">Felvett egy általános személyt:</string>
    <string name="people_picker_drag_started">Húzás elindítva</string>
    <string name="people_picker_drag_ended">A húzás befejeződött</string>
    <string name="people_picker_picked_personas_listener">Személyfigyelő</string>
    <string name="people_picker_suggestions_listener">Javaslatok figyelője</string>
    <string name="people_picker_persona_chip_click">A következőre kattintott: %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s címzett</item>
        <item quantity="other">%1$s címzett</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Állandó alsó lap kibontása</string>
    <string name="collapse_persistent_sheet_button"> Állandó alsó lap elrejtése</string>
    <string name="show_persistent_sheet_button"> Állandó alsó lap megjelenítése</string>
    <string name="new_view">Ez egy új nézet</string>
    <string name="toggle_sheet_content">Alsó lap tartalmának be-/kikapcsolása</string>
    <string name="switch_to_custom_content">Váltás egyéni tartalomra</string>
    <string name="one_line_content">Egysoros alsó lap tartalma</string>
    <string name="toggle_disable_all_items">Minden elem letiltásának be-/kikapcsolása</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Nézet hozzáadása/eltávolítása</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Összecsukott magasság módosítása</string>
    <string name="persistent_sheet_item_create_new_folder_title">Új mappa</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Új mappaelemre kattintott</string>
    <string name="persistent_sheet_item_edit_title">Szerkesztés</string>
    <string name="persistent_sheet_item_edit_toast">Az elem szerkesztésére kattintott</string>
    <string name="persistent_sheet_item_save_title">Mentés</string>
    <string name="persistent_sheet_item_save_toast">Az elem mentésére kattintott</string>
    <string name="persistent_sheet_item_zoom_in_title">Nagyítás</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Az elem nagyítására kattintott</string>
    <string name="persistent_sheet_item_zoom_out_title">Kicsinyítés</string>
    <string name="persistent_sheet_item_zoom_out_toast">Az elem kicsinyítésére kattintott</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Elérhető</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Tervező</string>
    <string name="persona_subtitle_engineer">Mérnök</string>
    <string name="persona_subtitle_manager">Vezető</string>
    <string name="persona_subtitle_researcher">Kutató</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (hosszú szöveges példa a csonkítás teszteléséhez)</string>
    <string name="persona_view_description_xxlarge">XXLarge avatar három sornyi szöveggel</string>
    <string name="persona_view_description_large">Nagy avatar két sornyi szöveggel</string>
    <string name="persona_view_description_small">Kis avatar egy sornyi szöveggel</string>
    <string name="people_picker_hint">Nincs tippel rendelkező megjelenített</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Letiltott általános személy lapka</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Hiba az általános személy lapkájában</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Általános személy lapkája bezárás ikon nélkül</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Alapszintű általános személy lapka</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">A kijelölt általános személy lapkájára kattintott.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Megosztás</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Követés</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Személyek meghívása</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Lap frissítése</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Megnyitás böngészőben</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Ez egy többsoros menü. A sorok maximális száma kettő, a többi szöveg csonkolódik.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Minden hír</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Mentett hírek</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Webhelyhírek</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Értesítés munkaidőn kívül</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Értesítés, ha nem aktív az asztalon</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">A következő elemre kattintott:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Egyszerű menü</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">2. egyszerű menü</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menü egy kijelölhető elemmel és egy elválasztóval</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Az összes kijelölhető elemet, ikont és hosszú szöveget tartalmazó menü</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Megjelenítés</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Körkörös folyamat</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Kicsi</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Közepes</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Nagy</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineáris előrehaladás</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Határozatlan</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Határozott</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Keresősáv</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofon visszahívása</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Automatikus javítás</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofon megnyomva</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Jobb oldali nézet megnyomva</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Billentyűzetkeresés megnyomva</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Tálca megjelenítése</string>
    <string name="fluentui_dismiss_snackbar">Tálca elvetése</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Művelet</string>
    <string name="snackbar_action_long">Hosszú szövegművelet</string>
    <string name="snackbar_single_line">Egysoros tálca</string>
    <string name="snackbar_multiline">Ez egy többsoros nasisáv. A sorok maximális száma kettő, a többi szöveg csonkolódik.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Ez egy közleményhez tartozó tálca. Az új funkciók kommunikálására szolgál.</string>
    <string name="snackbar_primary">Ez egy elsődleges tálca.</string>
    <string name="snackbar_light">Ez egy világos tálca.</string>
    <string name="snackbar_warning">Ez egy figyelmeztető tálca.</string>
    <string name="snackbar_danger">Ez egy veszélyes tálca.</string>
    <string name="snackbar_description_single_line">Rövid időtartam</string>
    <string name="snackbar_description_single_line_custom_view">Hosszú időtartam körkörös előrehaladással kis egyéni nézetként</string>
    <string name="snackbar_description_single_line_action">Rövid időtartam művelettel</string>
    <string name="snackbar_description_single_line_action_custom_view">Rövid időtartam művelettel és közepes egyéni nézettel</string>
    <string name="snackbar_description_single_line_custom_text_color">Rövid időtartam testre szabott szövegszínnel</string>
    <string name="snackbar_description_multiline">Hosszú időtartam</string>
    <string name="snackbar_description_multiline_custom_view">Hosszú időtartam kis egyéni nézettel</string>
    <string name="snackbar_description_multiline_action">Határozatlan időtartam művelet- és szövegfrissítésekkel</string>
    <string name="snackbar_description_multiline_action_custom_view">Rövid időtartam művelettel és közepes egyéni nézettel</string>
    <string name="snackbar_description_multiline_action_long">Rövid időtartam hosszú műveletszöveggel</string>
    <string name="snackbar_description_announcement">Rövid időtartam</string>
    <string name="snackbar_description_updated">Ez a szöveg frissült.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Tálca megjelenítése</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Szimpla vonal</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Többsoros</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Hirdetmény stílusa</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Elsődleges stílus</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Világos stílus</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Figyelmeztetés stílusa</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Veszély stílusa</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Kezdőlap</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Posta</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Beállítások</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Értesítés</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Továbbiak</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Szöveg igazítása</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Függőleges</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Vízszintes</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Szöveg nélkül</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Lapelemek</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Cím</string>
    <string name="cell_sample_description">Leírás</string>
    <string name="calculate_cells">100 cella betöltése/kiszámítása</string>
    <string name="calculate_layouts">100 elrendezés betöltése/kiszámítása</string>
    <string name="template_list">Sablonlista</string>
    <string name="regular_list">Normál lista</string>
    <string name="cell_example_title">Cím: Cella</string>
    <string name="cell_example_description">Leírás: Koppintás a tájolás módosításához</string>
    <string name="vertical_layout">Függőleges elrendezés</string>
    <string name="horizontal_layout">Vízszintes elrendezés</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Szabványos lap, 2 szegmens</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Szabványos lap, 3 szegmens</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Szabványos lap, 4 szegmens</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Szabványos lap személyhívóval</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Váltás lap</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Tabletták lap </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Koppintson az elemleíráshoz</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Koppintson az Egyéni naptár elemleíráshoz</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Koppintson az Egyéni szín elemleírásra</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Koppintson a belső elemleírás bezárásához</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Koppintson az egyéni nézet elemleírásához</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Felső egyéni szín elemleírása</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Felső vég elemleírása 10 dp X-eltolással</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Alsó kezdő elemleírás</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Alsó vég elemleírása 10 dp Y-eltolással</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">A belső elemleírás bezárása</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Elemleírás bezárva</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">A címsor világos 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Az 1. cím közepes 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">A 2. cím normál 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">A címsor normál 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Az 1. alcím normál 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">A 2. alcím közepes 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Az 1. törzs normál 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">A 2. törzs közepes 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">A felirat normál 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-verzió: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">%d. elem</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Mappa</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Kattintás</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Állványzat</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB kibontva</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB összecsukva</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Kattintson a lista frissítéséhez</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Fiók kinyitása</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menüelem</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">X eltolás (dp-ben)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Y eltolás (dp-ben)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Tartalomszöveg</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Tartalomszöveg ismétlése</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">A menü szélessége a tartalom szövegéhez képest változik. A maximális
        szélesség a képernyőméret 75%-ára van korlátozva. Az oldalról és az alulról mért tartalommargót token szabályozza. Ugyanaz a tartalomszöveg ismétlődik, hogy változzon
        a magasság.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Menü megnyitása</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Alapszintű kártya</string>
    <!-- UI Label for Card -->
    <string name="file_card">Fájlkártya</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Közleménykártya</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Véletlenszerű felhasználói felület</string>
    <!-- UI Label for Options -->
    <string name="card_options">Beállítások</string>
    <!-- UI Label for Title -->
    <string name="card_title">Cím</string>
    <!-- UI Label for text -->
    <string name="card_text">Szöveg</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Alszöveg</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">A szalaghirdetés másodlagos példánya szükség esetén két sorba törhető.</string>
    <!-- UI Label Button -->
    <string name="card_button">Gomb</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Párbeszédpanel megjelenítése</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Párbeszédpanel bezárása külső kattintáskor</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Párbeszédpanel bezárása a visszanyomáskor</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Párbeszédpanel bezárva</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Mégse</string>
    <!-- UI Label Ok -->
    <string name="ok">OK</string>
    <!-- A sample description -->
    <string name="dialog_description">A párbeszédpanel egy kis ablak, amely arra kéri a felhasználót, hogy hozzon meg egy döntést, vagy adjon meg további információkat.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Fiók kinyitása</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Fiók kibontása</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Fiók bezárása</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Fióktípus kiválasztása</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Fent</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">A teljes fiók a látható régióban jelenik meg.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Lent</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">A teljes fiók a látható régióban jelenik meg. Pöccintés felfelé mozdulat a tartalom görgetésével. Kiterjeszthető fiók kibontása fogóponton keresztül.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Bal dia felülre</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">A fiók csúsztatása a bal oldal látható területére.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Jobb dia felülre</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">A fiók csúsztatása a jobb oldal látható területére.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Alsó dia felülre</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">A fiók átcsúsztatása a képernyő aljáról a látható területre. A kibontható fiókon pöccintés felfelé mozdulat, és a többi rész átvitele a látható területre, &amp; majd görgetés.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Firka látható</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Fiók tartalmának kijelölése</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Teljes képernyős méretű görgethető tartalom</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Több mint félképernyős tartalom</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Kevesebb mint félképernyős tartalom</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Tartalom dinamikus méretezése</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Beágyazott fiók tartalma</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Kibontható</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Nyitott állapot kihagyása</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Mellőzés megakadályozása Scrimre kattintáskor</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Fogópont megjelenítése</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Cím</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Elemleírás szövege</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Koppintson az egyéni tartalom elemleírásához</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Legfelső kezdés </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Felső vége </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Alsó kezdés </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Alsó vége </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Középre </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Egyéni középen</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">A kibocsátási megjegyzések frissítései,</string>
    <string name="click_here">kattintson ide.</string>
    <string name="open_source_cross_platform">Nyílt forráskódú platformfüggetlen arculati rendszer.</string>
    <string name="intuitive_and_powerful">Intuitív és hatékony.</string>
    <string name="design_tokens">Tervezési tokenek</string>
    <string name="release_notes">Kibocsátási megjegyzések</string>
    <string name="github_repo">GitHub-adattár</string>
    <string name="github_repo_link">GitHub-adattár hivatkozása</string>
    <string name="report_issue">Probléma bejelentése</string>
    <string name="v1_components">V1-összetevők</string>
    <string name="v2_components">V2-összetevők</string>
    <string name="all_components">Mind</string>
    <string name="fluent_logo">Fluent embléma</string>
    <string name="new_badge">Új</string>
    <string name="modified_badge">Módosítva</string>
    <string name="api_break_badge">API-megszakítás</string>
    <string name="app_bar_more">Továbbiak</string>
    <string name="accent">Kiemelés</string>
    <string name="appearance">Megjelenés</string>
    <string name="choose_brand_theme">Válassza ki a márka témáját:</string>
    <string name="fluent_brand_theme">Fluent-márka</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Megjelenés kiválasztása</string>
    <string name="appearance_system_default">Rendszer alapértelmezése</string>
    <string name="appearance_light">Világos</string>
    <string name="appearance_dark">Sötét</string>
    <string name="demo_activity_github_link">Demótevékenység GitHub-hivatkozása</string>
    <string name="control_tokens_details">Vezérlőtokenek részletei</string>
    <string name="parameters">Paraméterek</string>
    <string name="control_tokens">Vezérlőtokenek</string>
    <string name="global_tokens">Globális tokenek</string>
    <string name="alias_tokens">Aliastokenek</string>
    <string name="sample_text">Szöveg</string>
    <string name="sample_icon">Minta ikon</string>
    <string name="color">Szín</string>
    <string name="neutral_color_tokens">Semleges színtokenek</string>
    <string name="font_size_tokens">Betűméret-tokenek</string>
    <string name="line_height_tokens">Vonalmagassági tokenek</string>
    <string name="font_weight_tokens">Betűvastagsági tokenek</string>
    <string name="icon_size_tokens">Ikonméret-tokenek</string>
    <string name="size_tokens">Mérettokenek</string>
    <string name="shadow_tokens">Árnyéktokenek</string>
    <string name="corner_radius_tokens">Corner RadiusTokens</string>
    <string name="stroke_width_tokens">Vonásszélességi tokenek</string>
    <string name="brand_color_tokens">Márkaszín-tokenek</string>
    <string name="neutral_background_color_tokens">Semleges háttérszíntokenek</string>
    <string name="neutral_foreground_color_tokens">Semleges előtérszíntokenek</string>
    <string name="neutral_stroke_color_tokens">Semleges vonási színtokenek</string>
    <string name="brand_background_color_tokens">Márka háttérszínének tokenjei</string>
    <string name="brand_foreground_color_tokens">Márka előtérszínének tokenjei</string>
    <string name="brand_stroke_color_tokens">Márkavonás színtokenjei</string>
    <string name="error_and_status_color_tokens">Hiba- és állapotszíntokenek</string>
    <string name="presence_tokens">Jelenléti színtokenek</string>
    <string name="typography_tokens">Tipográfiai tokenek</string>
    <string name="unspecified">Nincs megadva</string>

</resources>