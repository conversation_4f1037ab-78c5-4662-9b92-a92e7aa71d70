<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Vlot-UI-demo</string>
    <string name="app_title">Vlot-UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Het %s geselekteer</string>
    <string name="app_modifiable_parameters">Wysigbare parameters</string>
    <string name="app_right_accessory_view">Regterbykomstigheidsaansig</string>

    <string name="app_style">Styl</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Ikoon is gedruk</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Begin demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Mallemeule</string>
    <string name="actionbar_icon_radio_label">Ikoon</string>
    <string name="actionbar_basic_radio_label">Basies</string>
    <string name="actionbar_position_bottom_radio_label">Onder</string>
    <string name="actionbar_position_top_radio_label">Bo</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Aksiebalktipe</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Aksiebalk posisie</string>

    <!--AppBar-->
    <string name="app_bar_style">Toepassingbalkstyl</string>
    <string name="app_bar_subtitle">Onderskrif</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Onderste raam</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Ikoon vir navigasie is geklik.</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Vlag</string>
    <string name="app_bar_layout_menu_settings">Instellings</string>
    <string name="app_bar_layout_menu_search">Soek</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Rolgedrag: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Wissel rolgedrag</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Ikoon vir wissel navigasie</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Wys avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Ikoon vir wys terug</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Versteek ikoon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Wys ikoon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Verander soekbalkuitlegstyl</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Aansig vir wys as bykomstigheid</string>
    <string name="app_bar_layout_searchbar_action_view_button">Wys as aksie-aansig</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Wissel tussen temas (herskep aktiwiteit)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Wissel tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Item</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Ekstra rolbare inhoud</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Sirkelstyl</string>
    <string name="avatar_style_square">Vierkantstyl</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXGroot</string>
    <string name="avatar_size_xlarge">XGroot</string>
    <string name="avatar_size_large">Groot</string>
    <string name="avatar_size_medium">Medium</string>
    <string name="avatar_size_small">Klein</string>
    <string name="avatar_size_xsmall">XKlein</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Dubbel ekstra groot</string>
    <string name="avatar_size_xlarge_accessibility">Ekstra groot</string>
    <string name="avatar_size_xsmall_accessibility">Ekstra klein</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Maksimum vertoonde avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Oorvleuel avatartelling</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Raamtipe</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatar-groep met OverflowAvatarCount-stel sal nie aan maksimum vertoonde avatar nakom nie.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Gesigstapel</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Gesighoop</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Oorvleuel is geklik</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarAansig by indeks %d is geklik</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Kennisgewing-kenteken</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Stippel</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lys</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Karakter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Foto\'s</string>
    <string name="bottom_navigation_menu_item_news">Nuus</string>
    <string name="bottom_navigation_menu_item_alerts">Opletnotas</string>
    <string name="bottom_navigation_menu_item_calendar">Kalender</string>
    <string name="bottom_navigation_menu_item_team">Span</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Wissel etikette</string>
    <string name="bottom_navigation_three_menu_items_button">Wys drie kieslysitems</string>
    <string name="bottom_navigation_four_menu_items_button">Wys vier kieslysitems</string>
    <string name="bottom_navigation_five_menu_items_button">Wys vyf kieslysitems</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Etikette is %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Aktiveer vee af om te weier</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Wys met enkelreëlitems</string>
    <string name="bottom_sheet_with_double_line_items">Wys met dubbelreëlitems</string>
    <string name="bottom_sheet_with_single_line_header">Wys met enkelreëlloopkop</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Wys met dubbelreëlloopkop en verdelers</string>
    <string name="bottom_sheet_dialog_button">Wys</string>
    <string name="drawer_content_desc_collapse_state">Vou uit</string>
    <string name="drawer_content_desc_expand_state">Minimeer</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Klik %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Langklik %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Klik weier</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Voeg item in</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Werk item by</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Weier</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Voeg by</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Melding</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Vetdruk</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Kursief</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Onderstreep</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Deurhaal</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Ontdoen</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Herdoen</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Kolpunt</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lys</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Skakel</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Item werk tans by</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Spasiëring</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Weier posisie</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">BEGIN</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">EINDE</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Groepspasie</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Itemspasie</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Vlag</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Vlag-item is geklik</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Antwoord</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Antwoord-item is geklik</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Stuur aan</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Stuur-item aan is geklik</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Skrap</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Skrap-item is geklik</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Kamera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Neem ’n foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Kamera-item geklik</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galery</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Bekyk jou foto’s</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Galery-item is geklik</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Video’s</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Speel jou video\'s</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Video’s-item is geklik</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Bestuur</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Bestuur jou mediabiblioteek</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Bestuur-item is geklik</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">E-posaksies</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Dokumente</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Laas bygewerk 14:14</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Deel</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Deel-item is geklik</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Skuif</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Skuif-item is geklik</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Skrap</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Skrap-item is geklik</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Inligting</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Inligting-item is geklik</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Klok</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Klok-item is geklik</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Alarm-item is geklik</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Tydsone</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Tydsone-item is geklik</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Verskillende aansigte van knoppie</string>
    <string name="button">Knoppie</string>
    <string name="buttonbar">Knoppiebalk</string>
    <string name="button_disabled">Voorbeeld van gedeaktiveer-knoppie</string>
    <string name="button_borderless">Voorbeeld van raamlose knoppie</string>
    <string name="button_borderless_disabled">Voorbeeld van raamlose gedeaktiveer-knoppie</string>
    <string name="button_large">Voorbeeld van groot knoppie</string>
    <string name="button_large_disabled">Voorbeeld van groot gedeaktiveer-knoppie</string>
    <string name="button_outlined">Voorbeeld van knoppie met omlyning</string>
    <string name="button_outlined_disabled">Voorbeeld van gedeaktiveer-knoppie met omlyning</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Kies ’n datum</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Enkeldatum</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">Geen datum gekies nie</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Wys datumkieser</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Wys datumtydkieser met datum-duimgids gekies</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Wys datumtydkieser met tyd-duimgids gekies</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Wys datumtydkieser</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Datumreeks</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Begin:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Beëindig:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">Geen begin gekies nie</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">Geen einde gekies nie</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Kies begindatum</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Kies einddatum</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Datumtydbestek</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Kies datumtydbestek</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Wys dialoog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Wys laai</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Wys laaidialoog</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Geen vervaagde onderste dialoog nie</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Wys boonste laai</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Geen vervaagde boonste dialoog nie</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Wys ankeraansig se boonste dialoog</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Wys geen titel bo dialoog nie</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Wys onder titel bo dialoog</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Wys regterlaai</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Wys linkerlaai</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Titel, primêre teks</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Onderskrif, sekondêre teks</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Gepasmaakte subtitelteks</string>
    <!-- Footer -->
    <string name="list_item_footer">Loopvoet, tersiêre teks</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Enkelreëllys met grys subopskrifteks</string>
    <string name="list_item_sub_header_two_line">Tweereëllys</string>
    <string name="list_item_sub_header_two_line_dense">Tweereëllys met digte spasiëring</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Tweereëllys met gepasmaakte sekondêre onderskrifaansig</string>
    <string name="list_item_sub_header_three_line">Driereëllys met swart subopskrifteks</string>
    <string name="list_item_sub_header_no_custom_views">Lys items met geen gepasmaakte aansigte nie</string>
    <string name="list_item_sub_header_large_header">Lys items met groot gepasmaakte aansigte</string>
    <string name="list_item_sub_header_wrapped_text">Lysitems met omgevoude teks</string>
    <string name="list_item_sub_header_truncated_text">Lys items met afgekapte teks</string>
    <string name="list_item_sub_header_custom_accessory_text">Aksie</string>
    <string name="list_item_truncation_middle">Middelafkapping.</string>
    <string name="list_item_truncation_end">Beëindig afkapping.</string>
    <string name="list_item_truncation_start">Begin afkapping.</string>
    <string name="list_item_custom_text_view">Waarde</string>
    <string name="list_item_click">Jy het op die lysitem geklik.</string>
    <string name="list_item_click_custom_accessory_view">Jy het op die gepasmaakte bykomstigheidaansig geklik.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Jy het op die gepasmaakte subopskrif se gepasmaakte bykomstige aansig geklik.</string>
    <string name="list_item_more_options">Meer opsies</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Kies</string>
    <string name="people_picker_select_deselect_example">SelekteerDeselekteer</string>
    <string name="people_picker_none_example">Geen</string>
    <string name="people_picker_delete_example">Skrap</string>
    <string name="people_picker_custom_persona_description">Hierdie voorbeeld wys hoe om ’n gepasmaakte IPersona-objek te skep.</string>
    <string name="people_picker_dialog_title_removed">Jy het ’n persona verwyder:</string>
    <string name="people_picker_dialog_title_added">Jy het ’n persona bygevoeg:</string>
    <string name="people_picker_drag_started">Sleep het begin</string>
    <string name="people_picker_drag_ended">Sleep is beëindig</string>
    <string name="people_picker_picked_personas_listener">Personas-luisteraar</string>
    <string name="people_picker_suggestions_listener">Voorstelle luisteraar</string>
    <string name="people_picker_persona_chip_click">Jy het op %s geklik</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s ontvanger</item>
        <item quantity="other">%1$s ontvangers</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Vou voortdurende BottomSheet uit</string>
    <string name="collapse_persistent_sheet_button"> Versteek voortdurende BottomSheet</string>
    <string name="show_persistent_sheet_button"> Wys voortdurende ondersteblad</string>
    <string name="new_view">Hierdie is ’n nuwe aansig</string>
    <string name="toggle_sheet_content">Wissel ondersteblad se inhoud</string>
    <string name="switch_to_custom_content">Skakel oor na gepasmaakte inhoud</string>
    <string name="one_line_content">Wissel BottomSheet-inhoud</string>
    <string name="toggle_disable_all_items">Wissel deaktiveer alle items</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Voeg by/Verwyder aansig</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Verander uitgevoude hoogte</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nuwe omslag</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Nuwe omslag-item is geklik</string>
    <string name="persistent_sheet_item_edit_title">Redigeer</string>
    <string name="persistent_sheet_item_edit_toast">Redigeer-item is geklik</string>
    <string name="persistent_sheet_item_save_title">Stoor</string>
    <string name="persistent_sheet_item_save_toast">Stoor-item is geklik</string>
    <string name="persistent_sheet_item_zoom_in_title">Bekyk nader</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Bekyk nader-item is geklik</string>
    <string name="persistent_sheet_item_zoom_out_title">Bekyk van ver</string>
    <string name="persistent_sheet_item_zoom_out_toast">Bekyk van ver-item is geklik</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Beskikbaar</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Ontwerper</string>
    <string name="persona_subtitle_engineer">Ingenieur</string>
    <string name="persona_subtitle_manager">Bestuurder</string>
    <string name="persona_subtitle_researcher">Navorser</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (voorbeeld van langteks om afkapping te toets)</string>
    <string name="persona_view_description_xxlarge">XXGroot avatar met drie teksreëls</string>
    <string name="persona_view_description_large">Groot avatar met twee teksreëls</string>
    <string name="persona_view_description_small">Klein avatar met een teksreël</string>
    <string name="people_picker_hint">Geen met wenk wat wys nie</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Gedeaktiveerde Persona Chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Fout Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Persona Chip met geen sluit-ikoon nie</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Basiese Persona Chips</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Jy het op ’n geselekteerde Persona Chip geklik.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Deel</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Volg</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Nooi mense</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Verfris bladsy</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Maak oop in blaaier</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Dit is ’n multireël-opspringkieslys. Maksimum reëls is op twee gestel, die res van die teks sal afgekap word.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Alle nuus</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Gestoorde nuus</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Nuus vanaf werwe</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Verwitting buite werksure</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Stel in kennis wanneer onaktief op werkskerm is</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Jy het op die item geklik:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Eenvoudige kieslys</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Eenvoudige kieslys2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Kieslys met een geselekteerde item en ’n verdeler</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Kieslys met alle kiesbare items, ikone en langteks</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Wys</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Sirkulêrevordering</string>
    <string name="circular_progress_xsmall">XKlein</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Klein</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Medium</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Groot</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Lineêrevordering</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Onbepaald</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Bepaalde</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">Soekbalk</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Mikrofoon-terugbel</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Outokorrekteer</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Mikrofoon is gedruk</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Regteraansig is gedruk</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Sleutelbordsoektog is gedruk</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Wys Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Weier Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Aksie</string>
    <string name="snackbar_action_long">Aksie vir langteks</string>
    <string name="snackbar_single_line">Enkelreël snackbar</string>
    <string name="snackbar_multiline">Dit is ’n multilyn-snackbar. Maksimum reëls is op twee gestel, die res van die teks sal afgekap word.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Dit is ’n aankondiging-snackbar. Dit word gebruik om nuwe kenmerke te kommunikeer.</string>
    <string name="snackbar_primary">Dit is ’n primêre snackbar.</string>
    <string name="snackbar_light">Dit is ’n ligte snackbar.</string>
    <string name="snackbar_warning">Dit is ’n waarskuwing-snackbar.</string>
    <string name="snackbar_danger">Dit is ’n gevaar-snackbar.</string>
    <string name="snackbar_description_single_line">Kort tydsduur</string>
    <string name="snackbar_description_single_line_custom_view">Lang tydsduur met sirkulêrevordering as klein gepasmaakte aansig</string>
    <string name="snackbar_description_single_line_action">Kort tydsduur met aksie</string>
    <string name="snackbar_description_single_line_action_custom_view">Kort tydsduur met aksie en medium gepasmaakte aansig</string>
    <string name="snackbar_description_single_line_custom_text_color">Kort tydsduur met gepasmaakte tekskleur</string>
    <string name="snackbar_description_multiline">Lang tydsduur</string>
    <string name="snackbar_description_multiline_custom_view">Lang tydsduur met klein gepasmaakte aansig</string>
    <string name="snackbar_description_multiline_action">Onbeperkte tydsduur met aksie- en teksbywerkings</string>
    <string name="snackbar_description_multiline_action_custom_view">Kort tydsduur met aksie en medium gepasmaakte aansig</string>
    <string name="snackbar_description_multiline_action_long">Kort tydsduur met lang aksieteks</string>
    <string name="snackbar_description_announcement">Kort tydsduur</string>
    <string name="snackbar_description_updated">Hierdie teks is bygewerk.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Wys snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Enkellyn</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Multireël</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Aankondigingstyl</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primêre styl</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Ligtestyl</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Waarskuwingstyl</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Gevaarstyl</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Tuis</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Pos</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Instellings</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Kennisgewing</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Meer</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Teksbelyning</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertikaal</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horisontaal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Geen teks nie</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Duimgidsitem</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Titel</string>
    <string name="cell_sample_description">Beskrywing</string>
    <string name="calculate_cells">Laai/bereken 100 uitlegte</string>
    <string name="calculate_layouts">Laai/bereken 100 uitlegte</string>
    <string name="template_list">Sjabloonlys</string>
    <string name="regular_list">Gewone lys</string>
    <string name="cell_example_title">Titel: Sel</string>
    <string name="cell_example_description">Beskrywing: Tik om oriëntasie te verander</string>
    <string name="vertical_layout">Vertikale uitleg</string>
    <string name="horizontal_layout">Horisontale uitleg</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standaard duimgids 2-segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standaard duimgids 3-segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standaard duimgids 4-segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standaard duimgids met roepfoon</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Wissel duimgids</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Pille-duimgids </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tik vir nutsmiddelwenk</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tik vir gepasmaakte kalendernutsmiddelwenk</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Tik gepasmaakte kleurnutsmiddelwenk</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tik om binnenutsmiddelwenk te weier</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tik vir gepasmaakte aansignutsmiddelwenk</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Boonste gepasmaakte kleurnutsmiddelwenk</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Boonste eindnutsmiddelwenk met 10dp verplasingX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Onderste beginnutsmiddelwenk</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Onderste eindnutsmiddelwenk met 10dp verplasingY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Weier binnenutsmiddelwenk</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Nutsmiddelwenk is geweier</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Opskrif is ligte 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Titel 1 is medium 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Titel 2 is gereeld 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Opskrif is gewone 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Subopskrif 1 is gewone 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Subopskrif 2 is medium 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Inhoud 1 is gewone 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Inhoud 2 is medium 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Onderskrif is gewone 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK-weergawe: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Item %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Omslag</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Geklik</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Steier</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB is uitgevou</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB is ingevou</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Klik om lys te verfris</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Maak laai oop</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Kieslysitem</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Verplaas X (in dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Verplaas Y (in dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Inhoudteks</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Herhaal inhoudteks</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Kieslys se wydte sal verander ten opsigte van inhoudteks. Die maksimum
        wydte is beperk tot 75% skermgrootte. Die inhoudkantlyn van die kant en onderkant word deur die kenteken beheer. Dieselfde inhoudteks sal herhaal om die hoogte
        te wissel.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Maak kieslys oop</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Basiese kaart</string>
    <!-- UI Label for Card -->
    <string name="file_card">Lêerkaart</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Aankondigingskaart</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Lukrake UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opsies</string>
    <!-- UI Label for Title -->
    <string name="card_title">Titel</string>
    <!-- UI Label for text -->
    <string name="card_text">Teks</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Subteks</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Sekondêre kopie vir hierdie banier kan omvou na twee reëls indien nodig.</string>
    <!-- UI Label Button -->
    <string name="card_button">Knoppie</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Wys dialoog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Weier dialoog wanneer jy buite klik</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Weier dialoog op terug-druk</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialoog is geweier</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Kanselleer</string>
    <!-- UI Label Ok -->
    <string name="ok">Goed</string>
    <!-- A sample description -->
    <string name="dialog_description">’n Dialoogblokkie is ’n klein venster wat die gebruiker por om ’n besluit te neem of bykomende inligting in te voer.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Maak laai oop</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Vou laai uit</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Maak laai toe</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Kies laai-tipe</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Bokant</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Hele laai wys in die sigbare streek.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Onderkant</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Hele laaier wys in die sigbare streek. Vee op-beweging rol inhoud. Uitvoubare laai vou uit via sleephandvatsel.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Links gly oor</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Laai gly oor na sigbare streek vanaf linkerkant.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Regs gly oor</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Laai gly oor na sigbare streek vanaf regterkant.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Onderkant gly oor</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Laai gly oor na sigbare streek vanaf onderkant van skerm. Vee op-beweging op uitvoubare laai bring res van die deel na sigbare streek en rol dan.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Skerm is sigbaar</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Kies laai-inhoud</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Volskermgrootte rolbare inhoud</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Meer as die halwe skerminhoud</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Minder as die halwe skerminhoud</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dinamiese grootte-inhoud</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Geneste laai-inhoud</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Uitvoubaar</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Slaan oop status oor</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Voorkom weiering by Scrim-klik</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Wys handvatsel</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Titel</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Nutsmiddelwenkteks</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tik vir gepasmaakte inhoudnutsmiddelwenk</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Boonste begin </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Boonste kant </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Onderste Beginskerm </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Onderste kant </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Sentreer </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Pasmaaksentrum</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Vir bywerkings oor vrystellingsnotas, </string>
    <string name="click_here">klik hier.</string>
    <string name="open_source_cross_platform">Oopbronkruisplatform Ontwerpstelsel.</string>
    <string name="intuitive_and_powerful">Intuïtief &amp; kragtig.</string>
    <string name="design_tokens">Ontwerpkentekens</string>
    <string name="release_notes">Vrystellingsnotas</string>
    <string name="github_repo">GitHub Repo</string>
    <string name="github_repo_link">GitHub Repo-skakel</string>
    <string name="report_issue">Rapporteer kwessie</string>
    <string name="v1_components">V1-komponente</string>
    <string name="v2_components">V2-komponente</string>
    <string name="all_components">Alles</string>
    <string name="fluent_logo">Fluent-logo</string>
    <string name="new_badge">Nuwe</string>
    <string name="modified_badge">Gewysig</string>
    <string name="api_break_badge">API-breuk</string>
    <string name="app_bar_more">Meer</string>
    <string name="accent">Aksent</string>
    <string name="appearance">Voorkoms</string>
    <string name="choose_brand_theme">Kies jou handelsmerktema:</string>
    <string name="fluent_brand_theme">Fluent-handelsmerk</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Kies voorkoms</string>
    <string name="appearance_system_default">Stelselverstek</string>
    <string name="appearance_light">Lig</string>
    <string name="appearance_dark">Donker</string>
    <string name="demo_activity_github_link">Demoaktiwiteit GitHub-skakel</string>
    <string name="control_tokens_details">Beheerkentekenbesonderhede</string>
    <string name="parameters">Parameters</string>
    <string name="control_tokens">Kontrole-kentekens</string>
    <string name="global_tokens">Globale kentekens</string>
    <string name="alias_tokens">Alias-kentekens</string>
    <string name="sample_text">Teks</string>
    <string name="sample_icon">Voorbeeldikoon</string>
    <string name="color">Kleur</string>
    <string name="neutral_color_tokens">Neutrale kleurkentekens</string>
    <string name="font_size_tokens">Fontgrootte-kentekens</string>
    <string name="line_height_tokens">Lynhoogte-kentekens</string>
    <string name="font_weight_tokens">Fontgewigkentekens</string>
    <string name="icon_size_tokens">Ikoongrootte-kentekens</string>
    <string name="size_tokens">Grootte-kentekens</string>
    <string name="shadow_tokens">Skadukentekens</string>
    <string name="corner_radius_tokens">Hoekradiuskentekens</string>
    <string name="stroke_width_tokens">Haalwydte-kentekens</string>
    <string name="brand_color_tokens">Handelsmerkkleurkentekens</string>
    <string name="neutral_background_color_tokens">Neutrale agtergrondkleurkentekens</string>
    <string name="neutral_foreground_color_tokens">Neutrale voorgrondkleurkentekens</string>
    <string name="neutral_stroke_color_tokens">Neutrale haalkleurkentekens</string>
    <string name="brand_background_color_tokens">Handelsmerkagtergrondkleurkentekens</string>
    <string name="brand_foreground_color_tokens">Handelsmerkvoorgrondkleurkentekens</string>
    <string name="brand_stroke_color_tokens">Handelsmerkerkhaalkleurkentekens</string>
    <string name="error_and_status_color_tokens">Fout- en statuskleurkentekens</string>
    <string name="presence_tokens">Teenwoordigheidkleurtekens</string>
    <string name="typography_tokens">Tipografie-kentekens</string>
    <string name="unspecified">Ongespesifiseerd</string>

</resources>