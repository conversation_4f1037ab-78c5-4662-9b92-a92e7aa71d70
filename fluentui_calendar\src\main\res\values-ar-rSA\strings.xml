<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">ن</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">ث</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">ع</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">خ</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">ج</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">س</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">د</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">الانتقال إلى الأسبوع التالي</string>
    <string name="accessibility_goto_previous_week">الانتقال إلى الأسبوع السابق</string>
    <string name="accessibility_today">اليوم</string>
    <string name="accessibility_selected">محدد</string>

    <!-- *** Shared *** -->
    <string name="done">تم</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s، %2$s</string>
    <string name="today">اليوم</string>
    <string name="tomorrow">غداً</string>
    <string name="yesterday">أمس</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">وقت البدء</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">وقت الانتهاء</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">تاريخ البدء</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">تاريخ الانتهاء</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">اختيار الوقت</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">اختيار تاريخ</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">منتقي التاريخ والوقت</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">منتقي التاريخ</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">نطاق منتقي التاريخ الوقت</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">نطاق منتقي التاريخ</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">علامة تبويب وقت البدء</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">علامة تبويب وقت الانتهاء</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">علامة تبويب "تاريخ البدء"</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">علامة تبويب تاريخ الانتهاء</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">إغلاق مربع الحوار</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">زيادة شهر</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">حدد شهر قادم</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">إنقاص شهر</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">حدد شهر سابق</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">زيادة يوم</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">حدد اليوم التالي</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">إنقاص يوم</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">حدد يوم سابق</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">زيادة سنة</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">حدد السنة التالية</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">إنقاص سنة</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">حدد سنة سابقة</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">زيادة التاريخ</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">حدد تاريخ قادم</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">إنقاص التاريخ</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">حدد تاريخ سابق</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">زيادة ساعة</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">حدد ساعة قادمة</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">إنقاص ساعة</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">حدد ساعة سابقة</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">زيادة دقيقة</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">حدد دقيقة قادمة</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">إنقاص دقيقة</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">حدد دقيقة سابقة</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">التبديل بين الفترة الصباحية والمسائية</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">التبديل بين الفترة الصباحية والمسائية</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s محدد</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">تم التحديد</string>
</resources>
