<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <path
        android:fillColor="#8E8E8E"
        android:fillType="evenOdd"
        android:pathData="M8.204,4.0009C8.7318,4 9.2432,4.1847 9.6486,4.5226L9.6486,4.5226L12.0216,6.5007L19.75,6.5007C20.9926,6.5007 22,7.5081 22,8.7507L22,8.7507L22.0004,17.7555C22.0001,18.9983 20.9922,20.0055 19.7498,20.0045L19.7498,20.0045L4.2494,20.0004C3.0073,19.9995 2.0006,18.9925 2,17.75L2,17.75L2,6.2578C2,5.0167 3.005,4.0099 4.2461,4.0078L4.2461,4.0078ZM9.6375,9.9929C9.2355,10.3218 8.732,10.5016 8.2126,10.5016L8.2126,10.5016L3.499,10.5009L3.5,17.7496C3.5002,18.1295 3.7825,18.4435 4.1485,18.4935L4.2502,18.5004L19.7506,18.5045C20.1563,18.5048 20.4869,18.1825 20.5,17.7797L20.5,17.7797L20.5,8.7507C20.5,8.3365 20.1642,8.0007 19.75,8.0007L19.75,8.0007L12.073,7.9999ZM8.2066,5.5009L4.2487,5.5078C3.835,5.5085 3.5,5.8441 3.5,6.2578L3.5,6.2578L3.499,9.0009L8.2126,9.0016C8.3511,9.0016 8.4862,8.9632 8.6033,8.8917L8.6875,8.832L10.599,7.2669L8.6882,5.6748C8.58,5.5847 8.4493,5.5273 8.3113,5.5081L8.2066,5.5009Z"
        android:strokeWidth="1"
        android:strokeColor="#00000000" />
</vector>
