<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.microsoft.fluentui.drawer.DrawerView
        android:id="@+id/drawer"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="@dimen/fluentui_drawer_elevation"
        android:background="?attr/fluentuiDrawerBackgroundColor"
        android:clipToPadding="true"
        app:layout_behavior="@string/bottom_sheet_behavior"
        app:behavior_hideable="true"
        app:behavior_peekHeight="@dimen/fluentui_drawer_peek_height"
        app:fluentui_behaviorType="BOTTOM">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_drawer_handle"
            app:tint="?attr/fluentuiDrawerHandleColor"
            android:layout_marginTop="@dimen/fluentui_drawer_handle_vertical_margin"
            android:layout_marginBottom="@dimen/fluentui_drawer_handle_vertical_margin"
            android:layout_gravity="center"
            android:importantForAccessibility="no"/>

        <LinearLayout
            android:id="@+id/drawer_content"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

    </com.microsoft.fluentui.drawer.DrawerView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>