<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/default_layout_margin"
    tools:context=".demos.DrawerActivity">

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_drawer_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/drawer_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_drawer_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/drawer_dialog_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_no_fade_bottom_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_fade_bottom_dialog_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_anchor_view_top_dialog_button"
        android:layout_marginTop="@dimen/drawer_button_top_margin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/anchor_view_top_dialog_button" />


    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_no_title_top_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_title_top_dialog_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_below_title_top_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/below_title_top_dialog_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_top_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/drawer_top_dialog_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_no_fade_top_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_fade_top_dialog_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_left_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/drawer_left_dialog_button" />

    <com.microsoft.fluentui.widget.Button
        android:id="@+id/show_right_dialog_button"
        android:layout_marginTop="@dimen/fluentui_content_inset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/drawer_right_dialog_button" />
</LinearLayout>
