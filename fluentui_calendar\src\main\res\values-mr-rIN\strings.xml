<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">सोम</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">मंगळ</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">बुध</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">गुरू</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">शुक्र</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">शनि</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">रवि</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">पुढील आठवड्यावर जा</string>
    <string name="accessibility_goto_previous_week">मागील आठवड्यावर जा</string>
    <string name="accessibility_today">आज</string>
    <string name="accessibility_selected">निवडले</string>

    <!-- *** Shared *** -->
    <string name="done">पूर्ण झाले</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">आज</string>
    <string name="tomorrow">उद्या</string>
    <string name="yesterday">काल</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">प्रारंभ करण्याची वेळ</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">समाप्ती वेळ</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">प्रारंभ तारीख</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">समाप्ती तारीख</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">वेळ निवडा</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">तारीख निवडा</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">तारीख वेळ पिकर</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">तारीख पिकर</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">तारीख वेळ पिकर परिक्षेत्र</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">तारीख पिकर परिक्षेत्र</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">प्रारंभ वेळ टॅब</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">समाप्ती वेळ टॅब</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">प्रारंभ तारीख टॅब</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">समाप्ती तारीख टॅब</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">डायलॉग बंद करा</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">वर्धित महिना</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">पुढील महिना निवडा</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">घट महिना</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">मागील महिना निवडा</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">वर्धित दिवस</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">पुढील दिवस निवडा</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">घट दिवस</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">मागील दिवस निवडा</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">वर्धित वर्ष</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">पुढील वर्ष निवडा</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">घट वर्ष</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">मागील वर्ष निवडा</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">वर्धित तारीख</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">पुढील तारीख निवडा</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">घट तारीख</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">मागील तारीख निवडा</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">वर्धित तास</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">पुढील तास निवडा</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">घट तास</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">मागील तास निवडा</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">वर्धित मिनिट</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">पुढील मिनिट निवडा</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">घट मिनिट</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">मागील मिनिट निवडा</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">AM PM कालावधी टॉगल करा</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">AM PM कालावधी टॉगल करा</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s निवडले</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">निवडलेले</string>
</resources>
