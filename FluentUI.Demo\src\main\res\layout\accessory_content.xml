<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/new_discovery_tag"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:focusable="false"
    android:visibility="gone"
    app:cardBackgroundColor="?attr/fluentuiBackgroundPrimaryColor"
    app:cardCornerRadius="4dp"
    tools:visibility="visible">

    <TextView
        android:id="@+id/bottom_sheet_item_nudge"
        style="@style/new_discovery_tag"
        android:importantForAccessibility="no"
        android:maxLines="1"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="new"
        android:textIsSelectable="false" />

</androidx.cardview.widget.CardView>
