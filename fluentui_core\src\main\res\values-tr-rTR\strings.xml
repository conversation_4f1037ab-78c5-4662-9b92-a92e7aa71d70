<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">Birincil</string>
    <string name="fluentui_secondary"><PERSON><PERSON>cil</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">Kapat</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">Se<PERSON><PERSON></string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">Seç<PERSON> Değil</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">Simge</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">Simge</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">Devre dış<PERSON> bırakılmış</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">E<PERSON><PERSON> Düğmesi</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">Etkin</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">Sayfayı Kapatma</string>
    <!-- Describes close action -->
    <string name="fluentui_close">Kapatma</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">İptal</string>
    <!--name of the icon -->
    <string name="fluentui_search">Arama</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">Mikrofon</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">Metni Temizleme</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">Geri</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">Etkin</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">Devre Dışı Bırakılmış</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">Nötr</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">Marka</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">Karşıtlık</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">Vurgu</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">Uyarı</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">Tehlike</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">Bir hata oluştu</string>
    <string name="fluentui_error">Hata</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">İpucu</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">Köşeli Çift Ayraç</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">Ana Hat</string>

    <string name="fluentui_action_button_icon">Eylem düğmesi simgesi</string>
    <string name="fluentui_center">Orta metin</string>
    <string name="fluentui_accessory_button">Aksesuar Düğmeleri</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">Radyo Düğmesi</string>
    <string name="fluentui_label">Etiket</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">Geniş</string>
    <string name="fluentui_collapsed">Daraltılmış</string>

    <!--types of control -->
    <string name="fluentui_large">Büyük</string>
    <string name="fluentui_medium">Orta</string>
    <string name="fluentui_small">Küçük</string>
    <string name="fluentui_password_mode">Parola Modu</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">Alt Yazı</string>
    <string name="fluentui_assistive_text">Yardımcı Metin</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">Başlık</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">Kısa</string>"
    <string name="fluentui_long">Uzun</string>"
    <string name="fluentui_indefinite">Belirsiz</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">Düğmeye basıldı</string>
    <string name="fluentui_dismissed">Kapatılan</string>
    <string name="fluentui_timeout">Zaman Aşımı</string>
    <string name="fluentui_left_swiped">Sola Kaydırıldı</string>
    <string name="fluentui_right_swiped">Sağa Kaydırıldı</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">Metin</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">Sayı</string>
    <string name="fluentui_keyboard_phone">Telefon</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">E-posta</string>
    <string name="fluentui_keyboard_password">Parola</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">Ondalık</string>
</resources>