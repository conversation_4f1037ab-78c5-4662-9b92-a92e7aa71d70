<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Demostración de la interfaz de usuario de Fluent</string>
    <string name="app_title">Interfaz de usuario de Fluent</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Se seleccionó %s</string>
    <string name="app_modifiable_parameters">Parámetros modificables</string>
    <string name="app_right_accessory_view">Vista accesorio derecha</string>

    <string name="app_style">Estilo</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Icono presionado</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Iniciar demostración</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carrusel</string>
    <string name="actionbar_icon_radio_label">Icono</string>
    <string name="actionbar_basic_radio_label">Básico</string>
    <string name="actionbar_position_bottom_radio_label">Inferior</string>
    <string name="actionbar_position_top_radio_label">Superior</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">Tipo de ActionBar</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">Posición de ActionBar</string>

    <!--AppBar-->
    <string name="app_bar_style">Estilo de AppBar</string>
    <string name="app_bar_subtitle">Subtítulo</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Borde inferior</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Icono de navegación seleccionado</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Marca</string>
    <string name="app_bar_layout_menu_settings">Configuración</string>
    <string name="app_bar_layout_menu_search">Búsqueda</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Comportamiento de desplazamiento: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Alternar comportamiento de desplazamiento</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Alternar ícono de navegación</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Mostrar avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Mostrar ícono Atrás</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Ocultar ícono</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Mostrar ícono</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Alternar el estilo de diseño de la barra de búsqueda</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Mostrar como vista de accesorios</string>
    <string name="app_bar_layout_searchbar_action_view_button">Mostrar como vista de acción</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Alternar entre temas (vuelve a crear la actividad)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Alternar tema</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Elemento</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Contenido desplazable adicional</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Estilo de círculo</string>
    <string name="avatar_style_square">Estilo cuadrado</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Grande</string>
    <string name="avatar_size_medium">Mediana</string>
    <string name="avatar_size_small">Pequeño</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Doble extra grande</string>
    <string name="avatar_size_xlarge_accessibility">Extragrande</string>
    <string name="avatar_size_xsmall_accessibility">Muy pequeño</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Avatar máximo mostrado</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Recuento de avatares de desbordamiento</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Tipo de borde</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">El grupo de avatares con el conjunto OverflowAvatarCount no se ajustará al número máximo de avatares mostrados.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Pila de caras</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Pila facial</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Desbordamiento seleccionado</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView en el índice %d seleccionado</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Distintivo de notificación</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Punto</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">Lista</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Carácter</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Fotos</string>
    <string name="bottom_navigation_menu_item_news">Novedades</string>
    <string name="bottom_navigation_menu_item_alerts">Alertas</string>
    <string name="bottom_navigation_menu_item_calendar">Calendario</string>
    <string name="bottom_navigation_menu_item_team">Equipo</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Alternar etiquetas</string>
    <string name="bottom_navigation_three_menu_items_button">Mostrar tres elementos de menú</string>
    <string name="bottom_navigation_four_menu_items_button">Mostrar cuatro elementos de menú</string>
    <string name="bottom_navigation_five_menu_items_button">Mostrar cinco elementos de menú</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Las etiquetas son %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name="bottom_sheet_text_enable_swipe_dismiss">Habilitar el deslizamiento hacia abajo para descartar</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Mostrar con elementos de una sola línea</string>
    <string name="bottom_sheet_with_double_line_items">Mostrar con elementos de línea doble</string>
    <string name="bottom_sheet_with_single_line_header">Mostrar con encabezado de una sola línea</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Mostrar con encabezado de doble línea y divisores</string>
    <string name="bottom_sheet_dialog_button">Mostrar</string>
    <string name="drawer_content_desc_collapse_state">Expandir</string>
    <string name="drawer_content_desc_expand_state">Minimizar</string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Hacer clic en %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Clic largo %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Hacer clic en Descartar</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Insertar elemento</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Actualizar elemento</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Descartar</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Agregar</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Mención</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Negrita</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Cursiva</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Subrayado</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Tachado</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Deshacer</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Rehacer</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Viñeta</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">Lista</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Vínculo</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Actualizando elemento</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Espaciado</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Descartar posición</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">COMENZAR</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Espacio de grupo</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Espacio de elemento</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Marca</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Elemento de marca seleccionado</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Responder</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Elemento de respuesta seleccionado</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Reenviar</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Elemento de reenvío seleccionado</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Eliminar</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Eliminar elemento seleccionado</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Cámara</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Hacer una foto</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Elemento de cámara seleccionado</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Galería</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">Ver tus fotos</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Elemento de la galería seleccionado</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Reproducir videos</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Elemento de videos seleccionado</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Administrar</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Administrar la biblioteca multimedia</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Administrar elemento seleccionado</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Acciones de correo electrónico</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documentos</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Última actualización a las 2:14 p. m.</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Compartir</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Compartir elemento seleccionado</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Mover</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Mover elemento seleccionado</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Eliminar</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Eliminar elemento seleccionado</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Información</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Elemento de información seleccionado</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Reloj de pared</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Elemento de reloj seleccionado</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarma</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Elemento de alarma seleccionado</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Zona horaria</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Elemento de zona horaria seleccionado</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Diferentes vistas del botón</string>
    <string name="button">Botón</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Ejemplo de botón deshabilitado</string>
    <string name="button_borderless">Ejemplo de botón sin borde</string>
    <string name="button_borderless_disabled">Ejemplo de botón deshabilitado sin bordes</string>
    <string name="button_large">Ejemplo de botón grande</string>
    <string name="button_large_disabled">Ejemplo de botón deshabilitado grande</string>
    <string name="button_outlined">Ejemplo de botón con contorno</string>
    <string name="button_outlined_disabled">Ejemplo de botón deshabilitado resaltado</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Elegir una fecha</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Fecha única</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">No se seleccionó ninguna fecha</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Mostrar selector de fecha</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Mostrar el selector de fecha y hora con la pestaña de fecha seleccionada</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Mostrar el selector de fecha y hora con la pestaña hora seleccionada</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Mostrar selector de fecha y hora</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Intervalo de fechas</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Inicio:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">Fin:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">No se seleccionó ninguna fecha de inicio</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">No se seleccionó ningún final</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Selecciona la fecha de inicio</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Seleccionar fecha de finalización</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Intervalo de fecha y hora</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Seleccionar intervalo de fecha y hora</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Mostrar cuadro de diálogo</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Mostrar cajón</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Mostrar cuadro de diálogo de cajón</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">Sin cuadro de diálogo de atenuación inferior</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Mostrar cajón superior</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">Sin cuadro de diálogo de atenuación superior</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Mostrar el cuadro de diálogo superior de la vista de anclaje</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Mostrar cuadro de diálogo superior sin título</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Mostrar el cuadro de diálogo superior del título debajo</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Mostrar cajón derecho</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Mostrar cajón izquierdo</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Título, texto principal</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtítulo, texto secundario</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Texto de subtítulo personalizado</string>
    <!-- Footer -->
    <string name="list_item_footer">Pie de página, texto terciario</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Lista de una sola línea con texto de submenú gris</string>
    <string name="list_item_sub_header_two_line">Lista de dos líneas</string>
    <string name="list_item_sub_header_two_line_dense">Lista de dos líneas con espaciado denso</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Lista de dos líneas con vista de subtítulo secundario personalizada</string>
    <string name="list_item_sub_header_three_line">Lista de tres líneas con texto de subtítulo negro</string>
    <string name="list_item_sub_header_no_custom_views">Mostrar elementos sin vistas personalizadas</string>
    <string name="list_item_sub_header_large_header">Elementos de lista con vistas personalizadas grandes</string>
    <string name="list_item_sub_header_wrapped_text">Mostrar elementos con texto ajustado</string>
    <string name="list_item_sub_header_truncated_text">Enumerar elementos con texto truncado</string>
    <string name="list_item_sub_header_custom_accessory_text">Acción</string>
    <string name="list_item_truncation_middle">Truncamiento medio.</string>
    <string name="list_item_truncation_end">Fin del truncamiento.</string>
    <string name="list_item_truncation_start">Iniciar truncamiento.</string>
    <string name="list_item_custom_text_view">Valor</string>
    <string name="list_item_click">Hiciste clic en el elemento de lista.</string>
    <string name="list_item_click_custom_accessory_view">Hiciste clic en la vista de accesorios personalizada.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">Hiciste clic en la vista de accesorios personalizada del subtítulo.</string>
    <string name="list_item_more_options">Más opciones</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Seleccionar</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">Ninguno</string>
    <string name="people_picker_delete_example">Eliminar</string>
    <string name="people_picker_custom_persona_description">En este ejemplo se muestra cómo crear un objeto IPersona personalizado.</string>
    <string name="people_picker_dialog_title_removed">Quitaste una persona:</string>
    <string name="people_picker_dialog_title_added">Agregaste una persona:</string>
    <string name="people_picker_drag_started">Se inició el arrastre</string>
    <string name="people_picker_drag_ended">Arrastrar finalizado</string>
    <string name="people_picker_picked_personas_listener">Agente de escucha de Personas</string>
    <string name="people_picker_suggestions_listener">Agente de escucha de sugerencias</string>
    <string name="people_picker_persona_chip_click">Hiciste clic en %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s destinatario</item>
        <item quantity="many">%1$s destinatarios</item>
        <item quantity="other">%1$s destinatarios</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Expandir BottomSheet persistente</string>
    <string name="collapse_persistent_sheet_button"> Ocultar BottomSheet persistente</string>
    <string name="show_persistent_sheet_button"> Mostrar BottomSheet persistente</string>
    <string name="new_view">Esta es la nueva vista</string>
    <string name="toggle_sheet_content">Alternar contenido de la parte inferior</string>
    <string name="switch_to_custom_content">Cambiar a contenido personalizado</string>
    <string name="one_line_content">Contenido de la parte inferior de una línea</string>
    <string name="toggle_disable_all_items">Alternar deshabilitar todos los elementos</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Agregar o quitar vista</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Cambiar alto contraído</string>
    <string name="persistent_sheet_item_create_new_folder_title">Nueva carpeta</string>
    <string name="persistent_sheet_item_create_new_folder_toast">Nuevo elemento de carpeta seleccionado</string>
    <string name="persistent_sheet_item_edit_title">Editar</string>
    <string name="persistent_sheet_item_edit_toast">Editar elemento seleccionado</string>
    <string name="persistent_sheet_item_save_title">Guardar</string>
    <string name="persistent_sheet_item_save_toast">Guardar elemento seleccionado</string>
    <string name="persistent_sheet_item_zoom_in_title">Acercar</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Acercar elemento seleccionado</string>
    <string name="persistent_sheet_item_zoom_out_title">Alejar</string>
    <string name="persistent_sheet_item_zoom_out_toast">Alejar elemento seleccionado</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Disponible</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel García</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Diseñador</string>
    <string name="persona_subtitle_engineer">Ingeniero</string>
    <string name="persona_subtitle_manager">Administrador</string>
    <string name="persona_subtitle_researcher">Investigador</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (ejemplo de texto largo para probar el truncamiento)</string>
    <string name="persona_view_description_xxlarge">Avatar XXlarge con tres líneas de texto</string>
    <string name="persona_view_description_large">Avatar grande con dos líneas de texto</string>
    <string name="persona_view_description_small">Avatar pequeño con una línea de texto</string>
    <string name="people_picker_hint">Ninguno con sugerencia mostrada</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Chip de Persona deshabilitado</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Error en Chip de Persona</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Chip de Persona sin ícono de cierre</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Chip de persona básico</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">Hiciste clic en un Chip de Persona seleccionado.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Compartir</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Seguir</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Invitar a personas</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Actualizar página</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Abrir en el explorador</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">Este es un menú emergente de varias líneas. El número máximo de líneas se establece en dos, el resto del texto se truncará.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">Todas las noticias</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Noticias guardadas</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">Noticias de sitios</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Notificar fuera del horario laboral</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notificar cuando esté inactivo en el escritorio</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">Hiciste clic en el elemento:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Menú simple</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Menú simple2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menú con un elemento seleccionable y una línea divisoria</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menú con todos los elementos seleccionables, iconos y texto largo</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Mostrar</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Progreso circular</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Pequeño</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Mediana</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Grande</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Progreso lineal</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Indeterminado</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinados</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Devolución de llamada del micrófono</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autocorrección</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Micrófono presionado</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Vista derecha presionada</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Búsqueda de teclado presionada</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Mostrar barra de notificaciones emergentes</string>
    <string name="fluentui_dismiss_snackbar">Descartar barra de notificaciones emergentes</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Acción</string>
    <string name="snackbar_action_long">Acción de texto largo</string>
    <string name="snackbar_single_line">Barra de notificaciones emergentes de una sola línea</string>
    <string name="snackbar_multiline">Esta es una barra de herramientas multilínea. El número máximo de líneas se establece en dos, el resto del texto se truncará.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">Esta es una barra de anuncios. Se usa para comunicar nuevas características.</string>
    <string name="snackbar_primary">Esta es una barra principal.</string>
    <string name="snackbar_light">Esta es la barra de notificaciones emergentes delgada.</string>
    <string name="snackbar_warning">Esta es una barra de advertencia.</string>
    <string name="snackbar_danger">Esta es una barra peligrosa.</string>
    <string name="snackbar_description_single_line">Duración corta</string>
    <string name="snackbar_description_single_line_custom_view">Duración larga con progreso circular como vista personalizada pequeña</string>
    <string name="snackbar_description_single_line_action">Duración corta con acción</string>
    <string name="snackbar_description_single_line_action_custom_view">Duración corta con acción y vista personalizada mediana</string>
    <string name="snackbar_description_single_line_custom_text_color">Duración corta con color de texto personalizado</string>
    <string name="snackbar_description_multiline">Duración larga</string>
    <string name="snackbar_description_multiline_custom_view">Duración larga con vista personalizada pequeña</string>
    <string name="snackbar_description_multiline_action">Duración indefinida con actualizaciones de acción y texto</string>
    <string name="snackbar_description_multiline_action_custom_view">Duración corta con acción y vista personalizada mediana</string>
    <string name="snackbar_description_multiline_action_long">Duración corta con texto de acción larga</string>
    <string name="snackbar_description_announcement">Duración corta</string>
    <string name="snackbar_description_updated">Este texto se actualizó.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Mostrar barra de notificaciones emergentes</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Línea única</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Multilínea</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Estilo de anuncio</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Estilo principal</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Estilo claro</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Estilo de advertencia</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Estilo Peligro</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Inicio</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Correo</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Configuración</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notificación</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">Más</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Alineación del texto</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertical</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">Sin texto</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Elementos de pestaña</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Título</string>
    <string name="cell_sample_description">Descripción</string>
    <string name="calculate_cells">Cargar o calcular 100 celdas</string>
    <string name="calculate_layouts">Cargar/calcular 100 diseños</string>
    <string name="template_list">Lista de plantillas</string>
    <string name="regular_list">Lista normal</string>
    <string name="cell_example_title">Título: Celda</string>
    <string name="cell_example_description">Descripción: Pulsar para cambiar la orientación</string>
    <string name="vertical_layout">Vertical</string>
    <string name="horizontal_layout">Horizontal</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Pestaña estándar de 2 segmentos</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Pestaña estándar de 3 segmentos</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Pestaña estándar de 4 segmentos</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Pestaña estándar con paginación</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Cambiar pestaña</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Pestaña de píldoras </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Pulsar para obtener información sobre herramientas</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Pulsar para obtener información sobre herramientas del calendario personalizado</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Pulsar información sobre herramientas de color personalizado</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Pulsar para descartar información sobre herramientas interna</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Pulsar para obtener información sobre herramientas de vista personalizada</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Información sobre herramientas de color personalizado superior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Información sobre herramientas de extremo superior con desplazamiento de 10dpX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Información sobre herramientas de inicio inferior</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Información sobre herramientas de extremo inferior con desplazamiento de 10dpY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Descartar información sobre herramientas interna</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Información sobre herramientas descartada</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">El título es Claro 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">El título 1 es medio y de 20 sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">El título 2 es Regular 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">El título es Normal 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">El subtítulo 1 es normal y de 16 sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">El subtítulo 2 es medio y de 16 sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">El cuerpo 1 es normal y de 14 sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">El cuerpo 2 es medio y de 14 sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">El subtítulo es Normal 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">Versión del SDK: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Elemento %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Carpeta</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Haz clic</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Matriz</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB expandido</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB contraído</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Haz clic para actualizar la lista</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Abrir el cajón</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Elemento de menú</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Desplazamiento X (en dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Desplazamiento Y (en dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Texto de contenido</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Repetir texto de contenido</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">El ancho del menú cambiará con respecto al texto del contenido. El máximo
        el ancho está restringido a 75% de tamaño de pantalla. El margen de contenido del lado e inferior se rige por token. El mismo texto de contenido se repetirá para variar
        el alto.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Abrir menú</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Tarjeta básica</string>
    <!-- UI Label for Card -->
    <string name="file_card">Tarjeta de archivo</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Tarjeta de aviso</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">IU aleatoria</string>
    <!-- UI Label for Options -->
    <string name="card_options">Opciones</string>
    <!-- UI Label for Title -->
    <string name="card_title">Título</string>
    <!-- UI Label for text -->
    <string name="card_text">Texto</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Subtexto</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">La copia secundaria de este banner puede ajustarse a dos líneas si es necesario.</string>
    <!-- UI Label Button -->
    <string name="card_button">Botón</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Mostrar cuadro de diálogo</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Descartar cuadro de diálogo al hacer clic afuera</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Descartar cuadro de diálogo al volver atrás</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Cuadro de diálogo descartado</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Cancelar</string>
    <!-- UI Label Ok -->
    <string name="ok">Aceptar</string>
    <!-- A sample description -->
    <string name="dialog_description">Un cuadro de diálogo es una ventana pequeña que pide al usuario que tome una decisión o escriba información adicional.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Abrir el cajón</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Expandir cajón</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Cerrar Cajón</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Seleccionar tipo de cajón</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Superior</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Todo el cajón se muestra en la región visible.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Inferior</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Todo el cajón se muestra en la región visible. Desliza el dedo hacia arriba en el contenido de desplazamiento en movimiento. Expandir cajón expandible mediante el controlador de arrastre.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Deslizar a la izquierda</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Deslizar el cajón hacia la región visible desde el lado izquierdo.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Deslizar a la derecha</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Deslizar el cajón hacia la región visible desde el lado derecho.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Deslizar hacia abajo</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Deslizar el cajón hacia la región visible desde la parte inferior de la pantalla. Al deslizar el dedo hacia arriba sobre el cajón expandible, el resto de la parte se desliza a la región visible &amp; después, desplázate.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim Visible</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Seleccionar contenido del cajón</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Contenido desplazable de tamaño de pantalla completa</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">Más de la mitad del contenido de la pantalla</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Menos de la mitad del contenido de la pantalla</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Contenido de tamaño dinámico</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Contenido del cajón anidado</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Expansible</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Omitir estado abierto</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Impedir el descarte al hacer clic en Scrim</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Mostrar identificador</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Título</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Texto de información sobre herramientas</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Pulsar para obtener información sobre herramientas de contenido personalizado</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Inicio superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Extremo superior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Inicio inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Extremo inferior </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Centro </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Centro personalizado</string>

    <!-- V2 Demo App -->
    <string name="release_notes_updates">Para obtener actualizaciones sobre las notas de la versión, </string>
    <string name="click_here">haz clic aquí.</string>
    <string name="open_source_cross_platform">Sistema de diseño multiplataforma de código abierto.</string>
    <string name="intuitive_and_powerful">Intuitivo &amp; Poderoso.</string>
    <string name="design_tokens">Tokens de diseño</string>
    <string name="release_notes">Notas de la versión</string>
    <string name="github_repo">Repositorio de GitHub</string>
    <string name="github_repo_link">Vínculo de repositorio de GitHub</string>
    <string name="report_issue">Notificar problema</string>
    <string name="v1_components">Componentes de V1</string>
    <string name="v2_components">Componentes V2</string>
    <string name="all_components">Todo</string>
    <string name="fluent_logo">Logotipo de Fluent</string>
    <string name="new_badge">Nuevo</string>
    <string name="modified_badge">Modificado</string>
    <string name="api_break_badge">Interrupción de API</string>
    <string name="app_bar_more">Más</string>
    <string name="accent">Acento</string>
    <string name="appearance">Apariencia</string>
    <string name="choose_brand_theme">Elige el tema de tu marca:</string>
    <string name="fluent_brand_theme">Marca Fluent</string>
    <string name="one_note_theme">OneNote</string>
    <string name="word_theme">Word</string>
    <string name="excel_theme">Excel</string>
    <string name="powerpoint_theme">PowerPoint</string>
    <string name="m365_theme">M365</string>
    <string name="teams_theme">MS Teams</string>
    <string name="choose_appearance">Elegir apariencia</string>
    <string name="appearance_system_default">Predeterminado del sistema</string>
    <string name="appearance_light">Claro</string>
    <string name="appearance_dark">Oscuro</string>
    <string name="demo_activity_github_link">Vínculo de GitHub de la actividad de demostración</string>
    <string name="control_tokens_details">Detalles de tokens de control</string>
    <string name="parameters">Parámetros</string>
    <string name="control_tokens">Control Tokens</string>
    <string name="global_tokens">Tokens globales</string>
    <string name="alias_tokens">Tokens de alias</string>
    <string name="sample_text">Texto</string>
    <string name="sample_icon">Icono de ejemplo</string>
    <string name="color">Color</string>
    <string name="neutral_color_tokens">Tokens de color neutros</string>
    <string name="font_size_tokens">Tokens de tamaño de fuente</string>
    <string name="line_height_tokens">Tokens de alto de línea</string>
    <string name="font_weight_tokens">Tokens de espesor de fuente</string>
    <string name="icon_size_tokens">Tokens de tamaño de icono</string>
    <string name="size_tokens">Tokens de tamaño</string>
    <string name="shadow_tokens">Tokens de sombra</string>
    <string name="corner_radius_tokens">Tokens de radio de esquina</string>
    <string name="stroke_width_tokens">Tokens de ancho de trazo</string>
    <string name="brand_color_tokens">Tokens de color de marca</string>
    <string name="neutral_background_color_tokens">Tokens de color de fondo neutros</string>
    <string name="neutral_foreground_color_tokens">Tokens de color de primer plano neutros</string>
    <string name="neutral_stroke_color_tokens">Tokens de color de trazo neutro</string>
    <string name="brand_background_color_tokens">Tokens de color de fondo de marca</string>
    <string name="brand_foreground_color_tokens">Tokens de color de primer plano de marca</string>
    <string name="brand_stroke_color_tokens">Tokens de color de trazo de marca</string>
    <string name="error_and_status_color_tokens">Tokens de color de error y estado</string>
    <string name="presence_tokens">Tokens de color de presencia</string>
    <string name="typography_tokens">Tokens de tipografía</string>
    <string name="unspecified">Sin especificar</string>

</resources>