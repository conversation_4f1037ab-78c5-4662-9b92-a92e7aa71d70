<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">સો</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">મં</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">બુ</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">ગુ</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">શુ</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">શ</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">ર</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">આગલાં સપ્તાહ પર જાઓ</string>
    <string name="accessibility_goto_previous_week">પાછલાં સપ્તાહ પર જાઓ</string>
    <string name="accessibility_today">આજે</string>
    <string name="accessibility_selected">પસંદ કરેલ</string>

    <!-- *** Shared *** -->
    <string name="done">પૂર્ણ થયું</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">આજે</string>
    <string name="tomorrow">આવતી કાલે</string>
    <string name="yesterday">ગઈકાલે</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">પ્રારંભ સમય</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">સમાપ્તિ સમય</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">પ્રારંભ તારીખ</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">સમાપ્તિ તારીખ</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">સમય પસંદ કરો</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">તારીખ પસંદ કરો</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">તારીખ સમય પીકર</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">તારીખ પીકર</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">તારીખ સમય પીકર શ્રેણી</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">તારીખ પિકર શ્રેણી</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">પ્રારંભ સમય ટૅબ</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">સમાપ્તિ સમય ટૅબ</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">પ્રારંભ તારીખ ટૅબ</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">સમાપ્તિ તારીખ ટૅબ</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">સંવાદ બંધ કરો</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">વધારાનો મહિનો</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">આગલો મહિનો પસંદ કરો</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">ઘટાડાનો મહિનો</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">પાછલો મહિનો પસંદ કરો</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">વધારાનો દિવસ</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">આગલો દિવસ પસંદ કરો</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">ઘટાડાનો દિવસ</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">પાછલો દિવસ પસંદ કરો</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">વધારાનું વર્ષ</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">આગલું વર્ષ પસંદ કરો</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">ઘટાડાનું વર્ષ</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">પાછલું વર્ષ પસંદ કરો</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">વધારાની તારીખ</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">આગલી તારીખ પસંદ કરો</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">ઘટાડાની તારીખ</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">પાછલી તારીખ પસંદ કરો</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">વધારાનો કલાક</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">આગલો કલાક પસંદ કરો</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">ઘટાડાનો કલાક</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">પહેલાંના કલાક પસંદ કરો</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">વધારાની મિનિટ</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">આગલી મિનિટ પસંદ કરો</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">ઘટાડાની મિનિટ</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">પાછલી મિનિટ પસંદ કરો</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">AM PM અવધિને ટૉગલ કરો</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">AM PM અવધિને ટૉગલ કરો</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s પસંદ કરેલ છે</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">પસંદ કરેલ</string>
</resources>
