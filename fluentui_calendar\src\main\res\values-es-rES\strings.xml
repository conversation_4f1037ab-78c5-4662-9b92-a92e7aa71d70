<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources>

    <!-- weekday initials -->
    <!--Initial that represents the day Monday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="monday_initial" comment="initial for monday">L</string>
    <!--Initial that represents the day Tuesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="tuesday_initial" comment="initial for tuesday">M</string>
    <!--Initial that represents the day Wednesday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="wednesday_initial" comment="initial for wednesday">X</string>
    <!--Initial that represents the day Thursday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="thursday_initial" comment="initial for thursday">J</string>
    <!--Initial that represents the day Friday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="friday_initial" comment="initial for friday">V</string>
    <!--Initial that represents the day Saturday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="saturday_initial" comment="initial for saturday">S</string>
    <!--Initial that represents the day Sunday. For English this should be the first letter,
    but for other languages like Russian, it might be two or more letters.-->
    <string name="sunday_initial" comment="initial for sunday">D</string>

    <!-- accessibility -->
    <string name="accessibility_goto_next_week">Ir a la próxima semana</string>
    <string name="accessibility_goto_previous_week">Ir a la semana anterior</string>
    <string name="accessibility_today">hoy</string>
    <string name="accessibility_selected">Seleccionado</string>

    <!-- *** Shared *** -->
    <string name="done">Listo</string>

    <!-- *** CalendarView *** -->
    <string name="date_time">%1$s, %2$s</string>
    <string name="today">Hoy</string>
    <string name="tomorrow">Mañana</string>
    <string name="yesterday">Ayer</string>

    <!-- *** TimePicker *** -->
    <!--date picker tab title for start time range-->
    <string name="date_time_picker_start_time">Hora de inicio</string>
    <!--date picker tab title for end time range-->
    <string name="date_time_picker_end_time">Hora de finalización</string>
    <!--date picker tab title for start date range-->
    <string name="date_time_picker_start_date">Fecha de inicio</string>
    <!--date picker tab title for end date range-->
    <string name="date_time_picker_end_date">Fecha de finalización</string>
    <!--date picker dialog title for time selection-->
    <string name="date_time_picker_choose_time">Elige una hora</string>
    <!--date picker dialog title for date selection-->
    <string name="date_time_picker_choose_date">Elegir fecha</string>

    <!--accessibility-->
    <!--Announcement for date time picker-->
    <string name="date_time_picker_accessibility_dialog_title">Selector de fecha y hora</string>
    <!--Announcement for date picker-->
    <string name="date_picker_accessibility_dialog_title">Selector de fecha</string>
    <!--Announcement for date time picker range-->
    <string name="date_time_picker_range_accessibility_dialog_title">Intervalo del selector de fecha y hora</string>
    <!--Announcement for date picker range-->
    <string name="date_picker_range_accessibility_dialog_title">Intervalo de selector de fecha</string>
    <!--date time picker tab title for start time range-->
    <string name="date_time_picker_accessiblility_start_time">Pestaña de la hora de inicio</string>
    <!--date time picker tab title for end time range-->
    <string name="date_time_picker_accessiblility_end_time">Pestaña de la hora final</string>
    <!--date picker tab title for start date range-->
    <string name="date_picker_accessiblility_start_date">Pestaña de la fecha de inicio</string>
    <!--date picker tab title for end date range-->
    <string name="date_picker_accessiblility_end_date">Pestaña de la fecha de finalización</string>
    <!--date time picker dialog close button-->
    <string name="date_time_picker_accessibility_close_dialog_button">Cerrar cuadro de diálogo</string>
    <!--date number picker month increment button-->
    <string name="date_picker_accessibility_increment_month_button">Incrementar mes</string>
    <!-- date time picker click action next month announcement-->
    <string name="date_picker_accessibility_next_month_click_action">seleccionar siguiente mes</string>
    <!--date number picker month decrement button-->
    <string name="date_picker_accessibility_decrement_month_button">Disminuir mes</string>
    <!-- date time picker click action previous month announcement-->
    <string name="date_picker_accessibility_previous_month_click_action">seleccionar mes anterior</string>
    <!--date number picker day increment button-->
    <string name="date_picker_accessibility_increment_day_button">Incrementar día</string>
    <!-- date time picker click action next day announcement-->
    <string name="date_picker_accessibility_next_day_click_action">seleccionar siguiente día</string>
    <!--date number picker day decrement button-->
    <string name="date_picker_accessibility_decrement_day_button">Disminuir día</string>
    <!-- date time picker click action previous day announcement-->
    <string name="date_picker_accessibility_previous_day_click_action">seleccionar día anterior</string>
    <!--date number picker year increment button-->
    <string name="date_picker_accessibility_increment_year_button">Incrementar año</string>
    <!-- date time picker click action next year announcement-->
    <string name="date_picker_accessibility_next_year_click_action">seleccionar siguiente año</string>
    <!--date number picker year decrement button-->
    <string name="date_picker_accessibility_decrement_year_button">Disminuir año</string>
    <!-- date time picker click action previous year announcement-->
    <string name="date_picker_accessibility_previous_year_click_action">seleccionar año anterior</string>
    <!--date time number picker date increment button-->
    <string name="date_time_picker_accessibility_increment_date_button">Incrementar fecha</string>
    <!-- date time picker click action next date announcement-->
    <string name="date_picker_accessibility_next_date_click_action">seleccionar la siguiente fecha</string>
    <!--date time number picker date decrement button-->
    <string name="date_time_picker_accessibility_decrement_date_button">Disminuir fecha</string>
    <!-- date time picker click action previous date announcement-->
    <string name="date_picker_accessibility_previous_date_click_action">seleccionar fecha anterior</string>
    <!--date time number picker hour increment button-->
    <string name="date_time_picker_accessibility_increment_hour_button">Incrementar hora</string>
    <!-- date time picker click action next hour announcement-->
    <string name="date_picker_accessibility_next_hour_click_action">seleccionar siguiente hora</string>
    <!--date time number picker hour decrement button-->
    <string name="date_time_picker_accessibility_decrement_hour_button">Disminuir hora</string>
    <!-- date time picker click action previous hour announcement-->
    <string name="date_picker_accessibility_previous_hour_click_action">seleccionar hora anterior</string>
    <!--date time number picker minute increment button-->
    <string name="date_time_picker_accessibility_increment_minute_button">Incrementar minutos</string>
    <!-- date time picker click action next minute announcement-->
    <string name="date_picker_accessibility_next_minute_click_action">seleccionar siguiente minuto</string>
    <!--date time number picker minute decrement button-->
    <string name="date_time_picker_accessibility_decrement_minute_button">Disminuir minutos</string>
    <!-- date time picker click action previous minute announcement-->
    <string name="date_picker_accessibility_previous_minute_click_action">seleccionar minuto anterior</string>
    <!--date time number picker period (am/pm) toggle button-->
    <string name="date_time_picker_accessibility_period_toggle_button">Alternar período de a.m. y p.m.</string>
    <!--date time number picker click action period (am/pm) announcement-->
    <string name="date_time_picker_accessibility_period_toggle_click_action">alternar período de a.m. y p.m.</string>
    <!--Announces the selected date and/or time-->
    <string name="date_time_picker_accessibility_selected_date">%s seleccionado</string>

    <!-- *** Calendar *** -->

    <!--accessibility-->
    <!--Describes the action used to select calendar item-->
    <string name="calendar_adapter_accessibility_item_selected">seleccionado</string>
</resources>
