<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Describes the primary and secondary Strings -->
    <string name="fluentui_primary">主要</string>
    <string name="fluentui_secondary">次要</string>
    <!-- Describes dismiss behaviour -->
    <string name="fluentui_dismiss">關閉</string>
    <!-- Describes selected action-->
    <string name="fluentui_selected">已選取</string>
    <!-- Describes not selected action-->
    <string name="fluentui_not_selected">未選取</string>
    <!-- Describes the icon component -->
    <string name="fluentui_icon">圖示</string>
    <!-- Describes the image component with accent color -->
    <string name="fluentui_accent_icon">圖示</string>
    <!-- Describes disabled action-->
    <string name="fluentui_disabled">已停用</string>
    <!-- Describes the action button component -->
    <string name="fluentui_action_button">動作按鈕</string>
    <!-- Describes the dismiss button component -->
    <string name="fluentui_dismiss_button">Dismiss</string>
    <!-- Describes enabled action-->
    <string name="fluentui_enabled">已啟用</string>
    <!-- Describes close action for a sheet-->
    <string name="fluentui_close_sheet">關閉工作表</string>
    <!-- Describes close action -->
    <string name="fluentui_close">關閉</string>
    <!-- Describes cancel action -->
    <string name="fluentui_cancel">取消</string>
    <!--name of the icon -->
    <string name="fluentui_search">搜尋</string>
    <!--name of the icon -->
    <string name="fluentui_microphone">麥克風</string>
    <!-- Describes the action - to clear the text -->
    <string name="fluentui_clear_text">清除文字</string>
    <!-- Describes the action - back -->
    <string name="fluentui_back">上一頁</string>
    <!-- Describes the action - activated -->
    <string name="fluentui_activated">已啟動</string>
    <!-- Describes the action - deactivated -->
    <string name="fluentui_deactivated">停用</string>
    <!-- Describes the type - Neutral-->
    <string name="fluentui_neutral">沒意見</string>
    <!-- Describes the type - Brand-->
    <string name="fluentui_brand">品牌</string>
    <!-- Describes the type - Contrast-->
    <string name="fluentui_contrast">對比</string>
    <!-- Describes the type - Accent-->
    <string name="fluentui_accent">輔色</string>
    <!-- Describes the type - Warning-->
    <string name="fluentui_warning">警告</string>
    <!-- Describes the type - Danger-->
    <string name="fluentui_danger">危險</string>
    <!-- Describes the error string -->
    <string name="fluentui_error_string">發生錯誤</string>
    <string name="fluentui_error">錯誤</string>
    <!-- Describes the hint Text -->
    <string name="fluentui_hint">提示</string>
    <!--name of the icon -->
    <string name="fluentui_chevron">山形箭號</string>
    <!-- Describes the outline design of control -->
    <string name="fluentui_outline">大綱</string>

    <string name="fluentui_action_button_icon">動作按鈕圖示</string>
    <string name="fluentui_center">文字置中</string>
    <string name="fluentui_accessory_button">周邊按鈕</string>

    <!--Describes the control -->
    <string name="fluentui_radio_button">選項按鈕</string>
    <string name="fluentui_label">標籤</string>

    <!-- Describes the expand/collapsed state of control -->
    <string name="fluentui_expanded">已展開</string>
    <string name="fluentui_collapsed">已摺疊</string>

    <!--types of control -->
    <string name="fluentui_large">大型</string>
    <string name="fluentui_medium">中型</string>
    <string name="fluentui_small">小型</string>
    <string name="fluentui_password_mode">密碼模式</string>

    <!-- Decribes the subtitle component of list -->
    <string name="fluentui_subtitle">字幕</string>
    <string name="fluentui_assistive_text">輔助文字</string>

    <!-- Decribes the title component of list -->
    <string name="fluentui_title">標題</string>

    <!-- Durations for Snackbar -->
    <string name="fluentui_short">短</string>"
    <string name="fluentui_long">長</string>"
    <string name="fluentui_indefinite">無限期</string>"

    <!-- Describes the behaviour on components -->
    <string name="fluentui_button_pressed">按下的按鈕</string>
    <string name="fluentui_dismissed">已關閉</string>
    <string name="fluentui_timeout">逾時</string>
    <string name="fluentui_left_swiped">向左撥動</string>
    <string name="fluentui_right_swiped">向右撥動</string>

    <!-- Describes the Keyboard Types -->
    <string name="fluentui_keyboard_text">文字</string>
    <string name="fluentui_keyboard_ascii">ASCII</string>
    <string name="fluentui_keyboard_number">數字</string>
    <string name="fluentui_keyboard_phone">電話</string>
    <string name="fluentui_keyboard_uri">URI</string>
    <string name="fluentui_keyboard_email">電子郵件</string>
    <string name="fluentui_keyboard_password">密碼</string>
    <string name="fluentui_keyboard_number_password">NumberPassword</string>
    <string name="fluentui_keyboard_decimal">小數</string>
</resources>