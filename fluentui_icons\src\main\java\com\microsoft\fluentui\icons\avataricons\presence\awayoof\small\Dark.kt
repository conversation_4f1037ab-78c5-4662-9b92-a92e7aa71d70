package com.microsoft.fluentui.icons.avataricons.presence.awayoof.small

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import com.microsoft.fluentui.icons.avataricons.presence.awayoof.SmallGroup

val SmallGroup.Dark: ImageVector
    get() {
        if (_dark != null) {
            return _dark!!
        }
        _dark = Builder(name = "Dark", defaultWidth = 14.0.dp, defaultHeight = 14.0.dp,
                viewportWidth = 14.0f, viewportHeight = 14.0f).apply {
            path(fill = SolidColor(Color(0xFF000000)), stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(7.0f, 7.0f)
                moveToRelative(-6.0f, 0.0f)
                arcToRelative(6.0f, 6.0f, 0.0f, true, true, 12.0f, 0.0f)
                arcToRelative(6.0f, 6.0f, 0.0f, true, true, -12.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0xFFE959D9)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(7.3488f, 5.8508f)
                curveTo(7.5439f, 5.6554f, 7.5436f, 5.3388f, 7.3482f, 5.1437f)
                curveTo(7.1527f, 4.9486f, 6.8362f, 4.9489f, 6.641f, 5.1443f)
                lineTo(5.1436f, 6.6443f)
                curveTo(4.9487f, 6.8395f, 4.9487f, 7.1556f, 5.1436f, 7.3508f)
                lineTo(6.6411f, 8.8508f)
                curveTo(6.8362f, 9.0462f, 7.1527f, 9.0465f, 7.3482f, 8.8514f)
                curveTo(7.5436f, 8.6563f, 7.5439f, 8.3397f, 7.3488f, 8.1443f)
                lineTo(6.7031f, 7.4976f)
                horizontalLineTo(8.5027f)
                curveTo(8.7788f, 7.4976f, 9.0027f, 7.2737f, 9.0027f, 6.9976f)
                curveTo(9.0027f, 6.7214f, 8.7788f, 6.4976f, 8.5027f, 6.4976f)
                horizontalLineTo(6.7031f)
                lineTo(7.3488f, 5.8508f)
                close()
                moveTo(6.9977f, 2.0f)
                curveTo(4.2375f, 2.0f, 2.0f, 4.2375f, 2.0f, 6.9977f)
                curveTo(2.0f, 9.7578f, 4.2375f, 11.9954f, 6.9977f, 11.9954f)
                curveTo(9.7578f, 11.9954f, 11.9954f, 9.7578f, 11.9954f, 6.9977f)
                curveTo(11.9954f, 4.2375f, 9.7578f, 2.0f, 6.9977f, 2.0f)
                close()
                moveTo(3.0f, 6.9977f)
                curveTo(3.0f, 4.7898f, 4.7898f, 3.0f, 6.9977f, 3.0f)
                curveTo(9.2056f, 3.0f, 10.9954f, 4.7898f, 10.9954f, 6.9977f)
                curveTo(10.9954f, 9.2056f, 9.2056f, 10.9954f, 6.9977f, 10.9954f)
                curveTo(4.7898f, 10.9954f, 3.0f, 9.2056f, 3.0f, 6.9977f)
                close()
            }
        }
                .build()
        return _dark!!
    }

private var _dark: ImageVector? = null
